import org.jooq.meta.jaxb.SchemaMappingType

plugins {
    id("com.hellofresh.cif.application-conventions")
    `test-functional`
    alias(libs.plugins.jooq)
    alias(libs.plugins.openapi)
    alias(libs.plugins.gradle.docker.compose)
}

description = "Distribution center management API"
group = "$group.distribution-center-api"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        schemata = listOf(
                            SchemaMappingType().apply {
                                inputSchema = "public"
                            },
                            SchemaMappingType().apply {
                                inputSchema = "config"
                            }
                        )
                        includes = "distribution_center|inventory_processed_snapshots"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = true
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}
val generatedSourcesDir = "${project.layout.buildDirectory.get()}/generated/openapi"
sourceSets.getByName("main").java {
    srcDir("$generatedSourcesDir/src/main/kotlin")
}

tasks {
    openApiGenerate.configure {
        this.generatorName.set("kotlin-server")
        this.inputSpec.set("$projectDir/src/main/resources/openapi.yaml")
        this.outputDir.set(generatedSourcesDir)
        this.modelPackage.set("com.hellofresh.cif.distributionCenter.api.generated.model")

        this.configOptions.put("featureLocations", "false")
        this.configOptions.put("featureMetrics", "false")
        this.configOptions.put("serializationLibrary", "jackson")
        this.configOptions.put("interfaceOnly", "true")
        this.configOptions.put("enumPropertyNaming", "UPPERCASE")
        this.typeMappings.put("double", "java.math.BigDecimal")
        this.typeMappings.put("date-time", "java.time.OffsetDateTime")
        this.additionalProperties.put("useTags", true)
        this.globalProperties.put("apis", "false")
        this.globalProperties.put("models", "")
    }
    openApiValidate.configure {
        this.inputSpec.set("$projectDir/src/main/resources/openapi.yaml")
    }
    compileKotlin.configure {
        dependsOn("openApiValidate", "openApiGenerate")
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(libs.krontab)
    implementation(libs.ktor.client.encoding)
    implementation(libs.ktor.core)
    implementation(libs.ktor.content.negotiation)
    implementation(libs.ktor.jackson)
    implementation(libs.ktor.netty)
    implementation(libs.ktor.server.auth.jwt)
    implementation(libs.ktor.server.callid)
    implementation(libs.ktor.server.compression)
    implementation(libs.ktor.micrometer)
    implementation(libs.hellofresh.schemaregistry) {
        exclude(group = "com.google.api.grpc", module = "proto-google-common-protos")
    }
    implementation(libs.protobuf.grpc)
    implementation(projects.lib)
    implementation(projects.lib.db)
    implementation(projects.distributionCenterModels)

    testImplementation(libs.flyway.core)
    testImplementation(libs.ktor.test)
    testImplementation(libs.mockk)
    testImplementation(projects.libTests)
}
