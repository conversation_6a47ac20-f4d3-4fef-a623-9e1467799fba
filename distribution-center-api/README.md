# Distribution center API

A service to manage the distribution center information i.e supports retrieve, create, update APIs, whenever the user updates the existing DC
, the DC will be updated in the config/distribution_center table, then a one minute scheduler polls the config/distribution_center table
and check for the flag 'published', if its false, then publish to csku-inventory-forecast.intermediate.distribution-center.v*.

## How to start the distribution center locally ?

1. <PERSON> docker-compose up broker kafdrop
2. <PERSON> docker-compose up inventory-postgres db-migration
3. Create a topic “csku-inventory-forecast.intermediate.distribution-center.v*” in the broker 'http://localhost:19000'
4. Copy the following environment variables IDE in 'Edit Configurations'
   HF_INVENTORY_DB_MASTER_HOST=localhost;HF_INVENTORY_DB_PASSWORD=123456;HF_INVENTORY_DB_USERNAME=cif;HF_SCHEMA_REGISTRY_AIVEN_USERNAME=csku-inventory-forecast;
   HF_SCHEMA_REGISTRY_URL='https://kafka-live-hellofresh-live.aivencloud.com:23411';HF_TIER=local;HF_AUTH_SERVICE_JWT_SECRET_KEY=""
5. Add the broker address in application.properties
   bootstrap.servers=localhost:29092
