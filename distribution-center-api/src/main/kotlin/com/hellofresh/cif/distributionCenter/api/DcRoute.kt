package com.hellofresh.cif.distributionCenter.api

import com.hellofresh.cif.config.CountryMarketMapping
import com.hellofresh.cif.distributionCenter.api.generated.model.DcConfigurationResponse
import com.hellofresh.cif.distributionCenter.api.generated.model.ErrorResponse
import com.hellofresh.cif.distributionCenter.api.generated.model.SnapshotTime
import com.hellofresh.cif.distributionCenter.api.generated.model.UpdateDcConfigurationMultiParamRequest
import com.hellofresh.cif.distributionCenter.api.generated.model.UpdateDcConfigurationRequest
import com.hellofresh.cif.distributionCenter.model.DcParamRequest
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenter.repo.DcConfiguration
import com.hellofresh.cif.distributionCenter.service.DcAlreadyExistsException
import com.hellofresh.cif.distributionCenter.service.DistributionCenterService
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.authenticate
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.principal
import io.ktor.server.plugins.BadRequestException
import io.ktor.server.plugins.NotFoundException
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.put
import io.ktor.server.routing.route
import java.time.DayOfWeek
import java.time.LocalTime
import java.time.OffsetDateTime
import kotlinx.coroutines.withTimeout
import org.apache.logging.log4j.kotlin.logger

private const val DC_CODE = "dcCode"
fun Routing.distributionCenterRouteUpdate(
    dcService: DistributionCenterService,
    timeout: kotlin.time.Duration
) = route("/dc") {
    authenticate {
        put("/{$DC_CODE}") {
            runCatching {
                val authorEmail = this.call.getAuthorEmailFromJwt()
                val dcCode = this.call.parameters[DC_CODE]!!
                val newCleardown =
                    DayOfWeek.valueOf(this.call.receive<UpdateDcConfigurationRequest>().cleardownDay.value)
                withTimeout(timeout.inWholeMilliseconds) {
                    dcService.updateCleardownDay(dcCode, newCleardown, authorEmail)
                }
            }.onSuccess {
                if (it == null) {
                    call.respond(HttpStatusCode.NotFound)
                } else {
                    call.respond(HttpStatusCode.OK, it)
                }
            }.onFailure {
                call.handleResponseError(it)
            }
        }
        put("/{$DC_CODE}/v1") {
            runCatching {
                val authorEmail = this.call.getAuthorEmailFromJwt()
                val dcCode = this.call.parameters[DC_CODE]!!
                val updateRequest = call.receive<UpdateDcConfigurationMultiParamRequest>()
                val dcParam = getDcParam(dcCode, authorEmail, updateRequest)

                withTimeout(timeout.inWholeMilliseconds) { dcService.updateDc(dcParam) }
            }.onSuccess {
                if (it == null) {
                    call.respond(HttpStatusCode.NotFound)
                } else {
                    call.respond(HttpStatusCode.OK, it)
                }
            }.onFailure {
                call.handleResponseError(it)
            }
        }
    }
}

fun Routing.distributionCenterRouteRetrieve(
    dcService: DistributionCenterService
) = route("/dc") {
    authenticate {
        get("") {
            kotlin.runCatching {
                val country = call.parameters.getOrThrow("country").trim().uppercase()
                CountryMarketMapping.countryMarketMap[country] ?: throw NotFoundException("Country $country not found")
            }.onFailure { exception ->
                if (exception is NotFoundException) {
                    call.respond(HttpStatusCode.NotFound, call.handleResponseError(exception))
                } else {
                    call.respond(HttpStatusCode.BadRequest, call.handleResponseError(exception))
                }
            }.onSuccess { market ->
                runCatching {
                    dcService.getAllDcs(market)
                }.onSuccess {
                    if (it.isNotEmpty()) {
                        call.respond(HttpStatusCode.OK, it.map { dc -> mapToDcConfigurationResponse(dc) })
                    } else {
                        call.respond(HttpStatusCode.NoContent)
                    }
                }.onFailure {
                    call.handleResponseError(it)
                }
            }
        }
        get("/{$DC_CODE}") {
            runCatching {
                val dcCode = call.parameters[DC_CODE]!!
                val fromDateTime = call.parameters["from"]?.let {
                    OffsetDateTime.parse(it).toLocalDateTime()
                }
                val toDateTime = call.parameters["to"]?.let {
                    OffsetDateTime.parse(it).toLocalDateTime()
                }
                dcService.getByCode(dcCode, fromDateTime, toDateTime)
            }.onSuccess {
                if (it != null) {
                    call.respond(HttpStatusCode.OK, mapToDcConfigurationResponse(it))
                } else {
                    call.respond(HttpStatusCode.NotFound)
                }
            }.onFailure {
                call.handleResponseError(it)
            }
        }
    }
}

fun getDcParam(
    dcCode: String,
    authorEmail: String,
    updateRequest: UpdateDcConfigurationMultiParamRequest,
): DcParamRequest {
    val updatedProductionStart = DayOfWeek.valueOf(updateRequest.productionStart.value)
    val updatedCleardown = DayOfWeek.valueOf(updateRequest.cleardownDay.value)
    val updatedDcEnabled = updateRequest.enabled
    val updatedDcHasCleardown = updateRequest.hasCleardown
    return DcParamRequest(
        dcCode = dcCode,
        cleardown = updatedCleardown,
        productionStart = updatedProductionStart,
        enabled = updatedDcEnabled,
        hasCleardown = updatedDcHasCleardown,
        lastUpdatedByEmail = authorEmail,
        scheduledClearDownTime = updateRequest.scheduledCleardownTime?.let { LocalTime.parse(it) } ?: LocalTime.of(0, 0),
        wmsType = updateRequest.wmsType?.let { WmsSystem.valueOf(it) },
        poCutoffTime = updateRequest.poCutOffTime?.let { time -> LocalTime.parse(time) },
    )
}

private fun ApplicationCall.getAuthorEmailFromJwt(): String {
    val jwt = this.principal<JWTPrincipal>()
    requireNotNull(jwt) { "JWT Token doesn't exist" }
    val userEmail = jwt["email"]
    require(userEmail != null) { "Email has to be present in JWT. Aborting" }
    return userEmail
}

fun mapToDcConfigurationResponse(dcConfiguration: DcConfiguration) = DcConfigurationResponse(
    dcCode = dcConfiguration.dcCode,
    dcName = dcConfiguration.dcName,
    market = dcConfiguration.market,
    productionStartDay = toDayOfWeekResponse(dcConfiguration.productionStart),
    cleardownDay = dcConfiguration.cleardown?.let { toDayOfWeekResponse(it) },
    zoneId = dcConfiguration.zoneId.id,
    enabled = dcConfiguration.enabled,
    hasCleardown = dcConfiguration.hasCleardown,
    scheduledCleardownTime = dcConfiguration.scheduledClearDownTime.toString(),
    snapshots = dcConfiguration.snapshotTimes.map { snapshot ->
        SnapshotTime(
            snapshotId = snapshot.snapshotId,
            snapshotTime = snapshot.snapshotTime,
        )
    },
    poCutOffTime = dcConfiguration.poCutoffTime?.toString(),
    brands = dcConfiguration.brands,
)

fun Parameters.getOrThrow(name: String) = get(name)
    ?.let { it.ifBlank { throw RequestValidationException(name) } }
    ?: throw RequestValidationException(name)

data class RequestValidationException(
    val name: String,
    val statusCode: HttpStatusCode = HttpStatusCode.BadRequest
) : Exception("Required Parameter: $name is not specified")

fun toDayOfWeekResponse(dayOfWeek: DayOfWeek) =
    com.hellofresh.cif.distributionCenter.api.generated.model.DayOfWeek.valueOf(dayOfWeek.name)

suspend fun ApplicationCall.handleResponseError(throwable: Throwable) {
    val errorResponse = ErrorResponse(throwable.message)
    logger().warn("Failed to process request", throwable)
    when (throwable) {
        is IllegalArgumentException, is BadRequestException -> respond(HttpStatusCode.BadRequest, errorResponse)
        is NotFoundException -> respond(HttpStatusCode.NotFound, errorResponse)
        is DcAlreadyExistsException -> respond(HttpStatusCode.Conflict, errorResponse)
        else -> respond(HttpStatusCode.InternalServerError, errorResponse)
    }
}
