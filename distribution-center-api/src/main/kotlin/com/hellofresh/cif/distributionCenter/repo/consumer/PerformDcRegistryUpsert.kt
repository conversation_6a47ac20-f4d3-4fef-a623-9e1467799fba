package com.hellofresh.cif.distributionCenter.repo.consumer

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distribution_center_api.schema.config.Tables
import com.hellofresh.proto.stream.scm.registry.dc.v1beta1.DistributionCenter
import kotlinx.coroutines.future.await
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.jooq.Batch
import org.jooq.impl.DSL

private const val DEFAULT_PRODUCTION_START = "MONDAY"
private const val DEFAULT_CLEARDOWN = "FRIDAY"

class PerformDcRegistryUpsert(
    private val metricsDSLContext: MetricsDSLContext,
    private val emailAddress: String
) : suspend (ConsumerRecords<String, DistributionCenter>) -> Unit {

    override suspend fun invoke(records: ConsumerRecords<String, DistributionCenter>) {
        BatchQueryBuilder(metricsDSLContext).let {
            records.map { v -> it.bind(v, emailAddress) }
                .lastOrNull()
                ?.executeAsync()
        }?.await()
    }

    /**
     * Provides a safe interface to build PreparedStatement
     */
    private class BatchQueryBuilder(dsl: MetricsDSLContext) {
        private val query = dsl.withTagName("insert-distribution-center")
            .batch(
                Tables.DISTRIBUTION_CENTER.run {
                    DSL.insertInto(this)
                        .columns(
                            DC_CODE,
                            DC_NAME,
                            MARKET,
                            PRODUCTION_START,
                            CLEARDOWN,
                            ZONE_ID,
                            GLOBAL_DC,
                            ENABLED,
                            PUBLISHED,
                            HAS_CLEARDOWN,
                            LAST_UPDATED_BY_EMAIL,
                            WMS_TYPE,
                            BRANDS
                        )
                        .values(
                            null, null, null, null, null, null, null, false, true, false, null, null, emptyArray()
                        )
                        .onDuplicateKeyUpdate()
                        .set(DC_NAME, "")
                        .set(MARKET, "")
                        .set(ZONE_ID, "")
                        .setNull(GLOBAL_DC)
                        .set(PUBLISHED, false)
                        .set(WMS_TYPE, "")
                        .set(BRANDS, emptyArray())
                },
            )

        lateinit var runnableQuery: RunnableQuery

        fun bind(record: ConsumerRecord<String, DistributionCenter>, emailAddress: String): RunnableQuery {
            if (!this::runnableQuery.isInitialized) {
                runnableQuery = RunnableQuery(query)
            }
            val value = record.value()

            query.bind(
                value.code,
                value.name,
                value.marketList.single().code.toString().uppercase(),
                DEFAULT_PRODUCTION_START,
                DEFAULT_CLEARDOWN,
                value.timeZone,
                value.readGlobalDc(),
                false,
                false,
                false,
                emailAddress,
                value.wmsSystem.toString(),
                value.brandCodesList.map { it.toString() }.toTypedArray(),

                // set on duplicate
                value.name,
                value.marketList.single().code.toString().uppercase(),
                value.timeZone,
                value.readGlobalDc(),
                false,
                value.wmsSystem.toString(),
                value.brandCodesList.map { it.toString() }.toTypedArray(),
            )

            return runnableQuery
        }

        class RunnableQuery(val query: Batch) {
            fun executeAsync() = query.executeAsync()
        }

        private fun DistributionCenter.readGlobalDc() = if (this.hasGlobalThirdPwDc()) this.globalThirdPwDc else null
    }
}
