plugins {
    id("com.hellofresh.cif.common-conventions")
    alias(libs.plugins.jooq)
}

description = "Sku Specification Library"
group = "$group.sku-specification-lib"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "sku_specification|sku_specification_yf|sku_specification_view|uom"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    api(projects.skuModels)
    api(projects.distributionCenterModels)
    api(projects.lib)
    api(projects.lib.db)

    testImplementation(libs.flyway.core)
    testImplementation(libs.mockk)
    testImplementation(projects.libTests)
    testImplementation(testFixtures(projects.skuModels))
    testImplementation(testFixtures(projects.distributionCenterModels))
}
