package com.hellofresh.cif.skuSpecificationLib

import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.lib.KrontabScheduler
import com.hellofresh.cif.lib.MeteredJob
import com.hellofresh.cif.lib.Scheduler
import com.hellofresh.cif.lib.groupByFirst
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepository
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepositoryImpl
import com.hellofresh.sku.models.SkuSpecification
import io.micrometer.core.instrument.MeterRegistry
import java.util.UUID
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.ThreadPoolExecutor.CallerRunsPolicy
import java.util.concurrent.TimeUnit.HOURS
import java.util.concurrent.TimeUnit.SECONDS
import kotlinx.coroutines.runBlocking

private const val POOL_SIZE = 1

/**
 * Reads `SKU_SPECIFICATION` table in a background thread and caches the result. By
 * default, it uses the [KrontabScheduler] to schedule the read from the
 * [SkuSpecificationRepositoryImpl]. However, this could be overwritten by the caller.
 *
 * **The class is threadsafe.**
 */

class SkuSpecificationService(
    private val meterRegistry: MeterRegistry,
    private val repo: SkuSpecificationRepository = SkuSpecificationRepositoryImpl(
        DBConfiguration.jooqReadOnlyDslContext(
            POOL_SIZE,
            meterRegistry,
        ),
    ),
    jobTimePeriodSeconds: Int = 60,
    private val scheduler: Scheduler = shutdownNeeded {
        KrontabScheduler(
            jobTimePeriodSeconds,
            SECONDS,
            ThreadPoolExecutor(
                POOL_SIZE,
                POOL_SIZE,
                Long.MAX_VALUE,
                HOURS,
                ArrayBlockingQueue(POOL_SIZE, true),
                CallerRunsPolicy(),
            ),
        )
    }
) {

    /**
     * Assignment and read operations are atomic hence volatile is enough to make
     * the critical section threadsafe
     */
    @Volatile
    private var _skuSpecifications: Map<UUID, SkuSpecification> = emptyMap()

    init {
        scheduler.schedule {
            MeteredJob(
                meterRegistry,
                "SkuSpecificationService",
            ) { _skuSpecifications = loadSkuSpecifications() }.execute()
        }
    }

    /**
     * Mapping of Sku ID to Sku Specifications. Note that this mapping is
     * refreshed by a background task periodically. Use [fetchOnDemand] if stale
     * data is not favourable.
     */

    val specifications: Map<UUID, SkuSpecification>
        get() = readSkuSpecifications()

    fun skuCodeLookUp(distributionCenterConfigurations: List<DistributionCenterConfiguration>): SkuCodeLookUp =
        distributionCenterConfigurations.takeIf { it.isNotEmpty() }
            ?.let { dcs ->
                val skusByMarket = specifications.map { entry -> entry.value.market.uppercase() to entry.toPair() }.groupByFirst()
                dcs.groupBy { it.market.uppercase() }
                    .flatMap { (dcMarket, dcs) ->
                        val skus = skusByMarket[dcMarket.uppercase()].orEmpty()
                        dcs.flatMap { dc ->
                            skus.map { skuPair -> SkuCodeDcKey(skuPair.second.skuCode, dc.dcCode) to skuPair }
                        }
                    }.toMap()
            } ?: emptyMap()

    /**
     * Fetch the mapping of Sku UUID to Sku specifications from the source. This performs
     * a DB query. Use [specifications] if cached data is enough for the use.
     */
    fun fetchOnDemand(): Map<UUID, SkuSpecification> = loadSkuSpecifications()

    /**
     * Reads sku specifications if present and fetch them from db if needed
     */
    private fun readSkuSpecifications() =
        _skuSpecifications.ifEmpty {
            loadSkuSpecifications()
        }

    private fun loadSkuSpecifications() = runBlocking {
        repo.fetchSkuSpecifications()
    }.also {
        _skuSpecifications = it
    }
}

typealias SkuCodeLookUp = Map<SkuCodeDcKey, Pair<UUID, SkuSpecification>>

data class SkuCodeDcKey(
    val skuCode: String,
    val dcCode: String
)
