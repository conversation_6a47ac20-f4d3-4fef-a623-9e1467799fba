package com.hellofresh.cif.skuSpecificationLib.repo

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.sku_specification_lib.schema.Tables
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom.UOM_GAL
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom.UOM_UNRECOGNIZED
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom.UOM_UNSPECIFIED
import com.hellofresh.sku.models.SkuSpecification
import java.util.UUID
import kotlinx.coroutines.future.await

fun interface SkuSpecificationRepository {
    suspend fun fetchSkuSpecifications(): Map<UUID, SkuSpecification>
}

class SkuSpecificationRepositoryImpl(private val metricsDSLContext: MetricsDSLContext) : SkuSpecificationRepository {

    private val fetchSkuSpecificationsTag = "fetch-sku-specifications"

    override suspend fun fetchSkuSpecifications(): Map<UUID, SkuSpecification> =
        metricsDSLContext.withTagName(fetchSkuSpecificationsTag)
            .fetchAsync(Tables.SKU_SPECIFICATION_VIEW)
            .thenApply {
                it.map { sku ->
                    sku.id to
                        SkuSpecification(
                            coolingType = sku.coolingType,
                            name = sku.name,
                            packaging = sku.packaging,
                            skuCode = sku.code,
                            category = sku.category,
                            parentId = sku.parentId,
                            acceptableCodeLife = sku.acceptableCodeLife,
                            market = sku.market,
                            uom = sku.uom.toSkuUOM(),
                            brands = sku.brands?.toList() ?: emptyList(),
                        )
                }.toMap()
            }.await()
}

@Suppress("MagicNumber")
fun Uom.toSkuUOM() = when (this) {
    UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
    UOM_UNIT -> SkuUOM.UOM_UNIT
    UOM_KG -> SkuUOM.UOM_KG
    UOM_LBS -> SkuUOM.UOM_LBS
    UOM_GAL -> SkuUOM.UOM_GAL
    UOM_LITRE -> SkuUOM.UOM_LITRE
    UOM_OZ -> SkuUOM.UOM_OZ
    UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED }
