package com.hellofresh.cif.distributionCenterLib.repo

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepositoryImpl
import com.hellofresh.cif.skuSpecificationLib.repo.toSkuUOM
import com.hellofresh.cif.sku_specification_lib.schema.Tables
import com.hellofresh.cif.sku_specification_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_specification_lib.schema.Tables.SKU_SPECIFICATION_YF
import com.hellofresh.cif.sku_specification_lib.schema.enums.Uom
import com.hellofresh.cif.sku_specification_lib.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.sku_specification_lib.schema.tables.records.SkuSpecificationYfRecord
import com.hellofresh.sku.models.SkuSpecification
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.exception.IntegrityConstraintViolationException
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.assertThrows

class SkuSpecificationRepositoryImplTest {
    private val repo = SkuSpecificationRepositoryImpl(dsl)

    @BeforeTest
    fun cleanupDb() {
        dsl.deleteFrom(Tables.SKU_SPECIFICATION).execute()
        dsl.deleteFrom(Tables.SKU_SPECIFICATION_YF).execute()
        refreshSkuView()
    }

    private fun refreshSkuView() =
        dsl.query("refresh materialized view ${SKU_SPECIFICATION_VIEW.name}").execute()

    @Test
    fun `should fetch all DC Configurations`() {
        assertEquals(0, runBlocking { repo.fetchSkuSpecifications().size })
        // given
        val expected = (0..3).map {
            SkuSpecificationRecord().apply {
                id = UUID.randomUUID()
                coolingType = UUID.randomUUID().toString()
                name = UUID.randomUUID().toString()
                packaging = UUID.randomUUID().toString()
                code = UUID.randomUUID().toString()
                category = UUID.randomUUID().toString().take(3)
                parentId = UUID.randomUUID()
                acceptableCodeLife = 10
                market = UUID.randomUUID().toString()
                uom = Uom.entries.random()
                brands = emptyArray()
            }
        }
        val expectedYf = (0..3).map {
            SkuSpecificationYfRecord().apply {
                id = UUID.randomUUID()
                coolingType = UUID.randomUUID().toString()
                name = UUID.randomUUID().toString()
                packaging = UUID.randomUUID().toString()
                code = UUID.randomUUID().toString()
                category = UUID.randomUUID().toString().take(3)
                parentId = UUID.randomUUID()
                acceptableCodeLife = 10
                market = UUID.randomUUID().toString()
                uom = Uom.entries.random()
            }
        }
        dsl.batchInsert(expected).execute()
        dsl.batchInsert(expectedYf).execute()
        refreshSkuView()
        // when
        val actual = runBlocking { repo.fetchSkuSpecifications() }

        // then

        assertEquals(expected.size + expectedYf.size, actual.size)
        expected.forEach {
            assertSku(it, actual[it.id])
        }

        expectedYf.forEach {
            assertSku(it, actual[it.id])
        }
    }

    @Test
    fun `sku specification table takes precedence over youfoodz skus with same id`() {
        // given
        val default = SkuSpecificationRecord().apply {
            id = UUID.randomUUID()
            coolingType = UUID.randomUUID().toString()
            name = UUID.randomUUID().toString()
            packaging = UUID.randomUUID().toString()
            code = UUID.randomUUID().toString()
            category = UUID.randomUUID().toString().take(3)
            parentId = UUID.randomUUID()
            acceptableCodeLife = 10
            market = UUID.randomUUID().toString()
            uom = Uom.entries.random()
        }

        val youFoodzSku =
            SkuSpecificationYfRecord().apply {
                id = default.id
                coolingType = UUID.randomUUID().toString()
                name = UUID.randomUUID().toString()
                packaging = UUID.randomUUID().toString()
                code = UUID.randomUUID().toString()
                category = UUID.randomUUID().toString().take(3)
                parentId = UUID.randomUUID()
                acceptableCodeLife = 10
                market = UUID.randomUUID().toString()
                uom = Uom.entries.random()
            }
        dsl.batchInsert(default).execute()
        dsl.batchInsert(youFoodzSku).execute()
        refreshSkuView()
        // when
        val actual = runBlocking { repo.fetchSkuSpecifications() }

        // then

        assertEquals(1, actual.size)
        assertSku(default, actual[default.id])
        assertEquals(youFoodzSku, dsl.fetchOne(SKU_SPECIFICATION_YF))
    }

    @Test
    fun `sku specification table takes precedence over youfoodz skus with same code and market`() {
        // given
        val default = SkuSpecificationRecord().apply {
            id = UUID.randomUUID()
            coolingType = UUID.randomUUID().toString()
            name = UUID.randomUUID().toString()
            packaging = UUID.randomUUID().toString()
            code = UUID.randomUUID().toString()
            category = UUID.randomUUID().toString().take(3)
            parentId = UUID.randomUUID()
            acceptableCodeLife = 10
            market = UUID.randomUUID().toString()
            uom = Uom.entries.random()
        }

        val youFoodzSku =
            SkuSpecificationYfRecord().apply {
                id = UUID.randomUUID()
                coolingType = UUID.randomUUID().toString()
                name = UUID.randomUUID().toString()
                packaging = UUID.randomUUID().toString()
                code = default.code
                market = default.market
                category = UUID.randomUUID().toString().take(3)
                parentId = UUID.randomUUID()
                acceptableCodeLife = 10
                uom = Uom.entries.random()
            }
        dsl.batchInsert(default).execute()
        dsl.batchInsert(youFoodzSku).execute()
        refreshSkuView()
        // when
        val actual = runBlocking { repo.fetchSkuSpecifications() }

        // then

        assertEquals(1, actual.size)
        assertSku(default, actual[default.id])
        assertEquals(youFoodzSku, dsl.fetchOne(SKU_SPECIFICATION_YF))
    }

    @Test
    fun `throws exception when duplicates skus with code and market are added`() {
        val skuSpecRecord1 = SkuSpecificationRecord().apply {
            id = UUID.randomUUID()
            code = "code"
            market = "dach"
        }
        val skuSpecRecord2 = skuSpecRecord1.into(Tables.SKU_SPECIFICATION)
            .apply {
                id = UUID.randomUUID()
                code = "code"
                market = "dach"
            }

        assertThrows<IntegrityConstraintViolationException> {
            dsl.batchInsert(
                skuSpecRecord1,
                skuSpecRecord2,
            ).execute()
        }
    }

    private fun assertSku(expectedSku: SkuSpecificationRecord, sku: SkuSpecification?) {
        assertEquals(
            with(expectedSku) {
                SkuSpecification(
                    coolingType = coolingType,
                    name = name,
                    packaging = packaging,
                    skuCode = code,
                    category = category,
                    parentId = parentId,
                    acceptableCodeLife = acceptableCodeLife,
                    market = market,
                    uom = uom.toSkuUOM(),
                    brands = brands?.toList() ?: emptyList(),
                )
            },
            sku,
        )
    }

    private fun assertSku(expectedSku: SkuSpecificationYfRecord, sku: SkuSpecification?) {
        assertEquals(
            with(expectedSku) {
                SkuSpecification(
                    coolingType = coolingType,
                    name = name,
                    packaging = packaging,
                    skuCode = code,
                    category = category,
                    parentId = parentId,
                    acceptableCodeLife = acceptableCodeLife,
                    market = market,
                    uom = uom.toSkuUOM(),
                    brands = listOf("youfoodz")
                )
            },
            sku,
        )
    }

    companion object {
        private val dataSource = getMigratedDataSource()
        private val dbConfiguration = DefaultConfiguration()
            .apply {
                setSQLDialect(SQLDialect.POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            }
        private val dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
    }
}
