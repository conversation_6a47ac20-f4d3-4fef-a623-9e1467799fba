package com.hellofresh.cif.skuSpecification

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.lib.Scheduler
import com.hellofresh.cif.skuSpecificationLib.SkuCodeDcKey
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepository
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.default
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.unmockkAll
import java.time.Duration
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.assertThrows
import org.testcontainers.shaded.org.awaitility.Awaitility

class SkuSpecificationServiceTest {

    private val repo = mockk<SkuSpecificationRepository>(relaxed = true)
    private val scheduler = mockk<Scheduler>(relaxed = true)

    @BeforeEach
    fun clear() {
        unmockkAll()
    }

    @Test fun `repositoy returns error`() {
        val service = SkuSpecificationService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
        )
        coEvery { repo.fetchSkuSpecifications() }.throws(IllegalStateException("Some error"))
        assertThrows<IllegalStateException> { service.specifications }
    }

    @Test fun `fetch on demand is truly on demand`() {
        val service = SkuSpecificationService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
            scheduler = { },
        )

        coEvery { repo.fetchSkuSpecifications() } returns mapOf(UUID.randomUUID() to SkuSpecification.default)
        service.specifications
        service.fetchOnDemand()
        service.specifications
        coVerify(exactly = 2) { repo.fetchSkuSpecifications() }
    }

    @Test fun `scheduler repo update is executed`() {
        val expectedName = "UPDATED_NAME"
        val skuId = UUID.randomUUID()
        coEvery { repo.fetchSkuSpecifications() } returns mapOf(skuId to SkuSpecification.default) andThen
            mapOf(skuId to SkuSpecification.default.copy(name = expectedName))

        val service = SkuSpecificationService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
            jobTimePeriodSeconds = 2,
        )
        Awaitility
            .await()
            .pollInterval(Duration.ofSeconds(1))
            .atMost(Duration.ofSeconds(6))
            .until {
                service.specifications[skuId]?.name == expectedName
            }
    }

    @Test fun `multiple get calls on dcConfigurations trigger demand retrieval only once`() {
        coEvery { repo.fetchSkuSpecifications() } returns mapOf(UUID.randomUUID() to SkuSpecification.default)
        val service = SkuSpecificationService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
            scheduler = scheduler,
        )

        service.specifications
        service.specifications
        service.specifications

        coVerify(exactly = 1) { repo.fetchSkuSpecifications() }
    }

    @Test
    fun `returns sku lookup for given dcs`() {
        val skuId1 = UUID.randomUUID()
        val skuCode1 = UUID.randomUUID().toString()
        val market1 = "M1"
        val skuSpecification1 = SkuSpecification.default.copy(skuCode = skuCode1, market = market1)
        val skuId2 = UUID.randomUUID()
        val skuCode2 = UUID.randomUUID().toString()
        val market2 = "M2"
        val skuSpecification2 = SkuSpecification.default.copy(skuCode = skuCode2, market = market2)
        coEvery { repo.fetchSkuSpecifications() } returns mapOf(
            skuId1 to skuSpecification1,
            skuId2 to skuSpecification2,
        )

        val dc1 = DistributionCenterConfiguration.Companion.default("D1").copy(market = market1)
        val dc2 = DistributionCenterConfiguration.Companion.default("D2").copy(market = market2)

        val service = SkuSpecificationService(
            meterRegistry = SimpleMeterRegistry(),
            repo = repo,
            scheduler = scheduler,
        )

        val skuCodeLookUp = service.skuCodeLookUp(listOf(dc1, dc2))

        assertEquals(skuId1 to skuSpecification1, skuCodeLookUp[SkuCodeDcKey(skuCode1, dc1.dcCode)])
        assertNull(skuCodeLookUp[SkuCodeDcKey(skuCode2, dc1.dcCode)])

        assertEquals(skuId2 to skuSpecification2, skuCodeLookUp[SkuCodeDcKey(skuCode2, dc2.dcCode)])
        assertNull(skuCodeLookUp[SkuCodeDcKey(skuCode1, dc2.dcCode)])
    }
}
