input_resources:
  - label: sku_specification_topic
    kafka:
      addresses:
        - "${HF_KAFKA_BOOTSTRAP_SERVERS}"
      tls:
        enabled: ${TLS_ENABLED:true}
        root_cas_file: ${KAFKA_ROOT_CA}
      sasl:
        mechanism: ${HF_KAFKA_SASL_MECHANISM}
        user: ${KAFKA_USERNAME}
        password: ${HF_KAFKA_SASL_PASSWORD}
      consumer_group: "${SKU_SPECIFICATION_PROCESSOR_GROUP_ID}"
      client_id: "${KAFKA_CLIENT_ID}"
      target_version: 3.0.0
      topics:
        - "${SKU_SPECIFICATION_TOPIC}.v${SKU_SPECIFICATION_TOPIC_VERSION}"
      batching:
        count: 1500
        period: 100ms

output_resources:
  - label: sku_persist_to_database
    sql_raw:
      max_in_flight: 6
      driver: postgres
      dsn: postgres://${HF_INVENTORY_DB_USERNAME}:${HF_INVENTORY_DB_PASSWORD}@${INVENTORY_DB_URL}
      query: >-
        INSERT INTO sku_specification (id, parent_id, category, code, name, cooling_type, packaging, acceptable_code_life, market, uom, brands) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, COALESCE($10, 'UOM_UNIT')::uom, ARRAY(SELECT jsonb_array_elements_text($11::jsonb)))
        ON CONFLICT ON CONSTRAINT sku_specification_pkey DO UPDATE SET (parent_id, category, code, name, cooling_type, packaging, acceptable_code_life, market, uom, brands) = ($2, $3, $4, $5, $6, $7, $8, $9, COALESCE($10, 'UOM_UNIT')::uom, ARRAY(SELECT jsonb_array_elements_text($11::jsonb)))
      args_mapping: |
        root = [
          meta().kafka_key,
          this.parent_id,
          this.category,
          this.sku_code,
          this.name,
          this.cooling_type,
          this.packaging,
          this.acceptable_code_life,
          this.market,
          this.uom,
          this.brands.string()
        ]
