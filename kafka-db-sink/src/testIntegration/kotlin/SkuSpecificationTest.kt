import InfraPreparation.createKafkaProducer
import InfraPreparation.startBenthos
import InfraPreparation.startKafkaAndCreateTopics
import InfraPreparation.startPostgresAndRunMigrations
import java.sql.ResultSet
import java.time.Duration
import java.util.UUID
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.awaitility.Awaitility
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.testcontainers.containers.KafkaContainer
import org.testcontainers.containers.PostgreSQLContainer
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper
import org.testcontainers.shaded.com.fasterxml.jackson.databind.PropertyNamingStrategy
import org.testcontainers.shaded.com.fasterxml.jackson.databind.annotation.JsonNaming

class SkuSpecificationTest {
    private val objectMapper = ObjectMapper().findAndRegisterModules()
    private val skuValue = SkuValue(
        name = "Garlic Puree 1kg",
        packaging = "Mealkit",
        skuCode = "PRO-40-91791-4",
        parentId = UUID.randomUUID().toString(),
        coolingType = "Cooler",
        acceptableCodeLife = 2,
        market = "dach",
        brands = listOf("HelloFresh", "GreenChef"),
    )

    @AfterEach
    fun cleanupDb() {
        postgres.createConnection("")
            .createStatement()
            .executeUpdate("delete from sku_specification")
    }

    @Test
    fun `should insert the sku specification into DB when consumed from the kafka topic`() {
        // given
        val skuId = UUID.randomUUID()

        // when
        publishSkuSpecification(skuId, skuValue)

        // then
        assertDbResult(skuId, skuValue)
    }

    @Test
    fun `should insert the sku specification into DB when an incoming sku with no parentId`() {
        // given
        val value = skuValue.copy(parentId = null, skuCode = "PRO-40-91791-1")
        val skuId = UUID.randomUUID()

        // when
        publishSkuSpecification(skuId, value)

        // then
        assertDbResult(skuId, value)
    }

    @Test
    fun `should update the DB record when consuming an sku with the same ID`() {
        // given
        val skuId = UUID.randomUUID()
        publishSkuSpecification(skuId, skuValue)
        assertDbResult(skuId, skuValue)

        val updatedValue = skuValue.copy(
            name = skuValue.name + UUID.randomUUID(),
            packaging = skuValue.packaging + UUID.randomUUID(),
            skuCode = skuValue.skuCode + UUID.randomUUID(),
            parentId = UUID.randomUUID().toString(),
            coolingType = skuValue.coolingType + UUID.randomUUID(),
            acceptableCodeLife = skuValue.acceptableCodeLife + 1,
            market = "au",
        )

        // when
        publishSkuSpecification(skuId, updatedValue)

        // then
        assertDbResult(skuId, updatedValue)
    }

    @Test
    fun `should skip skus with the same sku code and market`() {
        // given
        val skuId = UUID.randomUUID()
        publishSkuSpecification(skuId, skuValue)
        assertDbResult(skuId, skuValue)

        val updatedValue = skuValue.copy(
            name = skuValue.name + UUID.randomUUID(),
            packaging = skuValue.packaging + UUID.randomUUID(),
            skuCode = skuValue.skuCode,
            parentId = UUID.randomUUID().toString(),
            coolingType = skuValue.coolingType + UUID.randomUUID(),
            acceptableCodeLife = skuValue.acceptableCodeLife + 1,
            market = skuValue.market,
        )

        val updatedSkuId = UUID.randomUUID()
        // when
        publishSkuSpecification(updatedSkuId, updatedValue)

        // then
        val statement = postgres.createConnection("").createStatement()
        val resultSet = statement.executeQuery("select * from sku_specification where id='$updatedSkuId'")
        val output = resultSet.next()
        assertFalse(output)
    }

    private fun publishSkuSpecification(skuId: UUID, skuValue: SkuValue) {
        val value = objectMapper.writeValueAsString(skuValue)
        val record = ProducerRecord(
            "$SKU_SPECIFICATION_TOPIC.v$SKU_SPECIFICATION_TOPIC_VERSION",
            skuId.toString(),
            value,
        )
        producer.send(record).get()
    }

    private fun assertDbResult(skuId: UUID, skuValue: SkuValue) {
        val statement = postgres.createConnection("").createStatement()

        lateinit var resultSet: ResultSet
        Awaitility
            .await()
            .atMost(Duration.ofSeconds(10))
            .until {
                resultSet = statement.executeQuery("select * from sku_specification where id='$skuId'")
                if (resultSet.next()) {
                    assertSku(skuId, skuValue, resultSet)
                } else {
                    false
                }
            }
    }

    private fun assertSku(skuId: UUID, skuValue: SkuValue, resultSet: ResultSet) =
        kotlin.runCatching {
            val dbId = resultSet.getString("id")
            val dbParentId = resultSet.getString("parent_id")
            val dbCode = resultSet.getString("code")
            val dbName = resultSet.getString("name")
            val dbCategory = resultSet.getString("category")
            val coolingType = resultSet.getString("cooling_type")
            val packaging = resultSet.getString("packaging")
            val acceptableCodeLife = resultSet.getInt("acceptable_code_life")
            val market = resultSet.getString("market")
            assertEquals(skuId.toString(), dbId)
            assertEquals(skuValue.parentId, dbParentId)
            assertEquals(skuValue.skuCode, dbCode)
            assertEquals(skuValue.name, dbName)
            assertEquals(skuValue.category, dbCategory)
            assertEquals(skuValue.coolingType, coolingType)
            assertEquals(skuValue.packaging, packaging)
            assertEquals(skuValue.acceptableCodeLife, acceptableCodeLife)
            assertEquals(skuValue.market, market)
            assertEquals(
                skuValue.brands.sorted(),
                resultSet.getArray("brands")?.array?.let { it as Array<*> }?.map { it.toString() }?.sorted(),
            )
            true
        }.getOrElse { false }

    companion object {
        private lateinit var kafka: KafkaContainer
        private lateinit var postgres: PostgreSQLContainer<*>
        private lateinit var producer: KafkaProducer<String, String?>

        internal const val SKU_SPECIFICATION_TOPIC = "csku-inventory-forecast.intermediate.sku-specification"
        internal const val SKU_SPECIFICATION_TOPIC_VERSION = "1"

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            postgres = startPostgresAndRunMigrations()
            kafka = startKafkaAndCreateTopics(
                listOf("$SKU_SPECIFICATION_TOPIC.v$SKU_SPECIFICATION_TOPIC_VERSION"),
            )
            producer = createKafkaProducer(kafka)
            startBenthos(
                mapOf(
                    "SKU_SPECIFICATION_PROCESSOR_GROUP_ID" to "sku-specification.test",
                    "SKU_SPECIFICATION_TOPIC" to SKU_SPECIFICATION_TOPIC,
                    "SKU_SPECIFICATION_TOPIC_VERSION" to SKU_SPECIFICATION_TOPIC_VERSION,
                    "HF_KAFKA_SASL_MECHANISM" to "none",
                ),
            )
        }
    }

    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy::class)
    private data class SkuValue(
        val name: String,
        val packaging: String,
        val skuCode: String,
        val coolingType: String,
        val parentId: String? = null,
        val acceptableCodeLife: Int = 0,
        val market: String,
        val brands: List<String> = emptyList(),
    ) {
        val category: String
            get() = skuCode.take(3)
    }
}
