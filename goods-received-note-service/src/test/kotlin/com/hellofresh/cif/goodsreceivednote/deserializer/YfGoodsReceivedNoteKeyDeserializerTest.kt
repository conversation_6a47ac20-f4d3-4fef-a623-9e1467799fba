package com.hellofresh.cif.goodsreceivednote.deserializer

import com.google.type.DateTime
import com.google.type.Decimal
import com.google.type.TimeZone
import com.hellofresh.cif.goodsreceivednote.models.GRNDeliveryLineState
import com.hellofresh.proto.stream.ye.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteKey as YfGoodsReceivedNoteKey
import com.hellofresh.proto.stream.ye.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue as YfGoodsReceivedNoteValue
import com.hellofresh.proto.stream.ye.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryLineState.DELIVERY_LINE_STATE_CLOSED
import com.hellofresh.proto.stream.ye.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryState as YfDeliveryState
import com.hellofresh.proto.stream.ye.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDelivery as YfPurchaseOrderDelivery
import com.hellofresh.proto.stream.ye.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDeliveryLine as YfPurchaseOrderDeliveryLine
import java.time.LocalDate
import java.time.Month
import java.time.ZoneOffset.UTC
import kotlin.test.Test
import kotlin.test.assertEquals

class YfGoodsReceivedNoteKeyDeserializerTest {

    @Test fun `should deserialize YfGoodsReceivedNoteKey and YfGoodsReceivedNoteValue correctly`() {
        val recordKey = createGoodsReceivedNoteKey()
        val poDate = LocalDate.now(UTC)
        val recordValue = createGoodsReceivedNoteValue(poDate)

        val key = recordKey.toByteArray()
        val value = recordValue.toByteArray()
        with(YfGoodsReceivedNoteKeyDeserializer.deserialize("", key)) {
            assertEquals(recordKey.dcCode, dcCode)
            assertEquals(recordKey.reference, reference)
        }
        val result = YfGoodsReceivedNoteValueDeserializer.deserialize("", value)
        with(result.deliveriesList.first()) {
            assertEquals(poDate.dayOfMonth, deliveryTime.dayOfMonth)
            assertEquals(poDate.month, deliveryTime.month)
            assertEquals(poDate.year, deliveryTime.year)
            assertEquals(GRNDeliveryLineState.DELIVERY_LINE_STATE_CLOSED, linesList[0].state)
            assertEquals("PTN-10-10021-7", linesList[0].skuCode)
            assertEquals("100", linesList[0].palletizedQuantity)
            assertEquals(2024, linesList[0].expirationDate?.year)
            assertEquals(Month.JUNE, linesList[0].expirationDate?.month)
            assertEquals(15, linesList[0].expirationDate?.dayOfMonth)
        }
    }

    private fun createGoodsReceivedNoteKey() = YfGoodsReceivedNoteKey.newBuilder()
        .setDcCode("VE")
        .setReference("keyReference")
        .build()

    private fun createGoodsReceivedNoteValue(
        poDate: LocalDate?,
        expectedDeliveryStartTime: LocalDate? = poDate,
        timezone: String = "UTC",
    ): YfGoodsReceivedNoteValue {
        val goodsReceivedNoteValueBuilder = YfGoodsReceivedNoteValue.newBuilder()
        val purchaseOrderDeliveryBuilder = YfPurchaseOrderDelivery.newBuilder()
        val palletizedQuantity = Decimal.newBuilder().setValue("100").build()
        val purchaseOrderDeliveryLine = YfPurchaseOrderDeliveryLine.newBuilder()
            .setSkuCode("PTN-10-10021-7")
            .setPalletizedQuantity(palletizedQuantity)
            .setState(DELIVERY_LINE_STATE_CLOSED)
            .setExpirationDate(DateTime.newBuilder().setYear(2024).setMonth(6).setDay(15))
            .build()
        val purchaseOrderDelivery = purchaseOrderDeliveryBuilder
            .setDeliveryTime(createDateTime())
            .apply {
                if (poDate != null) {
                    this.setDeliveryTime(
                        DateTime.newBuilder()
                            .setYear(poDate.year)
                            .setMonth(poDate.monthValue)
                            .setDay(poDate.dayOfMonth)
                            .setTimeZone(TimeZone.newBuilder().setId(timezone).build())
                            .build(),
                    )
                }
                if (expectedDeliveryStartTime != null) {
                    this.setExpectedDeliveryStartTime(
                        DateTime.newBuilder()
                            .setYear(expectedDeliveryStartTime.year)
                            .setMonth(expectedDeliveryStartTime.monthValue)
                            .setDay(expectedDeliveryStartTime.dayOfMonth)
                            .setTimeZone(TimeZone.newBuilder().setId(timezone).build())
                            .build(),
                    )
                }
            }
            .setState(YfDeliveryState.DELIVERY_STATE_CLOSED)
            .addLines(purchaseOrderDeliveryLine)
            .build()
        goodsReceivedNoteValueBuilder.addDeliveries(purchaseOrderDelivery)
        return goodsReceivedNoteValueBuilder.setDcCode("VE").build()
    }

    private fun createDateTime(): DateTime = DateTime.newBuilder()
        .setDay(20)
        .setMonth(2)
        .setYear(2023)
        .setHours(10)
        .setMinutes(10)
        .setSeconds(10)
        .setTimeZone(TimeZone.newBuilder().setId("UTC").build())
        .build()
}
