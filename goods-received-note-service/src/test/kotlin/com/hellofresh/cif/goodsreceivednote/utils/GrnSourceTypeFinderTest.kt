package com.hellofresh.cif.goodsreceivednote.utils

import com.hellofresh.cif.goodsreceivednote.service.GrnSourceType
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class GrnSourceTypeFinderTest {

    @ParameterizedTest(name = "Reference: {0} should be {1}")
    @CsvSource(
        // PURCHASE_ORDER
        "2351MO334106_E1, PURCHASE_ORDER",
        "2514DH765244_E2, PURCHASE_ORDER",
        "2448VE696772_E1, PURCHASE_ORDER",
        "2329VE215505_O1, PURCHASE_ORDER",
        "2431VE31072024_MK, PURCHASE_ORDER",
        "2440VE674005_E2, PURCHASE_ORDER",
        "2452VE712418_E3, PURCHASE_ORDER",
        "2326VE09062023_MJ, PURCH<PERSON>E_ORDER",
        "2320VE165045, PURCHASE_ORDER",
        "2443ve683149, PURCHASE_ORDER", // lowercase should also match
        "2506VE753463_MJ1, PURCHASE_ORDER",
        "260520231400, PURCHASE_ORDER", // special case: numbers only but assumed PO
        "2426VE628643:_MJ, PURCHASE_ORDER",

        // TRANSFER_ORDER
        "2530TIT54130, TRANSFER_ORDER",
        "2530TIT59686, TRANSFER_ORDER",
        "2530NJT70653, TRANSFER_ORDER",
        "2530NJT30940, TRANSFER_ORDER",
        "2552AET01638, TRANSFER_ORDER",

        // INVALID & DEFAULT
        "123INVALIDREF, PURCHASE_ORDER",
        "TIT59686, PURCHASE_ORDER",
        "2530-TIT-54130, PURCHASE_ORDER",
        "'', PURCHASE_ORDER"
    )
    fun `verify grn source types`(reference: String, expected: GrnSourceType) {
        val actual = GrnSourceTypeFinder.findGrnSourceType(reference)
        assertEquals(expected, actual)
    }
}
