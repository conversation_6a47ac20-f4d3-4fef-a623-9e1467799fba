package com.hellofresh.cif.goodsreceivednote.deserializer

import com.google.type.DateTime
import com.google.type.Decimal
import com.google.type.TimeZone
import com.hellofresh.cif.goodsreceivednote.models.GRNDeliveryLineState
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteKey
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryLineState.DELIVERY_LINE_STATE_CLOSED
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryState
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDelivery
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.PurchaseOrderDeliveryLine
import java.time.Instant
import java.time.LocalDate
import java.time.Month
import java.time.ZoneOffset.UTC
import kotlin.test.Test
import kotlin.test.assertEquals

class GoodsReceivedNoteKeyDeserializerTest {

    @Test fun `should deserialize GoodsReceivedNoteKey and GoodsReceivedNoteValue correctly`() {
        val recordKey = createGoodsReceivedNoteKey()
        val poDate = LocalDate.now(UTC)
        val recordValue = createGoodsReceivedNoteValue(poDate)

        val key = recordKey.toByteArray()
        val value = recordValue.toByteArray()
        with(GoodsReceivedNoteKeyDeserializer.deserialize("", key)) {
            assertEquals(recordKey.dcCode, dcCode)
            assertEquals(recordKey.reference, reference)
        }
        val result = GoodsReceivedNoteValueDeserializer.deserialize("", value)
        with(result.deliveriesList.first()) {
            assertEquals(poDate.dayOfMonth, deliveryTime.dayOfMonth)
            assertEquals(poDate.month, deliveryTime.month)
            assertEquals(poDate.year, deliveryTime.year)
            assertEquals(GRNDeliveryLineState.DELIVERY_LINE_STATE_CLOSED, linesList[0].state)
            assertEquals("PTN-10-10021-7", linesList[0].skuCode)
            assertEquals("100", linesList[0].palletizedQuantity)
            assertEquals(2024, linesList[0].expirationDate?.year)
            assertEquals(Month.JUNE, linesList[0].expirationDate?.month)
            assertEquals(15, linesList[0].expirationDate?.dayOfMonth)
        }
    }

    @Test
    fun `should fallback to expectedDeliveryStartTime if deliveryTime is invalid`() {
        val validExpectedDeliveryStartTime = LocalDate.now(UTC)
        val invalidDate: LocalDate = Instant.ofEpochMilli(0).atZone(UTC).toLocalDate()
        val recordKey = createGoodsReceivedNoteKey()
        val recordValue = createGoodsReceivedNoteValue(invalidDate, validExpectedDeliveryStartTime)

        val key = recordKey.toByteArray()
        val value = recordValue.toByteArray()
        with(GoodsReceivedNoteKeyDeserializer.deserialize("", key)) {
            assertEquals(recordKey.dcCode, dcCode)
            assertEquals(recordKey.reference, reference)
        }
        val result = GoodsReceivedNoteValueDeserializer.deserialize("", value)

        // then
        assertEquals(validExpectedDeliveryStartTime, result.deliveriesList.first().deliveryTime.toLocalDate())
    }

    private fun createGoodsReceivedNoteKey() = GoodsReceivedNoteKey.newBuilder()
        .setDcCode("VE")
        .setReference("keyReference")
        .build()

    private fun createGoodsReceivedNoteValue(
        poDate: LocalDate?,
        expectedDeliveryStartTime: LocalDate? = poDate,
        timezone: String = "UTC",
    ): GoodsReceivedNoteValue {
        val goodsReceivedNoteValueBuilder = GoodsReceivedNoteValue.newBuilder()
        val purchaseOrderDeliveryBuilder = PurchaseOrderDelivery.newBuilder()
        val palletizedQuantity = Decimal.newBuilder().setValue("100").build()
        val purchaseOrderDeliveryLine = PurchaseOrderDeliveryLine.newBuilder()
            .setSkuCode("PTN-10-10021-7")
            .setPalletizedQuantity(palletizedQuantity)
            .setState(DELIVERY_LINE_STATE_CLOSED)
            .setExpirationDate(DateTime.newBuilder().setYear(2024).setMonth(6).setDay(15))
            .build()
        val purchaseOrderDelivery = purchaseOrderDeliveryBuilder
            .setDeliveryTime(createDateTime())
            .setState(DeliveryState.DELIVERY_STATE_CLOSED)
            .apply {
                if (poDate != null) {
                    this.setDeliveryTime(
                        DateTime.newBuilder()
                            .setYear(poDate.year)
                            .setMonth(poDate.monthValue)
                            .setDay(poDate.dayOfMonth)
                            .setTimeZone(TimeZone.newBuilder().setId(timezone).build())
                            .build(),
                    )
                }
                if (expectedDeliveryStartTime != null) {
                    this.setExpectedDeliveryStartTime(
                        DateTime.newBuilder()
                            .setYear(expectedDeliveryStartTime.year)
                            .setMonth(expectedDeliveryStartTime.monthValue)
                            .setDay(expectedDeliveryStartTime.dayOfMonth)
                            .setTimeZone(TimeZone.newBuilder().setId(timezone).build())
                            .build(),
                    )
                }
            }
            .addLines(purchaseOrderDeliveryLine)
            .build()
        goodsReceivedNoteValueBuilder.addDeliveries(purchaseOrderDelivery)
        return goodsReceivedNoteValueBuilder.setDcCode("VE").build()
    }

    private fun createDateTime(): DateTime = DateTime.newBuilder()
        .setDay(20)
        .setMonth(2)
        .setYear(2023)
        .setHours(10)
        .setMinutes(10)
        .setSeconds(10)
        .setTimeZone(TimeZone.newBuilder().setId("UTC").build())
        .build()
}
