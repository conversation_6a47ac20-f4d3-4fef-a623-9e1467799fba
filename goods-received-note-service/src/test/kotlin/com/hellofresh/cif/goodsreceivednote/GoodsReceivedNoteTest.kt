package com.hellofresh.cif.goodsreceivednote

import com.hellofresh.cif.goodsreceivednote.models.GRNDeliveryLineState
import com.hellofresh.cif.goodsreceivednote.models.GRNKey
import com.hellofresh.cif.goodsreceivednote.models.GRNPurchaseOrderDelivery
import com.hellofresh.cif.goodsreceivednote.models.GRNPurchaseOrderDeliveryLine
import com.hellofresh.cif.goodsreceivednote.models.GRNValue
import com.hellofresh.cif.goodsreceivednote.service.GoodsReceivedNoteProcessor
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.DeliveryLineState
import io.micrometer.core.instrument.MeterRegistry
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.Duration
import java.time.Month
import java.time.OffsetDateTime
import java.time.ZoneOffset
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType.CREATE_TIME

class GoodsReceivedNoteTest {

    @Test
    fun `should be able to process goods received note topic records`() {
        // given
        val meterRegistryMock = mockk<MeterRegistry>(relaxed = true)
        val goodsReceivedNoteProcessorMock = mockk<GoodsReceivedNoteProcessor>(relaxed = true)
        val mockKafkaConsumerConfig = mockk<KafkaConsumer<GRNKey, GRNValue>>(relaxed = true)
        val processor = CoroutinesProcessor<GRNKey, GRNValue>(
            PollConfig(
                kotlin.time.Duration.parse("PT1S"),
                20L,
                kotlin.time.Duration.parse("PT15S"),
            ),
            mockKafkaConsumerConfig,
            meterRegistryMock,
            goodsReceivedNoteProcessorMock::processRecords,
            IgnoreAndContinueProcessing(
                meterRegistryMock,
                "grn_processor_write_failure",
            ),
        )

        val consumerRecord = createConsumerRecord()
        val slot = slot<
            ConsumerRecords<
                GRNKey,
                GRNValue,
                >,
            >()

        coEvery {
            goodsReceivedNoteProcessorMock.processRecords(capture(slot))
        } returns Unit

        every {
            mockKafkaConsumerConfig.poll(any<Duration>())
        } returns ConsumerRecords(mapOf(mockk<TopicPartition>() to mutableListOf(consumerRecord))) andThenAnswer {
            processor.close()
            ConsumerRecords.empty()
        }

        // when
        runBlocking { processor.run() }

        // then
        verify { mockKafkaConsumerConfig.poll(any<Duration>()) }
        verify { mockKafkaConsumerConfig.commitAsync() }

        val goodsReceivedNoteConsumedRecords = slot.captured
        goodsReceivedNoteConsumedRecords.first().key().apply {
            assertEquals("VE", dcCode)
            assertEquals("keyReference", reference)
        }
        goodsReceivedNoteConsumedRecords.first().value().apply {
            deliveriesList[0].apply {
                assertEquals(20, deliveryTime.dayOfMonth)
                assertEquals(Month.FEBRUARY, deliveryTime.month)
                assertEquals(2023, deliveryTime.year)
                assertEquals(10, deliveryTime.hour)
                assertEquals(10, deliveryTime.minute)
                assertEquals(10, deliveryTime.second)
                assertEquals("PTN-10-10021-7", linesList[0].skuCode)
                assertEquals("100", linesList[0].palletizedQuantity)
                assertEquals(2024, linesList[0].expirationDate?.year)
                assertEquals(Month.JUNE, linesList[0].expirationDate?.month)
                assertEquals(15, linesList[0].expirationDate?.dayOfMonth)
                assertEquals(DeliveryLineState.DELIVERY_LINE_STATE_CLOSED.name, linesList[0].state.name)
            }
        }
    }

    private fun createGoodsReceivedNoteValue(): GRNValue {
        val palletizedQuantity = "100"
        val purchaseOrderDeliveryLine = GRNPurchaseOrderDeliveryLine(
            skuCode = "PTN-10-10021-7",
            state = GRNDeliveryLineState.DELIVERY_LINE_STATE_CLOSED,
            skuUom = UOM_UNIT,
            palletizedQuantity = palletizedQuantity,
            expirationDate = OffsetDateTime.of(
                2024,
                6,
                15,
                0,
                0,
                0,
                0,
                ZoneOffset.UTC,
            )
                .toLocalDate(),
        )
        val grnPurchaseOrderDelivery = GRNPurchaseOrderDelivery(
            "ID",
            createOffsetDateTime(),
            listOf(purchaseOrderDeliveryLine),
        )
        return GRNValue(
            deliveriesList = listOf(grnPurchaseOrderDelivery),
        )
    }

    private fun createOffsetDateTime(): OffsetDateTime {
        val year = 2023
        val month = 2
        val day = 20
        val hour = 10
        val minute = 10
        val second = 10
        val offset = ZoneOffset.UTC
        return OffsetDateTime.of(year, month, day, hour, minute, second, 0, offset)
    }

    private fun createConsumerRecord(): ConsumerRecord<GRNKey, GRNValue> =
        ConsumerRecord(
            "goodsReceivedTopicMock",
            1,
            1L,
            1676991214923L,
            CREATE_TIME,
            21,
            234,
            GRNKey("VE", "keyReference"),
            createGoodsReceivedNoteValue(),
            RecordHeaders(),
            null,
        )
}
