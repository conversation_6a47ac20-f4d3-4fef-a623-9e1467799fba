package com.hellofresh.cif.goodsreceivednote.repo

import InfraPreparation
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.goodsReceivedNote.schema.Tables
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepositoryImpl
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.concurrent.Executors
import kotlin.test.BeforeTest
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeAll

@Suppress("UnnecessaryAbstractClass")
abstract class AbstractDbTest {
    @BeforeTest
    fun dbCleanup() {
        dsl.deleteFrom(Tables.GOODS_RECEIVED_NOTE).execute()
        dsl.delete(Tables.DC_CONFIG).execute()
    }

    companion object {
        private val dataSource = InfraPreparation.getMigratedDataSource()
        lateinit var dsl: MetricsDSLContext
        lateinit var goodsReceivedNoteRepo: GoodsReceivedNoteRepository
        lateinit var purchaseOrderRepository: PurchaseOrderRepository
        private lateinit var dcRepository: DcRepositoryImpl

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            goodsReceivedNoteRepo = GoodsReceivedNoteRepositoryImpl(dsl)
            dcRepository = DcRepositoryImpl(dsl)
            purchaseOrderRepository = PurchaseOrderRepositoryImpl(
                dsl,
                DcConfigService(
                    SimpleMeterRegistry(), repo = dcRepository,
                ),
                StatsigTestFeatureFlagClient(emptySet()),
            )
        }
    }
}
