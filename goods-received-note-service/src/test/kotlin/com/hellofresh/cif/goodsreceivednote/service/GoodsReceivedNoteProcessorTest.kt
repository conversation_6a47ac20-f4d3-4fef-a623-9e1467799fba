package com.hellofresh.cif.goodsreceivednote.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.goodsreceivednote.models.GRNDeliveryLineState
import com.hellofresh.cif.goodsreceivednote.models.GRNDeliveryLineState.DELIVERY_LINE_STATE_CLOSED
import com.hellofresh.cif.goodsreceivednote.models.GRNDeliveryLineState.DELIVERY_LINE_STATE_RECEIVED
import com.hellofresh.cif.goodsreceivednote.models.GRNKey
import com.hellofresh.cif.goodsreceivednote.models.GRNPurchaseOrderDelivery
import com.hellofresh.cif.goodsreceivednote.models.GRNPurchaseOrderDeliveryLine
import com.hellofresh.cif.goodsreceivednote.models.GRNValue
import com.hellofresh.cif.goodsreceivednote.repo.DeliveryStatus.CLOSED
import com.hellofresh.cif.goodsreceivednote.repo.DeliveryStatus.OPEN
import com.hellofresh.cif.goodsreceivednote.repo.GoodsReceivedNoteRepository
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.TimeRange
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.cif.skuSpecificationLib.repo.SkuSpecificationRepository
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.default
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import io.mockk.slot
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.time.ZonedDateTime
import java.util.Optional
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType
import org.junit.jupiter.api.BeforeEach

class GoodsReceivedNoteProcessorTest {
    private val goodsReceivedNoteRepo: GoodsReceivedNoteRepository = mockk(relaxed = true)
    private val purchaseOrderRepository: PurchaseOrderRepository = mockk()
    private val skuSpecificationRepository = mockk<SkuSpecificationRepository>()
    private val dcConfigRepository = mockk<DcRepository>()

    private val dcConfigService = DcConfigService(SimpleMeterRegistry(), dcConfigRepository)
    private val goodsReceivedNoteProcessor = GoodsReceivedNoteProcessor(
        goodsReceivedNoteRepo,
        purchaseOrderRepository,
        SkuSpecificationService(SimpleMeterRegistry(), skuSpecificationRepository),
        dcConfigService,
    )

    private val defaultDc = DistributionCenterConfiguration.default("DC")
    private val defaultDcCode = defaultDc.dcCode

    @BeforeEach
    fun beforeEach() {
        coEvery { dcConfigRepository.fetchDcConfigurations() } returns listOf(defaultDc)
    }

    @Test
    fun `should aggregate deliveries for skus in the same delivery id and delivery date`() {
        // given
        val poNumber = "2309GR085559"
        val poRef = poNumber + "_O1"
        val fromExpected = LocalDate.now(UTC).atTime(LocalTime.MIDNIGHT.atOffset(UTC))
        val purchaseOrder = PurchaseOrder(
            poNumber,
            poRef,
            UUID.randomUUID(),
            defaultDcCode,
            TimeRange(
                fromExpected.toZonedDateTime(),
                fromExpected.toZonedDateTime().plusDays(1).minusMinutes(1),
            ),
            null,
            emptyList(),
            poStatus = APPROVED,
        )

        val skuCode1 = "SPI-0001"
        val skuId1 = UUID.randomUUID()
        val poPalletizedQty1Sku1Closed = SkuQuantity.fromLong(11L)
        val poPalletizedQty2Sku1Closed = SkuQuantity.fromLong(12L)

        val skuCode2 = "SPI-0002"
        val skuId2 = UUID.randomUUID()
        val poPalletizedQty1Sku2Closed = SkuQuantity.fromLong(13L)
        val poPalletizedQty2Sku2Closed = SkuQuantity.fromLong(14L)

        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns listOf(purchaseOrder)
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId1 to SkuSpecification.default.copy(skuCode = skuCode1),
            skuId2 to SkuSpecification.default.copy(skuCode = skuCode2),
        )

        val poDelivery1Time = LocalDate.parse("2023-02-02")
        val deliveryId = UUID.randomUUID().toString()
        val poDelivery1 = newPoDelivery(
            poDelivery1Time,
            deliveryId,
            deliveries = listOf(
                PoDeliveryLine(skuCode1, poPalletizedQty1Sku1Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
                PoDeliveryLine(skuCode2, poPalletizedQty1Sku2Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
            ),
        )

        val poDelivery2Time = poDelivery1Time.plusDays(1)
        val deliveryId2 = UUID.randomUUID().toString()
        val poDelivery2 = newPoDelivery(
            poDelivery2Time,
            deliveryId2,
            deliveries = listOf(
                PoDeliveryLine(skuCode1, poPalletizedQty2Sku1Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
            ),
        )
        val poDelivery3 = newPoDelivery(
            poDelivery2Time,
            deliveryId2,
            deliveries = listOf(
                PoDeliveryLine(skuCode2, poPalletizedQty2Sku2Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
            ),
        )

        val record = grnKafkaRecord(defaultDcCode, poRef, listOf(poDelivery1, poDelivery2, poDelivery3))

        val slot = slot<List<Grn>>()
        coEvery { goodsReceivedNoteRepo.save(capture(slot)) } returns Unit

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(record) }

        // then
        val grnList = slot.captured
        assertEquals(1, grnList.size)
        grnList.first().also { grn ->
            assertEquals(4, grn.skus.size)
            assertEquals(GrnSourceType.PURCHASE_ORDER, grn.sourceType)
            assertEquals(
                poPalletizedQty1Sku1Closed,
                grn.skus.first {
                    it.skuId == skuId1 && it.deliveryId == deliveryId
                }.totalPalletizedQty,
            )
            assertEquals(
                poPalletizedQty1Sku2Closed,
                grn.skus.first {
                    it.skuId == skuId2 && it.deliveryId == deliveryId
                }.totalPalletizedQty,
            )
            assertEquals(
                poPalletizedQty2Sku1Closed,
                grn.skus.first {
                    it.skuId == skuId1 && it.deliveryId == deliveryId2
                }.totalPalletizedQty,
            )
            assertEquals(
                poPalletizedQty2Sku2Closed,
                grn.skus.first {
                    it.skuId == skuId2 && it.deliveryId == deliveryId2
                }.totalPalletizedQty,
            )
        }
    }

    @Test
    fun `should update the grn source as TRANSFER ORDER when the grn with po reference has transfer order format`() {
        // given
        val poNumber = "2530TIT59686"
        val fromExpected = LocalDate.now(UTC).atTime(LocalTime.MIDNIGHT.atOffset(UTC))
        val purchaseOrder = PurchaseOrder(
            poNumber,
            poNumber,
            UUID.randomUUID(),
            defaultDcCode,
            TimeRange(
                fromExpected.toZonedDateTime(),
                fromExpected.toZonedDateTime().plusDays(1).minusMinutes(1),
            ),
            null,
            emptyList(),
            poStatus = APPROVED,
        )

        val skuCode1 = "SPI-0001"
        val skuId1 = UUID.randomUUID()
        val poPalletizedQty1Sku1Closed = SkuQuantity.fromLong(11L)
        val poPalletizedQty2Sku1Closed = SkuQuantity.fromLong(12L)

        val skuCode2 = "SPI-0002"
        val skuId2 = UUID.randomUUID()
        val poPalletizedQty1Sku2Closed = SkuQuantity.fromLong(13L)
        val poPalletizedQty2Sku2Closed = SkuQuantity.fromLong(14L)

        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns listOf(purchaseOrder)
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId1 to SkuSpecification.default.copy(skuCode = skuCode1),
            skuId2 to SkuSpecification.default.copy(skuCode = skuCode2),
        )

        val poDelivery1Time = LocalDate.parse("2023-02-02")
        val deliveryId = UUID.randomUUID().toString()
        val poDelivery1 = newPoDelivery(
            poDelivery1Time,
            deliveryId,
            deliveries = listOf(
                PoDeliveryLine(skuCode1, poPalletizedQty1Sku1Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
                PoDeliveryLine(skuCode2, poPalletizedQty1Sku2Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
            ),
        )

        val poDelivery2Time = poDelivery1Time.plusDays(1)
        val deliveryId2 = UUID.randomUUID().toString()
        val poDelivery2 = newPoDelivery(
            poDelivery2Time,
            deliveryId2,
            deliveries = listOf(
                PoDeliveryLine(skuCode1, poPalletizedQty2Sku1Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
            ),
        )
        val poDelivery3 = newPoDelivery(
            poDelivery2Time,
            deliveryId2,
            deliveries = listOf(
                PoDeliveryLine(skuCode2, poPalletizedQty2Sku2Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
            ),
        )

        val record = grnKafkaRecord(defaultDcCode, poNumber, listOf(poDelivery1, poDelivery2, poDelivery3))

        val slot = slot<List<Grn>>()
        coEvery { goodsReceivedNoteRepo.save(capture(slot)) } returns Unit

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(record) }

        // then
        val grnList = slot.captured
        assertEquals(1, grnList.size)
        grnList.first().also { grn ->
            assertEquals(4, grn.skus.size)
            assertEquals(GrnSourceType.TRANSFER_ORDER, grn.sourceType)
            assertEquals(
                poPalletizedQty1Sku1Closed,
                grn.skus.first {
                    it.skuId == skuId1 && it.deliveryId == deliveryId
                }.totalPalletizedQty,
            )
            assertEquals(
                poPalletizedQty1Sku2Closed,
                grn.skus.first {
                    it.skuId == skuId2 && it.deliveryId == deliveryId
                }.totalPalletizedQty,
            )
            assertEquals(
                poPalletizedQty2Sku1Closed,
                grn.skus.first {
                    it.skuId == skuId1 && it.deliveryId == deliveryId2
                }.totalPalletizedQty,
            )
            assertEquals(
                poPalletizedQty2Sku2Closed,
                grn.skus.first {
                    it.skuId == skuId2 && it.deliveryId == deliveryId2
                }.totalPalletizedQty,
            )
        }
    }

    @Test
    fun `should aggregate closed deliveries for two skus in the same GRN event`() {
        // given
        val poNumber = "2309GR085559"
        val poRef = poNumber + "_O1"
        val now = LocalDate.now(UTC)
        val purchaseOrder = PurchaseOrder(
            poNumber,
            poRef,
            UUID.randomUUID(),
            defaultDcCode,
            TimeRange(now.atStartOfDay(UTC), now.atStartOfDay(UTC).plusMinutes(1)),
            null,
            emptyList(),
            poStatus = APPROVED,
        )

        val skuCode1 = "SPI-0001"
        val skuId1 = UUID.randomUUID()
        val poPalletizedQty1Sku1Closed = SkuQuantity.fromLong(11L, UOM_UNIT)
        val poPalletizedQty2Sku1Closed = SkuQuantity.fromLong(12L, UOM_UNIT)

        val skuCode2 = "SPI-0002"
        val skuId2 = UUID.randomUUID()
        val poPalletizedQty1Sku2Closed = SkuQuantity.fromLong(13L, UOM_UNIT)
        val poPalletizedQty2Sku2Closed = SkuQuantity.fromLong(14L, UOM_UNIT)

        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns listOf(purchaseOrder)
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId1 to SkuSpecification.default.copy(skuCode = skuCode1),
            skuId2 to SkuSpecification.default.copy(skuCode = skuCode2),
        )

        // In this scenario, the first delivery line has both sku1 and sku2 deliveries closed on poDelivery1Time
        // and the second delivery line has open deliveries for each sku
        val poDelivery1Time = LocalDate.parse("2023-02-02")
        val deliveryId = UUID.randomUUID().toString()
        val poDelivery1 = newPoDelivery(
            poDelivery1Time,
            deliveryId,
            deliveries = listOf(
                PoDeliveryLine(skuCode1, poPalletizedQty1Sku1Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
                PoDeliveryLine(skuCode1, poPalletizedQty2Sku1Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
                PoDeliveryLine(skuCode2, poPalletizedQty1Sku2Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
                PoDeliveryLine(skuCode2, poPalletizedQty2Sku2Closed.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
            ),
        )

        val poDelivery2Time = poDelivery1Time.plusDays(1)
        val deliveryId2 = UUID.randomUUID().toString()
        val poDelivery2 = newPoDelivery(
            poDelivery2Time,
            deliveryId2,
            deliveries = listOf(
                PoDeliveryLine(skuCode1, 10000L, DELIVERY_LINE_STATE_RECEIVED),
                PoDeliveryLine(skuCode2, 10001L, DELIVERY_LINE_STATE_RECEIVED),
            ),
        )

        val record = grnKafkaRecord(defaultDcCode, poRef, listOf(poDelivery1, poDelivery2))

        val slot = slot<List<Grn>>()
        coEvery { goodsReceivedNoteRepo.save(capture(slot)) } returns Unit
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId1 to SkuSpecification.default.copy(skuCode = skuCode1),
            skuId2 to SkuSpecification.default.copy(skuCode = skuCode2),
        )

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(record) }

        // then
        val grnList = slot.captured
        assertEquals(1, grnList.size)
        assertEquals(GrnSourceType.PURCHASE_ORDER, grnList.first().sourceType)
        assertEquals(4, grnList.first().skus.size)
        grnList.first().also { grn ->
            val grnSku1ClosedDeliveryRecord = grn.skus.find { it.skuId == skuId1 && it.status == CLOSED }
            assertEquals(
                poPalletizedQty1Sku1Closed + poPalletizedQty2Sku1Closed,
                grnSku1ClosedDeliveryRecord?.totalPalletizedQty,
            )
            assertEquals(poDelivery1Time, grnSku1ClosedDeliveryRecord?.deliveryDateTime?.toLocalDate())
            val grnSku1OpenDeliveryRecord = grn.skus.find { it.skuId == skuId1 && it.status == OPEN }
            assertEquals(0, grnSku1OpenDeliveryRecord?.totalPalletizedQty?.getValue()?.toLong())
            assertEquals(poDelivery2Time, grnSku1OpenDeliveryRecord?.deliveryDateTime?.toLocalDate())

            val grnSku2ClosedDeliveryRecord = grn.skus.find { it.skuId == skuId2 && it.status == CLOSED }
            assertEquals(
                poPalletizedQty1Sku2Closed + poPalletizedQty2Sku2Closed,
                grnSku2ClosedDeliveryRecord?.totalPalletizedQty,
            )
            assertEquals(poDelivery1Time, grnSku2ClosedDeliveryRecord?.deliveryDateTime?.toLocalDate())
            val grnSku2OpenDeliveryRecord = grn.skus.find { it.skuId == skuId2 && it.status == OPEN }
            assertEquals(0, grnSku2OpenDeliveryRecord?.totalPalletizedQty?.getValue()?.toLong())
            assertEquals(poDelivery2Time, grnSku2OpenDeliveryRecord?.deliveryDateTime?.toLocalDate())
        }
    }

    @Test
    fun `should aggregate deliveries and select min expiry date in case there are several line values in the same GRN event`() {
        // given
        val poNumber = "2309GR085559"
        val poRef = poNumber + "_O1"

        val skuCode1 = "SPI-0001"
        val skuId1 = UUID.randomUUID()
        val poPalletizedQty1Sku1Closed = 11L
        val poPalletizedQty2Sku1Closed = 12L

        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns emptyList()

        // In this scenario, the first delivery line has both sku1 and sku2 deliveries closed on poDelivery1Time
        // and the second delivery line has open deliveries for each sku
        val poDelivery1Time = LocalDate.parse("2023-02-02")
        val deliveryId = UUID.randomUUID().toString()
        val expectedExpiryDate = LocalDate.now().plusDays(40)
        val expiryDateFiltered = LocalDate.now().plusDays(100)
        val poDelivery1 = newPoDelivery(
            poDelivery1Time,
            deliveryId,
            deliveries = listOf(
                PoDeliveryLine(
                    skuCode1,
                    poPalletizedQty1Sku1Closed,
                    DELIVERY_LINE_STATE_CLOSED,
                    expectedExpiryDate.atStartOfDay(UTC),
                ),
                PoDeliveryLine(
                    skuCode1,
                    poPalletizedQty2Sku1Closed,
                    DELIVERY_LINE_STATE_CLOSED,
                    expiryDateFiltered.atStartOfDay(UTC),
                ),
            ),
        )

        val record = grnKafkaRecord(defaultDcCode, poRef, listOf(poDelivery1))

        val slot = slot<List<Grn>>()
        coEvery { goodsReceivedNoteRepo.save(capture(slot)) } returns Unit
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId1 to SkuSpecification.default.copy(skuCode = skuCode1),
        )

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(record) }

        // then
        val grnList = slot.captured
        assertEquals(1, grnList.size)
        assertEquals(1, grnList.first().skus.size)
        assertEquals(GrnSourceType.PURCHASE_ORDER, grnList.first().sourceType)
        grnList.first().also { grn ->
            val grnSku1ClosedDeliveryRecord = grn.skus.find { it.skuId == skuId1 && it.status == CLOSED }
            assertEquals(expectedExpiryDate, grnSku1ClosedDeliveryRecord?.expiryDate)
        }
    }

    @Test
    fun `a grn record where all lines are closed for a sku should result in a grn DB record having a CLOSED state`() {
        val skuCode = "PHF-123"
        val skuId = UUID.randomUUID()
        val poNumber = "some-po-number"
        val poRef = "some-po-ref"
        val deliveredQtyForSku = SkuQuantity.fromLong(11L)
        val now = LocalDate.now(UTC)
        val timezone = "Australia/Sydney"
        val closedDelivery = newPoDelivery(
            now,
            UUID.randomUUID().toString(),
            timezone,
            deliveries = listOf(
                PoDeliveryLine(skuCode, deliveredQtyForSku.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
            ),
        )
        val purchaseOrder = PurchaseOrder(
            poNumber,
            poRef,
            UUID.randomUUID(),
            defaultDcCode,
            TimeRange(now.atStartOfDay(UTC), now.atStartOfDay(UTC).plusMinutes(1)),
            null,
            emptyList(),
            poStatus = APPROVED,
        )
        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns listOf(purchaseOrder)
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId to SkuSpecification.default.copy(skuCode = skuCode),
        )

        val slot = slot<List<Grn>>()
        coEvery { goodsReceivedNoteRepo.save(capture(slot)) } returns Unit

        val grnKafkaRecord = grnKafkaRecord(defaultDcCode, poRef, listOf(closedDelivery))

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(grnKafkaRecord) }

        // then
        val grnDbRecords = slot.captured
        assertEquals(1, grnDbRecords.size)
        assertEquals(GrnSourceType.PURCHASE_ORDER, grnDbRecords.first().sourceType)
        assertEquals(CLOSED, grnDbRecords.first().skus.first().status)
        assertEquals(
            ZoneId.of(timezone).rules.getOffset(Instant.now()),
            grnDbRecords.first().skus.first().deliveryDateTime.offset,
        )
        assertEquals(deliveredQtyForSku, grnDbRecords.first().skus.first().totalPalletizedQty)
    }

    @Test fun `expiry is converted to the local date`() {
        val skuCode = "PHF-123"
        val skuId = UUID.randomUUID()
        val poNumber = "some-po-number"
        val poRef = "some-po-ref"
        val deliveredQtyForSku = 11L
        val now = LocalDate.now(UTC)

        val timeAtMidnight = ZonedDateTime.of(now.year, now.month.value, now.dayOfMonth, 23, 59, 59, 59, UTC)

        val closedDelivery = newPoDelivery(
            now,
            UUID.randomUUID().toString(),
            deliveries = listOf(
                PoDeliveryLine(skuCode, deliveredQtyForSku, DELIVERY_LINE_STATE_CLOSED, expiryDate = timeAtMidnight),
            ),
        )
        val purchaseOrder = PurchaseOrder(
            poNumber,
            poRef,
            UUID.randomUUID(),
            defaultDcCode,
            TimeRange(now.atStartOfDay(UTC), now.atStartOfDay(UTC).plusMinutes(1)),
            null,
            emptyList(),
            poStatus = APPROVED,
        )
        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns listOf(purchaseOrder)
        val slot = slot<List<Grn>>()
        coEvery { goodsReceivedNoteRepo.save(capture(slot)) } returns Unit
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId to SkuSpecification.default.copy(skuCode = skuCode),
        )

        val grnKafkaRecord = grnKafkaRecord(defaultDcCode, poRef, listOf(closedDelivery))

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(grnKafkaRecord) }

        // then
        val grnDbRecords = slot.captured
        assertEquals(1, grnDbRecords.size)

        // Day is incremented after converion
        assertEquals(timeAtMidnight.toLocalDate(), grnDbRecords.first().skus.first().expiryDate)
    }

    @Test
    fun `should store an GRN record having OPEN delivery state and 0 qty for a sku if the incoming grn has no DELIVERY_LINE_STATE_CLOSED for that sku`() {
        val skuCode = "SPI-000"
        val now = LocalDate.now(UTC)
        val poDeliveryReceived = newPoDelivery(
            now,
            UUID.randomUUID().toString(),
            deliveries = listOf(
                PoDeliveryLine(skuCode, 10000L, DELIVERY_LINE_STATE_RECEIVED),
            ),
        )
        val skuId = UUID.randomUUID()
        val poNumber = "num"
        val poRef = "ref1"
        val purchaseOrder = PurchaseOrder(
            poNumber,
            poRef,
            UUID.randomUUID(),
            defaultDcCode,
            TimeRange(now.atStartOfDay(UTC), now.atStartOfDay(UTC).plusMinutes(1)),
            null,
            emptyList(),
            poStatus = APPROVED,
        )
        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns listOf(purchaseOrder)
        val slot = slot<List<Grn>>()
        coEvery { goodsReceivedNoteRepo.save(capture(slot)) } returns Unit
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId to SkuSpecification.default.copy(skuCode = skuCode),
        )

        val record = grnKafkaRecord(defaultDcCode, poRef, listOf(poDeliveryReceived))

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(record) }

        // then
        val grnDbRecords = slot.captured
        assertEquals(1, grnDbRecords.size)
        assertEquals(OPEN, grnDbRecords.first().skus.first().status)
        assertEquals(GrnSourceType.PURCHASE_ORDER, grnDbRecords.first().sourceType)
        assertEquals(
            SkuQuantity.fromLong(0).getValue(),
            grnDbRecords.first().skus.first().totalPalletizedQty.getValue(),
        )
    }

    @Test
    fun `fallback to the grn record dc code if the purchase order is not found in the DB`() {
        val skuCode = "PHF-1010"
        val deliveredQtyForSku = SkuQuantity.fromLong(3L)
        val closedDelivery = newPoDelivery(
            LocalDate.now(UTC),
            UUID.randomUUID().toString(),
            deliveries = listOf(
                PoDeliveryLine(skuCode, deliveredQtyForSku.getValue().toLong(), DELIVERY_LINE_STATE_CLOSED),
            ),
        )
        val skuId = UUID.randomUUID()
        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns emptyList() // no PO returned for any given poRef
        val slot = slot<List<Grn>>()
        coEvery { goodsReceivedNoteRepo.save(capture(slot)) } returns Unit
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId to SkuSpecification.default.copy(skuCode = skuCode),
        )

        val grnKafkaRecord = grnKafkaRecord(defaultDcCode, "some-po-ref", listOf(closedDelivery))

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(grnKafkaRecord) }

        // then
        val grnDbRecords = slot.captured
        assertEquals(1, grnDbRecords.size)
        assertEquals(CLOSED, grnDbRecords.first().skus.first().status)
        assertEquals(GrnSourceType.PURCHASE_ORDER, grnDbRecords.first().sourceType)
        assertEquals(defaultDcCode, grnDbRecords.first().dcCode)
        assertEquals(deliveredQtyForSku, grnDbRecords.first().skus.first().totalPalletizedQty)
    }

    @Test
    fun `should fallback to expectedDeliveryStartTime if deliveryTime is missing`() {
        val skuCode = "SKU-1"
        val validExpectedDeliveryStartTime = LocalDate.now(UTC)
        val closedDelivery = newPoDelivery(
            poDate = null,
            deliveryId = UUID.randomUUID().toString(),
            expectedDeliveryStartTime = validExpectedDeliveryStartTime,
            deliveries = listOf(
                PoDeliveryLine(skuCode, 1, DELIVERY_LINE_STATE_CLOSED),
            ),
        )
        val skuId = UUID.randomUUID()

        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns emptyList() // no PO returned for any given poRef
        val slot = slot<List<Grn>>()
        coEvery { goodsReceivedNoteRepo.save(capture(slot)) } returns Unit
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId to SkuSpecification.default.copy(skuCode = skuCode),
        )

        val grnKafkaRecord = grnKafkaRecord(defaultDcCode, "some-po-ref", listOf(closedDelivery))

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(grnKafkaRecord) }

        // then
        val grnDbRecords = slot.captured
        assertEquals(1, grnDbRecords.size)
        assertEquals(validExpectedDeliveryStartTime, grnDbRecords.first().skus.first().deliveryDateTime.toLocalDate())
        assertEquals(GrnSourceType.PURCHASE_ORDER, grnDbRecords.first().sourceType)
    }

    @Test
    fun `skip grn record when a sku is not found`() {
        val now = LocalDate.now(UTC)
        val closedDelivery = newPoDelivery(
            now,
            UUID.randomUUID().toString(),
            deliveries = listOf(
                PoDeliveryLine("SPI-000", 10, DELIVERY_LINE_STATE_CLOSED),
            ),
        )
        val dcCode = "XX"
        val poNumber = "some-po-number"
        val poRef = "some-po-ref"
        val grnKafkaRecord = grnKafkaRecord(dcCode, poRef, listOf(closedDelivery))
        val purchaseOrder = PurchaseOrder(
            poNumber,
            poRef,
            UUID.randomUUID(),
            dcCode,
            TimeRange(now.atStartOfDay(UTC), now.atStartOfDay(UTC).plusMinutes(1)),
            null,
            emptyList(),
            poStatus = APPROVED,
        )
        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns listOf(purchaseOrder)
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf()

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(grnKafkaRecord) }

        // then
        coVerify(exactly = 0) { goodsReceivedNoteRepo.save(any()) }
    }

    @Test
    fun `skip grn record when a po ref contains the WA for SY dc code`() {
        val now = LocalDate.now(UTC)
        val closedDelivery = newPoDelivery(
            now,
            UUID.randomUUID().toString(),
            deliveries = listOf(
                PoDeliveryLine("SPI-000", 10, DELIVERY_LINE_STATE_CLOSED),
            ),
        )
        val dcCode = "SY"
        val dc = DistributionCenterConfiguration.default(dcCode)
        coEvery { dcConfigRepository.fetchDcConfigurations() } returns listOf(dc)
        dcConfigService.fetchOnDemand()

        val poNumber = "2441WA473435"
        val poRef = "2441WA473435_E1"
        val skuCode = "SPI-000"
        val skuId = UUID.randomUUID()
        val grnKafkaRecord = grnKafkaRecord(dcCode, poRef, listOf(closedDelivery))
        val purchaseOrder = PurchaseOrder(
            poNumber,
            poRef,
            UUID.randomUUID(),
            dcCode,
            TimeRange(now.atStartOfDay(UTC), now.atStartOfDay(UTC).plusMinutes(1)),
            null,
            emptyList(),
            poStatus = APPROVED,
        )
        coEvery { purchaseOrderRepository.findPurchaseOrders(any()) } returns listOf(purchaseOrder)
        coEvery { skuSpecificationRepository.fetchSkuSpecifications() } returns mapOf(
            skuId to SkuSpecification.default.copy(skuCode = skuCode),
        )

        // when
        runBlocking { goodsReceivedNoteProcessor.processRecords(grnKafkaRecord) }

        // then
        coVerify(exactly = 0) { goodsReceivedNoteRepo.save(any()) }
    }

    private fun grnKafkaRecord(
        dcCode: String,
        poRef: String,
        poDeliveries: List<GRNPurchaseOrderDelivery>,
    ): ConsumerRecords<GRNKey, GRNValue> {
        val grnKey = GRNKey(
            dcCode = dcCode,
            reference = poRef,
        )
        val grnValue = GRNValue(
            deliveriesList = poDeliveries,
        )

        return ConsumerRecords(
            mapOf(
                TopicPartition("test", 0) to
                    listOf(
                        ConsumerRecord(
                            "test", 0, 0,
                            0, TimestampType.CREATE_TIME, 0, 0,
                            grnKey, grnValue, RecordHeaders(emptyList()), Optional.empty(),
                        ),
                    ),
            ),
        )
    }

    private fun newPoDelivery(
        poDate: LocalDate?,
        deliveryId: String,
        timezone: String = "UTC",
        expectedDeliveryStartTime: LocalDate? = poDate,
        deliveries: List<PoDeliveryLine>,
    ): GRNPurchaseOrderDelivery {
        val deliveryLines = deliveries.map {
            newPoDeliveryLine(it.skuCode, it.qty, it.deliveryLineState, it.expiryDate)
        }

        val zoneId = ZoneId.of(timezone)

        val currentOffset = zoneId.rules.getOffset(Instant.now())

        val deliveryTime = when {
            poDate != null -> {
                OffsetDateTime.of(poDate, LocalTime.now(), currentOffset)
            }
            expectedDeliveryStartTime != null -> {
                OffsetDateTime.of(
                    expectedDeliveryStartTime.year,
                    expectedDeliveryStartTime.monthValue,
                    expectedDeliveryStartTime.dayOfMonth,
                    0,
                    0,
                    0,
                    0,
                    currentOffset
                )
            }
            else -> OffsetDateTime.now(UTC)
        }

        return GRNPurchaseOrderDelivery(
            id = deliveryId,
            deliveryTime = deliveryTime,
            linesList = deliveryLines,
        )
    }

    private fun newPoDeliveryLine(
        skuCode: String,
        qty: Long,
        grnDeliveryLineState: GRNDeliveryLineState = DELIVERY_LINE_STATE_CLOSED,
        expiryDate: ZonedDateTime?,
    ) =
        GRNPurchaseOrderDeliveryLine(
            skuCode = skuCode,
            state = grnDeliveryLineState,
            skuUom = UOM_UNIT,
            palletizedQuantity = qty.toString(),
            expirationDate = expiryDate?.toLocalDate() ?: LocalDate.now(),
        )
}

private data class PoDeliveryLine(
    val skuCode: String,
    val qty: Long,
    val deliveryLineState: GRNDeliveryLineState,
    val expiryDate: ZonedDateTime? = null
)
