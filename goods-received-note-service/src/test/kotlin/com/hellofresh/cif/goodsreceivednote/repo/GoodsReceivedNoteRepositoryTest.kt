package com.hellofresh.cif.goodsreceivednote.repo

import com.hellofresh.cif.distributionCenter.models.EuBerlinZoneId
import com.hellofresh.cif.goodsReceivedNote.schema.Tables
import com.hellofresh.cif.goodsReceivedNote.schema.enums.Uom
import com.hellofresh.cif.goodsReceivedNote.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.goodsReceivedNote.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.goodsReceivedNote.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.goodsReceivedNote.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.goodsReceivedNote.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.goodsreceivednote.repo.DeliveryStatus.CLOSED
import com.hellofresh.cif.goodsreceivednote.repo.DeliveryStatus.OPEN
import com.hellofresh.cif.goodsreceivednote.service.Grn
import com.hellofresh.cif.goodsreceivednote.service.GrnSku
import com.hellofresh.cif.goodsreceivednote.service.GrnSourceType.PURCHASE_ORDER
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.time.temporal.ChronoUnit
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class GoodsReceivedNoteRepositoryTest : AbstractDbTest() {
    @Test
    fun `should save multiple GoodsReceivedNote records`() {
        // given
        val poNr = "2024DH00"
        val poRef = "${poNr}_O1"
        val grn = Grn(
            "VE",
            poRef,
            PURCHASE_ORDER,
            listOf(
                GrnSku(
                    UUID.randomUUID().toString(),
                    OffsetDateTime.now(EuBerlinZoneId),
                    UUID.randomUUID(),
                    SkuQuantity.fromLong(9),
                    OPEN,
                    null,
                    SkuUOM.UOM_UNIT,
                ),
                GrnSku(
                    UUID.randomUUID().toString(),
                    OffsetDateTime.now(EuBerlinZoneId),
                    UUID.randomUUID(),
                    SkuQuantity.fromLong(1),
                    CLOSED,
                    LocalDate.now().plusDays(30),
                    SkuUOM.UOM_UNIT,
                ),
            ),
        )

        // when
        runBlocking { goodsReceivedNoteRepo.save(listOf(grn)) }

        // then
        val persistedRecords = dsl.selectFrom(Tables.GOODS_RECEIVED_NOTE).fetch().toList()
        assertEquals(2, persistedRecords.size)
        persistedRecords.forEach {
            assertEquals(poNr, it.poNumber)
        }
        grn.skus.forEach { grnsku ->
            persistedRecords.first { r -> r.skuId == grnsku.skuId }.also {
                assertEquals(grnsku.deliveryId, it.deliveryId)
                assertEquals(
                    grnsku.deliveryDateTime.atZoneSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS),
                    it.deliveryTime.atZoneSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS),
                )
                assertEquals(grnsku.expiryDate, it.expiryDate)
            }
        }
    }

    @ParameterizedTest
    @MethodSource("getUnitOfMeasurements")
    fun `should save GoodsReceivedNote with UOM`(skuUOM: SkuUOM, uom: Uom) {
        // given
        val poNr = "2024DH00"
        val poRef = "${poNr}_O1"
        val grn = Grn(
            "VE",
            poRef,
            PURCHASE_ORDER,
            listOf(
                GrnSku(
                    UUID.randomUUID().toString(),
                    OffsetDateTime.now(EuBerlinZoneId),
                    UUID.randomUUID(),
                    SkuQuantity.fromLong(9),
                    OPEN,
                    null,
                    skuUOM,
                )
            ),
        )

        // when
        runBlocking { goodsReceivedNoteRepo.save(listOf(grn)) }

        // then
        val persistedRecords = dsl.selectFrom(Tables.GOODS_RECEIVED_NOTE).fetch().toList()
        assertEquals(1, persistedRecords.size)
        persistedRecords.forEach {
            assertEquals(poNr, it.poNumber)
        }
        grn.skus.forEach { grnsku ->
            persistedRecords.first { r -> r.skuId == grnsku.skuId }.also {
                assertEquals(grnsku.deliveryId, it.deliveryId)
                assertEquals(
                    grnsku.deliveryDateTime.atZoneSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS),
                    it.deliveryTime.atZoneSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS),
                )
                assertEquals(grnsku.expiryDate, it.expiryDate)
                assertEquals(uom, it.uom)
            }
        }
    }

    @Test
    fun `should update an existing GoodsReceivedNote record`() {
        val grn = Grn(
            "VE",
            "2024DH00_O2",
            PURCHASE_ORDER,
            listOf(
                GrnSku(
                    UUID.randomUUID().toString(),
                    OffsetDateTime.of(LocalDate.now(), LocalTime.NOON, UTC),
                    UUID.randomUUID(),
                    SkuQuantity.fromLong(1),
                    OPEN,
                    null,
                    SkuUOM.UOM_UNIT,
                ),
            ),
        )

        val updatedGrn = GrnSku(
            UUID.randomUUID().toString(),
            OffsetDateTime.of(LocalDate.now(), LocalTime.NOON, UTC).plusHours(1),
            UUID.randomUUID(),
            SkuQuantity.fromLong(17),
            CLOSED,
            LocalDate.now(),
            SkuUOM.UOM_UNIT,
        )
        val grnUpdate = grn.copy(skus = listOf(updatedGrn))

        runBlocking { goodsReceivedNoteRepo.save(listOf(grn)) }
        runBlocking { goodsReceivedNoteRepo.save(listOf(grnUpdate)) }

        // then
        val records = dsl.selectFrom(Tables.GOODS_RECEIVED_NOTE).fetch().toList()
        records.first().let {
            assertEquals(updatedGrn.status.name, it.deliveryStatus)
            assertEquals(updatedGrn.totalPalletizedQty.getValue(), it.quantity)
            assertEquals(updatedGrn.deliveryId, it.deliveryId)
            assertEquals(
                updatedGrn.deliveryDateTime.truncatedTo(ChronoUnit.SECONDS),
                it.deliveryTime.withOffsetSameInstant(UTC).truncatedTo(ChronoUnit.SECONDS),
            )
            assertEquals(updatedGrn.expiryDate, it.expiryDate)
        }
    }

    @Test
    fun `should persist grn if poRef is malformatted and set its poNumber equals to that poRef`() {
        // given
        val grn = Grn(
            "VE",
            "TestTest",
            PURCHASE_ORDER,
            listOf(
                GrnSku(
                    UUID.randomUUID().toString(),
                    OffsetDateTime.of(LocalDate.now(), LocalTime.NOON, UTC),
                    UUID.randomUUID(),
                    SkuQuantity.fromLong(1),
                    OPEN,
                    null,
                    SkuUOM.UOM_UNIT,
                ),
            ),
        )

        // when
        runBlocking { goodsReceivedNoteRepo.save(listOf(grn)) }

        // then
        dsl.selectFrom(Tables.GOODS_RECEIVED_NOTE).fetch().first().let {
            assertEquals(grn.poRef, it.poNumber)
        }
    }

    companion object {
        @JvmStatic
        fun getUnitOfMeasurements(): Stream<Arguments> = Stream.of(
            Arguments.of(SkuUOM.UOM_UNIT, UOM_UNIT),
            Arguments.of(SkuUOM.UOM_UNSPECIFIED, Uom.UOM_UNSPECIFIED),
            Arguments.of(SkuUOM.UOM_UNRECOGNIZED, Uom.UOM_UNSPECIFIED),
            Arguments.of(SkuUOM.UOM_KG, UOM_KG),
            Arguments.of(SkuUOM.UOM_LBS, UOM_LBS),
            Arguments.of(SkuUOM.UOM_OZ, UOM_OZ),
            Arguments.of(SkuUOM.UOM_LITRE, UOM_LITRE),
        )
    }
}
