package com.hellofresh.cif.calculator

import com.google.common.cache.CacheBuilder
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.calculator.producer.filter.CalculationRepository
import com.hellofresh.cif.demand.DemandRepository
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.featureflags.Context
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.StockUpdate
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.InventoryService.Companion.calculationInputDcDateRanges
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.lib.groupBySecondToSet
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.model.SafetyStockValue
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.skuinput.repo.SkuInputData
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.cif.transferorder.db.TransferOrderRepository
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderOutbounds
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.SupplierSkuDetail
import java.time.Duration
import java.util.UUID
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging
import org.jetbrains.annotations.VisibleForTesting

@Suppress("LongParameterList")
class SkuInputDataService(
    private val skuInputDataRepository: SkuInputDataRepository,
    private val inventoryService: InventoryService,
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val transferOrderRepository: TransferOrderRepository,
    private val demandRepository: DemandRepository,
    private val stockUpdateService: StockUpdateService,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
    private val safetyStockRepository: SafetyStockRepository,
    private val calculationRepositories: Map<CalculatorMode, CalculationRepository<*, *>>
) {
    suspend fun fetchInputData(calculatorModes: List<CalculatorMode>, dcCodes: Set<String>) =
        withContext(Dispatchers.IO) {
            val skuInputData = skuInputDataRepository.fetchByDcs(dcCodes)
            val dcConfigs = skuInputData.dcConfig.values.toSet()
            val inventorySnapshots = inventoryService.fetchInventorySnapshots(dcConfigs)

            val dcsDateRanges = calculationInputDcDateRanges(dcConfigs, inventorySnapshots)

            val skuCalculationModeCandidates =
                fetchSkuCalculationCandidates(calculatorModes, dcsDateRanges, skuInputData.skuSpecification)

            val purchaseOrderInboundsDeferred = loadAsync(dcsDateRanges) { range, dcs ->
                purchaseOrderRepository.findPurchaseOrders(dcs, range)
            }

            // load transfer orders inbounds and outbounds deferred
            val transferOrderInboundsDeferred = loadAsync(dcsDateRanges) { range, dcs ->
                transferOrderRepository.fetchInboundOrders(dcs, range)
            }

            val transferOrderOutboundsDeferred = loadAsync(dcsDateRanges) { range, dcs ->
                transferOrderRepository.fetchOutboundOrders(dcs, range)
            }

            val demandsDeferred = loadAsync(dcsDateRanges) { range, dcs -> demandRepository.findDemands(dcs, range) }
            val stockUpdatesDeferred = async { getEnabledStockUpdates(dcConfigs) }
            val safetyStocksDeferred = async { fetchSafetyStocks(dcConfigs, dcsDateRanges) }

            val demands = Demands(demandsDeferred.await())
            val purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrderInboundsDeferred.await())
            val transferOrderInbounds = TransferOrderInbounds(transferOrderInboundsDeferred.await())
            val transferOrderOutbounds = TransferOrderOutbounds(transferOrderOutboundsDeferred.await())
            val stockUpdates = stockUpdatesDeferred.await()

            CalculationsInputData(
                skuCalculationModeCandidates,
                buildSkuDcCandidatesFromSources(
                    skuInputData,
                    inventorySnapshots,
                    demands,
                    purchaseOrderInbounds,
                    stockUpdates,
                ),
                inventorySnapshots,
                purchaseOrderInbounds,
                transferOrderInbounds = transferOrderInbounds,
                transferOrderOutbounds = transferOrderOutbounds,
                demands,
                stockUpdates,
                safetyStocksDeferred.await(),
                skuInputData.supplierSkuDetails,
            )
        }

    private suspend fun fetchSafetyStocks(
        dcConfigs: Set<DistributionCenterConfiguration>,
        dcsDateRanges: Map<DateRange, Set<DistributionCenterConfiguration>>
    ): Map<SafetyStockKey, SafetyStockValue> {
        val minimumWeek = getMinimumWeek(dcsDateRanges)
        return minimumWeek?.let { weekFrom ->
            safetyStockRepository
                .fetchSafetyStocks(weekFrom, dcConfigs.map { it.dcCode }.toSet())
                .associate {
                    it.toKey() to SafetyStockValue(it.value, it.strategy)
                }
        } ?: emptyMap()
    }

    @VisibleForTesting
    fun getMinimumWeek(
        dcsDateRanges: Map<DateRange, Set<DistributionCenterConfiguration>>,
    ): String? =
        dcsDateRanges.flatMap { (dateRange, dcs) ->
            dcs.map { dc -> DcWeek(dateRange.fromDate, dc.productionStart) }
        }
            .minByOrNull { it.value }?.value

    suspend fun getEnabledStockUpdates(dcs: Set<DistributionCenterConfiguration>) =
        getStockUpdates(
            dcs.filter { isMarketEnabled(it.market, statsigFeatureFlagClient) }
                .map { it.dcCode }.toSet(),
        )

    suspend fun getStockUpdates(dcs: Set<String>): Map<CalculationKey, SkuQuantity> =
        if (dcs.isEmpty()) {
            emptyMap()
        } else {
            stockUpdateService.getCurrentStockUpdates(dcs)
                .flatMap { (dcSku, stockUpdatesByDate) ->
                    stockUpdatesByDate.mapNotNull { it.value }.map { CalculationKey(dcSku.skuId, dcSku.dcCode, it.date) to it.quantity }
                }.toMap()
        }

    private fun isMarketEnabled(market: String, featureFlagClient: StatsigFeatureFlagClient) =
        featureFlagClient.isEnabledFor(
            StockUpdate(setOf(ContextData(Context.MARKET, market))),
        )

    private fun <T, K> CoroutineScope.loadAsync(
        dateRanges: Map<K, Set<DistributionCenterConfiguration>>,
        loader: suspend (dateRange: K, dcCodes: Set<DcCode>) -> List<T>
    ) = async { dateRanges.flatMap { (dateRange, dcs) -> loader(dateRange, dcs.map { it.dcCode }.toSet()) } }

    fun buildSkuDcCandidatesFromSources(
        skuInputData: SkuInputData,
        inventorySnapshots: InventorySnapshots,
        demands: Demands,
        purchaseOrderInbounds: PurchaseOrderInbounds,
        stockUpdates: Map<CalculationKey, SkuQuantity>
    ): Set<SkuDcCandidate> {
        val inventoryDcSkus = inventorySnapshots.getAvailableSkuDcs()
        val demandDcSkus = demands.demandList.map { demand -> demand.skuId to demand.dcCode }.toSet()
        val stockUpdateDcSkus = stockUpdates.keys.map { it.cskuId to it.dcCode }.toSet()

        val skuIdDcCodeToPoRefMap = purchaseOrderInbounds.pos.flatMap { po ->
            po.purchaseOrderSkus.map { v -> (v.skuId to po.dcCode) to po.poReference }
        }.groupBy({ (pair, _) -> pair }) { (_, reference) -> reference }

        val candidates = inventoryDcSkus + demandDcSkus + stockUpdateDcSkus + skuIdDcCodeToPoRefMap.keys

        return candidates
            .groupBySecondToSet()
            .flatMap { (dcCode, skuIds) ->
                skuInputData.dcConfig[dcCode]?.let { dc ->
                    skuIds.mapNotNull { skuId ->
                        skuInputData.skuSpecification.getSku(skuId, dcCode, skuIdDcCodeToPoRefMap[skuId to dcCode]?.toSet())
                            ?.let { skuSpec -> SkuDcCandidate(skuId, skuSpec, dc) }
                    }
                }
                    ?: run {
                        logger.error("Dc Config not found for $dcCode")
                        emptyList()
                    }
            }.toSet()
    }

    private fun fetchSkuCalculationCandidates(
        calculatorModes: List<CalculatorMode>,
        dcsDateRanges: Map<DateRange, Set<DistributionCenterConfiguration>>,
        skuSpecifications: Map<UUID, SkuSpecification>,
    ) =
        calculationRepositories
            .filter { (calcMode, _) -> calculatorModes.contains(calcMode) }
            .mapValues { (_, calculationRepository) ->
                dcsDateRanges.flatMap { (dateRange, dcs) ->
                    val dcMap = dcs.associateBy { it.dcCode }
                    calculationRepository.fetchSkuDcWithCalculations(dateRange, dcMap.keys)
                        .mapNotNull { (skuId, dcCode) ->
                            skuSpecifications.getSku(skuId, dcCode)
                                ?.let { spec ->
                                    dcMap[dcCode]?.let { dc -> SkuDcCandidate(skuId, spec, dc) }
                                }
                        }
                }.toSet()
            }

    private fun Map<UUID, SkuSpecification>.getSku(
        skuId: UUID,
        dcCode: String,
        poRefsSource: Set<String>? = null
    ) = this[skuId]
        ?: run {
            val brokenEntry = BrokenPoSku(skuId, dcCode, poRefsSource ?: emptySet())
            if (brokenSkusCacheMap.getIfPresent(brokenEntry) == null) {
                logger.error("Could not find sku, skipping. skuId: $skuId, dcCode: $dcCode, poRef: $poRefsSource")
                brokenSkusCacheMap.put(brokenEntry, brokenEntry.hashCode())
            }
            null
        }

    companion object : Logging {

        private val brokenSkusCacheMap = CacheBuilder
            .newBuilder()
            .expireAfterWrite(Duration.ofHours(6))
            .build<BrokenPoSku, Int>()

        private data class BrokenPoSku(
            val skuId: UUID,
            val dcCode: String,
            val poRef: Set<String>
        )
    }
}

data class CalculationsInputData(
    val skuCalculationModeCandidates: Map<CalculatorMode, Set<SkuDcCandidate>>,
    val skuDcCandidatesFromSources: Set<SkuDcCandidate>,
    val inventorySnapshots: InventorySnapshots,
    val purchaseOrderInbounds: PurchaseOrderInbounds,
    val transferOrderInbounds: TransferOrderInbounds,
    val transferOrderOutbounds: TransferOrderOutbounds,
    val demands: Demands,
    val stockUpdates: Map<CalculationKey, SkuQuantity>,
    val safetyStocks: Map<SafetyStockKey, SafetyStockValue>,
    val supplierSkuDetails: Map<UUID, List<SupplierSkuDetail>>,
) {

    fun getSkuDcCandidates(calculatorMode: CalculatorMode): Set<SkuDcCandidate> =
        skuDcCandidatesFromSources + skuCalculationModeCandidates[calculatorMode].orEmpty()

    companion object : Logging
}
