package com.hellofresh.cif.calculator

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.calculator.producer.Producer
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.MultiGauge
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.Tags
import java.util.concurrent.atomic.AtomicInteger
import kotlin.math.max
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging

private const val MAX_PARALLEL_SKU_CANDIDATES = 8000

class CalculatorService(
    private val skuInputDataService: SkuInputDataService,
    private val producers: List<Producer<CskuInventoryForecastKey, CskuInventoryForecastVal>>,
    private val calculatorClient: CalculatorClient,
    meterRegistry: MeterRegistry,
    private val preproductionCleardownDcs: Set<String> = emptySet(),
    private val podName: String,
) {

    private val skuCandidatesMultiGauge =
        MultiGauge.builder("calculator_job_sku_candidates")
            .register(meterRegistry)

    private val parallelismGaugeValue = AtomicInteger(0)

    init {
        meterRegistry.gauge("calculator_job_parallelism", parallelismGaugeValue)
    }

    suspend fun run(dcCodes: Set<String>) {
        logger.info("Running calculations for: ${dcCodes.joinToString()} in the pod = $podName")

        val calculationsInputData = skuInputDataService.fetchInputData(producers.map { it.calculatorMode() }, dcCodes)

        val skuCandidatesProducersMap = producers.associate {
            it to calculationsInputData.getSkuDcCandidates(it.calculatorMode())
        }.onEach { (producer, candidates) ->
            logger.info(
                "Sku Candidates ${producer.calculatorMode()}: ${candidates.size} - ${candidates.groupBy { it.dcCode }.mapValues { it.value.count() }}",
            )
        }

        val parallelism = calculateParallelism(skuCandidatesProducersMap)
        logger.info("Calculator Parallelism: $parallelism")
        withContext(Dispatchers.Default.limitedParallelism(parallelism)) {
            skuCandidatesProducersMap
                .forEach { (producer, skuCandidates) ->
                    launch {
                        val dailyCalculations = calculatorClient.runDailyCalculations(
                            InputData(
                                mode = producer.calculatorMode(),
                                skuDcCandidates = skuCandidates,
                                inventory = calculationsInputData.inventorySnapshots,
                                purchaseOrderInbounds = calculationsInputData.purchaseOrderInbounds,
                                transferOrderInbounds = calculationsInputData.transferOrderInbounds,
                                transferOrderOutbounds = calculationsInputData.transferOrderOutbounds,
                                demands = calculationsInputData.demands,
                                safetyStocks = calculationsInputData.safetyStocks,
                                supplierSku = calculationsInputData.supplierSkuDetails,
                                stockUpdates = calculationsInputData.stockUpdates,
                                preproductionCleardownDcs = preproductionCleardownDcs,
                            ),
                        )
                        producer.produce(dailyCalculations, CskuInventoryForecastMapper::toInventoryForecast)
                    }
                }
        }
    }

    private fun calculateParallelism(skuCandidatesProducersMap: Map<Producer<CskuInventoryForecastKey, CskuInventoryForecastVal>, Set<SkuDcCandidate>>): Int {
        val maxSkuCandidateSetCount = skuCandidatesProducersMap.maxOf { it.value.size }

        val parallelism = max(1, producers.size - maxSkuCandidateSetCount / MAX_PARALLEL_SKU_CANDIDATES)

        updateMetrics(skuCandidatesProducersMap, parallelism)

        return parallelism
    }

    private fun updateMetrics(
        skuCandidatesProducersMap:
        Map<Producer<CskuInventoryForecastKey, CskuInventoryForecastVal>, Set<SkuDcCandidate>>,
        parallelism: Int
    ) {
        skuCandidatesMultiGauge.register(
            skuCandidatesProducersMap.map { (producer, candidates) ->
                MultiGauge.Row.of(
                    Tags.of(
                        Tag.of("mode", producer.calculatorMode().name),
                    ),
                    candidates.count(),
                )
            },
            true,
        )
        parallelismGaugeValue.set(parallelism)
    }

    companion object : Logging
}
