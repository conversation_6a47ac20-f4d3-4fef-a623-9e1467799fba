package com.hellofresh.cif.calculator.producer.filter

import com.hellofresh.cif.calculator.models.CalculatorMode
import org.apache.kafka.clients.producer.ProducerRecord

interface RecordFilter<K, V> {

    val calculatorMode: CalculatorMode
    // Returns the records which are not filtered out

    fun load(dcCodes: Set<String>)
    fun filter(records: List<ProducerRecord<K, V>>): List<ProducerRecord<K, V>>
    fun filter(key: K, value: V): Boolean
    fun update(record: ProducerRecord<K, V>)
}
