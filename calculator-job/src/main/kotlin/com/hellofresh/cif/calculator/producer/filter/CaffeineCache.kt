package com.hellofresh.cif.calculator.producer.filter

import com.github.benmanes.caffeine.cache.Caffeine
import com.github.benmanes.caffeine.cache.Scheduler
import com.hellofresh.cif.calculator.models.CalculatorMode
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics
import java.util.concurrent.TimeUnit.HOURS

interface InMemoryCache<in K, V> {
    fun put(key: K, value: V?)
    fun isNotInCache(key: K, value: V?): <PERSON>olean
}

class CaffeineCache<in K, V>(
    meterRegistry: MeterRegistry,
    calculatorMode: CalculatorMode,
    ttlHour: Long,
    cacheSize: Long,
) : InMemoryCache<K, V> {

    private val tags = listOf(Tag.of("mode", calculatorMode.name), Tag.of("name", "memory"))

    private val hitCounter = Counter.builder("filter_hit_counter")
        .description("Record the number of times the record filter was able to filter a record")
        .tags(tags)
        .register(meterRegistry)

    private val missCounter = Counter.builder("filter_miss_counter")
        .description("Record the number of times the record filter was not able to filter a record")
        .tags(tags)
        .register(meterRegistry)

    // TODO: Add alert on the high miss ratio
    // TODO: Alert on caffeine eviction

    private val cache = Caffeine.newBuilder()
        .scheduler(Scheduler.systemScheduler())
        .expireAfterAccess(ttlHour, HOURS)
        .maximumSize(cacheSize)
        .recordStats()
        .build<K, Int>().also {
            CaffeineCacheMetrics.monitor(meterRegistry, it, "db-cache-$calculatorMode")
        }

    override fun put(key: K, value: V?) {
        cache.put(key, serialize(value))
    }

    override fun isNotInCache(key: K, value: V?) =
        (get(key)?.let { it != serialize(value) } != false)
            .also { missed ->
                if (missed) missCounter.increment() else hitCounter.increment()
            }

    private fun get(key: K) = cache.getIfPresent(key)

    internal fun invalidateAll() {
        cache.invalidateAll()
    }

    companion object {
        fun <T> serialize(value: T?) = value?.hashCode()
    }
}
