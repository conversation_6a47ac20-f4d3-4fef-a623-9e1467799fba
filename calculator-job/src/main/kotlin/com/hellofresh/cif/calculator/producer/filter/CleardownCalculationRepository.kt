package com.hellofresh.cif.calculator.producer.filter

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.calculator.models.SupplierSkuPoDueInVal
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.CalculatorMode.PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.calculator.schema.Tables.CALCULATION
import com.hellofresh.cif.calculator.schema.enums.Uom
import com.hellofresh.cif.calculator.schema.enums.Uom.UOM_GAL
import com.hellofresh.cif.calculator.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.calculator.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.calculator.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.calculator.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.calculator.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.calculator.schema.enums.Uom.UOM_UNRECOGNIZED
import com.hellofresh.cif.calculator.schema.enums.Uom.UOM_UNSPECIFIED
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.lib.kafka.serde.objectMapper
import com.hellofresh.cif.models.SkuUOM
import java.math.BigDecimal.ZERO
import org.jooq.Record
import org.jooq.Table

class CleardownCalculationRepository(
    metricsDSLContext: MetricsDSLContext,
    calculatorMode: CalculatorMode
) : BaseCalculationRepository(metricsDSLContext, calculatorMode) {

    init {
        require(calculatorMode.isCleardown()) { "calculator mode should by cleardown $calculatorMode" }
    }

    override fun toCskuInventoryForecastKey(record: Record, table: Table<*>) =
        with(record) {
            CskuInventoryForecastKey(
                cskuId = get(table.field(CALCULATION.CSKU_ID)),
                dcCode = get(table.field(CALCULATION.DC_CODE)),
                date = get(table.field(CALCULATION.DATE)),
            )
        }

    override fun toCskuInventoryForecastValue(record: Record, table: Table<*>) = with(record) {
        CskuInventoryForecastVal(
            expired = get(table.field(CALCULATION.EXPIRED)),
            openingStock = get(table.field(CALCULATION.OPENING_STOCK)),
            present = get(table.field(CALCULATION.PRESENT)),
            actualInbound = get(table.field(CALCULATION.ACTUAL_INBOUND)),
            actualInboundPurchaseOrders = mapToPoExcludeEmpty(get(table.field(CALCULATION.ACTUAL_INBOUND_PO))),
            expectedInbound = get(table.field(CALCULATION.EXPECTED_INBOUND)),
            expectedInboundPurchaseOrders = mapToPoExcludeEmpty(get(table.field(CALCULATION.EXPECTED_INBOUND_PO))),
            demanded = get(table.field(CALCULATION.DEMANDED)),
            closingStock = get(table.field(CALCULATION.CLOSING_STOCK)),
            dailyNeeds = get(table.field(CALCULATION.DAILY_NEEDS)),
            productionWeek = get(table.field(CALCULATION.PRODUCTION_WEEK)),
            productionWeekStartStock = ZERO,
            actualConsumption = get(table.field(CALCULATION.ACTUAL_CONSUMPTION)) ?: ZERO,
            safetyStock = get(table.field(CALCULATION.SAFETYSTOCK)),
            strategy = get(table.field(CALCULATION.STRATEGY)),
            safetyStockNeeds = get(table.field(CALCULATION.SAFETYSTOCK_NEEDS)),
            stagingStock = ZERO,
            storageStock = ZERO,
            stockUpdate = get(table.field(CALCULATION.STOCK_UPDATE)),
            purchaseOrderDueInForSuppliers =
            get(table.field(CALCULATION.PURCHASE_ORDER_DUE_IN_FOR_SUPPLIERS))?.let { jsonb ->
                objectMapper.readValue<List<SupplierSkuPoDueInVal>?>(jsonb.data())?.sortedBy { it.hashCode() }
            },
            maxPurchaseOrderDueIn = get(table.field(CALCULATION.MAX_PURCHASE_ORDER_DUE_IN)),
            netNeeds = get(table.field(CALCULATION.NET_NEEDS)) ?: ZERO,
            unusableInventory =
            // TODO CPS CHECK WHY PRE PROD DOESNT HAVE UNUSABLE COLUMN
            field(table.field(CALCULATION.UNUSABLE_INVENTORY))
                ?.get(this)
                ?.let { jsonb ->
                    objectMapper.readValue<List<ForecastInventory>?>(jsonb.data())?.sortedBy { it.hashCode() }
                },
            uom = mapSkuUom(get(table.field(CALCULATION.UOM))!!),
            expectedInboundTransferOrders = mapToPoExcludeEmpty(
                get(table.field(CALCULATION.EXPECTED_INBOUND_TRANSFER_ORDERS))
            ),
            expectedInboundTransferOrdersQuantity = get(table.field(CALCULATION.EXPECTED_INBOUND_TRANSFER_ORDERS_QUANTITY))
                ?: ZERO,
            expectedOutboundTransferOrders = mapToPoExcludeEmpty(
                get(table.field(CALCULATION.EXPECTED_OUTBOUND_TRANSFER_ORDERS))
            ),
            expectedOutboundTransferOrdersQuantity = get(table.field(CALCULATION.EXPECTED_OUTBOUND_TRANSFER_ORDERS_QUANTITY))
                ?: ZERO,
            actualInboundTransferOrders = mapToPoExcludeEmpty(
                get(table.field(CALCULATION.ACTUAL_INBOUND_TRANSFER_ORDERS))
            ),
            actualInboundTransferOrdersQuantity = get(table.field(CALCULATION.ACTUAL_INBOUND_TRANSFER_ORDERS_QUANTITY))
                ?: ZERO,
        )
    }

    override fun toCskuInventoryForecastDbValue(sourceValue: CskuInventoryForecastVal): CskuInventoryForecastVal =
        sourceValue.copy(
            purchaseOrderDueInForSuppliers = sourceValue.purchaseOrderDueInForSuppliers?.sortedBy { it.hashCode() },
            productionWeekStartStock = ZERO,
            storageStock = ZERO,
            stagingStock = ZERO,
            unusableInventory = if (PRE_PRODUCTION == calculatorMode) {
                null
            } else {
                sourceValue.unusableInventory?.sortedBy { it.hashCode() }
            },
        )
}

fun mapSkuUom(uom: Uom) =
    when (uom) {
        UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
        UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
        UOM_UNIT -> SkuUOM.UOM_UNIT
        UOM_KG -> SkuUOM.UOM_KG
        UOM_LBS -> SkuUOM.UOM_LBS
        UOM_GAL -> SkuUOM.UOM_GAL
        UOM_LITRE -> SkuUOM.UOM_LITRE
        UOM_OZ -> SkuUOM.UOM_OZ
    }

fun createCalculationRepository(metricsDSLContext: MetricsDSLContext) =
    CleardownCalculationRepository(metricsDSLContext, PRODUCTION)

fun createPreProdCalculationRepository(metricsDSLContext: MetricsDSLContext) =
    CleardownCalculationRepository(metricsDSLContext, PRE_PRODUCTION)
