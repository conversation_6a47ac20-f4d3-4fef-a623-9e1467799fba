package com.hellofresh.cif.calculator

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.distributioncenter.DcWeightRepositoryImpl
import com.hellofresh.cif.calculator.producer.filter.RecordFilter
import com.hellofresh.cif.calculator.producer.filter.createCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.createLiveCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.createPreProdCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.createPreProdLiveCalculationRepository
import com.hellofresh.cif.checks.CheckResult
import com.hellofresh.cif.checks.Checks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.lib.KrontabScheduler
import com.hellofresh.cif.lib.MeteredJob
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.dcbalancer.DcWeightLoadBalancer
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.shutdown.shutdownNeeded
import io.ktor.server.response.respond
import io.ktor.server.routing.get
import io.ktor.server.routing.routing
import io.micrometer.core.instrument.MeterRegistry
import java.time.Duration
import java.time.Instant
import java.util.concurrent.ArrayBlockingQueue
import java.util.concurrent.ThreadPoolExecutor
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeUnit.SECONDS
import org.apache.logging.log4j.kotlin.Logging

private const val STATUS_SERVER_HTTP_PORT = 8081
private const val NUMBER_OF_DB_CONNECTIONS = 4

private const val POOL_SIZE = 1
private const val PREPRODUCTION_CLEARDOWN_DCS = "preproduction.cleardown.dcs"

// Single thread running infinitely with backpressure to run in
// the calling thread. We shall always have 1 job running. Also,
// a job should only start once the previous job has finished.
private val jobExecutor = ThreadPoolExecutor(
    POOL_SIZE,
    POOL_SIZE,
    Long.MAX_VALUE,
    TimeUnit.HOURS,
    ArrayBlockingQueue(POOL_SIZE, true),
    ThreadPoolExecutor.CallerRunsPolicy(),
)

@Suppress("UtilityClassWithPublicConstructor")
class Application {
    companion object : Logging {
        @JvmStatic fun main(args: Array<String>) {
            val meterRegistry = createMeterRegistry()
            val metricsDSLContext = DBConfiguration.jooqReadOnlyDslContext(
                NUMBER_OF_DB_CONNECTIONS,
                meterRegistry,
            )

            val cleardownCalculationRepository = createCalculationRepository(metricsDSLContext)
            val preProdCleardownCalculationRepository = createPreProdCalculationRepository(metricsDSLContext)
            val liveInventoryCalculationRepository = createLiveCalculationRepository(metricsDSLContext)
            val preProdLiveInventoryCalculationRepository = createPreProdLiveCalculationRepository(metricsDSLContext)

            val producers = producers(
                meterRegistry,
                cleardownCalculationRepository = cleardownCalculationRepository,
                preProdCleardownCalculationRepository = preProdCleardownCalculationRepository,
                liveInventoryCalculationRepository = liveInventoryCalculationRepository,
                preProdLiveInventoryCalculationRepository = preProdLiveInventoryCalculationRepository,
            )

            val dcWeightLoadBalancer = dcLoadBalancer(metricsDSLContext, producers.map { it.recordFilter }.toList())

            runStatusServer(meterRegistry, STATUS_SERVER_HTTP_PORT, initStartUpCheck(dcWeightLoadBalancer))

            val preproductionCleardownDcs = ConfigurationLoader.getStringOrFail(
                PREPRODUCTION_CLEARDOWN_DCS,
            )
                .split(",").toSet()

            val dcCodes = dcWeightLoadBalancer.loadDcs()
            logger.info("Booting Calculator job with dcs $dcCodes")

            val statsigFeatureFlagClient = statsigFeatureFlagClient()

            val skuInputDataService = skuInputDataService(
                metricsDSLContext,
                statsigFeatureFlagClient,
                meterRegistry,
                cleardownCalculationRepository = cleardownCalculationRepository,
                preProdCleardownCalculationRepository = preProdCleardownCalculationRepository,
                liveInventoryCalculationRepository = liveInventoryCalculationRepository,
                preProdLiveInventoryCalculationRepository = preProdLiveInventoryCalculationRepository,
            )

            val calculator = CalculatorService(
                skuInputDataService = skuInputDataService,
                producers = producers,
                calculatorClient = CalculatorClient(statsigFeatureFlagClient),
                meterRegistry = meterRegistry,
                preproductionCleardownDcs,
                podName(),
            )
            val calculatorJob = MeteredJob(meterRegistry, "calculator-job") {
                val runDcCodes = dcWeightLoadBalancer.loadDcs()
                calculator.run(runDcCodes)
            }

            shutdownNeeded {
                KrontabScheduler(
                    period = jobTimePeriod().toSeconds().toInt(),
                    timeUnit = SECONDS,
                    executor = jobExecutor,
                )
            }.schedule { calculatorJob.execute() }
        }
    }
}

private fun dcLoadBalancer(
    metricsDSLContext: MetricsDSLContext,
    recordsFilters: List<RecordFilter<CskuInventoryForecastKey, CskuInventoryForecastVal>>,
): DcWeightLoadBalancer =
    DcWeightLoadBalancer(
        replicaCount(),
        podName(),
        DcWeightRepositoryImpl(metricsDSLContext),
        DcWeightLoadBalancer.Companion.Configuration(includedDcs(), excludedDcs()),
        recordsFilters.map { it::load }.toList(),
    )

private fun preproductionCleardownDcs() = ConfigurationLoader.getStringOrFail(
    PREPRODUCTION_CLEARDOWN_DCS
).split(",").toSet()

private fun includedDcs() = ConfigurationLoader.getSet("HF_DCS_INCLUDED")
private fun excludedDcs() = ConfigurationLoader.getSet("HF_DCS_EXCLUDED")

private fun replicaCount() = ConfigurationLoader.getIntegerOrFail("HF_REPLICA_COUNT")

private fun podName() = ConfigurationLoader.getStringOrFail("HF_POD_NAME")

private fun statsigFeatureFlagClient() = StatsigFactory.build(
    ::shutdownHook,
    sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
    userId = ConfigurationLoader.getStringOrFail("application.name"),
    isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
    hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
)

private fun jobTimePeriod(): Duration = Duration.parse(ConfigurationLoader.getStringOrFail("job.time_period"))

fun runStatusServer(meterRegistry: MeterRegistry, port: Int, startUpCheck: Checks) =
    StatusServer.run(
        meterRegistry = meterRegistry,
        port = port,
        startUpCheck = startUpCheck,
    ) {
        routing {
            get("state") {
                val params = call.request.queryParameters
                val status = CacheQueryRouter.get(
                    params["mode"],
                    params["skuId"],
                    params["dcCode"],
                    params["date"],
                )

                call.respond(status.code, status.body)
            }
        }
    }

private fun initStartUpCheck(dcWeightLoadBalancer: DcWeightLoadBalancer) = run {
    val start = Instant.now()
    val maxDuration = Duration.parse(ConfigurationLoader.getStringOrFail("startupcheck.loadcs.maxduration"))
    StartUpChecks.add {
        CheckResult(
            "Calc Cache Load",
            dcWeightLoadBalancer.loadedDcCodes.isNotEmpty() || Duration.between(start, Instant.now()) > maxDuration,
        )
    }
}
