package com.hellofresh.cif.calculator.producer

import com.hellofresh.cif.calculator.producer.filter.RecordFilter
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.lib.recordSuspended
import com.hellofresh.topic.Topic
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Timer
import java.util.concurrent.ConcurrentHashMap
import java.util.concurrent.TimeUnit.NANOSECONDS
import java.util.concurrent.atomic.AtomicLong
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.catch
import kotlinx.coroutines.flow.collect
import kotlinx.coroutines.flow.filter
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.flow.onEach
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.logging.log4j.kotlin.Logging

typealias KProducer<K, V> = org.apache.kafka.clients.producer.Producer<K, V>

/**
 * Producer that accepts batches of data and produces Kafka records.
 */
class BatchProducer<K, V>(
    meterRegistry: MeterRegistry,
    private val topic: Topic<K, V>,
    val recordFilter: RecordFilter<K, V>,
    producerSupplier: () -> KProducer<K, V> = {
        KafkaProducer(kafkaProducerConfig(), topic.keySerde.serializer(), topic.valSerde.serializer())
    },
) : Producer<K, V>, AutoCloseable {

    private val calculatorMode by recordFilter::calculatorMode

    /**
     * Configured number of threads that [BatchProducer] can use to concurrently
     * produce records.
     *
     * @return configured number of threads. If not configured, defaults to 1.
     */
    private val numThreads = ConfigurationLoader.getIntegerOrDefault("batch.producer.num_threads", 1)

    /**
     * [Producer] instance used to produce Kafka records.
     *
     * If no implementation is specified through the producerSupplier, the default
     * supplier creates a [KafkaProducer] instance.
     */
    private val producer = producerSupplier()

    private val taskDurationBuilder = Timer.builder("task_duration")
        .description("Record the elapsed time of the execution of the task.")

    private val flushDuration = taskDurationBuilder
        .tags("name", "flush")
        .register(meterRegistry)

    private val producerTaskDuration = taskDurationBuilder
        .tags("name", "producer")
        .register(meterRegistry)

    private val filterTaskDuration = taskDurationBuilder
        .tags("name", "filter")
        .register(meterRegistry)

    private val successMetrics = Counter.builder("producer_success")
        .description("count of records successfully written by the producer.")
        .register(meterRegistry)

    private val failureMetrics = Counter.builder("producer_failure")
        .description("count of records failed to be written by the producer.")
        .register(meterRegistry)

    /**
     * Takes a batch of key/value pairs, splits it into a configured amount of
     * buckets of Kafka records, and finally produces all records.
     *
     * @param batch - a map containing key/value pairs that will become Kafka
     * records.
     */

    override suspend fun <Source> produce(
        sources: List<Source>,
        mapSource: (Source) -> Pair<K, V>
    ) {
        logger.info("$calculatorMode size: ${sources.size}")
        val status = Status()

        val startTime = System.nanoTime()

        flow {
            recordSuspended(filterTaskDuration) {
                sources.forEach { source ->
                    val entry = mapSource(source)
                    checkNotNull(entry.second) { "Null calculations are not supported." }
                    emit(entry)
                }
            }
        }
            .filter { (k, v) -> recordFilter.filter(k, v) }
            .onEach { (k, v) -> send(k, v, status) }
            .catch {
                logger.error("Error while producing records", it)
            }.flowOn(Dispatchers.IO.limitedParallelism(numThreads))
            .collect()

        runCatching { flushDuration.record(producer::flush) }
            .onFailure { th -> logger.error("Exception when flushing to kafka", th) }

        logger.info("Success:${status.success.toDouble()}")
        logger.info("failure:${status.failure.toDouble()}")

        successMetrics.increment(status.success.toDouble())
        failureMetrics.increment(status.failure.toDouble())
        producerTaskDuration.record(System.nanoTime() - startTime, NANOSECONDS)
        if (status.failure != 0L) logger.error(status.message())
        logger.info("Successfully produced ${status.success} records out of ${status.total} for $calculatorMode")
    }

    private fun send(key: K, value: V, status: Status) =
        runCatching {
            val record = ProducerRecord(topic.name, key, value)
            producer.send(record) { _, th ->
                status.recordOperation()
                if (th == null) recordFilter.update(record) else status.recordFailure(th)
            }
        }.onFailure { status.recordFailure(it) }

    class Status {
        private val _total = AtomicLong(0)
        val total: Long get() = _total.get()
        private val _failure = AtomicLong(0)
        val failure: Long get() = _failure.get()
        val success: Long get() = total - failure
        private val causes = ConcurrentHashMap<Key, AtomicLong>()

        fun message(): String {
            val sb = StringBuilder()
            sb.appendLine("Fail to produce $failure records because:")
            causes.toList()
                .sortedBy { (_, v) -> -1 * v.get() }
                .forEach { (k, v) ->
                    sb.appendLine("${k.className}, repeated ${v.get()} times with cause:${k.message}.")
                }

            return sb.toString()
        }

        fun recordOperation() {
            _total.incrementAndGet()
        }

        fun recordFailure(th: Throwable) {
            _failure.incrementAndGet()
            causes.compute(Key(th)) { _, count ->
                count?.apply { incrementAndGet() } ?: AtomicLong(1)
            }
        }

        // Throwable does not have equals function we need
        private data class Key(val className: String, val message: String) {
            constructor(th: Throwable) : this(th.javaClass.simpleName, th.message ?: "No  Message")
        }
    }

    override fun calculatorMode() = calculatorMode

    override fun close() {
        producer.close()
    }

    companion object : Logging
}

private fun kafkaProducerConfig() = ConfigurationLoader.loadKafkaProducerConfigurations() +
    mapOf(
        ProducerConfig.COMPRESSION_TYPE_CONFIG to ConfigurationLoader.getStringOrFail(ProducerConfig.COMPRESSION_TYPE_CONFIG),
        ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION to
            ConfigurationLoader.getStringOrFail(ProducerConfig.MAX_IN_FLIGHT_REQUESTS_PER_CONNECTION),
        ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG to ConfigurationLoader.getStringOrFail(ProducerConfig.ENABLE_IDEMPOTENCE_CONFIG),
        ProducerConfig.ACKS_CONFIG to ConfigurationLoader.getStringOrFail(ProducerConfig.ACKS_CONFIG),
    )
