package com.hellofresh.cif.calculator

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.producer.filter.QueryableCache
import io.ktor.http.HttpStatusCode
import java.time.LocalDate
import java.util.UUID

object CacheQueryRouter {
    private val objectMapper = ObjectMapper().findAndRegisterModules().setPropertyNamingStrategy(SnakeCaseStrategy())
    lateinit var prodCache: QueryableCache<CskuInventoryForecastKey, CskuInventoryForecastVal?>
    lateinit var preProdCache: QueryableCache<CskuInventoryForecastKey, CskuInventoryForecastVal?>

    fun get(mode: String?, skuId: String?, dcCode: String?, date: String?): Status = checkNull(mode, "mode")
        ?: checkNull(skuId, "skuId")
        ?: checkNull(dcCode, "dcCode")
        ?: checkNull(date, "date")
        ?: get(mode!!, CskuInventoryForecastKey(UUID.fromString(skuId!!), dcCode!!, LocalDate.parse(date)))

    private fun checkNull(data: String?, name: String) = if (data == null) NullField(name) else null

    private fun get(mode: String, k: CskuInventoryForecastKey): Status = when {
        !this::prodCache.isInitialized || !this::preProdCache.isInitialized -> NotInitialized()
        mode == "prod" -> Success(objectMapper.writeValueAsString(prodCache.query(k)))
        mode == "preprod" -> Success(objectMapper.writeValueAsString(preProdCache.query(k)))
        else -> BadRequest("Supported modes: prod/preprod")
    }
}

sealed class Status(val body: String, val code: HttpStatusCode)
class NullField(val name: String) : Status("Query param:$name is not given", HttpStatusCode.BadRequest)
class Success(body: String) : Status(body, HttpStatusCode.OK)
class BadRequest(body: String) : Status(body, HttpStatusCode.BadRequest)
class NotInitialized : Status(
    "State stores are not initialised yet. Try again later",
    HttpStatusCode.InternalServerError
)
