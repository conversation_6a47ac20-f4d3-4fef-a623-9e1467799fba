package com.hellofresh.cif.calculator

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.calculator.models.SupplierSkuPoDueInVal
import com.hellofresh.cif.calculator.models.DayCalculationResult

object CskuInventoryForecastMapper {
    fun toInventoryForecastMap(
        dayCalculations: List<DayCalculationResult>
    ): Map<CskuInventoryForecastKey, CskuInventoryForecastVal> =
        dayCalculations.associate {
            toInventoryForecastKey(it) to toInventoryForecastValue(it)
        }

    fun toInventoryForecast(dayCalculation: DayCalculationResult) =
        toInventoryForecastKey(dayCalculation) to toInventoryForecastValue(dayCalculation)

    fun toInventoryForecastKey(dayCalculation: DayCalculationResult) =
        with(dayCalculation) {
            CskuInventoryForecastKey(cskuId, dcCode, date)
        }

    fun toInventoryForecastValue(
        dayCalculation: DayCalculationResult
    ): CskuInventoryForecastVal =
        with(dayCalculation) {
            CskuInventoryForecastVal(
                expired = unusable.getValue(),
                openingStock = openingStock.getValue(),
                closingStock = closingStock.getValue(),
                present = present.getValue(),
                actualInbound = actualInbound.getValue(),
                actualInboundPurchaseOrders = actualInboundPurchaseOrders,
                expectedInbound = expectedInbound.getValue(),
                expectedInboundPurchaseOrders = expectedInboundPurchaseOrders,
                demanded = demanded.getValue(),
                dailyNeeds = dailyNeeds.getValue(),
                productionWeekStartStock = productionWeekStartStock.getValue(),
                productionWeek = productionWeek,
                actualConsumption = actualConsumption.getValue(),
                safetyStock = safetyStock?.getValue(),
                strategy = strategy,
                safetyStockNeeds = safetyStockNeeds?.getValue(),
                stagingStock = stagingStock.getValue(),
                storageStock = storageStock.getValue(),
                stockUpdate = stockUpdate?.getValue(),
                purchaseOrderDueInForSuppliers = purchaseOrderDueInForSuppliers?.map { supplierSkuPoDueIn ->
                    SupplierSkuPoDueInVal(
                        supplierId = supplierSkuPoDueIn.supplierId,
                        poDueIn = supplierSkuPoDueIn.poDueIn,
                    )
                },
                maxPurchaseOrderDueIn = maxPurchaseOrderDueIn,
                netNeeds = netNeeds.getValue(),
                unusableInventory = unusableInventory?.map { inventory ->
                    ForecastInventory(
                        inventory.qty.getValue(),
                        inventory.expiryDate,
                        inventory.locationType,
                    )
                },
                uom = uom,
                expectedInboundTransferOrders = expectedInboundTransferOrders,
                expectedInboundTransferOrdersQuantity = expectedInboundTransferOrdersQuantity?.getValue(),
                expectedOutboundTransferOrders = expectedOutboundTransferOrders,
                expectedOutboundTransferOrdersQuantity = expectedOutboundTransferOrdersQuantity?.getValue(),
                actualInboundTransferOrders = actualInboundTransferOrders,
                actualInboundTransferOrdersQuantity = actualInboundTransferOrdersQuantity?.getValue(),
            )
        }
}
