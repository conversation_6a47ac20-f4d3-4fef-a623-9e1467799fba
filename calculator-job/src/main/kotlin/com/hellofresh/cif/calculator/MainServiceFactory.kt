package com.hellofresh.cif.calculator

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.calculator.models.inventoryCalculationsTopic
import com.hellofresh.calculator.models.liveInventoryCalculationsTopic
import com.hellofresh.calculator.models.liveInventoryPreProdCalculationsTopic
import com.hellofresh.calculator.models.preProdCalculationsTopic
import com.hellofresh.cif.calculator.producer.BatchProducer
import com.hellofresh.cif.calculator.producer.filter.BaseCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.CleardownCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.DbRecordFilter
import com.hellofresh.cif.calculator.producer.filter.LiveInventoryCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.RecordFilter
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.demand.DemandRepositoryImpl
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryActivityRepositoryImpl
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.LiveInventoryRepositoryImpl
import com.hellofresh.cif.inventory.StockUpdateRepositoryImpl
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepositoryImpl
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.transferorder.db.TransferOrderRepositoryImpl
import com.hellofresh.topic.Topic
import io.micrometer.core.instrument.MeterRegistry

@Suppress("LongParameterList")
internal fun skuInputDataService(
    metricsDSLContext: MetricsDSLContext,
    statigFeatureFlagClient: StatsigFeatureFlagClient,
    meterRegistry: HelloFreshMeterRegistry,
    cleardownCalculationRepository: CleardownCalculationRepository,
    preProdCleardownCalculationRepository: CleardownCalculationRepository,
    liveInventoryCalculationRepository: LiveInventoryCalculationRepository,
    preProdLiveInventoryCalculationRepository: LiveInventoryCalculationRepository
): SkuInputDataService {
    val dcConfigService = DcConfigService(meterRegistry, DcRepositoryImpl(metricsDSLContext))
    val inventoryService = InventoryService(
        InventoryRepositoryImpl(metricsDSLContext),
        LiveInventoryRepositoryImpl(metricsDSLContext),
        InventoryActivityRepositoryImpl(metricsDSLContext),
        statigFeatureFlagClient,
    )
    return SkuInputDataService(
        SkuInputDataRepositoryImpl(metricsDSLContext, dcConfigService),
        inventoryService,
        PurchaseOrderRepositoryImpl(
            metricsDSLContext,
            DcConfigService(meterRegistry),
            statigFeatureFlagClient,
        ),
        TransferOrderRepositoryImpl(metricsDSLContext, dcConfigService),
        DemandRepositoryImpl(metricsDSLContext, statigFeatureFlagClient),
        StockUpdateService(inventoryService, StockUpdateRepositoryImpl(metricsDSLContext)),
        statigFeatureFlagClient,
        SafetyStockRepository(metricsDSLContext),
        listOf(
            cleardownCalculationRepository,
            preProdCleardownCalculationRepository,
            liveInventoryCalculationRepository,
            preProdLiveInventoryCalculationRepository,
        ).associateBy { it.calculatorMode },
    )
}

internal fun producers(
    meterRegistry: MeterRegistry,
    cleardownCalculationRepository: CleardownCalculationRepository,
    preProdCleardownCalculationRepository: CleardownCalculationRepository,
    liveInventoryCalculationRepository: LiveInventoryCalculationRepository,
    preProdLiveInventoryCalculationRepository: LiveInventoryCalculationRepository,
): List<BatchProducer<CskuInventoryForecastKey, CskuInventoryForecastVal>> =
    listOf(
        producer(
            meterRegistry,
            inventoryCalculationsTopic,
            recordFilter(meterRegistry, cleardownCalculationRepository),
        ),
        producer(
            meterRegistry,
            preProdCalculationsTopic,
            recordFilter(meterRegistry, preProdCleardownCalculationRepository),
        ),
        producer(
            meterRegistry,
            liveInventoryCalculationsTopic,
            recordFilter(meterRegistry, liveInventoryCalculationRepository),
        ),
        producer(
            meterRegistry,
            liveInventoryPreProdCalculationsTopic,
            recordFilter(meterRegistry, preProdLiveInventoryCalculationRepository),
        ),
    )

private fun recordFilter(
    meterRegistry: MeterRegistry,
    baseCalculationRepository: BaseCalculationRepository
) = DbRecordFilter(
    meterRegistry,
    baseCalculationRepository,
)

private fun producer(
    meterRegistry: MeterRegistry,
    topic: Topic<CskuInventoryForecastKey, CskuInventoryForecastVal>,
    recordsFilter: RecordFilter<CskuInventoryForecastKey, CskuInventoryForecastVal>,
) = shutdownNeeded { BatchProducer(meterRegistry, topic, recordsFilter) }
