{{- range $key, $value := .Values.statefulsets }}
{{- if or ($value.minAvailable) ($value.maxUnavailable) }}
{{- if lt $value.minAvailable $value.replicaCount }}
---
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ template "cif-calculator-job.fullname" $ }}-{{ $key }}
  labels:
    {{- include "cif-calculator-job.labels" $ | nindent 4 }}
    app: {{ template "cif-calculator-job.name" $ }}
spec:
  {{- with $value.minAvailable }}
  minAvailable: {{ . }}
  {{- end }}
  {{- with $value.maxUnavailable }}
  maxUnavailable: {{ . }}
  {{- end }}
  selector:
    matchLabels:
      {{- include "cif-calculator-job.selectorLabels" $ | nindent 6 }}
      app: {{ template "cif-calculator-job.name" $ }}-{{ $key }}
{{- end }}
{{- end }}
{{- end }}
