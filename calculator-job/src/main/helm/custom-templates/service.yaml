{{- range $key, $value := .Values.services }}
{{- if $value.enabled }}
---
apiVersion: v1
kind: Service
metadata:
  name: {{ template "<CHARTNAME>.fullname" $ }}-{{ $key }}-k8s
  labels:
    {{- include "<CHARTNAME>.labels" $ | nindent 4 }}
    app: {{ template "<CHARTNAME>.name" $ }}-{{ $key }}
  {{- with $value.annotations }}
  annotations: {{- toYaml . | nindent 4 }}
  {{- end }}
spec:
  type: {{ $value.type }}
  ports:
  {{- if $value.ports }}
  {{- range $label,$port := $value.ports }}
  - port: {{ $port }}
    targetPort: {{ $label }}
    protocol: TCP
    name: {{ $label }}
  {{- end }}
  {{- else }}
  - port: 80
    targetPort: http
    protocol: TCP
    name: http
  {{- end }}
  clusterIP: None
  selector:
    {{- include "<CHARTNAME>.selectorLabels" $ | nindent 4 }}
    app: {{ template "<CHARTNAME>.name" $ }}-{{ $key }}
{{- end }}
{{- end }}
