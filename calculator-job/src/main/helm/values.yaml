---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
# Deployment experiment, see build.gradle.kts
tag: 'latest'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

statefulsets:
  app:
    replicaCount: 9
    minAvailable: 0
    containerPorts:
      http: 8081
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    resources:
      requests:
        memory: '2Gi'
        cpu: '1.5'
      limits:
        memory: '2Gi'
        cpu: '1.5'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    podAnnotations: { }
    env:
      HF_INVENTORY_DB_HOST: 'vault:@tier@/key-value/data/inventory#DB_REPLICA_HOST'
      HF_INVENTORY_READONLY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#READONLY_DB_PASSWORD'
      HF_INVENTORY_READONLY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#READONLY_DB_USERNAME'
      HF_AIVEN_PASSWORD: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#password'
      HF_STATSIG_SDK_KEY: 'vault:@tier@/key-value/data/misc#STATSIG_SDK_KEY'
      HF_DCS_EXCLUDED: "NJ"
    hpa:
      enabled: false
    spotInstance:
      preferred: true
    startupProbe:
      httpGet:
        path: /startup
        port: 8081
      initialDelaySeconds: 10
      timeoutSeconds: 3
      failureThreshold: 30
      periodSeconds: 5
    livenessProbe:
      httpGet:
        path: /health
        port: 8081
      initialDelaySeconds: 60
      periodSeconds: 15
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3

  app-us-nj:
      replicaCount: 1
      minAvailable: 0
      containerPorts:
          http: 8081
      repository: '@dockerRepository@'
      pullPolicy: IfNotPresent
      resources:
          requests:
              memory: '4Gi'
              cpu: '1'
          limits:
              memory: '4Gi'
              cpu: '1'
      nodeSelector: { }
      tolerations: [ ]
      affinity: { }
      podAnnotations: { }
      env:
          HF_INVENTORY_DB_HOST: 'vault:@tier@/key-value/data/inventory#DB_REPLICA_HOST'
          HF_INVENTORY_READONLY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#READONLY_DB_PASSWORD'
          HF_INVENTORY_READONLY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#READONLY_DB_USERNAME'
          HF_AIVEN_PASSWORD: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#password'
          HF_STATSIG_SDK_KEY: 'vault:@tier@/key-value/data/misc#STATSIG_SDK_KEY'
          HF_DCS_INCLUDED: "NJ"
      hpa:
          enabled: false
      spotInstance:
          preferred: true
      startupProbe:
          httpGet:
              path: /startup
              port: 8081
          initialDelaySeconds: 10
          timeoutSeconds: 3
          failureThreshold: 30
          periodSeconds: 5
      livenessProbe:
          httpGet:
              path: /health
              port: 8081
          initialDelaySeconds: 60
          periodSeconds: 15
          timeoutSeconds: 3
          successThreshold: 1
          failureThreshold: 3

services:
  app:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/prometheus'
    enabled: true
    type: ClusterIP
    ports:
      http: 80
  app-us-nj:
    enablePrometheus: true
    metricPortName: 'http'
    metricPath: '/prometheus'
    enabled: true
    type: ClusterIP
    ports:
      http: 80


configMap:
  HF_TIER: '@tier@'
  SLACK_ALERT_CHANNEL: '@slackAlertChannel@-@tier@'
  SLACK_WEBHOOK: 'vault:common/key-value/data/misc#SLACK_URL'

