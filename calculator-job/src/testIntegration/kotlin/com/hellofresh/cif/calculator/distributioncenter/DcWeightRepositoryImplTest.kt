package com.hellofresh.cif.calculator.distributioncenter

import com.hellofresh.cif.calculator.TestPrepare
import com.hellofresh.cif.calculator.schema.Tables
import com.hellofresh.cif.calculator.schema.tables.records.CalculationRecord
import com.hellofresh.cif.calculator.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.time.DayOfWeek
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test

internal class DcWeightRepositoryImplTest : TestPrepare() {

    @Test
    fun `fetch dc return just enabled dcs`() {
        val enabledDcs = (0..5).map {
            createDcRecord(UUID.randomUUID().toString(), true)
        }.also {
            dsl.batchInsert(it).execute()
        }

        val disabledDcs = (0..2).map {
            createDcRecord(UUID.randomUUID().toString(), false)
        }.also {
            dsl.batchInsert(it).execute()
        }
        refreshDcConfigWeightView()

        val fetchDcsWithWeight = runBlocking { DcWeightRepositoryImpl(dsl).fetchDcsWithWeight() }

        assertEquals(enabledDcs.map { it.dcCode }, fetchDcsWithWeight.map { it.dcCode })
        fetchDcsWithWeight.forEach { assertEquals(0, it.weight) }

        with(fetchDcsWithWeight.map { it.dcCode }.toSet()) {
            disabledDcs.forEach { assertTrue(!contains(it.dcCode)) }
        }
    }

    @Test
    fun `fetch dc return dcs with weight using calculations`() {
        val dcs = (0..5L).associate {
            createDcRecord(UUID.randomUUID().toString(), true) to it + 1L
        }.also {
            dsl.batchInsert(it.keys).execute()
            it.forEach { (dcRecord, count) ->
                (0 until count).map {
                    insertCalculation(UUID.randomUUID(), dcRecord.dcCode)
                }
            }
        }

        refreshDcConfigWeightView()

        val fetchDcsWithWeight = runBlocking { DcWeightRepositoryImpl(dsl).fetchDcsWithWeight() }

        dcs.forEach { (dcRecord, count) ->
            with(fetchDcsWithWeight.first { it.dcCode == dcRecord.dcCode }) {
                assertEquals(count, this.weight)
            }
        }
    }

    private fun insertCalculation(skuId: UUID, dcCode: String) {
        dsl.batchInsert(
            CalculationRecord()
                .apply {
                    cskuId = skuId
                    this.dcCode = dcCode
                    date = LocalDate.now(UTC).plusWeeks(1)
                    productionWeek = DcWeek(date, MONDAY).toString()
                },
        ).execute()
    }

    private fun createDcRecord(dcCode: String, enabled: Boolean) =
        DcConfigRecord(
            dcCode, "dach", DayOfWeek.TUESDAY.name, DayOfWeek.TUESDAY.name, "UTC", enabled,
            LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null, LocalTime.now(),
            arrayOf("brand1", "brand2"),
        )

    private fun refreshDcConfigWeightView() = dsl.query(
        "refresh materialized view ${Tables.DC_CONFIG_WEIGHT_VIEW.name}",
    ).execute()
}
