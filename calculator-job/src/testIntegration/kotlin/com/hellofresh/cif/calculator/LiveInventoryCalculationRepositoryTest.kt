package com.hellofresh.cif.calculator

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.producer.filter.LiveInventoryCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.SkuDc
import com.hellofresh.cif.calculator.producer.filter.createLiveCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.createPreProdLiveCalculationRepository
import com.hellofresh.cif.calculator.schema.Tables
import com.hellofresh.cif.calculator.schema.enums.Uom
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuUOM.UOM_LBS
import com.hellofresh.cif.models.SkuUOM.UOM_OZ
import java.math.BigDecimal
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class LiveInventoryCalculationRepositoryTest : TestPrepare() {
    private val dcCodeVE = "VE"
    private lateinit var liveInventoryCalculationProdRepository: LiveInventoryCalculationRepository
    private lateinit var liveInventoryCalculationPreProdRepository: LiveInventoryCalculationRepository

    @BeforeEach
    fun init() {
        liveInventoryCalculationProdRepository = createLiveCalculationRepository(dsl)
        liveInventoryCalculationPreProdRepository = createPreProdLiveCalculationRepository(dsl)
    }

    @Test
    fun `should fill cache from live inventory prod calculations having empty POs`() {
        // given
        val key = CskuInventoryForecastKey(UUID.randomUUID(), dcCodeVE, LocalDate.now())
        val value = CskuInventoryForecastVal(
            ZERO, ZERO, ZERO, ZERO, emptySet(), ZERO, emptySet(), ZERO, ZERO, ZERO,
            ZERO, "2023-W11", ZERO, ZERO, strategy = defaultTestStrategy, ZERO, ZERO, netNeeds = TEN, uom = UOM_LBS,
            expectedInboundTransferOrdersQuantity = ZERO,
            expectedOutboundTransferOrdersQuantity = ZERO, actualInboundTransferOrdersQuantity = ZERO
        )
        val calculations = mapOf(key to value)
        insertLiveInventoryCalculation(calculations)
        insertLiveInventoryPreProdCalculation(calculations)

        // when
        val liveResults = runBlocking { liveInventoryCalculationProdRepository.load(setOf(dcCodeVE), 1).toList() }
        val livePreProd = runBlocking { liveInventoryCalculationPreProdRepository.load(setOf(dcCodeVE), 1).toList() }

        // then
        assertEquals(calculations.toMap(), liveResults.toMap())
        assertEquals(calculations.toMap(), livePreProd.toMap())
    }

    @Test
    fun `should fetch sku and dcs with calculations`() {
        // given
        val key1 = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())
        val value1 = CskuInventoryForecastVal(
            ZERO, ZERO, ZERO, ZERO, emptySet(), ZERO, emptySet(), ZERO, ZERO, ZERO,
            ZERO, "2023-W11", ZERO, ZERO, strategy = defaultTestStrategy, ZERO, ZERO, netNeeds = TEN, uom = UOM_OZ,
        )
        val key2 = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())
        val value2 = CskuInventoryForecastVal(
            ZERO, ZERO, BigDecimal.ONE, ZERO, emptySet(), ZERO, emptySet(), ZERO, ZERO, ZERO,
            ZERO, "2023-W11", ZERO, ZERO, strategy = defaultTestStrategy, ZERO, ZERO, netNeeds = TEN, uom = UOM_OZ,
        )
        val key3 = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())
        val value3 = CskuInventoryForecastVal(
            ZERO, ZERO, ZERO, ZERO, emptySet(), ZERO, emptySet(), BigDecimal.ONE, ZERO, ZERO,
            ZERO, "2023-W11", ZERO, ZERO, strategy = defaultTestStrategy, ZERO, ZERO, netNeeds = TEN, uom = UOM_OZ,
        )
        val calculations = mapOf(key1 to value1, key2 to value2, key3 to value3)
        insertLiveInventoryCalculation(calculations)
        insertLiveInventoryPreProdCalculation(calculations)

        // when
        val calculationResults =
            runBlocking {
                liveInventoryCalculationProdRepository.fetchSkuDcWithCalculations(
                    DateRange.oneDay(key1.date),
                    setOf(key1.dcCode)
                )
            }
        val preProdCalculationsResults =
            runBlocking {
                liveInventoryCalculationPreProdRepository.fetchSkuDcWithCalculations(
                    DateRange.oneDay(key1.date),
                    setOf(key1.dcCode)
                )
            }

        // then
        val expected = setOf(SkuDc(key2.cskuId, key2.dcCode), SkuDc(key3.cskuId, key3.dcCode))
        assertEquals(expected, calculationResults)
        assertEquals(expected, preProdCalculationsResults)
    }

    @Test
    fun `should fetch sku and dcs with calculations for given dcs and dates`() {
        // given

        val value = CskuInventoryForecastVal(
            ZERO, ZERO, BigDecimal.ONE, ZERO, emptySet(), ZERO, emptySet(), ZERO, ZERO, ZERO,
            ZERO, "2023-W11", ZERO, ZERO, strategy = defaultTestStrategy, ZERO, ZERO, netNeeds = TEN, uom = UOM_OZ,
        )
        val key1 = CskuInventoryForecastKey(UUID.randomUUID(), "V1", LocalDate.now().minusWeeks(1))
        val key2 = CskuInventoryForecastKey(UUID.randomUUID(), "V2", LocalDate.now())
        val key3 = CskuInventoryForecastKey(UUID.randomUUID(), "V3", LocalDate.now())

        val calculations = mapOf(key1 to value, key2 to value, key3 to value)
        insertLiveInventoryCalculation(calculations)
        insertLiveInventoryPreProdCalculation(calculations)

        // when
        val calculationResults = runBlocking {
            liveInventoryCalculationProdRepository.fetchSkuDcWithCalculations(
                DateRange(key2.date.minusDays(1), key2.date.plusDays(2)),
                setOf(key1.dcCode, key3.dcCode),
            )
        }
        val preProdCalculationsResults = runBlocking {
            liveInventoryCalculationPreProdRepository.fetchSkuDcWithCalculations(
                DateRange(key2.date.minusDays(1), key2.date.plusDays(2)),
                setOf(key1.dcCode, key3.dcCode),
            )
        }

        // then
        val expected = setOf(SkuDc(key3.cskuId, key3.dcCode))
        assertEquals(expected, calculationResults)
        assertEquals(expected, preProdCalculationsResults)
    }

    private fun insertLiveInventoryCalculation(
        calculations: Map<CskuInventoryForecastKey, CskuInventoryForecastVal>
    ) {
        calculations.forEach { (calcKey, calcVal) ->
            with(Tables.LIVE_INVENTORY_CALCULATION) {
                dsl.insertInto(this).columns(
                    CSKU_ID,
                    DATE,
                    DC_CODE,
                    EXPIRED,
                    PRESENT,
                    ACTUAL_INBOUND,
                    ACTUAL_INBOUND_PO,
                    EXPECTED_INBOUND,
                    EXPECTED_INBOUND_PO,
                    CLOSING_STOCK,
                    DAILY_NEEDS,
                    PRODUCTION_WEEK,
                    DEMANDED,
                    OPENING_STOCK,
                    ACTUAL_CONSUMPTION,
                    SAFETYSTOCK,
                    SAFETYSTOCK_NEEDS,
                    STAGING_STOCK,
                    STORAGE_STOCK,
                    NET_NEEDS,
                    UOM,
                ).values(
                    calcKey.cskuId,
                    calcKey.date,
                    calcKey.dcCode,
                    calcVal.expired,
                    calcVal.present,
                    calcVal.actualInbound,
                    calcVal.actualInboundPurchaseOrders?.joinToString(","),
                    calcVal.expectedInbound,
                    calcVal.expectedInboundPurchaseOrders?.joinToString(","),
                    calcVal.closingStock,
                    calcVal.dailyNeeds,
                    calcVal.productionWeek,
                    calcVal.demanded,
                    calcVal.openingStock,
                    calcVal.actualConsumption,
                    calcVal.safetyStock,
                    calcVal.safetyStockNeeds,
                    calcVal.stagingStock,
                    calcVal.storageStock,
                    calcVal.netNeeds,
                    Uom.valueOf(calcVal.uom.name),
                ).execute()
            }
        }
    }

    private fun insertLiveInventoryPreProdCalculation(
        calculations: Map<CskuInventoryForecastKey, CskuInventoryForecastVal>
    ) {
        calculations.forEach { (calcKey, calcVal) ->
            with(Tables.LIVE_INVENTORY_PRE_PRODUCTION_CALCULATION) {
                dsl.insertInto(this).columns(
                    CSKU_ID,
                    DATE,
                    DC_CODE,
                    EXPIRED,
                    PRESENT,
                    ACTUAL_INBOUND,
                    ACTUAL_INBOUND_PO,
                    EXPECTED_INBOUND,
                    EXPECTED_INBOUND_PO,
                    CLOSING_STOCK,
                    DAILY_NEEDS,
                    PRODUCTION_WEEK,
                    DEMANDED,
                    OPENING_STOCK,
                    ACTUAL_CONSUMPTION,
                    SAFETYSTOCK,
                    SAFETYSTOCK_NEEDS,
                    STAGING_STOCK,
                    STORAGE_STOCK,
                    NET_NEEDS,
                    UOM,
                ).values(
                    calcKey.cskuId,
                    calcKey.date,
                    calcKey.dcCode,
                    calcVal.expired,
                    calcVal.present,
                    calcVal.actualInbound,
                    calcVal.actualInboundPurchaseOrders?.joinToString(","),
                    calcVal.expectedInbound,
                    calcVal.expectedInboundPurchaseOrders?.joinToString(","),
                    calcVal.closingStock,
                    calcVal.dailyNeeds,
                    calcVal.productionWeek,
                    calcVal.demanded,
                    calcVal.openingStock,
                    calcVal.actualConsumption,
                    calcVal.safetyStock,
                    calcVal.safetyStockNeeds,
                    calcVal.stagingStock,
                    calcVal.storageStock,
                    calcVal.netNeeds,
                    Uom.valueOf(calcVal.uom.name),
                ).execute()
            }
        }
    }
}
