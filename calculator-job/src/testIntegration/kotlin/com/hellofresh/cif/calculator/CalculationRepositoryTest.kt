package com.hellofresh.cif.calculator

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.producer.filter.CleardownCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.SkuDc
import com.hellofresh.cif.calculator.producer.filter.createCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.createPreProdCalculationRepository
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuUOM.UOM_OZ
import java.math.BigDecimal
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class CalculationRepositoryTest : TestPrepare() {
    private val dcCode = "VE"
    private lateinit var calculationRepository: CleardownCalculationRepository
    private lateinit var preProdCalculationRepository: CleardownCalculationRepository

    @BeforeEach
    fun init() {
        calculationRepository = createCalculationRepository(dsl)
        preProdCalculationRepository = createPreProdCalculationRepository(dsl)
    }

    @Test
    fun `should return calculation from DB`() {
        // given
        val calculations =
            generateCalculations(10, dcCode).mapValues {
                it.value.copy(
                    stagingStock = ZERO,
                    storageStock = ZERO,
                    expectedInboundTransferOrdersQuantity = ZERO,
                    expectedOutboundTransferOrdersQuantity = ZERO,
                    actualInboundTransferOrdersQuantity = ZERO
                )
            }
        insertProdCalculations(calculations)
        // when
        val results = runBlocking { calculationRepository.load(setOf(dcCode), 1).toList() }

        // then
        assertEquals(calculations.toMap(), results.toMap())
    }

    @Test
    fun `should fill from PreProdCalculation in DB`() {
        // given
        val calculations =
            generateCalculations(10, dcCode).mapValues {
                it.value.copy(
                    stagingStock = ZERO,
                    storageStock = ZERO,
                    expectedInboundTransferOrdersQuantity = ZERO,
                    expectedOutboundTransferOrdersQuantity = ZERO,
                    actualInboundTransferOrdersQuantity = ZERO
                )
            }
        insertPreProdCalculations(calculations)

        // when
        val results = runBlocking { preProdCalculationRepository.load(setOf(dcCode), 1).toList() }

        // then
        assertEquals(calculations.toMap(), results.toMap())
    }

    @Test
    fun `should fill cache from live inventory prod calculations having empty POs`() {
        // given
        val key = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())
        val value = CskuInventoryForecastVal(
            ZERO, ZERO, ZERO, ZERO, emptySet(), ZERO, emptySet(), ZERO, ZERO, ZERO,
            ZERO, "2023-W11", ZERO, ZERO, strategy = defaultTestStrategy, ZERO, ZERO, netNeeds = TEN, uom = UOM_OZ,
            expectedInboundTransferOrdersQuantity = ZERO,
            expectedOutboundTransferOrdersQuantity = ZERO, actualInboundTransferOrdersQuantity = ZERO
        )
        val calculations = mapOf(key to value)
        insertProdCalculations(calculations)
        insertPreProdCalculations(calculations)

        // when
        val calculationResults = runBlocking { calculationRepository.load(setOf(dcCode), 1).toList() }
        val preProdCalculationsResults = runBlocking { preProdCalculationRepository.load(setOf(dcCode), 1).toList() }

        // then
        assertEquals(calculations.toMap(), calculationResults.toMap())
        assertEquals(calculations.toMap(), preProdCalculationsResults.toMap())
    }

    @Test
    fun `should fetch sku and dcs with calculations`() {
        // given
        val key1 = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())
        val value1 = CskuInventoryForecastVal(
            ZERO, ZERO, ZERO, ZERO, emptySet(), ZERO, emptySet(), ZERO, ZERO, ZERO,
            ZERO, "2023-W11", ZERO, ZERO, strategy = defaultTestStrategy, ZERO, ZERO, netNeeds = TEN, uom = UOM_OZ,
        )
        val key2 = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())
        val value2 = CskuInventoryForecastVal(
            ZERO, ZERO, BigDecimal.ONE, ZERO, emptySet(), ZERO, emptySet(), ZERO, ZERO, ZERO,
            ZERO, "2023-W11", ZERO, ZERO, strategy = defaultTestStrategy, ZERO, ZERO, netNeeds = TEN, uom = UOM_OZ,
        )
        val key3 = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())
        val value3 = CskuInventoryForecastVal(
            ZERO, ZERO, ZERO, ZERO, emptySet(), ZERO, emptySet(), BigDecimal.ONE, ZERO, ZERO,
            ZERO, "2023-W11", ZERO, ZERO, strategy = defaultTestStrategy, ZERO, ZERO, netNeeds = TEN, uom = UOM_OZ,
        )
        val calculations = mapOf(key1 to value1, key2 to value2, key3 to value3)
        insertProdCalculations(calculations)
        insertPreProdCalculations(calculations)

        // when
        val calculationResults = runBlocking {
            calculationRepository.fetchSkuDcWithCalculations(
                DateRange.oneDay(key1.date),
                setOf(key1.dcCode)
            )
        }
        val preProdCalculationsResults =
            runBlocking {
                preProdCalculationRepository.fetchSkuDcWithCalculations(
                    DateRange.oneDay(key1.date),
                    setOf(key1.dcCode)
                )
            }

        // then
        val expected = setOf(SkuDc(key2.cskuId, key2.dcCode), SkuDc(key3.cskuId, key3.dcCode))
        assertEquals(expected, calculationResults)
        assertEquals(expected, preProdCalculationsResults)
    }

    @Test
    fun `should fetch sku and dcs with calculations for given dcs and dates`() {
        // given

        val value = CskuInventoryForecastVal(
            ZERO, ZERO, BigDecimal.ONE, ZERO, emptySet(), ZERO, emptySet(), ZERO, ZERO, ZERO,
            ZERO, "2023-W11", ZERO, ZERO, strategy = defaultTestStrategy, ZERO, ZERO, netNeeds = TEN, uom = UOM_OZ,
        )
        val key1 = CskuInventoryForecastKey(UUID.randomUUID(), "V1", LocalDate.now().minusWeeks(1))
        val key2 = CskuInventoryForecastKey(UUID.randomUUID(), "V2", LocalDate.now())
        val key3 = CskuInventoryForecastKey(UUID.randomUUID(), "V3", LocalDate.now())

        val calculations = mapOf(key1 to value, key2 to value, key3 to value)
        insertProdCalculations(calculations)
        insertPreProdCalculations(calculations)

        // when
        val calculationResults = runBlocking {
            calculationRepository.fetchSkuDcWithCalculations(
                DateRange(key2.date.minusDays(1), key2.date.plusDays(2)),
                setOf(key1.dcCode, key3.dcCode),
            )
        }
        val preProdCalculationsResults = runBlocking {
            preProdCalculationRepository.fetchSkuDcWithCalculations(
                DateRange(key2.date.minusDays(1), key2.date.plusDays(2)),
                setOf(key1.dcCode, key3.dcCode),
            )
        }

        // then
        val expected = setOf(SkuDc(key3.cskuId, key3.dcCode))
        assertEquals(expected, calculationResults)
        assertEquals(expected, preProdCalculationsResults)
    }
}
