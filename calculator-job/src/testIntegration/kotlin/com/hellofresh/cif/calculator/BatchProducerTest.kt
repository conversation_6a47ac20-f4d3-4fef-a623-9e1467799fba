package com.hellofresh.cif.calculator

import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.producer.BatchProducer
import com.hellofresh.cif.calculator.producer.filter.DbRecordFilter
import com.hellofresh.cif.calculator.producer.filter.createCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.createLiveCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.createPreProdCalculationRepository
import com.hellofresh.cif.calculator.producer.filter.createPreProdLiveCalculationRepository
import com.hellofresh.cif.lib.kafka.serde.JacksonSerializer
import com.hellofresh.topic.Topic
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.producer.MockProducer
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import random

val meterRegistry = SimpleMeterRegistry()

class BatchProducerTest : TestPrepare() {
    private val dcCode = "VE"
    private val objectMapper = jacksonObjectMapper().findAndRegisterModules()
    private lateinit var dbRecordFilters:
        Map<CalculatorMode, DbRecordFilter<CskuInventoryForecastKey, CskuInventoryForecastVal>>
    private lateinit var producers:
        Map<CalculatorMode, BatchProducer<CskuInventoryForecastKey, CskuInventoryForecastVal>>
    private lateinit var kafkaProducers:
        Map<CalculatorMode, MockProducer<CskuInventoryForecastKey, CskuInventoryForecastVal>>

    @BeforeEach
    fun init() {
        super.setUp()
        val calcRepo = createCalculationRepository(dsl)
        val preprodCalcRepo = createPreProdCalculationRepository(dsl)
        val liveCalcRepo = createLiveCalculationRepository(dsl)
        val preprodLiveCalcRepo = createPreProdLiveCalculationRepository(dsl)
        val topic =
            Topic<CskuInventoryForecastKey, CskuInventoryForecastVal>("private.csku-inventory-forecast.test", 1)
        dbRecordFilters = mapOf(
            calcRepo.calculatorMode to DbRecordFilter(meterRegistry = meterRegistry, repo = calcRepo),
            preprodCalcRepo.calculatorMode to DbRecordFilter(meterRegistry = meterRegistry, repo = preprodCalcRepo),
            liveCalcRepo.calculatorMode to DbRecordFilter(meterRegistry = meterRegistry, repo = liveCalcRepo),
            preprodLiveCalcRepo.calculatorMode to DbRecordFilter(meterRegistry = meterRegistry, repo = preprodLiveCalcRepo),
        )
        kafkaProducers = CalculatorMode.entries.associateWith {
            MockProducer(
                true,
                JacksonSerializer<CskuInventoryForecastKey>(objectMapper),
                JacksonSerializer<CskuInventoryForecastVal>(objectMapper),
            )
        }

        producers = dbRecordFilters.map {
            it.key to BatchProducer(meterRegistry, topic, it.value, producerSupplier = { kafkaProducers[it.key]!! })
        }.toMap()
    }

    @ParameterizedTest
    @EnumSource(value = CalculatorMode::class)
    fun `should not publish calculations when record is filtered`(calculatorMode: CalculatorMode) {
        // given
        val k = CskuInventoryForecastKey(UUID.randomUUID(), dcCode, LocalDate.now())
        val v = CskuInventoryForecastVal.Companion.random().copy(strategy = defaultTestStrategy)
        val calculations = mapOf(k to v)
        when (calculatorMode) {
            CalculatorMode.PRODUCTION -> insertProdCalculations(calculations)
            CalculatorMode.PRE_PRODUCTION -> insertPreProdCalculations(calculations)
            CalculatorMode.LIVE_INVENTORY_PRODUCTION -> insertLiveCalculations(calculations)
            CalculatorMode.LIVE_INVENTORY_PRE_PRODUCTION -> insertLivePreProdCalculations(calculations)
        }

        dbRecordFilters[calculatorMode]?.load(setOf(dcCode))

        // when
        runBlocking { producers[calculatorMode]?.produce(listOf(k to v), { it }) }

        // then
        assertEquals(true, kafkaProducers[calculatorMode]?.history()?.isEmpty())
    }
}
