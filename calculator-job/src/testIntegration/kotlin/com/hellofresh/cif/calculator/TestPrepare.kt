package com.hellofresh.cif.calculator

import InfraPreparation
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.producer.filter.InMemoryCache
import com.hellofresh.cif.calculator.schema.Tables
import com.hellofresh.cif.calculator.schema.enums.Uom
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.models.SkuUOM.UOM_OZ
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.random.Random
import org.jooq.JSONB
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach

open class TestPrepare {
    lateinit var dsl: MetricsDSLContext

    @AfterEach
    fun tearDown() {
        dsl.deleteFrom(Tables.CALCULATION).execute()
        dsl.deleteFrom(Tables.PRE_PRODUCTION_CALCULATION).execute()
        dsl.deleteFrom(Tables.LIVE_INVENTORY_CALCULATION).execute()
        dsl.deleteFrom(Tables.LIVE_INVENTORY_PRE_PRODUCTION_CALCULATION).execute()
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
    }

    @BeforeEach
    fun setUp() {
        val defaultConfiguration = DefaultConfiguration()
            .apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
                setExecutor(Executors.newSingleThreadExecutor())
            }
        dsl = DSL.using(
            defaultConfiguration,
        ).withMetrics(SimpleMeterRegistry())
    }

    fun insertProdCalculations(records: Map<CskuInventoryForecastKey, CskuInventoryForecastVal>) {
        records.forEach { (k, v) ->
            with(Tables.CALCULATION) {
                dsl.insertInto(this).columns(
                    CSKU_ID,
                    DATE,
                    DC_CODE,
                    EXPIRED,
                    PRESENT,
                    ACTUAL_INBOUND,
                    ACTUAL_INBOUND_PO,
                    EXPECTED_INBOUND_PO,
                    CLOSING_STOCK,
                    DAILY_NEEDS,
                    PRODUCTION_WEEK,
                    DEMANDED,
                    OPENING_STOCK,
                    EXPECTED_INBOUND,
                    ACTUAL_CONSUMPTION,
                    SAFETYSTOCK,
                    STRATEGY,
                    SAFETYSTOCK_NEEDS,
                    STOCK_UPDATE,
                    NET_NEEDS,
                    UOM,
                    EXPECTED_INBOUND_TRANSFER_ORDERS,
                    EXPECTED_INBOUND_TRANSFER_ORDERS_QUANTITY,
                    ACTUAL_INBOUND_TRANSFER_ORDERS,
                    ACTUAL_INBOUND_TRANSFER_ORDERS_QUANTITY,
                    EXPECTED_OUTBOUND_TRANSFER_ORDERS,
                    EXPECTED_OUTBOUND_TRANSFER_ORDERS_QUANTITY
                ).values(
                    k.cskuId,
                    k.date,
                    k.dcCode,
                    v.expired,
                    v.present,
                    v.actualInbound,
                    v.actualInboundPurchaseOrders?.joinToString(),
                    v.expectedInboundPurchaseOrders?.joinToString(),
                    v.closingStock,
                    v.dailyNeeds,
                    v.productionWeek,
                    v.demanded,
                    v.openingStock,
                    v.expectedInbound,
                    v.actualConsumption,
                    v.safetyStock,
                    v.strategy,
                    v.safetyStockNeeds,
                    v.stockUpdate,
                    v.netNeeds,
                    Uom.valueOf(v.uom.name),
                    v.expectedInboundTransferOrders?.joinToString(),
                    v.expectedInboundTransferOrdersQuantity,
                    v.actualInboundTransferOrders?.joinToString(),
                    v.actualInboundTransferOrdersQuantity,
                    v.expectedOutboundTransferOrders?.joinToString(),
                    v.expectedOutboundTransferOrdersQuantity
                )
                    .execute()
            }
        }
    }

    fun insertPreProdCalculations(records: Map<CskuInventoryForecastKey, CskuInventoryForecastVal>) {
        records.forEach { (k, v) ->
            with(Tables.PRE_PRODUCTION_CALCULATION) {
                dsl.insertInto(this).columns(
                    CSKU_ID,
                    DATE,
                    DC_CODE,
                    EXPIRED,
                    PRESENT,
                    ACTUAL_INBOUND,
                    ACTUAL_INBOUND_PO,
                    EXPECTED_INBOUND_PO,
                    CLOSING_STOCK,
                    DAILY_NEEDS,
                    PRODUCTION_WEEK,
                    DEMANDED,
                    OPENING_STOCK,
                    EXPECTED_INBOUND,
                    ACTUAL_CONSUMPTION,
                    SAFETYSTOCK,
                    STRATEGY,
                    SAFETYSTOCK_NEEDS,
                    STOCK_UPDATE,
                    NET_NEEDS,
                    UOM,
                    EXPECTED_INBOUND_TRANSFER_ORDERS,
                    EXPECTED_INBOUND_TRANSFER_ORDERS_QUANTITY,
                    ACTUAL_INBOUND_TRANSFER_ORDERS,
                    ACTUAL_INBOUND_TRANSFER_ORDERS_QUANTITY,
                    EXPECTED_OUTBOUND_TRANSFER_ORDERS,
                    EXPECTED_OUTBOUND_TRANSFER_ORDERS_QUANTITY
                ).values(
                    k.cskuId,
                    k.date,
                    k.dcCode,
                    v.expired,
                    v.present,
                    v.actualInbound,
                    v.actualInboundPurchaseOrders?.joinToString(),
                    v.expectedInboundPurchaseOrders?.joinToString(),
                    v.closingStock,
                    v.dailyNeeds,
                    v.productionWeek,
                    v.demanded,
                    v.openingStock,
                    v.expectedInbound,
                    v.actualConsumption,
                    v.safetyStock,
                    v.strategy,
                    v.safetyStockNeeds,
                    v.stockUpdate,
                    v.netNeeds,
                    Uom.valueOf(v.uom.name),
                    v.expectedInboundTransferOrders?.joinToString(),
                    v.expectedInboundTransferOrdersQuantity,
                    v.actualInboundTransferOrders?.joinToString(),
                    v.actualInboundTransferOrdersQuantity,
                    v.expectedOutboundTransferOrders?.joinToString(),
                    v.expectedOutboundTransferOrdersQuantity
                )
                    .execute()
            }
        }
    }

    fun insertLiveCalculations(records: Map<CskuInventoryForecastKey, CskuInventoryForecastVal>) {
        records.forEach { (k, v) ->
            with(Tables.LIVE_INVENTORY_CALCULATION) {
                dsl.insertInto(this).columns(
                    CSKU_ID,
                    DATE,
                    DC_CODE,
                    EXPIRED,
                    STAGING_STOCK,
                    STORAGE_STOCK,
                    PRESENT,
                    ACTUAL_INBOUND,
                    ACTUAL_INBOUND_PO,
                    EXPECTED_INBOUND_PO,
                    CLOSING_STOCK,
                    DAILY_NEEDS,
                    PRODUCTION_WEEK,
                    DEMANDED,
                    OPENING_STOCK,
                    EXPECTED_INBOUND,
                    ACTUAL_CONSUMPTION,
                    SAFETYSTOCK,
                    SAFETYSTOCK_NEEDS,
                    NET_NEEDS,
                    PURCHASE_ORDER_DUE_IN_FOR_SUPPLIERS,
                    MAX_PURCHASE_ORDER_DUE_IN,
                    UOM,
                    EXPECTED_INBOUND_TRANSFER_ORDERS,
                    EXPECTED_INBOUND_TRANSFER_ORDERS_QUANTITY,
                    ACTUAL_INBOUND_TRANSFER_ORDERS,
                    ACTUAL_INBOUND_TRANSFER_ORDERS_QUANTITY,
                    EXPECTED_OUTBOUND_TRANSFER_ORDERS,
                    EXPECTED_OUTBOUND_TRANSFER_ORDERS_QUANTITY
                ).values(
                    k.cskuId,
                    k.date,
                    k.dcCode,
                    v.expired,
                    v.stagingStock,
                    v.storageStock,
                    v.present,
                    v.actualInbound,
                    v.actualInboundPurchaseOrders?.joinToString(),
                    v.expectedInboundPurchaseOrders?.joinToString(),
                    v.closingStock,
                    v.dailyNeeds,
                    v.productionWeek,
                    v.demanded,
                    v.openingStock,
                    v.expectedInbound,
                    v.actualConsumption,
                    v.safetyStock,
                    v.safetyStockNeeds,
                    v.netNeeds,
                    JSONB.jsonbOrNull(
                        v.purchaseOrderDueInForSuppliers?.let {
                            objectMapper.writeValueAsString(v.purchaseOrderDueInForSuppliers)
                        }
                    ),
                    v.maxPurchaseOrderDueIn,
                    Uom.valueOf(v.uom.name),
                    v.expectedInboundTransferOrders?.joinToString(),
                    v.expectedInboundTransferOrdersQuantity,
                    v.actualInboundTransferOrders?.joinToString(),
                    v.actualInboundTransferOrdersQuantity,
                    v.expectedOutboundTransferOrders?.joinToString(),
                    v.expectedOutboundTransferOrdersQuantity
                )
                    .execute()
            }
        }
    }

    fun insertLivePreProdCalculations(records: Map<CskuInventoryForecastKey, CskuInventoryForecastVal>) {
        records.forEach { (k, v) ->
            with(Tables.LIVE_INVENTORY_PRE_PRODUCTION_CALCULATION) {
                dsl.insertInto(this).columns(
                    CSKU_ID,
                    DATE,
                    DC_CODE,
                    EXPIRED,
                    STAGING_STOCK,
                    STORAGE_STOCK,
                    PRESENT,
                    ACTUAL_INBOUND,
                    ACTUAL_INBOUND_PO,
                    EXPECTED_INBOUND_PO,
                    CLOSING_STOCK,
                    DAILY_NEEDS,
                    PRODUCTION_WEEK,
                    DEMANDED,
                    OPENING_STOCK,
                    EXPECTED_INBOUND,
                    ACTUAL_CONSUMPTION,
                    SAFETYSTOCK,
                    SAFETYSTOCK_NEEDS,
                    NET_NEEDS,
                    PURCHASE_ORDER_DUE_IN_FOR_SUPPLIERS,
                    MAX_PURCHASE_ORDER_DUE_IN,
                    UOM,
                    EXPECTED_INBOUND_TRANSFER_ORDERS,
                    EXPECTED_INBOUND_TRANSFER_ORDERS_QUANTITY,
                    ACTUAL_INBOUND_TRANSFER_ORDERS,
                    ACTUAL_INBOUND_TRANSFER_ORDERS_QUANTITY,
                    EXPECTED_OUTBOUND_TRANSFER_ORDERS,
                    EXPECTED_OUTBOUND_TRANSFER_ORDERS_QUANTITY
                ).values(
                    k.cskuId,
                    k.date,
                    k.dcCode,
                    v.expired,
                    v.stagingStock,
                    v.storageStock,
                    v.present,
                    v.actualInbound,
                    v.actualInboundPurchaseOrders?.joinToString(),
                    v.expectedInboundPurchaseOrders?.joinToString(),
                    v.closingStock,
                    v.dailyNeeds,
                    v.productionWeek,
                    v.demanded,
                    v.openingStock,
                    v.expectedInbound,
                    v.actualConsumption,
                    v.safetyStock,
                    v.safetyStockNeeds,
                    v.netNeeds,
                    JSONB.jsonbOrNull(
                        v.purchaseOrderDueInForSuppliers?.let {
                            objectMapper.writeValueAsString(v.purchaseOrderDueInForSuppliers)
                        }
                    ),
                    v.maxPurchaseOrderDueIn,
                    Uom.valueOf(v.uom.name),
                    v.expectedInboundTransferOrders?.joinToString(),
                    v.expectedInboundTransferOrdersQuantity,
                    v.actualInboundTransferOrders?.joinToString(),
                    v.actualInboundTransferOrdersQuantity,
                    v.expectedOutboundTransferOrders?.joinToString(),
                    v.expectedOutboundTransferOrdersQuantity
                )
                    .execute()
            }
        }
    }

    fun generateCalculations(n: Int, dcCode: String): Map<CskuInventoryForecastKey, CskuInventoryForecastVal> = buildMap {
        repeat(n + 1) { _ ->
            put(
                CskuInventoryForecastKey(UUID.randomUUID(), dcCode, LocalDate.now()),
                CskuInventoryForecastVal(
                    expired = Random.nextDouble(1000.0).toBigDecimal(),
                    present = Random.nextDouble(1000.0).toBigDecimal(),
                    actualInbound = Random.nextDouble(1000.0).toBigDecimal(),
                    actualInboundPurchaseOrders = setOf(UUID.randomUUID().toString()),
                    expectedInboundPurchaseOrders = setOf(UUID.randomUUID().toString()),
                    closingStock = Random.nextDouble(1000.0).toBigDecimal(),
                    dailyNeeds = Random.nextDouble(1000.0).toBigDecimal(),
                    productionWeekStartStock = BigDecimal.ZERO,
                    productionWeek = "2022-W20",
                    demanded = Random.nextDouble(1000.0).toBigDecimal(),
                    openingStock = Random.nextDouble(1000.0).toBigDecimal(),
                    expectedInbound = Random.nextDouble(1000.0).toBigDecimal(),
                    actualConsumption = Random.nextDouble(1000.0).toBigDecimal(),
                    safetyStock = Random.nextDouble(1000.0).toBigDecimal(),
                    strategy = defaultTestStrategy,
                    safetyStockNeeds = Random.nextDouble(1000.0).toBigDecimal(),
                    stagingStock = Random.nextDouble(1000.0).toBigDecimal(),
                    storageStock = Random.nextDouble(1000.0).toBigDecimal(),
                    stockUpdate = 0.0.toBigDecimal(),
                    netNeeds = Random.nextDouble(1000.0).toBigDecimal(),
                    uom = UOM_OZ,
                    expectedInboundTransferOrders = setOf(UUID.randomUUID().toString(), UUID.randomUUID().toString()),
                    expectedInboundTransferOrdersQuantity = Random.nextDouble(1000.0).toBigDecimal(),
                    actualInboundTransferOrders = setOf(UUID.randomUUID().toString(), UUID.randomUUID().toString()),
                    actualInboundTransferOrdersQuantity = Random.nextDouble(1000.0).toBigDecimal(),
                    expectedOutboundTransferOrders = setOf(UUID.randomUUID().toString(), UUID.randomUUID().toString()),
                    expectedOutboundTransferOrdersQuantity = Random.nextDouble(1000.0).toBigDecimal(),
                ),
            )
        }
    }

    companion object {
        val dataSource = InfraPreparation.getMigratedDataSource()
        val objectMapper = jacksonObjectMapper().findAndRegisterModules()
        const val defaultTestStrategy = "UNSPECIFIED_STRATEGY"
    }
}

class Cache(val map: MutableMap<CskuInventoryForecastKey, CskuInventoryForecastVal>) :
    InMemoryCache<CskuInventoryForecastKey, CskuInventoryForecastVal> {
    override fun put(key: CskuInventoryForecastKey, value: CskuInventoryForecastVal?) {
        value?.let { map[key] = value }
    }

    override fun isNotInCache(key: CskuInventoryForecastKey, value: CskuInventoryForecastVal?): Boolean =
        map[key]?.let { value != it } ?: true
}
