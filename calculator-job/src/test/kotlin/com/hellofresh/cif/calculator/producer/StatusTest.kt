package com.hellofresh.cif.calculator.producer

import kotlin.test.Test
import kotlin.test.assertEquals

internal class StatusTest {

    @Test fun `produce correct logging message`() {
        val s = BatchProducer.Status()
        s.recordFailure(IllegalStateException("test message"))
        s.recordFailure(IllegalStateException("test message"))
        s.recordFailure(IllegalStateException("test message"))

        s.recordFailure(IllegalStateException("another test message"))
        s.recordFailure(IllegalStateException("another test message"))

        val expected = """
            Fail to produce 5 records because:
            IllegalStateException, repeated 3 times with cause:test message.
            IllegalStateException, repeated 2 times with cause:another test message.

        """.trimIndent()

        assertEquals(expected, s.message())
    }

    @Test fun `return correct success count`() {
        val s = BatchProducer.Status()
        s.recordOperation()
        s.recordOperation()
        s.recordFailure(IllegalStateException("test message"))

        assertEquals(1, s.success)
    }
}
