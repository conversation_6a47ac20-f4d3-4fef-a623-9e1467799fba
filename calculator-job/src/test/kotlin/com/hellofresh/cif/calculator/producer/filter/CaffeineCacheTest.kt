package com.hellofresh.cif.calculator.producer.filter

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.models.CalculatorMode
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertTrue
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Test
import random

internal class CaffeineCacheTest {
    private val maxSize = 10000L
    private val cache = CaffeineCache<CskuInventoryForecastKey, CskuInventoryForecastVal>(
        SimpleMeterRegistry(),
        CalculatorMode.PRODUCTION,
        1,
        maxSize,
    )

    @AfterEach
    fun afterEach() {
        cache.invalidateAll()
    }

    @Test
    fun `can lookup the key`() {
        val key = CskuInventoryForecastKey(UUID.randomUUID(), "VE", LocalDate.now())
        val value = CskuInventoryForecastVal.Companion.random()
        cache.put(key, value)
        assertFalse(cache.isNotInCache(key, value))

        assertTrue(cache.isNotInCache(key, CskuInventoryForecastVal.Companion.random()))
    }

    @Test
    fun `random generated values are present in cache`() {
        val values = (0..maxSize).map {
            val key = CskuInventoryForecastKey(UUID.randomUUID(), UUID.randomUUID().toString(), LocalDate.now())
            val value = CskuInventoryForecastVal.Companion.random()
            cache.put(key, value)
            key to value
        }
        values.forEach { (k, v) ->
            assertFalse(cache.isNotInCache(k, v))
        }

        (0..100).forEach {
            val key = CskuInventoryForecastKey(UUID.randomUUID(), UUID.randomUUID().toString(), LocalDate.now())
            val value = CskuInventoryForecastVal.Companion.random()
            assertTrue(cache.isNotInCache(key, value))
        }
    }
}
