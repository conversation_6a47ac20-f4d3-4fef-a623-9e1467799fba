package com.hellofresh.cif.calculator.producer

import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.calculator.producer.filter.RecordFilter
import com.hellofresh.topic.Topic
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.producer.MockProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.StringSerializer

class BatchProducerTest {
    private val testProducer = TestProducer<String, String?>(MockProducer(true, StringSerializer(), StringSerializer()))
    private val testTopic = Topic<String, String?>("private.csku-inventory-forecast.test", 1)
    private val meterRegistry = SimpleMeterRegistry()
    private var batchProducer = BatchProducer(
        topic = testTopic,
        meterRegistry = meterRegistry,
        producerSupplier = { testProducer },
        recordFilter = memoryTestBatchFilter(),
    )

    @Test
    fun `empty batches causes flush but metrics are not changed`() {
        runBlocking { batchProducer.produce(mapOf()) }
        assertTrue(testProducer.wasFlushed())
        assertEquals(0, meterRegistry.find("producer_success").counter()?.count()?.toInt())
        assertEquals(0, meterRegistry.find("producer_failure").counter()?.count()?.toInt())
    }

    @Test
    fun `empty batches due to filtering cause no update to metrics`() {
        val batchOne = mapOf("A" to "Z", "B" to "Y")
        val batchTwo = mapOf("A" to "Z", "B" to "Y")

        runBlocking { batchProducer.produce(batchOne) }
        assertTrue(testProducer.wasFlushed())

        runBlocking { batchProducer.produce(batchTwo) }
        assertTrue(testProducer.wasFlushed())

        assertEquals(2, meterRegistry.find("producer_success").counter()?.count()?.toInt())
        assertEquals(0, meterRegistry.find("producer_failure").counter()?.count()?.toInt())
    }

    @Test
    fun `flush is always called`() {
        val batch = mapOf(
            "A" to "Z",
        )

        runBlocking { batchProducer.produce(batch) }
        assertTrue(testProducer.wasFlushed())
    }

    @Test
    fun `null values throws an exception`() {
        val batch = mapOf("A" to null)
        runBlocking { batchProducer.produce(batch) }
        assertEquals(0, testProducer.readRecords().size)
    }

    @Test
    fun `produce completion updates record filter`() {
        val batchOne = mapOf("A" to "Z", "B" to "Y")
        val batchTwo = mapOf("A" to "Z", "B" to "Y")

        runBlocking { batchProducer.produce(batchOne) }
        assertEquals(2, testProducer.readRecords().size)

        runBlocking { batchProducer.produce(batchTwo) }
        assertEquals(0, testProducer.readRecords().size)
    }

    @Test
    fun `exception in sending the record is not thrown to the consumer`() {
        val batch = mapOf("A" to "Z", "B" to "Y")
        val registry = SimpleMeterRegistry()
        runBlocking {
            BatchProducer(
                topic = testTopic,
                meterRegistry = registry,
                // No serializer set will fail `send`
                producerSupplier = ::MockProducer,
                recordFilter = memoryTestBatchFilter(),
            ).produce(batch)
        }

        val metric = registry.find("producer_failure").counter()
        assertNotNull(metric)
        assertEquals(2, metric.count().toInt())
    }

    private companion object {
        fun memoryTestBatchFilter(): RecordFilter<String, String?> = object : RecordFilter<String, String?> {

            private val values = mutableListOf<String?>()

            override val calculatorMode: CalculatorMode = PRODUCTION

            override fun load(dcCodes: Set<String>) {
                // Do Nothing
            }

            override fun update(record: ProducerRecord<String, String?>) {
                values.add(record.value())
            }

            override fun filter(records: List<ProducerRecord<String, String?>>): List<ProducerRecord<String, String?>> =
                records.filter { producerRecord ->
                    !values.contains(producerRecord.value())
                }

            override fun filter(key: String, value: String?): Boolean =
                !values.contains(value)
        }
    }
}
