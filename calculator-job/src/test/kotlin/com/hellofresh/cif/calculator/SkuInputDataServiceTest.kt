package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.calculator.producer.filter.CalculationRepository
import com.hellofresh.cif.calculator.producer.filter.SkuDc
import com.hellofresh.cif.demand.DemandRepository
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.Context
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.inventory.DcSku
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.inventory.random
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuQuantity.Companion.ZERO
import com.hellofresh.cif.models.purchaseorder.PoStatus
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.skuinput.repo.SkuInputData
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.cif.transferorder.db.TransferOrderRepository
import com.hellofresh.cif.transferorder.model.TransferOrder
import com.hellofresh.cif.transferorder.model.TransferOrderSku
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import com.hellofresh.demand.models.Demand
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.sku.models.SkuSpecification
import io.mockk.coEvery
import io.mockk.mockk
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class SkuInputDataServiceTest {

    @Test fun `fetch stock updates for the given dcs`() {
        val stockUpdateService = mockk<StockUpdateService>()
        val safetyStockRepository = mockk<SafetyStockRepository>()
        val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
        val dc1Stock = StockUpdate.random().copy(dcCode = "dc1", date = LocalDate.now())
        val dc2Stock = StockUpdate.random().copy(dcCode = "dc2", date = LocalDate.now())
        val dcCodes = setOf(dc1Stock.dcCode, dc2Stock.dcCode)

        coEvery {
            stockUpdateService.getCurrentStockUpdates(dcCodes)
        } returns mapOf(
            DcSku(dc1Stock.dcCode, dc1Stock.skuId) to mapOf(dc1Stock.date to dc1Stock),
            DcSku(dc2Stock.dcCode, dc2Stock.skuId) to mapOf(dc2Stock.date to dc2Stock),
        )

        val keyToStockUpdates = runBlocking {
            SkuInputDataService(
                mockk(relaxed = true),
                mockk(relaxed = true),
                mockk(relaxed = true),
                transferOrderRepository = mockk(relaxed = true),
                mockk(relaxed = true),
                stockUpdateService,
                statsigFeatureFlagClient,
                safetyStockRepository,
                emptyMap(),
            ).getStockUpdates(dcCodes)
        }

        assertEquals(2, keyToStockUpdates.size)
        assertEquals(
            dc1Stock.quantity,
            keyToStockUpdates[CalculationKey(dc1Stock.skuId, dc1Stock.dcCode, dc1Stock.date)],
        )
        assertEquals(
            dc2Stock.quantity,
            keyToStockUpdates[CalculationKey(dc2Stock.skuId, dc2Stock.dcCode, dc2Stock.date)],
        )
    }

    @Test fun `should get minimum dc week`() {
        val today = LocalDate.parse(
            "2024-06-05",
            DateTimeFormatter.ofPattern("yyyy-MM-dd"),
        )
        val stockUpdateService = mockk<StockUpdateService>()
        val safetyStockRepository = mockk<SafetyStockRepository>()
        val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
        val dc1 = DistributionCenterConfiguration.default().copy(
            productionStart = DayOfWeek.MONDAY,
            dcCode = "BV",
            zoneId = ZoneId.of("Europe/London"),
        )
        val dc2 = DistributionCenterConfiguration.default().copy(
            productionStart = DayOfWeek.WEDNESDAY,
            dcCode = "VE",
            zoneId = ZoneId.of("Europe/Berlin"),
        )
        val dc3 = DistributionCenterConfiguration.default().copy(
            productionStart = DayOfWeek.FRIDAY,
            dcCode = "IT",
            zoneId = ZoneId.of("Europe/Rome"),
        )

        val dcRanges = mapOf(
            DateRange(today.minusDays(1), today.plusDays(2)) to setOf(dc1, dc2, dc3), // W23 - min
            DateRange(today.plusDays(7), today.plusDays(14)) to setOf(dc1, dc2, dc3), // W24
            DateRange(today.plusDays(14), today.plusDays(21)) to setOf(dc1, dc2, dc3), // W25
            DateRange(today.plusDays(21), today.plusDays(28)) to setOf(dc1, dc2, dc3), // W26
        )
        val mondayMinimumDcWeek =
            SkuInputDataService(
                mockk(relaxed = true),
                mockk(relaxed = true),
                mockk(relaxed = true),
                transferOrderRepository = mockk(relaxed = true),
                mockk(relaxed = true),
                stockUpdateService,
                statsigFeatureFlagClient,
                safetyStockRepository,
                emptyMap(),
            ).getMinimumWeek(dcRanges)

        assertEquals("2024-W23", mondayMinimumDcWeek)
    }

    @ParameterizedTest
    @EnumSource(CalculatorMode::class)
    fun `should include skus from input sources`(calculatorMode: CalculatorMode) {
        val dc1 = DistributionCenterConfiguration.default().copy(
            productionStart = DayOfWeek.MONDAY,
            dcCode = "BV",
            zoneId = ZoneId.of("Europe/London"),
        )
        val dc2 = DistributionCenterConfiguration.default().copy(
            productionStart = DayOfWeek.WEDNESDAY,
            dcCode = "VE",
            zoneId = ZoneId.of("Europe/Berlin"),
        )
        val dcs = setOf(dc1, dc2)
        val dcCodes = dcs.map { it.dcCode }.toSet()

        val inventoryService = mockk<InventoryService>()

        val inventory = SkuInventory(UUID.randomUUID(), emptyList())
        coEvery { inventoryService.fetchInventorySnapshots(dcs) } returns InventorySnapshots(
            listOf(InventorySnapshot(dc1.dcCode, UUID.randomUUID(), LocalDateTime.now(), listOf(inventory))),
            emptyList(), emptyList(),
        )

        val demandRepository = mockk<DemandRepository>()
        val demand = Demand(UUID.randomUUID(), dc1.dcCode, LocalDate.now(), ZERO)
        coEvery { demandRepository.findDemands(dcCodes, any()) } returns listOf(demand)

        val stockUpdateService = mockk<StockUpdateService>()
        val stockUpdate =
            StockUpdate(
                UUID.randomUUID(), dc1.dcCode, LocalDate.now(), "", ZERO, "", null, null, "", 0,
                LocalDateTime.now(
                    UTC,
                ),
                false,
            )
        coEvery { stockUpdateService.getCurrentStockUpdates(dcCodes) } returns mapOf(
            DcSku(
                stockUpdate.dcCode,
                stockUpdate.skuId,
            ) to mapOf(stockUpdate.date to stockUpdate),
        )

        val purchaseOrderRepository = mockk<PurchaseOrderRepository>()
        val transferOrderRepository = mockk<TransferOrderRepository>()
        val purchaseOrderSku = PurchaseOrderSku(UUID.randomUUID(), ZERO, emptyList(), null)
        coEvery { purchaseOrderRepository.findPurchaseOrders(dcCodes, any()) } returns listOf(
            PurchaseOrder(
                "",
                "",
                null,
                dc1.dcCode,
                null,
                null,
                listOf(purchaseOrderSku),
                emptyList(),
                PoStatus.APPROVED,
            ),
        )
        coEvery { transferOrderRepository.fetchInboundOrders(dcCodes, any()) } returns listOf(
            TransferOrder(
                transferOrderNumber = "TO-123",
                sourceDc = dc2.dcCode,
                destinationDc = dc1.dcCode,
                week = "2024-W23",
                marketCode = "DACH",
                status = TransferOrderStatus.STATE_DELIVERED,
                expectedDeliveryTimeslot = null,
                transferOrderSkus = listOf(
                    TransferOrderSku(
                        toNumber = "TO-W30",
                        skuId = UUID.randomUUID(),
                        expectedQuantity = SkuQuantity.fromLong(100),
                        supplierId = UUID.randomUUID(),
                        supplierName = "Supplier A",
                        deliveries = emptyList(),
                    )
                )
            )
        )
        coEvery { transferOrderRepository.fetchOutboundOrders(dcCodes, any()) } returns listOf(
            TransferOrder(
                transferOrderNumber = "TO-456",
                sourceDc = dc2.dcCode,
                destinationDc = dc1.dcCode,
                week = "2024-W31",
                marketCode = "DACH",
                status = TransferOrderStatus.STATE_DELIVERED,
                expectedDeliveryTimeslot = null,
                transferOrderSkus = listOf(
                    TransferOrderSku(
                        toNumber = "TO-123",
                        skuId = UUID.randomUUID(),
                        expectedQuantity = SkuQuantity.fromLong(200),
                        supplierId = UUID.randomUUID(),
                        supplierName = "Supplier B",
                        deliveries = emptyList(),
                    )
                )
            )
        )

        val calculationRepository = mockk<CalculationRepository<*, *>>()
        val preProdCalculationRepository = mockk<CalculationRepository<*, *>>()
        val liveCalculationRepository = mockk<CalculationRepository<*, *>>()
        val livePreProdCalculationRepository = mockk<CalculationRepository<*, *>>()

        val skuDc = SkuDc(UUID.randomUUID(), dc2.dcCode)
        when (calculatorMode) {
            CalculatorMode.PRODUCTION -> coEvery {
                calculationRepository.fetchSkuDcWithCalculations(any(), dcCodes)
            } returns setOf(skuDc)

            CalculatorMode.PRE_PRODUCTION -> coEvery {
                preProdCalculationRepository.fetchSkuDcWithCalculations(any(), dcCodes)
            } returns setOf(skuDc)

            CalculatorMode.LIVE_INVENTORY_PRODUCTION -> coEvery {
                liveCalculationRepository.fetchSkuDcWithCalculations(
                    any(),
                    dcCodes,
                )
            } returns setOf(skuDc)

            CalculatorMode.LIVE_INVENTORY_PRE_PRODUCTION -> coEvery {
                livePreProdCalculationRepository.fetchSkuDcWithCalculations(
                    any(),
                    dcCodes,
                )
            } returns setOf(skuDc)
        }

        val skusDc1 = listOf(inventory.skuId, demand.skuId, stockUpdate.skuId, purchaseOrderSku.skuId)
        val skusDc2 = listOf(skuDc.skuId)

        val skuInputDataRepository = mockk<SkuInputDataRepository>()
        val skuSpecifications = (skusDc1 + skusDc2).associate { it to mockk<SkuSpecification>() }
        coEvery { skuInputDataRepository.fetchByDcs(dcCodes) } returns SkuInputData(
            dcs.associateBy { it.dcCode }, skuSpecifications,
        )

        val calculationsInputData = runBlocking {
            SkuInputDataService(
                skuInputDataRepository = skuInputDataRepository,
                inventoryService = inventoryService,
                purchaseOrderRepository = purchaseOrderRepository,
                transferOrderRepository = transferOrderRepository,
                demandRepository = demandRepository,
                stockUpdateService = stockUpdateService,
                statsigFeatureFlagClient = StatsigTestFeatureFlagClient(
                    setOf(FeatureFlag.StockUpdate(setOf(ContextData(Context.MARKET, dc1.market))))
                ),
                safetyStockRepository = mockk(relaxed = true),
                calculationRepositories = mapOf(
                    CalculatorMode.PRODUCTION to calculationRepository,
                    CalculatorMode.PRE_PRODUCTION to preProdCalculationRepository,
                    CalculatorMode.LIVE_INVENTORY_PRODUCTION to liveCalculationRepository,
                    CalculatorMode.LIVE_INVENTORY_PRE_PRODUCTION to livePreProdCalculationRepository,
                ),
            ).fetchInputData(listOf(calculatorMode), dcCodes)
        }
        val expectedSkuDcCandidatesFromSource = skusDc1.map { SkuDcCandidate(it, skuSpecifications[it]!!, dc1) }.toSet()
        val expectedSkuDcCandidatesFromCalculations = skusDc2.map {
            SkuDcCandidate(
                it,
                skuSpecifications[it]!!,
                dc2,
            )
        }.toSet()

        assertEquals(expectedSkuDcCandidatesFromSource, calculationsInputData.skuDcCandidatesFromSources)
        assertEquals(
            expectedSkuDcCandidatesFromCalculations,
            calculationsInputData.skuCalculationModeCandidates[calculatorMode],
        )
        assertEquals(
            expectedSkuDcCandidatesFromSource + expectedSkuDcCandidatesFromCalculations,
            calculationsInputData.getSkuDcCandidates(calculatorMode),
        )
    }
}
