package com.hellofresh.cif.calculator

import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.EuBerlinZoneId
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity.Companion.ZERO
import com.hellofresh.cif.models.SkuQuantity.Companion.fromLong
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderOutbounds
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryCleardown
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.sku.models.SkuSpecification
import java.time.DayOfWeek.THURSDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.Test

internal class CalculatorClientTest {

    private val defaultLocation = Location("", LOCATION_TYPE_STAGING, null)

    @Test
    fun `should propagate an experiment value to opening and closing stock for all following calculations if sku has unexpired inventory for that day`() {
        // given
        val inventoryQty = fromLong(100)
        val today = LocalDate.now()
        val inventorySnapshot = InventorySnapshot(
            dcCode,
            UUID.randomUUID(),
            LocalDateTime.now(),
            listOf(
                SkuInventory(
                    skuId,
                    listOf(Inventory(inventoryQty, null, defaultLocation)),
                ),
            ),
        )
        val calculationKey = CalculationKey(skuId, dcCode, today)
        val experimentQty = fromLong(500, inventoryQty.unitOfMeasure)

        val inputData = initialInputData.copy(
            inventory = inventorySnapshots(initialInputData.dcConfig, listOf(inventorySnapshot)),
            stockUpdates = mapOf(calculationKey to experimentQty),
        )

        // when
        val calculations = calculatorClient.runDailyCalculations(inputData)
        val calculationToday = calculations.first {
            it.date == today
        }

        // then
        assertTrue(calculationToday.demanded.isZero())
        assertEquals(inventoryQty, calculationToday.openingStock)
        assertEquals((experimentQty + inventoryQty), calculationToday.closingStock)
        calculations.drop(1).forEach {
            assertEquals((experimentQty + inventoryQty), it.openingStock)
            assertEquals((experimentQty + inventoryQty), it.closingStock)
            assertTrue(it.demanded.isZero())
            assertTrue(it.expectedInbound.isZero())
        }
    }

    @Test
    fun `should apply a negative experiment value to closing stock`() {
        // given
        val today = LocalDate.now()
        val calculationKey = CalculationKey(skuId, dcCode, today)
        val demandQty = fromLong(100L)
        val experimentQty = fromLong(-500L, demandQty.unitOfMeasure)
        val inputData = initialInputData.copy(
            demands = Demands(
                listOf(
                    Demand(calculationKey.cskuId, calculationKey.dcCode, calculationKey.date, demandQty),
                ),
            ),
            stockUpdates = mapOf(calculationKey to experimentQty),
        )

        // when
        val calculations = calculatorClient.runDailyCalculations(inputData)
        val calculationToday = calculations.first {
            it.date == today
        }

        // then
        assertTrue(calculationToday.openingStock.isZero())
        assertEquals(demandQty, calculationToday.demanded)
        assertTrue(calculationToday.closingStock < ZERO)
        assertEquals(
            experimentQty.abs() + demandQty,
            calculationToday.closingStock.abs(),
        )
        // and since closing stock today is negative then  all remaining calculations have opening stock = 0
        calculations.drop(1).forEach {
            assertTrue(it.openingStock.isZero())
            assertTrue(it.closingStock.isZero())
        }
    }

    @Suppress("MaxLineLength")
    @Test
    fun `should propagate experimental quantities to opening and closing stock for all calculations if sku has unexpired inventory and demand for that day`() {
        // given
        val demandQty = fromLong(13L)
        val inventoryQty = fromLong(11L, demandQty.unitOfMeasure)
        val today = LocalDate.now()
        val inventorySnapshot = InventorySnapshot(
            dcCode,
            UUID.randomUUID(),
            LocalDateTime.now(),
            listOf(
                SkuInventory(
                    skuId,
                    listOf(Inventory(inventoryQty, null, defaultLocation)),
                ),
            ),
        )
        val calculationKey = CalculationKey(skuId, dcCode, today)
        val experimentQty = fromLong(17L, demandQty.unitOfMeasure)
        val closingStockToday = inventoryQty + experimentQty - demandQty

        val inputData = initialInputData.copy(
            inventory = inventorySnapshots(initialInputData.dcConfig, listOf(inventorySnapshot)),
            demands = Demands(
                listOf(
                    Demand(
                        calculationKey.cskuId,
                        calculationKey.dcCode,
                        calculationKey.date,
                        demandQty,
                    ),
                ),
            ),
            stockUpdates = mapOf(calculationKey to experimentQty),
        )

        // when
        val calculations = calculatorClient.runDailyCalculations(inputData)
        val calculationToday = calculations.first {
            it.date == today
        }

        // then
        assertEquals(inventoryQty, calculationToday.openingStock)
        assertEquals(demandQty, calculationToday.demanded)
        assertEquals(closingStockToday, calculationToday.closingStock)
        calculations.drop(1).forEach {
            assertEquals(closingStockToday, it.openingStock)
            assertEquals(closingStockToday, it.closingStock)
        }
    }

    @Test
    fun `should apply multiple day experiment quantities`() {
        // given
        val demandQtyDay1 = fromLong(13L)
        val inventoryQtyDay1 = fromLong(11L, demandQtyDay1.unitOfMeasure)
        val day1 = LocalDate.now()
        val experimentQtyDay1 = fromLong(17L, demandQtyDay1.unitOfMeasure)
        val calculationKeyDay1 = CalculationKey(skuId, dcCode, day1)
        val inventoryValueDay1 = InventorySnapshot(
            dcCode,
            UUID.randomUUID(),
            LocalDateTime.now(),
            listOf(
                SkuInventory(
                    skuId,
                    listOf(Inventory(inventoryQtyDay1, null, defaultLocation)),
                ),
            ),
        )
        val inventoryQtyDay2 = fromLong(21L, demandQtyDay1.unitOfMeasure)
        val demandQtyDay2 = fromLong(23L, demandQtyDay1.unitOfMeasure)
        val experimentQtyDay2 = fromLong(27L, demandQtyDay1.unitOfMeasure)
        val day2 = day1.plusDays(1)
        val calculationKeyDay2 = CalculationKey(skuId, dcCode, day2)
        val inventoryValueDay2 = InventorySnapshot(
            dcCode,
            UUID.randomUUID(),
            day2.atStartOfDay(),
            listOf(
                SkuInventory(
                    skuId,
                    listOf(Inventory(inventoryQtyDay2, null, defaultLocation)),
                ),
            ),
        )

        val inputData = initialInputData.copy(
            inventory = inventorySnapshots(initialInputData.dcConfig, listOf(inventoryValueDay1, inventoryValueDay2)),
            demands = Demands(
                listOf(
                    Demand(
                        calculationKeyDay1.cskuId,
                        calculationKeyDay1.dcCode,
                        calculationKeyDay1.date,
                        demandQtyDay1,
                    ),
                    Demand(
                        calculationKeyDay2.cskuId,
                        calculationKeyDay2.dcCode,
                        calculationKeyDay2.date,
                        demandQtyDay2,
                    ),
                ),
            ),
            stockUpdates = mapOf(
                calculationKeyDay1 to experimentQtyDay1,
                calculationKeyDay2 to experimentQtyDay2,
            ),
        )

        // when
        val calculations = calculatorClient.runDailyCalculations(inputData)
        val calculationDay1 = calculations.first { it.date == day1 }
        val calculationDay2 = calculations.first { it.date == day2 }

        // then
        assertEquals(inventoryQtyDay1, calculationDay1.openingStock)
        val closingStockDay1 = inventoryQtyDay1 + experimentQtyDay1 - demandQtyDay1
        assertEquals(closingStockDay1, calculationDay1.closingStock)
        assertEquals(closingStockDay1, calculationDay2.openingStock)
        assertEquals(
            (closingStockDay1 + experimentQtyDay2 - demandQtyDay2),
            calculationDay2.closingStock,
        )
        calculations.drop(2).forEach {
            assertEquals((closingStockDay1 + experimentQtyDay2 - demandQtyDay2), it.openingStock)
            assertEquals((closingStockDay1 + experimentQtyDay2 - demandQtyDay2), it.closingStock)
        }
    }

    @Test
    fun `calculations are produced for all skus present in input inventories`() {
        // given
        val today = LocalDateTime.now(dc.zoneId)
        val inventoryValueDay1 = InventorySnapshot(
            dcCode,
            UUID.randomUUID(),
            today,
            listOf(
                SkuInventory(UUID.randomUUID(), emptyList()),
                SkuInventory(UUID.randomUUID(), emptyList()),
            ),
        )

        val inventoryValueDay2 = InventorySnapshot(
            dcCode,
            UUID.randomUUID(),
            today.minusDays(2),
            listOf(
                SkuInventory(UUID.randomUUID(), emptyList()),
                SkuInventory(UUID.randomUUID(), emptyList()),
            ),
        )

        val expectedSkus = (inventoryValueDay1.skus.map { it.skuId } + inventoryValueDay2.skus.map { it.skuId }).toSet()

        val inputData = initialInputData.copy(
            inventory = inventorySnapshots(initialInputData.dcConfig, listOf(inventoryValueDay1, inventoryValueDay2)),
            skuDcCandidates =
            expectedSkus.map { SkuDcCandidate(it, skuSpecification, dc) }.toSet(),
        )

        // when
        val calculations = calculatorClient.runDailyCalculations(inputData)

        // then

        assertEquals(expectedSkus, calculations.map { it.cskuId }.toSet())
    }

    companion object {
        private val skuId = UUID.randomUUID()
        private const val dcCode = "VE"
        private val dc = DistributionCenterConfiguration(
            dcCode,
            productionStart = THURSDAY,
            cleardown = THURSDAY,
            zoneId = EuBerlinZoneId,
            market = "DACH",
            hasCleardown = false,
            wmsType = WmsSystem.UNRECOGNIZED,
        )
        private val skuSpecification = SkuSpecification(
            "",
            "",
            "",
            "",
            "",
            null,
            0,
            "DACH",
        )
        private val calculatorClient = CalculatorClient(
            StatsigTestFeatureFlagClient(emptySet()),
        )
        private val initialInputData = InputData(
            mode = PRODUCTION,
            skuDcCandidates = setOf(SkuDcCandidate(skuId, skuSpecification, dc)),
            InventorySnapshots(emptyList(), emptyList(), emptyList()),
            PurchaseOrderInbounds(emptyList()),
            transferOrderInbounds = TransferOrderInbounds(emptyList()),
            transferOrderOutbounds = TransferOrderOutbounds(emptyList()),
            Demands(emptyList()),
            safetyStocks = emptyMap(),
            stockUpdates = emptyMap(),
            supplierSku = emptyMap(),
            preproductionCleardownDcs = emptySet(),
        )

        private fun inventorySnapshots(
            dcConfig: Map<DcCode, DistributionCenterConfiguration>,
            inventorySnapshots: List<InventorySnapshot>
        ) = InventoryCleardown.createInventorySnapshotsWithScheduledCleardown(
            dcConfig,
            inventorySnapshots,
            emptyList(),
        )
    }
}
