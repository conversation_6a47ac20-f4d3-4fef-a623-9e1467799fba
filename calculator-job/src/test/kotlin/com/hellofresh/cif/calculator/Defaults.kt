package com.hellofresh.cif.calculator

import com.hellofresh.sku.models.SkuSpecification
import java.util.UUID

val DEFAULT_UUID = UUID.randomUUID()
const val DEFAULT_DC_CODE = "VE"
const val DEFAULT_TEST_STRATEGY_ALGORITHM = "ALGORITHM_FORECASTVARIANCE"
internal val defaultSku = SkuSpecification(
    skuCode = "PHF-00-00000-00",
    name = "a name",
    category = "PHF",
    coolingType = "type",
    packaging = "packaging",
    acceptableCodeLife = 0,
    market = "dach"
)
