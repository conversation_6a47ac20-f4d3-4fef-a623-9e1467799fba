package com.hellofresh.cif.calculator.producer

import java.util.concurrent.Future
import java.util.concurrent.atomic.AtomicBoolean
import org.apache.kafka.clients.producer.Callback
import org.apache.kafka.clients.producer.MockProducer
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.clients.producer.RecordMetadata

class TestProducer<K, V>(private val mock: MockProducer<K, V>) : Producer<K, V> by mock {
    /**
     * Holds data produced to a specific topic associated with a [String] key
     * that can be used to identify bucketed data (e.g. when batches were split
     * and produced concurrently for performance boosts). The key is the name of
     * the [Thread] that was used to produce the data. This means that if the
     * data was not produced concurrently, there will be only one key/value in
     * the underlying map.
     **/
    private val topicDataPerThread = mutableMapOf<String, MutableList<ProducerRecord<K, V>>>()

    /**
     * Whether the producer was flushed or not.
     */
    private val flushed = AtomicBoolean(false)

    override fun send(record: ProducerRecord<K, V>, callback: Callback?): Future<RecordMetadata> {
        synchronized(topicDataPerThread) {
            topicDataPerThread.compute(Thread.currentThread().name) { _, v ->
                v?.apply { add(record) } ?: mutableListOf(record)
            }
        }
        return mock.send(record, callback)
    }

    override fun flush() {
        mock.flush()
        flushed.compareAndSet(false, true)
    }

    /**
     * Reads all data from the topic mimicking the behaviour of a Consumer#poll()
     * followed by Consumer#commitSync, behaving as if the offset were committed.
     * This means that one can only read the data once and, the following reads
     * will only get new [ProducerRecord] sent to the topic.
     *
     * @return a map of all produced records up until the point they are read
     * associated with the name of the [Thread] they were produced from.
     */
    fun readRecordsPerThread(): Map<String, List<ProducerRecord<K, V>>> {
        synchronized(topicDataPerThread) {
            val tmp = topicDataPerThread.toMap()
            topicDataPerThread.clear()
            return tmp
        }
    }

    /**
     * Reads all data from the topic mimicking the behaviour of a Consumer#poll()
     * followed by Consumer#commitSync, behaving as if the offsets were committed.
     * This means that one can only read the data once and, the following reads
     * will only get new [ProducerRecord] sent to the topic.
     *
     * @return list of all produced records up until the point they are read.
     */
    fun readRecords(): List<ProducerRecord<K, V>> {
        synchronized(topicDataPerThread) {
            val tmp = topicDataPerThread.values.flatten()
            topicDataPerThread.clear()
            return tmp
        }
    }

    /**
     * Returns whether the call to Producer#flush was performed. It then sets
     *
     * return true in case the Producer#flush() was called again.
     */
    fun wasFlushed() = flushed.getAndSet(false)
}
