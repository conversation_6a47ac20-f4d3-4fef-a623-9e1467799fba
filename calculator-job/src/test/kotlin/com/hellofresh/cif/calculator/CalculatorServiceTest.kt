package com.hellofresh.cif.calculator

import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.CalculatorMode.PRE_PRODUCTION
import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.calculator.producer.Producer
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.model.SafetyStockValue
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderOutbounds
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryCleardown
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.SkuInventory
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TWO
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.assertThrows

class CalculatorServiceTest {

    private val meterRegistry = SimpleMeterRegistry()
    private val skuInputDataService = mockk<SkuInputDataService>()
    private val calculatorClient =
        CalculatorClient(StatsigTestFeatureFlagClient(emptySet()))
    private val dcCodes = setOf(DEFAULT_DC_CODE)

    private val dcConfig = DistributionCenterConfiguration.default(DEFAULT_DC_CODE)
    private val dcConfigs = mapOf(
        DEFAULT_DC_CODE to dcConfig,
    )
    private val calculationsInputData = CalculationsInputData(
        emptyMap(),
        setOf(
            SkuDcCandidate(
                DEFAULT_UUID,
                defaultSku,
                dcConfig,
            ),
        ),
        InventoryCleardown.createInventorySnapshotsWithScheduledCleardown(dcConfigs, emptyList(), emptyList()),
        PurchaseOrderInbounds(emptyList()),
        transferOrderInbounds = TransferOrderInbounds(emptyList()),
        transferOrderOutbounds = TransferOrderOutbounds(emptyList()),
        Demands(emptyList()),
        mapOf(),
        safetyStocks = emptyMap(),
        supplierSkuDetails = emptyMap(),
    )

    private fun createInventory(qty: BigDecimal) = Inventory(
        SkuQuantity.fromBigDecimal(qty),
        null,
        Location("", LOCATION_TYPE_STAGING, null),
    )

    @BeforeEach
    internal fun setUp() {
        coEvery { skuInputDataService.fetchInputData(any(), any()) } returns calculationsInputData
    }

    @Test
    fun `calculation are not produced if repository returned error`() {
        val producerMock = mockk<Producer<CskuInventoryForecastKey, CskuInventoryForecastVal>>()
        coEvery {
            skuInputDataService.fetchInputData(any(), any())
        } throws IllegalStateException("Unable to fetch")
        assertThrows<Exception> {
            runBlocking {
                CalculatorService(
                    skuInputDataService,
                    listOf(producerMock, producerMock),
                    calculatorClient,
                    SimpleMeterRegistry(),
                    emptySet(),
                    "cif-calculator-job-app-0"
                ).run(dcCodes)
            }
        }
    }

    @Test
    fun `inventory corrections cause recalculation from the latest cleardown`() {
        val producer = NoopProducer(PRODUCTION)
        val preprodProducer = NoopProducer(PRE_PRODUCTION)
        val cskuId = DEFAULT_UUID
        val latestCleardown = DistributionCenterConfiguration.default().getLatestCleardown().minusDays(1)

        coEvery { skuInputDataService.fetchInputData(any(), any()) } returns calculationsInputData.copy(
            inventorySnapshots = inventorySnapshots(
                calculationsInputData.skuDcCandidatesFromSources.associateBy({ it.dcCode }) { it.dc },
                listOf(
                    InventorySnapshot(
                        DEFAULT_DC_CODE,
                        UUID.randomUUID(), latestCleardown.atStartOfDay(),
                        listOf(
                            SkuInventory(
                                cskuId, listOf(createInventory(ONE)),
                            ),
                        ),
                    ),
                ),
            ),
        )

        runBlocking {
            CalculatorService(
                skuInputDataService,
                listOf(producer, preprodProducer),
                calculatorClient,
                meterRegistry,
                emptySet(),
                "cif-calculator-job-app-0"
            ).run(dcCodes)
        }

        assertTrue(producer.calculations.isNotEmpty())
        producer.calculations.forEach {
            assertEquals(ONE, it.value?.present)
        }

        coEvery { skuInputDataService.fetchInputData(any(), any()) } returns calculationsInputData.copy(
            inventorySnapshots = inventorySnapshots(
                calculationsInputData.skuDcCandidatesFromSources.associateBy({ it.dcCode }) { it.dc },
                listOf(
                    InventorySnapshot(
                        DEFAULT_DC_CODE,
                        UUID.randomUUID(), latestCleardown.atStartOfDay(),
                        listOf(
                            SkuInventory(
                                cskuId, listOf(createInventory(TWO)),
                            ),
                        ),
                    ),
                ),
            ),
        )
        runBlocking {
            CalculatorService(
                skuInputDataService,
                listOf(producer, preprodProducer),
                calculatorClient,
                meterRegistry,
                emptySet(),
                "cif-calculator-job-app-0"
            ).run(dcCodes)
        }

        assertTrue(producer.calculations.isNotEmpty())
        producer.calculations.forEach {
            assertEquals(TWO, it.value?.present)
        }
    }

    @Test
    fun `calculations are produced for all calculation modes`() {
        val producer = NoopProducer(PRODUCTION)
        val preprodProducer = NoopProducer(PRE_PRODUCTION)
        val cskuId = DEFAULT_UUID
        val calculationKey = CalculationKey(cskuId, DEFAULT_DC_CODE, LocalDate.now())
        val inputData = calculationsInputData.copy(
            demands = Demands(
                listOf(
                    Demand(
                        calculationKey.cskuId,
                        calculationKey.dcCode,
                        calculationKey.date,
                        SkuQuantity.fromLong(100),
                    ),
                ),
            ),
        )
        coEvery { skuInputDataService.fetchInputData(any(), dcCodes) } returns inputData
        runBlocking {
            CalculatorService(
                skuInputDataService,
                listOf(producer, preprodProducer),
                calculatorClient,
                meterRegistry,
                emptySet(),
                "cif-calculator-job-app-0"
            ).run(dcCodes)
        }

        assertTrue(producer.calculations.isNotEmpty())
        assertTrue(preprodProducer.calculations.isNotEmpty())
        assertTrue { producer.calculations.keys.all { it.cskuId == cskuId } }
        assertTrue { preprodProducer.calculations.keys.all { it.cskuId == cskuId } }
    }

    @Test
    fun `safetyStockNeeds should be calculated when proper values are provided`() {
        val producer = NoopProducer(PRODUCTION)
        val preprodProducer = NoopProducer(PRE_PRODUCTION)
        val cskuId = DEFAULT_UUID
        val latestCleardown = DistributionCenterConfiguration.default().getLatestCleardown().minusDays(1)
        val safetyStock: Long = 100
        val inventory = BigDecimal(49)

        val safetyStockWeeks = setOf(
            DcWeek(dcConfig.getLatestProductionStart(), dcConfig.productionStart),
            DcWeek(dcConfig.getLatestProductionStart().plusWeeks(2), dcConfig.productionStart),
        )
        coEvery {
            skuInputDataService.fetchInputData(any(), any())
        } returns calculationsInputData.copy(
            safetyStocks = safetyStockWeeks.associate {
                SafetyStockKey(DEFAULT_DC_CODE, it, cskuId) to SafetyStockValue(
                    safetyStock,
                    DEFAULT_TEST_STRATEGY_ALGORITHM,
                )
            },
            inventorySnapshots = inventorySnapshots(
                calculationsInputData.skuDcCandidatesFromSources.associateBy({ it.dcCode }) { it.dc },
                listOf(
                    InventorySnapshot(
                        DEFAULT_DC_CODE,
                        UUID.randomUUID(), latestCleardown.atStartOfDay(),
                        listOf(
                            SkuInventory(
                                cskuId, listOf(createInventory(inventory)),
                            ),
                        ),
                    ),
                ),
            ),
        )

        runBlocking {
            CalculatorService(
                skuInputDataService,
                listOf(producer, preprodProducer),
                calculatorClient,
                meterRegistry,
                emptySet(),
                "cif-calculator-job-app-0"
            ).run(dcCodes)
        }

        assertTrue(producer.calculations.isNotEmpty())
        producer.calculations.forEach {
            if (safetyStockWeeks.contains(DcWeek(it.key.date, dcConfig.productionStart))) {
                assertEquals(safetyStock, it.value?.safetyStock?.toLong())
                assertEquals(inventory.toLong() - safetyStock, it.value?.safetyStockNeeds?.toLong())
            } else {
                assertEquals(0, it.value?.safetyStock?.toInt() ?: 0)
                assertEquals(0, it.value?.safetyStockNeeds?.toInt() ?: 0)
            }
        }
    }

    @Test
    fun `calculations are produced for all sku candidates from inputData(mode and sources)`() {
        val producer = NoopProducer(PRODUCTION)
        val preprodProducer = NoopProducer(PRE_PRODUCTION)
        val skuDcCandidate1 = SkuDcCandidate(UUID.randomUUID(), defaultSku, dcConfig)
        val skuDcCandidate2 = SkuDcCandidate(UUID.randomUUID(), defaultSku, dcConfig)
        val inputData = calculationsInputData.copy(
            skuCalculationModeCandidates = mapOf(
                PRODUCTION to setOf(skuDcCandidate1),
                PRE_PRODUCTION to setOf(skuDcCandidate2),
            ),
        )
        coEvery {
            skuInputDataService.fetchInputData(
                match { it.containsAll(setOf(PRODUCTION, PRE_PRODUCTION)) },
                dcCodes,
            )
        } returns inputData
        runBlocking {
            CalculatorService(
                skuInputDataService,
                listOf(producer, preprodProducer),
                calculatorClient,
                meterRegistry,
                emptySet(),
                "cif-calculator-job-app-0"
            ).run(dcCodes)
        }

        assertTrue(
            producer.calculations.keys.any {
                it.dcCode == skuDcCandidate1.dcCode &&
                    it.cskuId == skuDcCandidate1.skuId
            },
        )

        assertTrue(
            producer.calculations.keys.any {
                it.dcCode == DEFAULT_DC_CODE &&
                    it.cskuId == DEFAULT_UUID
            },
        )

        assertTrue(
            preprodProducer.calculations.keys.any {
                it.dcCode == skuDcCandidate2.dcCode &&
                    it.cskuId == skuDcCandidate2.skuId
            },
        )

        assertTrue(
            preprodProducer.calculations.keys.any {
                it.dcCode == DEFAULT_DC_CODE &&
                    it.cskuId == DEFAULT_UUID
            },
        )
    }

    @Test
    fun `safetyStockNeeds should be zero when closing stock is greater than needs`() {
        val producer = NoopProducer(PRODUCTION)
        val preprodProducer = NoopProducer(PRE_PRODUCTION)
        val cskuId = DEFAULT_UUID
        val latestCleardown = DistributionCenterConfiguration.default().getLatestCleardown().minusDays(1)
        val safetyStock: Long = 100
        val inventory = BigDecimal(150)

        val safetyStockWeeks = setOf(
            DcWeek(dcConfig.getLatestProductionStart(), dcConfig.productionStart),
            DcWeek(dcConfig.getLatestProductionStart().plusWeeks(2), dcConfig.productionStart),
        )
        coEvery {
            skuInputDataService.fetchInputData(any(), any())
        } returns calculationsInputData.copy(
            safetyStocks = safetyStockWeeks.associate {
                SafetyStockKey(DEFAULT_DC_CODE, it, cskuId) to SafetyStockValue(
                    safetyStock = safetyStock,
                    strategy = DEFAULT_TEST_STRATEGY_ALGORITHM,
                )
            },
            inventorySnapshots = inventorySnapshots(
                calculationsInputData.skuDcCandidatesFromSources.associateBy({ it.dcCode }) { it.dc },
                listOf(
                    InventorySnapshot(
                        DEFAULT_DC_CODE,
                        UUID.randomUUID(), latestCleardown.atStartOfDay(),
                        listOf(
                            SkuInventory(
                                cskuId, listOf(createInventory(inventory)),
                            ),
                        ),
                    ),
                ),
            ),
        )

        runBlocking {
            CalculatorService(
                skuInputDataService,
                listOf(producer, preprodProducer),
                calculatorClient,
                meterRegistry,
                emptySet(),
                "cif-calculator-job-app-0"
            ).run(dcCodes)
        }

        assertTrue(producer.calculations.isNotEmpty())
        producer.calculations.forEach {
            if (safetyStockWeeks.contains(DcWeek(it.key.date, dcConfig.productionStart))) {
                assertEquals(safetyStock, it.value?.safetyStock?.toLong())
                assertEquals(0, it.value?.safetyStockNeeds?.toLong())
            } else {
                assertEquals(0, it.value?.safetyStock?.toInt() ?: 0)
            }
        }
    }

    @Test
    fun `should perform calculations for the respective CalculatorMode used by CalculatorService`() {
        // given
        val producers = CalculatorMode.values().map {
            val producer: Producer<CskuInventoryForecastKey, CskuInventoryForecastVal> = mockk(relaxed = true)
            every { producer.calculatorMode() } returns it
            producer
        }

        val calculatorClientMock: CalculatorClient = mockk()
        val slot = mutableListOf<InputData>()
        every { calculatorClientMock.runDailyCalculations(capture(slot)) } returns emptyList()

        // when
        runBlocking {
            CalculatorService(
                skuInputDataService,
                producers,
                calculatorClientMock,
                meterRegistry,
                emptySet(),
                "cif-calculator-job-app-0"
            ).run(dcCodes)
        }

        // then
        assertEquals(4, slot.size)
        slot.forEach {
            verify(exactly = 1) { calculatorClientMock.runDailyCalculations(it) }
        }
        assertEquals(producers.map { it.calculatorMode() }.sorted(), slot.map { it.mode }.sorted())
        producers.forEach {
            coVerify(exactly = 1) { it.produce(any<List<*>>(), any()) }
        }
    }

    private fun inventorySnapshots(
        dcConfigs: Map<DcCode, DistributionCenterConfiguration>,
        inventorySnapshots: List<InventorySnapshot>
    ) = InventoryCleardown.createInventorySnapshotsWithScheduledCleardown(
        dcConfigs,
        inventorySnapshots,
        emptyList(),
    )
}
