package com.hellofresh.cif.calculator.producer.filter

import com.hellofresh.cif.calculator.models.CalculatorMode.PRODUCTION
import com.hellofresh.topic.Topic
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.every
import io.mockk.mockk
import kotlin.test.Test
import kotlin.test.assertEquals
import org.apache.kafka.clients.producer.ProducerRecord
import org.junit.jupiter.api.BeforeEach

private class Cache(val map: MutableMap<String, String>) : InMemoryCache<String, String> {
    override fun put(key: String, value: String?) {
        value?.let { map[key] = value }
    }

    override fun isNotInCache(key: String, value: String?): Boolean = map[key]?.let { value != it } ?: true
}

class DbRecordFilterTest {
    private val testTopic = Topic<String, String?>("private.csku-inventory-forecast.test", 1)
    private val calculationRepository = mockk<CalculationRepository<String, String>>()
    private val cache = Cache(mutableMapOf())

    private fun dbFilter(
        repo: CalculationRepository<String, String>,
        cache: Cache,
    ) = DbRecordFilter(
        meterRegistry = SimpleMeterRegistry(),
        repo = repo,
        cacheSupplier = { cache },
    )

    @BeforeEach
    fun setUp() {
        every { calculationRepository.load(any(), any()) } returns emptySequence()
        every { calculationRepository.calculatorMode } returns PRODUCTION
        every { calculationRepository.toCskuInventoryForecastDbValue(any()) } answers { firstArg() }
    }

    @Test fun `does not filter first seen tombstone`() {
        val records = listOf(ProducerRecord<String, String?>(testTopic.name, null, "A", null))
        val calculationRepository = mockk<CalculationRepository<String, String?>>()
        every { calculationRepository.load(any(), any()) } returns emptySequence()
        every { calculationRepository.calculatorMode } returns PRODUCTION
        every { calculationRepository.toCskuInventoryForecastDbValue(any()) } answers { firstArg() }
        assertEquals(
            records,
            DbRecordFilter(
                meterRegistry = SimpleMeterRegistry(),
                repo = calculationRepository,
                cacheSupplier = { CaffeineCache(SimpleMeterRegistry(), calculationRepository.calculatorMode, 1, 10) },
            ).filter(records),
        )
    }

    @Test fun `filter records that have the same key and value targeting the same topic`() {
        val record = ProducerRecord(testTopic.name, null, "A", "Z")
        val recordFilter = dbFilter(calculationRepository, cache)
        recordFilter.update(record)
        assertEquals(emptyList(), recordFilter.filter(listOf(record)))
    }

    @Test fun `do not filter records that have the same key and different value targeting the same topic`() {
        val recordAZ = ProducerRecord(testTopic.name, null, "A", "Z")
        val recordAY = ProducerRecord(testTopic.name, null, "A", "Y")

        val recordFilter = dbFilter(calculationRepository, cache)

        recordFilter.update(recordAZ)
        assertEquals(listOf(recordAY), recordFilter.filter(listOf(recordAY)))
    }

    @Test fun `do not filter records that have never been seen before targeting the same topic`() {
        val record = ProducerRecord(testTopic.name, null, "A", "Z")
        val recordFilter = dbFilter(calculationRepository, cache)
        assertEquals(listOf(record), recordFilter.filter(listOf(record)))
    }

    @Test fun `filter the record present in the DB after calling load`() {
        val kv = mapOf("A" to "Z")
        val dcCodes = setOf("dcCode")
        every { calculationRepository.load(dcCodes, DAYS_IN_THE_PAST) } returns kv.map { it.key to it.value }.asSequence()
        val recordFilter = dbFilter(calculationRepository, cache).also { it.load(dcCodes) }

        val records = kv.map {
            ProducerRecord(testTopic.name, null, it.key, it.value)
        }

        assertEquals(emptyList(), recordFilter.filter(records))
    }

    @Test fun `query the DB only once`() {
        val kv = mapOf(
            "A" to "Z",
            "B" to "Y",
            "C" to "X",
        )
        every { calculationRepository.load(any(), any()) } returns kv.map { it.key to it.value }.asSequence()

        val recordFilter = dbFilter(calculationRepository, cache)

        val records = kv.map {
            ProducerRecord(testTopic.name, null, it.key, it.value)
        }

        records.forEach {
            recordFilter.update(it)
            recordFilter.filter(records)
        }

        assertEquals(kv, cache.map)
    }
}
