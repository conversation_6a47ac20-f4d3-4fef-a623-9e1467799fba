package com.hellofresh.cif.inventorylivejob.job

import com.hellofresh.cif.demand.DemandRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventorylivejob.FunctionalTest
import com.hellofresh.cif.inventorylivejob.service.ActivityInventoryProcessor
import com.hellofresh.cif.inventorylivejob.service.DemandProcessor
import com.hellofresh.cif.inventorylivejob.service.InventoryLiveSnapshotProcessor
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryMovementsTypeId
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STORAGE
import com.hellofresh.inventory.models.default
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import com.hellofresh.inventory.models.inventory.live.SkuLiveInventory
import com.hellofresh.inventory.models.inventory.live.default
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit.SECONDS
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class InventoryLiveSnapshotJobIntegrationTest : FunctionalTest() {

    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())

    @Test
    fun `live inventory job process inventory activities`() {
        val skuId = UUID.randomUUID()
        val zoneId = ZoneId.of(newDcConfigRecord.zoneId)
        val todaySnapshotTime = OffsetDateTime.now(zoneId).withHour(12).withMinute(0)
        val yesterdaySnapshotTime = todaySnapshotTime.minusDays(1)

        val yesterdayLiveInventory = Inventory(
            qty = SkuQuantity.fromBigDecimal(BigDecimal(100L)),
            expiryDate = LocalDate.now(ZoneOffset.UTC).plusDays(10),
            location = Location(
                id = "location-id",
                type = LOCATION_TYPE_STAGING,
                transportModuleId = null,
            ),
        )

        val todayInventory = Inventory(
            qty = SkuQuantity.fromBigDecimal(BigDecimal(100L)),
            expiryDate = LocalDate.now(ZoneOffset.UTC).plusDays(20),
            location = Location("", LOCATION_TYPE_STORAGE, null),
        )

        val inventoryMovementValue = createInventoryMovementValue().copy(
            destinationLocationId = yesterdayLiveInventory.location.id,
            destinationLocationType = yesterdayLiveInventory.location.type,
            quantity = SkuQuantity.fromBigDecimal(BigDecimal(50)),
            expirationDate = yesterdayLiveInventory.expiryDate,
        )
        val activityRecord = createInventoryMovementRecord(inventoryMovementValue).apply {
            this.skuId = skuId
            this.activityTime = todaySnapshotTime.minusHours(1)
            this.typeId = InventoryMovementsTypeId.MPR.name
        }
        val liveYesterdaySnapshot = LiveInventorySnapshot.default().copy(
            dcCode = newDcConfigRecord.dcCode,
            snapshotTime = yesterdaySnapshotTime.toLocalDateTime(),
            skus = listOf(
                SkuLiveInventory(
                    skuId = skuId,
                    inventory = listOf(yesterdayLiveInventory),
                ),
            ),
        )

        val todaySnapshot = com.hellofresh.inventory.models.InventorySnapshot.default().copy(
            dcCode = newDcConfigRecord.dcCode,
            snapshotTime = todaySnapshotTime.toLocalDateTime(),
            skus = listOf(
                com.hellofresh.inventory.models.SkuInventory(
                    skuId = skuId,
                    inventory = listOf(todayInventory),
                ),
            ),
        )

        dsl.batchInsert(
            newDcConfigRecord,
            activityRecord,
            createInventoryLiveSnapshotRecord(liveYesterdaySnapshot, zoneId),
            createInventorySnapshotRecord(todaySnapshot, zoneId),
        ).execute()

        runBlocking {
            InventoryLiveSnapshotJob(
                dcConfigService,
                InventoryRepositoryImpl(dsl),
                inventoryLiveSnapshotRepository,
                InventoryLiveSnapshotProcessor(
                    ActivityInventoryProcessor(inventoryActivityRepository),
                    DemandProcessor(
                        DemandRepositoryImpl(dsl, statsigFeatureFlagClient),
                        SkuInputDataRepositoryImpl(dsl, dcConfigService),
                        statsigFeatureFlagClient,
                    ),
                    inventoryService = InventoryService(
                        inventoryRepository,
                        liveInventoryRepository,
                        inventoryActivityRepository,
                        statsigFeatureFlagClient,
                    ),
                    inventoryRepository,
                ),
                emptySet(),
            ).run()
        }

        val liveSnapshotsMap = runBlocking {
            inventoryLiveSnapshotRepository.fetchLiveInventorySnapshot(
                setOf(newDcConfigRecord.dcCode),
                DateRange(yesterdaySnapshotTime.toLocalDate(), todaySnapshotTime.toLocalDate()),
            )
        }.associateBy { it.snapshotTime.toLocalDate() }

        assertEquals(
            liveYesterdaySnapshot.let { it.copy(snapshotTime = it.snapshotTime.truncatedTo(SECONDS)) },
            liveSnapshotsMap[yesterdaySnapshotTime.toLocalDate()]!!.let {
                it.copy(
                    snapshotTime = it.snapshotTime.truncatedTo(SECONDS),
                )
            },
        )

        val newTodayLive = liveSnapshotsMap[todaySnapshotTime.toLocalDate()]!!

        assertEquals(todaySnapshot.snapshotId, newTodayLive.snapshotId)
        assertEquals(todaySnapshot.snapshotTime.truncatedTo(SECONDS), newTodayLive.snapshotTime.truncatedTo(SECONDS))

        assertEquals(1, newTodayLive.skus.count())
        assertTrue(newTodayLive.skus.all { it.skuId == skuId })

        assertEquals(3, newTodayLive.skus.first().inventory.size)
        assertEquals(
            inventoryMovementValue.quantity.getValue() + yesterdayLiveInventory.qty.getValue(),
            newTodayLive.skus.first().inventory.filter { it.isStaging() }.sumOf { it.qty.getValue() },
        )

        assertEquals(todayInventory.qty, newTodayLive.skus.first().inventory.first { it.isStorage() }.qty)
    }
}
