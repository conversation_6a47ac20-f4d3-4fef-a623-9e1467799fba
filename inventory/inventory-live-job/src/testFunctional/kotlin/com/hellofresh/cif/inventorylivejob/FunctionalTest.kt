package com.hellofresh.cif.inventorylivejob

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.inventory.InventoryActivityRepository
import com.hellofresh.cif.inventory.InventoryActivityRepositoryImpl
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventory.LiveInventoryRepositoryImpl
import com.hellofresh.cif.inventory_live_job.schema.Tables.DC_CONFIG
import com.hellofresh.cif.inventory_live_job.schema.Tables.INVENTORY_ACTIVITY
import com.hellofresh.cif.inventory_live_job.schema.Tables.INVENTORY_LIVE_SNAPSHOT
import com.hellofresh.cif.inventory_live_job.schema.Tables.INVENTORY_SNAPSHOT
import com.hellofresh.cif.inventory_live_job.schema.enums.InventoryActivityType.MOV
import com.hellofresh.cif.inventory_live_job.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.inventory_live_job.schema.tables.records.InventoryActivityRecord
import com.hellofresh.cif.inventory_live_job.schema.tables.records.InventoryLiveSnapshotRecord
import com.hellofresh.cif.inventory_live_job.schema.tables.records.InventorySnapshotRecord
import com.hellofresh.cif.inventorylivejob.repository.InventoryLiveSnapshotRepository
import com.hellofresh.cif.inventorylivejob.repository.impl.InventoryLiveSnapshotRepositoryImpl
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.InventoryAdjustmentTypeId.PCK
import com.hellofresh.inventory.models.InventoryMovementValue
import com.hellofresh.inventory.models.InventoryValue
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_PRODUCTION
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_UNSPECIFIED
import com.hellofresh.inventory.models.inventory.live.LiveInventorySnapshot
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.JSONB
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {
    val newDcConfigRecord = DcConfigRecord(
        "VE", "DACH", "MONDAY", "FRIDAY", "Europe/Berlin",
        true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, LocalTime.now(), null, LocalTime.now(),
        emptyArray()
    )

    fun createInventoryLiveSnapshotRecord(inventoryLiveSnapshot: LiveInventorySnapshot, dcZoneId: ZoneId = UTC) =
        with(inventoryLiveSnapshot) {
            InventoryLiveSnapshotRecord(
                dcCode,
                snapshotTime.toLocalDate(),
                skus.first().skuId,
                snapshotId,
                snapshotTime.atZone(dcZoneId).toOffsetDateTime(),
                JSONB.valueOf(objectMapper.writeValueAsString(InventoryValue(skus.flatMap { it.inventory }))),
                LocalDateTime.now(UTC),
                LocalDateTime.now(UTC),
                skus.first().cleardownTime?.atZone(dcZoneId)?.toOffsetDateTime(),
            )
        }

    fun createInventorySnapshotRecord(
        inventorySnapshot: com.hellofresh.inventory.models.InventorySnapshot,
        dcZoneId: ZoneId = UTC
    ) =
        with(inventorySnapshot) {
            InventorySnapshotRecord(
                dcCode,
                snapshotTime.toLocalDate(),
                skus.first().skuId,
                snapshotId,
                JSONB.valueOf(
                    objectMapper.writeValueAsString(
                        com.hellofresh.inventory.models.InventoryValue(
                            skus.flatMap {
                                it.inventory
                            },
                        ),
                    ),
                ),
                LocalDateTime.now(UTC),
                LocalDateTime.now(UTC),
                snapshotTime.atZone(dcZoneId).toOffsetDateTime(),
            )
        }

    fun createInventoryMovementRecord(inventoryMovementValue: InventoryMovementValue = createInventoryMovementValue()) = InventoryActivityRecord().apply {
        hashMessage = "0"
        activityId = UUID.randomUUID()
        activityTime = OffsetDateTime.now(UTC)
        dcCode = newDcConfigRecord.dcCode
        skuId = UUID.randomUUID()
        type = MOV
        typeId = PCK.name
        value = JSONB.valueOf(
            objectMapper.writeValueAsString(inventoryMovementValue),
        )
        publishedTime = OffsetDateTime.now(UTC)
    }

    fun createInventoryMovementValue() = InventoryMovementValue(
        quantity = SkuQuantity.fromBigDecimal(BigDecimal(100)),
        originLocationId = "origin-mov-location-1",
        originLocationType = LOCATION_TYPE_UNSPECIFIED,
        destinationLocationId = "mov-dest-location-1",
        destinationLocationType = LOCATION_TYPE_PRODUCTION,
        expirationDate = null,
        remainingQuantity = SkuQuantity.fromBigDecimal(BigDecimal.ZERO),
        transportModuleId = "movTmId",
        poNumber = "poNumber",
    )

    @AfterEach
    fun clear() {
        dsl.deleteFrom(INVENTORY_LIVE_SNAPSHOT).execute()
        dsl.deleteFrom(INVENTORY_SNAPSHOT).execute()
        dsl.deleteFrom(INVENTORY_ACTIVITY).execute()
        dsl.deleteFrom(DC_CONFIG).execute()
    }

    companion object {
        lateinit var dsl: MetricsDSLContext
        lateinit var inventoryLiveSnapshotRepository: InventoryLiveSnapshotRepository
        lateinit var inventoryActivityRepository: InventoryActivityRepository
        lateinit var dcConfigService: DcConfigService
        lateinit var inventoryRepository: InventoryRepositoryImpl
        lateinit var liveInventoryRepository: LiveInventoryRepositoryImpl
        val objectMapper = jacksonObjectMapper().findAndRegisterModules()
        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newFixedThreadPool(2))
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            dcConfigService = DcConfigService(SimpleMeterRegistry(), DcRepositoryImpl(dsl))
            inventoryLiveSnapshotRepository = InventoryLiveSnapshotRepositoryImpl(dsl, dsl, dcConfigService)
            inventoryActivityRepository = InventoryActivityRepositoryImpl(dsl)
            inventoryRepository = InventoryRepositoryImpl(dsl)
            liveInventoryRepository = LiveInventoryRepositoryImpl(dsl)
        }
    }
}
