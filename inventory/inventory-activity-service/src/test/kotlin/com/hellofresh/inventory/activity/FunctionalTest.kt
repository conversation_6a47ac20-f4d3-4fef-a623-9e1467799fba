package com.hellofresh.inventory.activity

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.databind.ObjectMapper
import com.google.protobuf.Timestamp
import com.google.type.Decimal
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.inventory.activity.schema.Tables
import com.hellofresh.cif.inventory.activity.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.sku_inputs_lib.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.sku_inputs_lib.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.dateUtil.models.toProtoTimestamp
import com.hellofresh.inventory.activity.repository.InventoryActivityRepository
import com.hellofresh.proto.service.ordering.purchaseOrder.v1beta1.PurchaseOrderNumber
import com.hellofresh.proto.service.ordering.purchaseOrder.v1beta1.PurchaseOrderRevision
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.LocationType
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.StockState
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.StockState.STOCK_STATE_ACTIVE
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityValue
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityValue.InventoryAdjustment
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityValue.InventoryMovement
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.Random
import java.util.UUID
import java.util.concurrent.Executors
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.INVENTORY_ACTIVITY).execute()
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
        dsl.deleteFrom(SKU_SPECIFICATION).execute()
    }

    companion object {

        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()

        lateinit var dsl: MetricsDSLContext
        lateinit var dcConfigRepository: DcRepository
        lateinit var dcConfigService: DcConfigService
        lateinit var skuInputDataRepository: SkuInputDataRepository
        lateinit var inventoryActivityRepository: InventoryActivityRepository

        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            dcConfigRepository = DcRepositoryImpl(dsl)
            dcConfigService = DcConfigService(SimpleMeterRegistry(), dcConfigRepository)
            skuInputDataRepository = SkuInputDataRepositoryImpl(dsl, dcConfigService)
            inventoryActivityRepository = InventoryActivityRepository(dsl)
        }

        fun createInventoryActivityMovement(
            dcCode: String,
            skuCode: String,
            stockState: StockState = STOCK_STATE_ACTIVE,
            expirationTime: Timestamp? = OffsetDateTime.now(ZoneOffset.UTC).plusDays(10).toProtoTimestamp(),
            block: InventoryActivityValue.Builder.() -> Unit = {}
        ) =
            InventoryActivityValue.newBuilder().apply {
                activityId = UUID.randomUUID().toString()
                activityTime = OffsetDateTime.now(ZoneOffset.UTC).toProtoTimestamp()
                reasonCode = UUID.randomUUID().toString()
                distributionCenterCode = dcCode
                this.skuCode = skuCode
                this.stockState = stockState
                expirationTime?.also { this.expirationTime = expirationTime }
                inventoryMovement = InventoryMovement.newBuilder().apply {
                    quantity = Decimal.newBuilder().setValue("25.6").build()
                    movementTypeId = UUID.randomUUID().toString()
                    movementTypeName = UUID.randomUUID().toString()
                    originLocationId = UUID.randomUUID().toString()
                    originLocationType = LocationType.LOCATION_TYPE_STAGING
                    destinationLocationId = UUID.randomUUID().toString()
                    destinationLocationType = LocationType.LOCATION_TYPE_STORAGE
                    remainingQuantity = Decimal.newBuilder().setValue("5").build()
                    if (Random().nextBoolean()) transportModuleId = UUID.randomUUID().toString()
                }.build()
                if (Random().nextBoolean()) {
                    purchaseOrderRevision = PurchaseOrderRevision.newBuilder().setNumber(
                        PurchaseOrderNumber.newBuilder().setFormatted("poN001Adj"),
                    ).build()
                }

                publishedTime = OffsetDateTime.now(ZoneOffset.UTC).plusMinutes(10).toProtoTimestamp()
            }.also(block)
                .build()

        fun createInventoryActivityAdjustment(dcCode: String, skuCode: String) =
            InventoryActivityValue.newBuilder().apply {
                activityId = UUID.randomUUID().toString()
                reasonCode = UUID.randomUUID().toString()
                activityTime = OffsetDateTime.now(ZoneOffset.UTC).toProtoTimestamp()
                distributionCenterCode = dcCode
                this.skuCode = skuCode
                stockState = STOCK_STATE_ACTIVE
                expirationTime = OffsetDateTime.now(ZoneOffset.UTC).plusDays(10).toProtoTimestamp()
                inventoryAdjustment = InventoryAdjustment.newBuilder().apply {
                    stockChangeQty = Decimal.newBuilder().setValue("100.5").build()
                    activityTypeId = UUID.randomUUID().toString()
                    activityTypeName = UUID.randomUUID().toString()
                    locationId = UUID.randomUUID().toString()
                    locationType = LocationType.LOCATION_TYPE_STAGING
                    remainingQuantity = Decimal.newBuilder().setValue("50").build()
                    if (Random().nextBoolean()) transportModuleId = UUID.randomUUID().toString()
                }.build()
                if (Random().nextBoolean()) {
                    purchaseOrderRevision = PurchaseOrderRevision.newBuilder().setNumber(
                        PurchaseOrderNumber.newBuilder().setFormatted("poN001Adj"),
                    ).build()
                }
                publishedTime = OffsetDateTime.now(ZoneOffset.UTC).plusMinutes(10).toProtoTimestamp()
            }.build()

        fun createDcConfig() =
            DcConfigRecord(
                "DC", "DACH", "MONDAY", "FRIDAY", "Europe/Berlin",
                true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null, LocalTime.now(),
                emptyArray()
            )

        fun createSkuSpecificationRecord(
            market: String
        ) = SkuSpecificationRecord().apply {
            id = UUID.randomUUID()
            parentId = UUID.randomUUID()
            name = "Name"
            code = UUID.randomUUID().toString()
            category = "SPI"
            acceptableCodeLife = 0
            coolingType = ""
            packaging = ""
            this.market = market
            uom = UOM_LBS
        }

        fun refreshSkuView() =
            dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()
    }
}
