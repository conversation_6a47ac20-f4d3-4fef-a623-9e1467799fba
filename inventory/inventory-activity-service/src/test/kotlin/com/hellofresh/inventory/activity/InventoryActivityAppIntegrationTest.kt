package com.hellofresh.inventory.activity

import InfraPreparation.createKafkaProducer
import InfraPreparation.startKafkaAndCreateTopics
import com.hellofresh.cif.inventory.activity.schema.Tables.INVENTORY_ACTIVITY
import com.hellofresh.cif.inventory.activity.schema.enums.InventoryActivityType
import com.hellofresh.cif.inventory.activity.schema.tables.records.InventoryActivityRecord
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.inventory.activity.service.InventoryActivityConsumer
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityKey
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityValue
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Duration
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.Serializer
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.testcontainers.containers.KafkaContainer
import org.testcontainers.shaded.org.awaitility.Awaitility

class InventoryActivityAppIntegrationTest : FunctionalTest() {

    private val dcConfigRecord = createDcConfig()
    private val skuSpecificationRecord = createSkuSpecificationRecord(dcConfigRecord.market)

    @Test
    fun `should consume from inventory activity topic and process records`() {
        // given
        dsl.batchInsert(dcConfigRecord, skuSpecificationRecord).execute()
        refreshSkuView()

        val key = InventoryActivityKey.newBuilder().apply {
            this.skuCode = skuSpecificationRecord.code
            this.distributionCenterCode = dcConfigRecord.dcCode
        }.build()

        val value = createInventoryActivityMovement(dcConfigRecord.dcCode, skuSpecificationRecord.code)

        val record = ProducerRecord(TOPIC_NAME, key, value)

        // when
        topicProducer.send(record).get()

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                launchProcessor(
                    1,
                    meterRegistry,
                    InventoryActivityConsumer(
                        dcConfigService,
                        skuInputDataRepository,
                        inventoryActivityRepository,
                    ),
                    pollConfig,
                    consumerProcessorConfig(consumerConfig),
                )
            }

            val records = mutableListOf<InventoryActivityRecord>()
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    val results = dsl.selectFrom(INVENTORY_ACTIVITY).fetch()
                    records.addAll(results)
                    records.count() == 1
                }

            app.cancel()

            with(records.first()) {
                assertEquals(value.activityId, activityId.toString())
                assertEquals(value.distributionCenterCode, dcCode)
                assertEquals(skuId, this.skuId)
                assertEquals(InventoryActivityType.MOV, type)
                assertEquals(value.reasonCode, typeId)
            }
        }
    }

    companion object {
        private lateinit var topicProducer: KafkaProducer<InventoryActivityKey, InventoryActivityValue>
        private lateinit var consumerConfig: Map<String, String>
        private lateinit var pollConfig: PollConfig

        private lateinit var kafka: KafkaContainer

        private val meterRegistry = SimpleMeterRegistry()

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            kafka = startKafkaAndCreateTopics(
                listOf(TOPIC_NAME),
            )

            topicProducer = createKafkaProducer(
                kafka,
                Serializer<InventoryActivityKey> { _, data -> data.toByteArray() },
                Serializer<InventoryActivityValue> { _, data -> data.toByteArray() },
            )

            consumerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                    ConsumerConfig.GROUP_ID_CONFIG to "csku-inventory-forecast.inventory-activity-service-test-integration.v1",
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
                )
            pollConfig = PollConfig(
                Duration.ofSeconds(1).toKotlinDuration(),
                100,
                Duration.ofSeconds(15).toKotlinDuration(),
            )
        }
    }
}
