package com.hellofresh.inventory.activity.service

import com.google.protobuf.Timestamp
import com.google.protobuf.util.Timestamps
import com.google.type.Decimal
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.skuinput.repo.DcSkuCodeKey
import com.hellofresh.dateUtil.models.isNotDefaultValues
import com.hellofresh.dateUtil.models.toOffsetDateTime
import com.hellofresh.dateUtil.models.toProtoTimestamp
import com.hellofresh.inventory.activity.FunctionalTest.Companion.createInventoryActivityAdjustment
import com.hellofresh.inventory.activity.FunctionalTest.Companion.createInventoryActivityMovement
import com.hellofresh.inventory.activity.service.InventoryActivityMapper.toInventoryActivity
import com.hellofresh.inventory.models.InventoryActivity
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.LocationType
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.StockState.STOCK_STATE_ACTIVE
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityValue
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityValue.InventoryMovement
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.default
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.time.temporal.ChronoUnit.SECONDS
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class InventoryActivityMapperTest {

    private val dcConfig = DistributionCenterConfiguration.Companion.default()
    private val skuSpecificationPair = UUID.randomUUID() to SkuSpecification.default
    private val skuLookLookUp = mapOf(
        DcSkuCodeKey(dcConfig.dcCode, skuSpecificationPair.second.skuCode) to skuSpecificationPair,
    )

    @Test fun `inventory activity adjustment topic value  is mapped to domain model`() {
        val recordValue = createInventoryActivityAdjustment(dcConfig.dcCode, skuSpecificationPair.second.skuCode)
        with(
            toInventoryActivity(
                recordValue,
                dcConfig,
                skuLookLookUp,
            )!! as com.hellofresh.inventory.models.InventoryAdjustment,
        ) {
            assertInventoryActivity(recordValue, this)
            assertEquals(recordValue.reasonCode, typeId)
            assertEquals(
                recordValue.inventoryAdjustment.stockChangeQty.value.toBigDecimal(),
                quantity.getValue(),
            )
            assertEquals(recordValue.inventoryAdjustment.locationId, locationId)
            assertEquals(recordValue.inventoryAdjustment.locationType.name, locationType.name)
            assertEquals(
                recordValue.inventoryAdjustment.remainingQuantity.value.toBigDecimal(),
                remainingQuantity.getValue()
            )
            assertEquals(
                if (recordValue.inventoryAdjustment.hasTransportModuleId()) recordValue.inventoryAdjustment.transportModuleId else null,
                transportModuleId,
            )
            assertEquals(
                if (recordValue.hasPurchaseOrderRevision()) recordValue.purchaseOrderRevision.number.formatted else null,
                poNumber,
            )
        }
    }

    @Test fun `inventory activity movement topic value is mapped to domain model`() {
        val recordValue = createInventoryActivityMovement(dcConfig.dcCode, skuSpecificationPair.second.skuCode)

        with(
            toInventoryActivity(
                recordValue,
                dcConfig,
                skuLookLookUp,
            )!! as com.hellofresh.inventory.models.InventoryMovement,
        ) {
            assertInventoryActivity(recordValue, this)
            assertEquals(
                recordValue.inventoryMovement.quantity.value.toBigDecimal(),
                quantity.getValue(),
            )
            assertEquals(recordValue.reasonCode, typeId)
            assertEquals(recordValue.inventoryMovement.originLocationId, originLocationId)
            assertEquals(recordValue.inventoryMovement.originLocationType.name, originLocationType.name)
            assertEquals(recordValue.inventoryMovement.destinationLocationId, destinationLocationId)
            assertEquals(recordValue.inventoryMovement.destinationLocationType.name, destinationLocationType.name)
            assertEquals(
                recordValue.inventoryMovement.remainingQuantity.value.toBigDecimal(),
                remainingQuantity.getValue()
            )
            assertEquals(
                if (recordValue.inventoryMovement.hasTransportModuleId()) recordValue.inventoryMovement.transportModuleId else null,
                transportModuleId,
            )
            assertEquals(
                if (recordValue.hasPurchaseOrderRevision()) recordValue.purchaseOrderRevision.number.formatted else null,
                poNumber,
            )
        }
    }

    @ParameterizedTest
    @MethodSource("invalidExpirationTimes")
    fun `inventory activity expiration date is optional`(expirationTime: Timestamp?) {
        val recordValue = createInventoryActivityMovement(
            dcCode = dcConfig.dcCode,
            skuCode = skuSpecificationPair.second.skuCode,
            expirationTime = expirationTime,
        )

        with(
            toInventoryActivity(
                recordValue,
                dcConfig,
                skuLookLookUp,
            )!! as com.hellofresh.inventory.models.InventoryMovement,
        ) {
            assertInventoryActivity(recordValue, this)
        }
    }

    @Test fun `inventory activity topic value can not be mapped if sku mapping is missing`() {
        val recordValue = InventoryActivityValue.newBuilder().apply {
            activityId = UUID.randomUUID().toString()
            activityTime = OffsetDateTime.now(ZoneOffset.UTC).toProtoTimestamp()
            distributionCenterCode = dcConfig.dcCode
            stockState = STOCK_STATE_ACTIVE
            expirationTime = OffsetDateTime.now(ZoneOffset.UTC).plusDays(10).toProtoTimestamp()
            inventoryMovement = InventoryMovement.newBuilder().apply {
                quantity = Decimal.newBuilder().setValue("25.6").build()
                movementTypeId = UUID.randomUUID().toString()
                movementTypeName = UUID.randomUUID().toString()
                originLocationId = UUID.randomUUID().toString()
                originLocationType = LocationType.LOCATION_TYPE_STAGING
                destinationLocationId = UUID.randomUUID().toString()
                destinationLocationType = LocationType.LOCATION_TYPE_STORAGE
                remainingQuantity = Decimal.newBuilder().setValue("5").build()
            }.build()
            publishedTime = OffsetDateTime.now(ZoneOffset.UTC).plusMinutes(10).toProtoTimestamp()
        }.build()
        assertNull(toInventoryActivity(recordValue, dcConfig, emptyMap()))
    }

    private fun assertInventoryActivity(recordValue: InventoryActivityValue, inventoryActivity: InventoryActivity) {
        with(inventoryActivity) {
            assertEquals(recordValue.activityId, activityId.toString())
            assertEquals(
                recordValue.activityTime.toOffsetDateTime(dcConfig.zoneId).truncatedTo(SECONDS),
                activityTime.truncatedTo(SECONDS),
            )
            assertEquals(recordValue.distributionCenterCode, dcCode)
            assertEquals(skuSpecificationPair.first, skuId)
            assertEquals(
                if (recordValue.expirationTime.isNotDefaultValues()) {
                    recordValue.expirationTime.toOffsetDateTime(dcConfig.zoneId).toLocalDate()
                } else {
                    null
                },
                expirationDate,
            )
            assertEquals(
                recordValue.publishedTime.toOffsetDateTime(dcConfig.zoneId).truncatedTo(SECONDS),
                publishedTime.truncatedTo(SECONDS),
            )
        }
    }

    companion object {
        @JvmStatic
        fun invalidExpirationTimes() =
            listOf(
                null,
                Timestamp.getDefaultInstance(),
                Timestamps.MAX_VALUE,
                Timestamps.MIN_VALUE,
            )
    }
}
