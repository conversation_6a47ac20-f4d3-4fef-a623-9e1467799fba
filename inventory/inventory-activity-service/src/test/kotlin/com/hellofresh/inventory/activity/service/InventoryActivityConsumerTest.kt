package com.hellofresh.inventory.activity.service

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.skuinput.repo.DcSkuCodeKey
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.inventory.activity.FunctionalTest.Companion.createInventoryActivityMovement
import com.hellofresh.inventory.activity.repository.InventoryActivityMessage
import com.hellofresh.inventory.activity.repository.InventoryActivityRepository
import com.hellofresh.proto.service.ordering.purchaseOrder.v1beta1.PurchaseOrderNumber
import com.hellofresh.proto.service.ordering.purchaseOrder.v1beta1.PurchaseOrderRevision
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.StockState.STOCK_STATE_LOST
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityKey
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityValue
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.default
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.mockk
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class InventoryActivityConsumerTest {

    private val meterRegistry = SimpleMeterRegistry()
    private val dcConfigRepository = mockk<DcRepository>()
    private val dcConfigService = DcConfigService(meterRegistry, dcConfigRepository)
    private val skuInputDataRepository = mockk<SkuInputDataRepository>()
    private val inventoryActivityRepository = mockk<InventoryActivityRepository>(relaxed = true)

    private val inventoryActivityConsumer = InventoryActivityConsumer(
        dcConfigService,
        skuInputDataRepository,
        inventoryActivityRepository,
    )
    private val distributionCenterConfiguration = DistributionCenterConfiguration.default()
    private val distributionCenterConfigurationForSY = DistributionCenterConfiguration.default("SY")
    private val skuSpecificationPair = UUID.randomUUID() to SkuSpecification.default
    private val skuSpecificationPairForSY = UUID.randomUUID() to SkuSpecification.default

    private val key = InventoryActivityKey.newBuilder().build()
    private val value = createInventoryActivityMovement(
        distributionCenterConfiguration.dcCode,
        skuSpecificationPair.second.skuCode,
    )

    private val skuLookUpMap = mapOf(
        DcSkuCodeKey(distributionCenterConfiguration.dcCode, skuSpecificationPair.second.skuCode) to skuSpecificationPair,
        DcSkuCodeKey(distributionCenterConfigurationForSY.dcCode, skuSpecificationPairForSY.second.skuCode) to skuSpecificationPairForSY,
    )

    private fun consumerRecords(key: InventoryActivityKey, value: InventoryActivityValue) =
        ConsumerRecords(mapOf(mockk<TopicPartition>() to mutableListOf(ConsumerRecord("", 0, 0, key, value))))

    @BeforeEach
    fun beforeEach() {
        coEvery { dcConfigRepository.fetchDcConfigurations() } returns listOf(
            distributionCenterConfiguration,
        )
        coEvery {
            skuInputDataRepository.fetchSkuLookUp(
                setOf(
                    distributionCenterConfiguration.dcCode,
                ),
            )
        } returns skuLookUpMap
    }

    @Test
    fun `active inventory activity messages are consumed`() {
        runBlocking {
            inventoryActivityConsumer(consumerRecords(key, value))

            val expectedInventoryActivity = InventoryActivityMapper.toInventoryActivity(
                value,
                distributionCenterConfiguration,
                skuLookUpMap,
            )
            coVerify(exactly = 1) {
                inventoryActivityRepository.save(
                    withArg<List<InventoryActivityMessage>> {
                        assertEquals(1, it.size)
                        assertEquals(
                            expectedInventoryActivity,
                            it.first().inventoryActivity,
                        )
                    },
                )
            }
        }
    }

    @Test
    fun `non active inventory activity messages are filtered`() {
        val nonActiveValue = value.toBuilder().setStockState(STOCK_STATE_LOST).build()

        runBlocking {
            inventoryActivityConsumer(consumerRecords(key, nonActiveValue))
            coVerify(exactly = 0) {
                inventoryActivityRepository.save(any())
            }
        }
    }

    @Test
    fun `inventory activity messages with unknown dcs are filtered`() {
        val unknownDcKey = key.toBuilder().setDistributionCenterCode(UUID.randomUUID().toString()).build()
        val unknownDcValue = value.toBuilder().setDistributionCenterCode(key.distributionCenterCode).build()

        runBlocking {
            inventoryActivityConsumer(consumerRecords(unknownDcKey, unknownDcValue))
            coVerify(exactly = 0) {
                inventoryActivityRepository.save(any())
            }
        }
    }

    @Test
    fun `inventory activity messages with unknown sku mapping are filtered`() {
        val unknownSkuCodeKey = key.toBuilder().setSkuCode(UUID.randomUUID().toString()).build()
        val unknownSkuCodeValue = value.toBuilder().setSkuCode(unknownSkuCodeKey.skuCode).build()
        runBlocking {
            inventoryActivityConsumer(consumerRecords(unknownSkuCodeKey, unknownSkuCodeValue))
            coVerify(exactly = 0) {
                inventoryActivityRepository.save(any())
            }
        }
    }

    @Test
    fun `inventory activity messages with po ref containing WA for dc code SY are not included`() {
        coEvery { dcConfigRepository.fetchDcConfigurations() } returns listOf(
            distributionCenterConfigurationForSY,
        )
        coEvery {
            skuInputDataRepository.fetchSkuLookUp(
                setOf(
                    distributionCenterConfigurationForSY.dcCode,
                ),
            )
        } returns mapOf(
            DcSkuCodeKey(distributionCenterConfigurationForSY.dcCode, skuSpecificationPairForSY.second.skuCode) to skuSpecificationPairForSY,
        )
        val keyWithSYDcCode = key.toBuilder().setDistributionCenterCode("SY").build()
        val valueWithSYDcCode = value.toBuilder().setDistributionCenterCode("SY")
            .setPurchaseOrderRevision(
                PurchaseOrderRevision.newBuilder().setNumber(
                    PurchaseOrderNumber.newBuilder().setFormatted("2441WA473435").build(),
                ),
            ).build()
        runBlocking {
            inventoryActivityConsumer(consumerRecords(keyWithSYDcCode, valueWithSYDcCode))
            coVerify(exactly = 0) {
                inventoryActivityRepository.save(any())
            }
        }
    }
}
