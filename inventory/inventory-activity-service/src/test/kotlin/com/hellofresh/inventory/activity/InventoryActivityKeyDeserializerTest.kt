package com.hellofresh.inventory.activity

import com.google.type.Decimal
import com.hellofresh.dateUtil.models.toProtoTimestamp
import com.hellofresh.inventory.activity.deserializer.InventoryActivityKeyDeserializer
import com.hellofresh.inventory.activity.deserializer.InventoryActivityValueDeserializer
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.LocationType
import com.hellofresh.proto.shared.distributionCenter.inventory.v1.StockState.STOCK_STATE_ACTIVE
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityKey
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityValue
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityValue.InventoryAdjustment
import com.hellofresh.proto.stream.distributionCenter.inventory.activity.v1.InventoryActivityValue.InventoryMovement
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.assertThrows

class InventoryActivityKeyDeserializerTest {
    @Test
    fun `inventory activity Key is deserialized`() {
        val recordKey = InventoryActivityKey.newBuilder().apply {
            distributionCenterCode = UUID.randomUUID().toString()
            skuCode = UUID.randomUUID().toString()
        }.build()

        val key = recordKey.toByteArray()
        with(InventoryActivityKeyDeserializer.deserialize("", key)) {
            assertEquals(recordKey.distributionCenterCode, distributionCenterCode)
            assertEquals(recordKey.skuCode, skuCode)
        }
    }

    @Test
    fun `inventory Activity adjustment is deserialized`() {
        val expectedActivityTime = OffsetDateTime.now(UTC)
        val expectedPublishedTime = expectedActivityTime.plusMinutes(10)
        val expectedExpirationTime = expectedActivityTime.plusDays(10)

        val recordValue = InventoryActivityValue.newBuilder().apply {
            activityId = UUID.randomUUID().toString()
            activityTime = expectedActivityTime.toProtoTimestamp()
            stockState = STOCK_STATE_ACTIVE
            expirationTime = expectedExpirationTime.toProtoTimestamp()
            inventoryAdjustment = InventoryAdjustment.newBuilder().apply {
                stockChangeQty = Decimal.newBuilder().setValue("100.5").build()
                activityTypeId = UUID.randomUUID().toString()
                activityTypeName = UUID.randomUUID().toString()
                locationId = UUID.randomUUID().toString()
                locationType = LocationType.LOCATION_TYPE_STAGING
                remainingQuantity = Decimal.newBuilder().setValue("50").build()
            }.build()
            publishedTime = expectedPublishedTime.toProtoTimestamp()
        }.build()

        val value = recordValue.toByteArray()
        assertEquals(recordValue, InventoryActivityValueDeserializer.deserialize("", value))
    }

    @Test
    fun `inventory activity movement is deserialized`() {
        val expectedActivityTime = OffsetDateTime.now(UTC)
        val expectedPublishedTime = expectedActivityTime.plusMinutes(10)
        val expectedExpirationTime = expectedActivityTime.plusDays(10)

        val recordValue = InventoryActivityValue.newBuilder().apply {
            activityId = UUID.randomUUID().toString()
            activityTime = expectedActivityTime.toProtoTimestamp()
            stockState = STOCK_STATE_ACTIVE
            expirationTime = expectedExpirationTime.toProtoTimestamp()
            inventoryMovement = InventoryMovement.newBuilder().apply {
                quantity = Decimal.newBuilder().setValue("25.6").build()
                movementTypeId = UUID.randomUUID().toString()
                movementTypeName = UUID.randomUUID().toString()
                originLocationId = UUID.randomUUID().toString()
                originLocationType = LocationType.LOCATION_TYPE_STAGING
                destinationLocationId = UUID.randomUUID().toString()
                destinationLocationType = LocationType.LOCATION_TYPE_STORAGE
                remainingQuantity = Decimal.newBuilder().setValue("5").build()
            }.build()
            publishedTime = expectedPublishedTime.toProtoTimestamp()
        }.build()

        val value = recordValue.toByteArray()

        assertEquals(recordValue, InventoryActivityValueDeserializer.deserialize("", value))
    }

    @Test
    fun `key can not be null`() {
        assertThrows<IllegalStateException> {
            InventoryActivityKeyDeserializer.deserialize("", null)
        }
    }

    @Test
    fun `value can not be null`() {
        assertThrows<IllegalStateException> {
            assertNull(InventoryActivityValueDeserializer.deserialize("", null, null as ByteArray?))
        }
    }
}
