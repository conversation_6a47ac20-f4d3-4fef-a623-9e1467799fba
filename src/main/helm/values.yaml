---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
# Deployment experiment, see build.gradle.kts
tag: 'latest'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

provisionDashboards:
  enabled: true
  dashboardLabel: 'grafana_dashboard'

rds-prom-exporter:
  environment: '@tier@'
  tribe: '@tribe@'
  squad: '@squad@'

  config:
    - instance: 'inventory-db000-@tier@'
      alerts:
        RDSDiskWillFillInXHours: { }
        RDSConnectionNumberHigh: { }
        RDSDiskIsXPercentFull: { }
        RDSCPUUsageHigh:
          threshold: 90
          for: 5m
        RDSMemoryUsageHigh: { }
        RDSDiskReadLatencyIncrease: { }

prometheus-cloudwatch-exporter:
    serviceAccount:
        annotations:
            eks.amazonaws.com/role-arn: 'arn:aws:iam::************:role/csku-inventory-forecast-@tier@-role'

    serviceMonitor:
        enabled: true
        interval: 1m
        labels:
            prometheus: csku-inventory-forecast-sqs-@tier@

    config: |-
        region: eu-west-1
        period_seconds: 60
        delay_seconds: 30
        set_timestamp: false
        use_get_metric_data: true
        metrics:
        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateAgeOfOldestMessage
          aws_statistics: [Maximum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: []

        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateNumberOfMessagesDelayed
          aws_statistics: [Average]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateNumberOfMessagesNotVisible
          aws_statistics: [Average]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: ApproximateNumberOfMessagesVisible
          aws_statistics: [Average]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfEmptyReceives
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfMessagesDeleted
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfMessagesReceived
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: NumberOfMessagesSent
          aws_statistics: [Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]

        - aws_namespace: AWS/SQS
          aws_metric_name: SentMessageSize
          aws_statistics: [Average, Sum]
          aws_dimensions: [QueueName]
          aws_dimension_select_regex:
            QueueName: ["ip_safety_stock_multiplier_file_upload_notifications_@tier@","supply_automation_3pw_inventory_notification_@tier@"]


