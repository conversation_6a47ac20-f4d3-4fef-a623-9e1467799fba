{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": false, "iconColor": "rgba(0, 211, 255, 1)", "name": "Deployment", "target": {"limit": 100, "matchAny": false, "tags": ["cif-csku-inventory-forecast", "Deployments"], "type": "tags"}, "type": "dashboard"}, {"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": false, "iconColor": "rgba(0, 211, 255, 1)", "name": "Deployment", "target": {"limit": 100, "matchAny": false, "tags": ["cif-csku-inventory-forecast", "Deployments"], "type": "tags"}, "type": "dashboard"}, {"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": false, "iconColor": "rgba(0, 211, 255, 1)", "name": "Deployment", "target": {"limit": 100, "matchAny": false, "tags": ["cif-csku-inventory-forecast", "Deployments"], "type": "tags"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3565, "links": [], "panels": [{"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 79, "panels": [{"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "There are currently 3 inventory jobs running actual_consumption_schedule every 2 hours, delete_log_schedule once in a day, purchase_orders_view_schedule every 1 minute, if any of these job fails or did not run at the expected time then displays the cron job failed count.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 4, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 0, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 2}, "id": 80, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.6.0-82840.patch1-83472", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum(procurement_csku_inventory_forecast_sanity_checker_monitor_inventory_cron_jobs) by (type, jobName)", "interval": "", "legendFormat": "{{type}} - {{jobName}}", "range": true, "refId": "A"}], "title": "Inventory cron job status", "type": "timeseries"}], "title": "Inventory Cron Job Status", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 1}, "id": 93, "panels": [{"datasource": {"type": "loki", "uid": "DbAZrmOVz"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 94, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-79828", "targets": [{"datasource": {"type": "loki", "uid": "DbAZrmOVz"}, "editorMode": "code", "expr": "count_over_time({squad=\"supply-automation\", app=\"cif-calculator-job-app\"} |~ \"SkuUom Mismatch:\"[$__range])", "queryType": "range", "refId": "A"}, {"datasource": {"type": "loki", "uid": "DbAZrmOVz"}, "editorMode": "code", "expr": "count_over_time({squad=\"supply-automation\", app=\"cif-goods-received-note-service-app\"} |~ `UOM mismatch for skuId:` [$__range])", "hide": false, "queryType": "range", "refId": "B"}], "title": "SkuUOM Mismatch", "type": "timeseries"}], "title": "SkuUOM Mismatch", "type": "row"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 2}, "id": 77, "panels": [], "title": "Consumer Lag", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "log(lag)", "axisPlacement": "left", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 2, "pointSize": 2, "scaleDistribution": {"log": 2, "type": "log"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line+area"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 500}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 3}, "id": 54, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0-82840.patch1-83472", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "max(kafka_consumergroup_group_lag{group=~\"csku-inventory-forecast.*|cif-kafka-db-sink.*\", consumer_id!~\".*StreamThread.*\"}) by (group)", "format": "time_series", "hide": false, "instant": false, "interval": "", "intervalFactor": 1, "legendFormat": "{{name}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "max(kafka_consumergroup_group_lag{group=~\"sku-demand-forecast.*\", group!~\".*consumer.*\"}) by (group)", "hide": false, "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "B"}], "title": "Consumer lag per consumer", "transparent": true, "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "To view the AWS SQS lag ", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 13}, "id": 104, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0-82840.patch1-83472", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (queue_name) (aws_sqs_approximate_number_of_messages_visible_average{service=\"cif-csku-inventory-forecast-prometheus-cloudwatch-exporter\"})", "hide": false, "instant": false, "legendFormat": "{{queue_name}}", "range": true, "refId": "B"}], "title": "AWS SQS ", "transparent": true, "type": "timeseries"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 23}, "id": 69, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "The number of SKUs that have different demand quantity between their source of truth (OT) and CIF.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 31}, "id": 4, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0-76537", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{production_week=~\"$production_week\", type=\"mismatch_count\", source=\"demand\", dc=~\"$distribution_center\"}) by (dc, production_week)", "interval": "", "legendFormat": "{{dc}} - {{production_week}}", "refId": "A"}], "title": "Demand Mismatch Count / Week (OT vs CIF)", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "The sum of the difference of each SKU demand quantity between the source of truth (OT) and CIF.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 31}, "id": 6, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0-76537", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{production_week=~\"$production_week\", type=\"mismatch_amount\", source=\"demand\", dc=~\"$distribution_center\"}) by (dc, production_week)", "interval": "", "legendFormat": "{{dc}} - {{production_week}}", "refId": "A"}], "title": "Demand Mismatch Amount / Week (OT vs CIF)", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "When the inventory information for a SKU exists in the source of truth (OT) but not in CIF.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "noValue": "0", "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 39}, "id": 8, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0-76537", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{production_week=~\"$production_week\", type=\"missed_in_cif\", source=\"demand\", dc=~\"$distribution_center\"}) by (dc, production_week)", "interval": "", "legendFormat": "{{dc}} - {{production_week}}", "refId": "A"}], "title": "CIF Missing Demand / Week (OT vs CIF)", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "The number of recipes collected in a specific week. This metric indicates at least two things:\n1) How many recipes there's in a specific week for a specific countrh; 2) Problems where we have zero recipes and therefore we won't produce any demand.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 0}, {"color": "green", "value": 1}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 39}, "id": 23, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0-76537", "targets": [{"exemplar": true, "expr": "sku_demand_forecast_collected_recipes{production_week=~\"$production_week\"}", "interval": "", "legendFormat": "{{country}} - {{production_week}}", "refId": "A"}], "title": "SDF Recipes / Week", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 67}, "id": 75, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "expr": "sum(increase(sku_demand_forecast_demand_forecast_tombstone_counter_total{}[1m]))", "refId": "A"}], "title": "Number of Demand Forecast Tombstones in a minute", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 67}, "id": 86, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum(sku_demand_forecast_sku_demand_forecast_sanity_checker_last_updated_at{application=\"demand-matcher-job\", job=\"demand-matcher-job\", service=\"demand-matcher-job\"}) by (dc_code, week)", "range": true, "refId": "A"}], "title": "demand last updatedAt by dc and week", "type": "timeseries"}], "title": "Demand", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 24}, "id": 90, "panels": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 1}, "id": 91, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-79828", "targets": [{"editorMode": "code", "expr": "sum(procurement_csku_inventory_forecast_sanity_checker_sku_id_collision)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Sku ID Collision Metrics", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 1}, "id": 92, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "pluginVersion": "11.4.0-79828", "targets": [{"editorMode": "code", "expr": "sum(procurement_csku_inventory_forecast_sanity_checker_sku_code_market_collision)", "legendFormat": "__auto", "range": true, "refId": "A"}], "title": "Sku Code Market Collision", "type": "timeseries"}], "title": "Sku ID Collision Metrics", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 25}, "id": 73, "panels": [{"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "The number of times a CSKU code to CSKU id mapping was not found", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "exemplar": true, "expr": "sum(irate(procurement_csku_inventory_forecast_missing_csku_code_to_id_mapping_counter_total[10m])) by (application)\n", "instant": false, "interval": "", "legendFormat": "{{application}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "exemplar": true, "expr": "sum(irate(sku_demand_forecast_missing_csku_code_to_id_mapping_counter_total[10m])) by (application)", "hide": false, "interval": "", "legendFormat": "{{application}}", "refId": "B"}], "title": "Missing CSKU code to ID mappings", "type": "timeseries"}, {"fieldConfig": {"defaults": {}, "overrides": []}, "gridPos": {"h": 2, "w": 24, "x": 0, "y": 40}, "id": 39, "options": {"code": {"language": "plaintext", "showLineNumbers": false, "showMiniMap": false}, "content": "<div class=\"dashboard-header text-center\">\n  <span>DC Information known/unknown and enabled/disabled</span>\n</div>", "mode": "html"}, "pluginVersion": "11.3.0-76537", "title": "", "transparent": true, "type": "text"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Record the number of times a DC was unknown", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 62}, "id": 40, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0-76537", "targets": [{"exemplar": false, "expr": "sum(csku_inventory_forecast_unknown_dc_counter_total{dc=~\"$distribution_center\"}) by (dc)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 10, "legendFormat": "{{dc}}", "refId": "B"}], "title": "Unknown DC Count", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "Record the number of times a DC was found disabled", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 62}, "id": 41, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0-76537", "targets": [{"exemplar": false, "expr": "sum(csku_inventory_forecast_disabled_dc_counter_total{dc=~\"$distribution_center\"}) by (dc)", "format": "time_series", "hide": false, "interval": "", "intervalFactor": 10, "legendFormat": "{{dc}}", "refId": "B"}], "title": "Disabled DC Count", "type": "bargauge"}], "title": "Missing mappings for DC and CSKU", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 26}, "id": 67, "panels": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "The number of SKUs that have different PO quantities between their source of truth (PO) and Calculations.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "id": 45, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.0-59882", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{type=\"mismatch_count\", source=\"purchase_order\", dc=~\"$distribution_center\"}) by (dc)", "interval": "", "legendFormat": "{{dc}}", "refId": "A"}], "title": "PO Mismatch Count  (PO vs Calculations) - Graph", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "The number of SKUs that have different PO quantities between their source of truth (PO) and Calculations.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "id": 46, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0-76537", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{type=\"mismatch_count\", source=\"purchase_order\", dc=~\"$distribution_center\", production_week=~\"$production_week\"}) by (dc, production_week)", "interval": "", "legendFormat": "{{dc}} - {{production_week}}", "refId": "A"}], "title": "PO Mismatch Count  (PO vs Calculations) / Week - Bar", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "The sum of the difference of each SKU expected inbound quantity between the source of truth (PO) and Calculations.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 61}, "id": 47, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.0-59882", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{type=\"mismatch_amount\", source=\"purchase_order\", dc=~\"$distribution_center\"}) by (dc)", "interval": "", "legendFormat": "{{dc}}", "refId": "A"}], "title": "PO Mismatch Amount (PO vs Calculations) - Graph", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "The sum of the difference of each SKU expected inbound quantity between the source of truth (PO) and Calculations.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 61}, "id": 48, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0-76537", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{type=\"mismatch_amount\", source=\"purchase_order\", dc=~\"$distribution_center\", production_week=~\"$production_week\"}) by (dc, production_week)", "interval": "", "legendFormat": "{{dc}} - {{production_week}}", "refId": "A"}], "title": "PO Mismatch Amount (PO vs Calculations) / Week - Bar", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "When the PO information for a SKU exists in the source of truth (PO) but not in Calculations.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 69}, "id": 49, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.0-59882", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{type=\"missed_in_cif\", source=\"purchase_order\", dc=~\"$distribution_center\"}) by (dc)", "interval": "", "legendFormat": "{{dc}}", "refId": "A"}], "title": "Calculations Missing POs (PO vs Calculations) - Graph", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "When the PO information for a SKU exists in the source of truth (PO) but not in Calculations.", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 69}, "id": 50, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0-76537", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{type=\"missed_in_cif\", source=\"purchase_order\", dc=~\"$distribution_center\", production_week=~\"$production_week\"}) by (dc, production_week)", "interval": "", "legendFormat": "{{dc}} - {{production_week}}", "refId": "A"}], "title": "Calculations Missing POs (PO vs Calculations) / Week - Bar", "type": "bargauge"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "When the PO information for a SKU exists in Calculations but not in the source of truth (PO Tables).", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 77}, "id": 51, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "multi", "sort": "none"}}, "pluginVersion": "10.2.0-59882", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{type=\"missed_in_source_of_truth\", source=\"purchase_order\", dc=~\"$distribution_center\"}) by (dc)", "interval": "", "legendFormat": "{{dc}}", "refId": "A"}], "title": "SoT Missing POs (PO DB vs Calculations) - Graph", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "When the PO information for a SKU exists in the Calculations but not in the source of truth (PO).", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 77}, "id": 52, "options": {"displayMode": "basic", "legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "maxVizHeight": 300, "minVizHeight": 10, "minVizWidth": 0, "namePlacement": "auto", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true, "sizing": "auto", "text": {}, "valueMode": "color"}, "pluginVersion": "11.3.0-76537", "targets": [{"exemplar": true, "expr": "sum(csku_inventory_forecast_sanity_checker_comparison{type=\"missed_in_source_of_truth\", source=\"purchase_order\", dc=~\"$distribution_center\", production_week=~\"$production_week\"}) by (dc, production_week)", "interval": "", "legendFormat": "{{dc}} - {{production_week}}", "refId": "A"}], "title": "SoT Missing POs (PO vs Calculations) / Week - Bar", "type": "bargauge"}], "title": "Purchase orders", "type": "row"}, {"collapsed": true, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 27}, "id": 81, "panels": [{"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 36}, "id": 82, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0-82840.patch1-83472", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum(procurement_csku_inventory_forecast_sanity_checker_live_inventory_comparison_total{dc=~\"$distribution_center\",locationType=\"staging\"}) by (dc, inventorySource)", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{dc}} - {{inventorySource}}", "range": true, "refId": "A", "useBackend": false}], "title": "Staging Inventory", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 36}, "id": 84, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0-82840.patch1-83472", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum(procurement_csku_inventory_forecast_sanity_checker_live_inventory_comparison_total{dc=~\"$distribution_center\",locationType=\"storage\"}) by (dc, inventorySource)", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{dc}} - {{inventorySource}}", "range": true, "refId": "A", "useBackend": false}], "title": "Storage Inventory", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 64}, "id": 83, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0-82840.patch1-83472", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum(procurement_csku_inventory_forecast_sanity_checker_live_inventory_comparison_skus{dc=~\"$distribution_center\", type=\"missing_snapshot\"}) by (dc)", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{dc}} - inventory", "range": true, "refId": "A", "useBackend": false}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum(csku_inventory_forecast_sanity_checker_live_inventory_comparison_skus{dc=~\"$distribution_center\", type=\"missing_live_snapshot\"}) by (dc)", "fullMetaSearch": false, "hide": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{dc}} - live inventory", "range": true, "refId": "B", "useBackend": false}], "title": "Missing <PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 64}, "id": 85, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.6.0-82840.patch1-83472", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "disableTextWrap": false, "editorMode": "code", "expr": "sum(procurement_csku_inventory_forecast_sanity_checker_live_inventory_comparison_skus{dc=\"$distribution_center\", type=\"mismatch_sku\"}) by (dc)", "fullMetaSearch": false, "includeNullMetadata": true, "instant": false, "legendFormat": "{{dc}}", "range": true, "refId": "A", "useBackend": false}], "title": "Sku Mismatches", "type": "timeseries"}], "title": "Live Inventory", "type": "row"}], "preload": false, "refresh": "", "schemaVersion": 41, "tags": [], "templating": {"list": [{"current": {"text": "metrics_live", "value": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "includeAll": false, "label": "Metrics Datasource", "name": "metrics_ds", "options": [], "query": "prometheus", "refresh": 1, "regex": "/^metrics_/", "type": "datasource"}, {"current": {"text": ["2025-W13"], "value": ["2025-W13"]}, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "definition": "label_values(production_week)", "includeAll": false, "multi": true, "name": "production_week", "options": [], "query": {"query": "label_values(production_week)", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "^\\d{4}-W\\d{2}$", "sort": 1, "type": "query"}, {"current": {"text": ["BV"], "value": ["BV"]}, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "definition": "label_values(dc)", "includeAll": true, "multi": true, "name": "distribution_center", "options": [], "query": {"query": "label_values(dc)", "refId": "StandardVariableQuery"}, "refresh": 1, "regex": "", "sort": 1, "type": "query"}]}, "time": {"from": "now-3h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "SIV Accuracy Metrics", "uid": "IAg60LicgKdHzit6UgpNi6Nz5C9IHUR8N1aKhWr1", "version": 5, "weekStart": ""}