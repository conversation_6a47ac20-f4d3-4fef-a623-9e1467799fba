{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}, {"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": false, "iconColor": "rgba(0, 211, 255, 1)", "name": "Deployment", "target": {"limit": 100, "matchAny": false, "tags": ["cif-csku-inventory-forecast", "Deployments"], "type": "tags"}, "type": "dashboard"}, {"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": false, "iconColor": "rgba(0, 211, 255, 1)", "name": "Deployment", "target": {"limit": 100, "matchAny": false, "tags": ["cif-csku-inventory-forecast", "Deployments"], "type": "tags"}, "type": "dashboard"}, {"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": false, "iconColor": "rgba(0, 211, 255, 1)", "name": "Deployment", "target": {"limit": 100, "matchAny": false, "tags": ["cif-csku-inventory-forecast", "Deployments"], "type": "tags"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3345, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 106, "panels": [], "title": "Core Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "CPU Units", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 1}, "id": 55, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "", "uid": ""}, "exemplar": true, "expr": "sum(rate(container_cpu_usage_seconds_total{namespace=\"scm\", pod=~\"cif-$application.*\", container!=\"POD\", container!=\"\"}[1m])) by (pod)", "interval": "", "legendFormat": "Usage: {{pod}}", "refId": "A"}, {"datasource": {"type": "", "uid": ""}, "expr": "sum(kube_pod_container_resource_limits_cpu_cores{namespace=\"scm\", pod=~\"cif-$application.*\", container!=\"POD\", container!=\"\"}) by (pod)", "hide": false, "interval": "", "legendFormat": "Limit: {{pod}}", "refId": "B"}, {"datasource": {"type": "", "uid": ""}, "expr": "sum(kube_pod_container_resource_requests_cpu_cores{namespace=\"scm\", pod=~\"cif-$application.*\", container!=\"POD\", container!=\"\"}) by (pod)", "interval": "", "legendFormat": "Requests: {{pod}}", "refId": "C"}], "title": "CPU Usage (Pod)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 1}, "id": 57, "options": {"legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "", "uid": ""}, "expr": "sum(increase(container_cpu_cfs_throttled_periods_total{namespace=\"scm\", pod=~\"cif-$application.*\", container!=\"POD\", container!=\"\"}[5m])) by (container, pod, namespace) / sum(increase(container_cpu_cfs_periods_total{namespace=\"scm\", pod=~\"cif-$application.*\", container!=\"POD\", container!=\"\"}[5m])) by (container, pod, namespace)", "interval": "", "legendFormat": "Pod: {{pod}}", "refId": "A"}], "title": "% of CPU Throttle per Container", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 1}, "id": 53, "options": {"legend": {"calcs": ["mean", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "", "uid": ""}, "expr": "sum(procurement_csku_inventory_forecast_system_cpu_usage{application=~\".*$application.*\", pod!=\"\"}) by (application, pod)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "system by pod: {{pod}}", "refId": "A"}, {"datasource": {"type": "", "uid": ""}, "expr": "sum(procurement_csku_inventory_forecast_process_cpu_usage{application=~\".*$application.*\", pod!=\"\"}) by (application, pod)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "process by pod: {{pod}}", "refId": "B"}], "title": "CPU Usage (Application)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 10}, "id": 59, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "", "uid": ""}, "expr": "sum(container_memory_working_set_bytes{namespace=\"scm\",pod=~\"cif-$application.*\", container!=\"POD\", container!=\"\"}) by (application, pod)", "interval": "", "legendFormat": "Usage: {{pod}}", "refId": "A"}, {"datasource": {"type": "", "uid": ""}, "expr": "sum(kube_pod_container_resource_requests_memory_bytes{namespace=\"scm\",pod=~\"cif-$application.*\", container!=\"POD\", container!=\"\"}) by (application, pod)", "hide": false, "interval": "", "legendFormat": "Request: {{pod}}", "refId": "B"}, {"datasource": {"type": "", "uid": ""}, "expr": "sum(kube_pod_container_resource_limits_memory_bytes{namespace=\"scm\",pod=~\"cif-$application.*\", container!=\"POD\", container!=\"\"}) by (application, pod)", "hide": false, "interval": "", "legendFormat": "Limit: {{pod}}", "refId": "C"}], "title": "Memory Usage (Pod)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 8, "y": 10}, "id": 14, "options": {"legend": {"calcs": ["mean", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "expr": "sum(procurement_csku_inventory_forecast_jvm_memory_used_bytes{application=~\".*$application.*\",area=\"heap\"}) by (pod)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "used by pod: {{pod}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "expr": "sum(procurement_csku_inventory_forecast_jvm_memory_committed_bytes{application=~\".*$application.*\",area=\"heap\"}) by (pod)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "committed by pod: {{pod}}", "refId": "B"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "expr": "sum(procurement_csku_inventory_forecast_jvm_memory_max_bytes{application=~\".*$application.*\",area=\"heap\"}) by (pod)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "max by pod: {{pod}}", "refId": "C"}], "title": "JVM <PERSON>", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 16, "y": 10}, "id": 52, "options": {"legend": {"calcs": ["mean", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "", "uid": ""}, "expr": "sum(procurement_csku_inventory_forecast_jvm_memory_used_bytes{application=~\".*$application.*\", pod!=\"\"}) by (application, pod)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "used by pod: {{pod}}", "refId": "A"}, {"datasource": {"type": "", "uid": ""}, "expr": "sum(procurement_csku_inventory_forecast_jvm_memory_committed_bytes{application=~\".*$application.*\", pod!=\"\"}) by (application, pod)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "committed by pod: {{pod}}", "refId": "B"}, {"datasource": {"type": "", "uid": ""}, "expr": "sum(procurement_csku_inventory_forecast_jvm_memory_max_bytes{application=~\".*$application.*\", pod!=\"\"}) by (application, pod)", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "max by pod: {{pod}}", "refId": "C"}], "title": "JVM Total Mem", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 19}, "id": 128, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "irate(procurement_csku_inventory_forecast_jvm_gc_pause_seconds_count{namespace=\"scm\", application=\"$application\"}[5m])", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{action}} [{{cause}}]: {{pod}}", "range": true, "refId": "A"}], "title": "JVM - GC Count", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "s"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 12, "y": 19}, "id": 129, "options": {"legend": {"calcs": ["mean", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "irate(procurement_csku_inventory_forecast_jvm_gc_pause_seconds_sum{namespace=\"scm\", application=\"$application\"}[5m])", "hide": false, "interval": "", "intervalFactor": 2, "legendFormat": "{{action}} [{{cause}}]: {{pod}}", "range": true, "refId": "A"}], "title": "JVM - GC Stop the World Duration", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 29}, "id": 61, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "", "uid": ""}, "expr": "abs(kube_deployment_status_replicas_available{namespace=\"scm\", deployment=~\"cif-$application.*\"})", "interval": "", "legendFormat": "Deployment: {{deployment}}", "refId": "A"}], "title": "# of Replicas", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 29}, "id": 38, "options": {"legend": {"calcs": ["mean", "max", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "", "uid": ""}, "exemplar": true, "expr": "sum(procurement_csku_inventory_forecast_hikaricp_connections) by (application)", "hide": false, "interval": "", "legendFormat": "{{application}}", "refId": "A"}], "title": "DB Connections", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 38}, "id": 39, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "", "uid": ""}, "expr": "sum(increase(procurement_csku_inventory_forecast_hikaricp_connections_timeout_total{}[60m])) by (application)", "format": "time_series", "hide": false, "interval": "", "legendFormat": "$application", "refId": "A"}], "title": "DB Timeout [60m]", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": []}, "gridPos": {"h": 7, "w": 24, "x": 0, "y": 46}, "id": 63, "options": {"legend": {"calcs": ["mean", "max", "min"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "", "uid": ""}, "expr": "avg(istio_slo:service_availability:ratio_rate2m{destination_service_namespace=\"scm\", destination_app=~\"cif-$application.*\", destination_service_name=~\".*\"} > 0)", "interval": "", "legendFormat": "", "refId": "A"}], "title": "System Availability", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 53}, "id": 104, "panels": [], "title": "Kafka Metrics", "type": "row"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 54}, "id": 69, "options": {"legend": {"calcs": ["mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "expr": "max(kafka_consumer_group_rep_lag{service=\"$kafka_service\",service_type=\"kafka\", topic=~\"$kafka_topic\", name!~\"scm.inventory.qa.*\"}) by (topic,name)", "interval": "", "legendFormat": "Consumer Group: {{ name }}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "expr": "max(kafka_consumer_group_rep_lag{service=\"$kafka_service\",service_type=\"kafka\", topic=~\"$kafka_topic\", name=~\"scm.inventory.qa.*\"}) by (topic,name)", "hide": true, "interval": "", "legendFormat": "QA Consumer Group: {{ name }}", "refId": "B"}], "title": "Consumer Lag", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 54}, "id": 71, "options": {"legend": {"calcs": ["mean"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"expr": "sum(rate(kafka_server_BrokerTopicMetrics_MessagesInPerSec_Count{service=\"$kafka_service\", service_type=\"kafka\", topic!~\"__.*\", topic!~\"_.*\", topic!=\"\", topic=~\"$kafka_topic\"}[1m])) by (topic)", "interval": "", "legendFormat": "Topic: {{topic}}", "refId": "A"}], "title": "Inbound Messages (Throughput)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "The average cache hit ratio defined as the ratio of cache read hits over the total cache read requests.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "stepBefore", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["production-calculator  (demand-store)"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 62}, "id": 108, "options": {"legend": {"calcs": [], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"exemplar": false, "expr": "max(csku_inventory_forecast_kafka_stream_record_cache_hit_ratio_avg{application=~\".*$application.*\"}) by (application, record_cache_id)", "interval": "", "legendFormat": "{{application}}  ({{record_cache_id}})", "refId": "A"}], "title": "State Store Cache Hit (Avg)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 62}, "id": 95, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"expr": "max(procurement_csku_inventory_forecast_kafka_consumer_time_between_poll_max{application=~\".*$application.*\"}) by (application, thread)", "interval": "", "legendFormat": "{{application}}-thread-{{thread}}", "refId": "A"}], "title": "Max Time Between Poll per Thread", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 70}, "id": 91, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"expr": "sum(rate(csku_inventory_forecast_component_failure_total{name=\"deserialization\"}[1m])) by (topic)", "interval": "", "legendFormat": "", "refId": "A"}], "title": "# of Deserialization Errors per Topic", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "poll-idle-ratio-avg=time-inside-poll / total-time\n\nA value approaching 1.0 means consumer is idle in poll (ex. waiting for records) while a value approaching 0.0 means the application is busy processing in user code.\n\nA low value (approaching 0.0) could indicate a potential issue or performance bottleneck in user code.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 70}, "id": 97, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "expr": "max(procurement_csku_inventory_forecast_kafka_consumer_poll_idle_ratio_avg{application=~\".*$application.*\"}) by (application, thread)", "interval": "", "legendFormat": "{{application}}-thread-{{thread}}", "range": true, "refId": "A"}], "title": "Max Poll Idle Ratio (avg) per Thread", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 78}, "id": 99, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"expr": "sum(csku_inventory_forecast_kafka_stream_thread_poll_rate{application=~\".*$application.*\"}) by (application,thread)", "interval": "", "legendFormat": "{{application}}-thread-{{thread}}", "refId": "A"}], "title": "Poll Rate per Thread", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 86}, "id": 131, "panels": [], "title": "RDS", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "percentunit"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "/"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 12, "x": 0, "y": 87}, "id": 133, "options": {"legend": {"calcs": ["min", "mean"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "sum by (instance) (node_filesystem_avail_bytes{ job=\"cif-csku-inventory-forecast-rds-prom-exporter-k8s\"}) / sum by (instance) (node_filesystem_size_bytes{ job=\"cif-csku-inventory-forecast-rds-prom-exporter-k8s\"})", "format": "time_series", "interval": "0", "intervalFactor": 2, "legendFormat": "{{ instance }}", "range": true, "refId": "A"}], "title": "Free Storage Space Percentage", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "bytes"}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "/"}, "properties": [{"id": "color", "value": {"fixedColor": "green", "mode": "fixed"}}]}]}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 87}, "id": 140, "options": {"legend": {"calcs": ["lastNotNull", "min"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "node_filesystem_avail_bytes{job=\"cif-csku-inventory-forecast-rds-prom-exporter-k8s\"}", "format": "time_series", "interval": "0", "intervalFactor": 2, "legendFormat": "{{ instance }} | mountpoint: {{ mountpoint }}", "range": true, "refId": "A"}], "title": "Free Storage Space", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 93}, "id": 135, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "rdsosmetrics_diskIO_readIOsPS{job=\"cif-csku-inventory-forecast-rds-prom-exporter-k8s\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ instance }}", "range": true, "refId": "A"}], "title": "Read IOPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "ops"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 93}, "id": 137, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "rdsosmetrics_diskIO_writeIOsPS{job=\"cif-csku-inventory-forecast-rds-prom-exporter-k8s\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ instance }}", "range": true, "refId": "A"}], "title": "Write IOPS", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "The number of I/O requests waiting in the queue before they can reach the disk.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "opm"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 101}, "id": 138, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "aws_rds_disk_queue_depth_average{job=\"cif-csku-inventory-forecast-rds-prom-exporter-k8s\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ instance }}", "range": true, "refId": "A"}], "title": "Disk Queue Depth", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "The average number of bytes read from disk per second for local storage.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 2, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "links": [], "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "binBps"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 101}, "id": 139, "options": {"legend": {"calcs": ["mean", "lastNotNull", "max"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "aws_rds_read_throughput_average{job=\"cif-csku-inventory-forecast-rds-prom-exporter-k8s\"}", "format": "time_series", "interval": "", "intervalFactor": 2, "legendFormat": "{{ instance }}", "range": true, "refId": "A"}], "title": "Read Throughput", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 109}, "id": 73, "panels": [], "title": "Custom Metrics", "type": "row"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 110}, "id": 114, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "exemplar": true, "expr": "histogram_quantile(0.99, sum(increase(procurement_csku_inventory_forecast_import_duration_seconds_bucket{application=\"calculator-job\"}[15m])) by (le,pod))", "interval": "", "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "title": "P99 calculator job duration", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["db-query-dcs-with-weight"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 110}, "id": 112, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "desc"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "max(procurement_csku_inventory_forecast_task_duration_seconds_max{application=\"$application\"}) by (name)", "hide": false, "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "max by (type,name) (procurement_csku_inventory_forecast_jooq_seconds_max{application=\"$application\"})", "hide": false, "legendFormat": "db-{{type}}-{{name}}", "range": true, "refId": "B"}], "title": "Duration of tasks and DB queries", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "Calculator Job Max Dc/Sku candidates", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 14, "x": 0, "y": 118}, "id": 143, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "exemplar": true, "expr": "max(procurement_csku_inventory_forecast_calculator_job_sku_candidates{application=\"calculator-job\"}) by (pod)", "interval": "", "legendFormat": "{{pod}}", "range": true, "refId": "A"}], "title": "calculator job candidates", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "smooth", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 0, "y": 126}, "id": 100, "options": {"legend": {"calcs": ["mean", "sum"], "displayMode": "table", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": false, "expr": "sum(rate(procurement_csku_inventory_forecast_filter_hit_counter_total{application=~\".*$application.*\"}[1m])) by (mode)", "instant": false, "interval": "", "legendFormat": "HIT: {{mode}}", "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": false, "expr": "sum(rate(csku_inventory_forecast_filter_miss_counter_total{application=~\".*$application.*\"}[1m])) by (mode)", "hide": false, "instant": false, "interval": "", "legendFormat": "MISS: {{topic}}", "refId": "B"}], "title": "Record Filtering (Hit & Miss)", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 12, "x": 12, "y": 126}, "id": 117, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(procurement_csku_inventory_forecast_filter_miss_counter_total{name!=\"\"}[5m])) by (mode)", "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 135}, "id": 116, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(procurement_csku_inventory_forecast_filter_hit_counter_total{name!=\"\"}[5m])) by (mode)", "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "Cache Hits Rate", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "Actual consumption service consumes pick 2 light records and persist into the database in batches, whenever there is a failure while writing to the database then this metric will be viewed in Grafana.", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 135}, "id": 142, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum(rate(procurement_csku_inventory_forecast_pick2light_write_failure_total[5m]))", "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}], "title": "Number Of Actual Consumption Data Write Failures", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 143}, "id": 87, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "expr": "sum(increase(cif_inventory_forecast_db_sink_pipeline_processor_0_error[10m])) by (job)", "interval": "", "legendFormat": "Processor: 0", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "expr": "sum(increase(cif_inventory_forecast_db_sink_pipeline_processor_1_error[10m])) by (job)", "interval": "", "legendFormat": "Processor: 1", "range": true, "refId": "B"}], "title": "DB Sink Processor Error", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 143}, "id": 89, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "expr": "sum(increase(cif_inventory_forecast_db_sink_pipeline_processor_0_dropped[10m])) by (job)", "interval": "", "legendFormat": "Job: {{job}}", "range": true, "refId": "A"}], "title": "DB Sink Processor Dropped", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "Record the number of times a DC was unknown", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "never", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 10, "w": 12, "x": 0, "y": 151}, "id": 102, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "multi", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "exemplar": false, "expr": "sum(rate(procurement_csku_inventory_forecast_unknown_dc_counter_total{application=\"$application\"}[1m])) by (dc)", "instant": false, "interval": "", "legendFormat": "hit:{{topic}}", "refId": "A"}], "title": "Unknown DC Count", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 161}, "id": 119, "panels": [], "title": "API Metrics", "type": "row"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 10}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 5, "x": 0, "y": 162}, "id": 123, "options": {"minVizHeight": 75, "minVizWidth": 75, "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showThresholdLabels": false, "showThresholdMarkers": true, "sizing": "auto", "text": {}}, "pluginVersion": "11.5.0-81311", "targets": [{"exemplar": true, "expr": "sum(rate(istio_requests_total{destination_service_name=\"cif-$application-k8s\"}[1m]))", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Request Rate ", "type": "gauge"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "Requests/min by HTTP routes", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": 3600000, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "decimals": 2, "mappings": [], "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short"}, "overrides": []}, "gridPos": {"h": 7, "w": 19, "x": 5, "y": 162}, "id": 121, "options": {"legend": {"calcs": ["max", "mean"], "displayMode": "table", "placement": "right", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": false, "expr": "sum by (route,method,status) (increase(procurement_csku_inventory_forecast_ktor_http_server_requests_seconds_count{application=\"$application\"}[1m])) != 0", "format": "time_series", "instant": false, "interval": "", "legendFormat": "{{method}} {{route}}: [{{status}}]", "range": true, "refId": "A"}], "title": "Requests/min by Route ", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 0, "y": 169}, "id": 141, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "sum(increase(istio_requests_total{destination_service_name=\"cif-$application-k8s\"}[1m])) by (response_code)", "interval": "", "legendFormat": "HTTP {{response_code}}", "range": true, "refId": "A"}], "title": "# Requests (1m)", "type": "timeseries"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 7, "w": 12, "x": 12, "y": 169}, "id": 124, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"exemplar": true, "expr": "histogram_quantile(0.99,max(increase(istio_request_duration_milliseconds_bucket{destination_service_name=\"cif-$application-k8s\"}[1m])) by (le,pod))/1000", "interval": "", "legendFormat": "", "refId": "A"}], "title": "Istio P99 Latency  (1 min)", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 176}, "id": 126, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "exemplar": true, "expr": "max(procurement_csku_inventory_forecast_ktor_http_server_requests_seconds_max{application=\"forecast-api\", route != \"/favicon.ico\"}) by (route)", "interval": "", "legendFormat": "{{route}}", "range": true, "refId": "A"}], "title": "<PERSON><PERSON>ncy (s)", "type": "timeseries"}, {"datasource": {"default": false, "type": "prometheus", "uid": "${metrics_ds}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 176}, "id": 127, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "11.5.0-81311", "targets": [{"datasource": {"type": "prometheus", "uid": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "editorMode": "code", "exemplar": true, "expr": "max(procurement_csku_inventory_forecast_task_duration_seconds_max{application=\"forecast-api\"}) by (name)", "interval": "", "legendFormat": "{{name}}", "range": true, "refId": "A"}, {"datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "editorMode": "code", "expr": "max by (type,name) (csku_inventory_forecast_jooq_seconds_max{application=\"forecast-api\"})", "hide": false, "legendFormat": "db-{{type}}-{{name}}", "range": true, "refId": "B"}], "title": "Task Max Duration (s)", "type": "timeseries"}], "preload": false, "refresh": "", "schemaVersion": 40, "tags": ["scm"], "templating": {"list": [{"current": {"text": "metrics_live", "value": "bcde3167-579a-4ff2-a459-d2748e37f2a0"}, "includeAll": false, "label": "Metrics Datasource", "name": "metrics_ds", "options": [], "query": "prometheus", "refresh": 1, "regex": "/^metrics_/", "type": "datasource"}, {"current": {"text": "forecast-api", "value": "forecast-api"}, "includeAll": false, "name": "application", "options": [{"selected": false, "text": "output-demand-forecast", "value": "output-demand-forecast"}, {"selected": false, "text": "benthos-source-to-sink", "value": "benthos-source-to-sink"}, {"selected": false, "text": "kafka-db-sink", "value": "kafka-db-sink"}, {"selected": false, "text": "calculator-job", "value": "calculator-job"}, {"selected": true, "text": "forecast-api", "value": "forecast-api"}, {"selected": false, "text": "forecast-api-db", "value": "forecast-api-db"}, {"selected": false, "text": "actual-consumption-service", "value": "actual-consumption-service"}, {"selected": false, "text": "sku-specification-service", "value": "sku-specification-service"}, {"selected": false, "text": "supplier", "value": "supplier"}, {"selected": false, "text": "demand", "value": "demand"}, {"selected": false, "text": "kafka-db-sink", "value": "kafka-db-sink"}, {"selected": false, "text": "forecast-api-db", "value": "forecast-api-db"}, {"selected": false, "text": "goods-received-note-service", "value": "goods-received-note-service"}, {"selected": false, "text": "distribution-center-api", "value": "distribution-center-api"}, {"selected": false, "text": "purchase-order-service", "value": "purchase-order-service"}, {"selected": false, "text": "inventory-variance-job", "value": "inventory-variance-job"}, {"selected": false, "text": "sanity-checker", "value": "sanity-checker"}, {"selected": false, "text": "inventory-snapshot-service", "value": "inventory-snapshot-service"}, {"selected": false, "text": "supply-quantity-recommendation-job", "value": "supply-quantity-recommendation-job"}, {"selected": false, "text": "safety-stock-job", "value": "safety-stock-job"}], "query": "output-demand-forecast,benthos-source-to-sink,kafka-db-sink,calculator-job,forecast-api,forecast-api-db,actual-consumption-service,sku-specification-service,supplier,demand,kafka-db-sink,forecast-api-db,goods-received-note-service,distribution-center-api,purchase-order-service,inventory-variance-job,sanity-checker,inventory-snapshot-service,supply-quantity-recommendation-job,safety-stock-job", "type": "custom"}, {"current": {"text": "All", "value": "$__all"}, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "definition": "label_values(kafka_server_BrokerTopicMetrics_MessagesInPerSec_Count{service=\"$kafka_service\",service_type=\"kafka\", topic!~\"__.*\", topic!~\"_.*\", topic!=\"\", topic=~\"(private.)?csku-inventory-forecast.*\",topic!~\".*changelog\"}, topic)\n", "includeAll": true, "multi": true, "name": "kafka_topic", "options": [], "query": {"query": "label_values(kafka_server_BrokerTopicMetrics_MessagesInPerSec_Count{service=\"$kafka_service\",service_type=\"kafka\", topic!~\"__.*\", topic!~\"_.*\", topic!=\"\", topic=~\"(private.)?csku-inventory-forecast.*\",topic!~\".*changelog\"}, topic)\n", "refId": "StandardVariableQuery"}, "refresh": 2, "regex": "", "sort": 1, "type": "query"}, {"current": {"text": "All", "value": ["$__all"]}, "datasource": {"type": "prometheus", "uid": "${metrics_ds}"}, "definition": "label_values(kafka_server_KafkaServer_BrokerState_Value{service_type=\"kafka\",service!=\"\"}, service)", "includeAll": true, "multi": true, "name": "kafka_service", "options": [], "query": {"query": "label_values(kafka_server_KafkaServer_BrokerState_Value{service_type=\"kafka\",service!=\"\"}, service)", "refId": "Prometheus-kafka_service-Variable-Query"}, "refresh": 2, "regex": "", "type": "query"}, {"current": {"text": "", "value": ""}, "datasource": {"type": "datasource", "uid": "grafana"}, "definition": "", "includeAll": false, "name": "query0", "options": [], "query": "", "refresh": 1, "regex": "", "type": "query"}]}, "time": {"from": "now-1h", "to": "now"}, "timepicker": {"refresh_intervals": ["5s", "10s", "30s", "1m", "5m", "15m", "30m", "1h", "2h", "1d"]}, "timezone": "", "title": "CSKU Inventory Forecast", "uid": "UF4VV8fuWdFMRRnuSRFhNXUrp11cwtvTJq1yfBvb", "version": 10, "weekStart": ""}