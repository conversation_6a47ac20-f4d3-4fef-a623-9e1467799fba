{"annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": 3098, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": 36, "panels": [], "title": "API Performance", "type": "row"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "semi-dark-green", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 0, "y": 1}, "id": 57, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "sum(count_over_time({app_id=\"${app_id}\",kind=\"event\"} |~ \"$app\" | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"200\" [$__range])) by (app)", "queryType": "range", "refId": "A"}], "title": "200 (OK Request)", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 1}, "id": 39, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "sum(count_over_time({app_id=\"${app_id}\",kind=\"event\"} |~ \"$app\" | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"400\" [$__range])) by (app)", "queryType": "range", "refId": "A"}], "title": "400 (Bad Request)", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 1}, "id": 37, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "sum(count_over_time({app_id=\"${app_id}\",kind=\"event\"} |~ \"$app\" | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"401\" [$__range])) by (app)", "hide": false, "queryType": "range", "refId": "B"}], "title": "401 (Unauthorized)", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 1}, "id": 43, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "sum(count_over_time({app_id=\"${app_id}\",kind=\"event\"} |~ \"$app\" | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"403\" [$__range])) by (app)", "queryType": "range", "refId": "A"}], "title": "403 (Forbidden)", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 15, "y": 1}, "id": 44, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "sum(count_over_time({app_id=\"${app_id}\",kind=\"event\"} |~ \"$app\" | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"404\" [$__range])) by (app)", "queryType": "range", "refId": "A"}], "title": "404 (Not Found)", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 18, "y": 1}, "id": 42, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "sum(count_over_time({app_id=\"${app_id}\",kind=\"event\"} |~ \"$app\" | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"429\" [$__range])) by (app)", "queryType": "range", "refId": "A"}], "title": "429 (Too Many Requests/rate limiting)", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 6, "y": 5}, "id": 38, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "sum(count_over_time({app_id=\"${app_id}\",kind=\"event\"} |~ \"$app\" | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"500\" [$__range])) by (app)", "queryType": "range", "refId": "A"}], "title": "500 (Internal Server Error)", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 9, "y": 5}, "id": 40, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "sum(count_over_time({app_id=\"${app_id}\",kind=\"event\"} |~ \"$app\" | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"503\" [$__range])) by (app)", "queryType": "range", "refId": "A"}], "title": "503 (Service Unavailable)", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 3, "x": 12, "y": 5}, "id": 41, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "sum(count_over_time({app_id=\"${app_id}\",kind=\"event\"} |~ \"$app\" | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"504\" [$__range])) by (app)", "queryType": "range", "refId": "A"}], "title": "504 (Gateway timeout)", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 9}, "id": 45, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({app_id=\"${app_id}\", kind=\"event\"} |~ \"$app\"  | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"400\" [$__range])) by (event_data_http_url))", "queryType": "instant", "refId": "A"}], "title": "Most Frequent 400 Errors", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "event_name": "Name", "value": "Error"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 9}, "id": 46, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({app_id=\"${app_id}\", kind=\"event\"} |~ \"$app\"  | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"401\" [$__range])) by (event_data_http_url))", "queryType": "instant", "refId": "A"}], "title": "Most Frequent 401 Errors", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "event_name": "Name", "value": "Error"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 17}, "id": 47, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({kind=\"event\", app_id=\"${app_id}\"} |~ \"$app\"  | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"403\" [$__range])) by (event_data_http_url))", "queryType": "instant", "refId": "A"}], "title": "Most Frequent 403 Errors", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "event_name": "Name", "value": "Error"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 17}, "id": 48, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({kind=\"event\", app_id=\"${app_id}\"} |~ \"$app\"  | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"404\" [$__range])) by (event_data_http_url))", "queryType": "instant", "refId": "A"}], "title": "Most Frequent 404 Errors", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "event_name": "Name", "value": "Error"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 25}, "id": 49, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({kind=\"event\", app_id=\"${app_id}\"} |~ \"$app\"  | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"429\" [$__range])) by (event_data_http_url))", "queryType": "instant", "refId": "A"}], "title": "Most Frequent 429 Errors", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "event_name": "Name", "value": "Error"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 25}, "id": 50, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({kind=\"event\", app_id=\"${app_id}\"} |~ \"$app\"  | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"500\" [$__range])) by (event_data_http_url))", "queryType": "instant", "refId": "A"}], "title": "Most Frequent 500 Errors", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "event_name": "Name", "value": "Error"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 33}, "id": 51, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({kind=\"event\", app_id=\"${app_id}\"} |~ \"$app\"  | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"503\" [$__range])) by (event_data_http_url))", "queryType": "instant", "refId": "A"}], "title": "Most Frequent 503 Errors", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "event_name": "Name", "value": "Error"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 33}, "id": 52, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({kind=\"event\", app_id=\"${app_id}\"} |~ \"$app\"  | logfmt | user_attr_country=~\"${country}\" | event_data_http_status_code=\"504\" [$__range])) by (event_data_http_url))", "queryType": "instant", "refId": "A"}], "title": "Most Frequent 504 Errors", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "event_name": "Name", "value": "Error"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"log": 2, "type": "log"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 41}, "id": 53, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "avg_over_time(\n  {app_id=\"${app_id}\", kind=\"event\"}\n  |~ \"$app\"\n  |= \"faro.performance.resource\"\n  | logfmt\n  | user_attr_country=~\"${country}\"\n  | event_data_name=~\".*gw/scm/cif/forecast.*\"\n  | unwrap event_data_duration\n  [${__interval}]\n) by (event_data_name)", "hide": false, "queryType": "range", "refId": "D"}], "title": "Forecast api response time", "type": "timeseries"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 41}, "id": 54, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "avg_over_time(\n  {app_id=\"${app_id}\", kind=\"event\"}\n  |~ \"$app\"\n  |= \"faro.performance.resource\"\n  | logfmt\n  | user_attr_country=~\"${country}\"\n  | event_data_name=~\".*gw/scm/cif/notes.*\"\n  | unwrap event_data_duration\n  [${__interval}]\n) by (event_data_name)", "hide": false, "queryType": "range", "refId": "D"}], "title": "Notes api response time", "type": "timeseries"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 0, "y": 52}, "id": 56, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "avg_over_time(\n  {app_id=\"${app_id}\", kind=\"event\"}\n  |~ \"$app\"\n  |= \"faro.performance.resource\"\n  | logfmt\n  | user_attr_country=~\"${country}\"\n  | event_data_name=~\".*gw/scm/cif/calculation/dailyView.*\"\n  | unwrap event_data_duration\n  [${__interval}]\n) by (event_data_name)", "hide": false, "queryType": "range", "refId": "D"}], "title": "Daily calculation api response time", "type": "timeseries"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 11, "w": 12, "x": 12, "y": 52}, "id": 55, "options": {"legend": {"calcs": [], "displayMode": "hidden", "placement": "right", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "avg_over_time(\n  {app_id=\"${app_id}\", kind=\"event\"}\n  |~ \"$app\"\n  |= \"faro.performance.resource\"\n  | logfmt\n  | user_attr_country=~\"${country}\"\n  | event_data_name=~\".*gw/scm/cif/calculation/weeklyView.*\"\n  | unwrap event_data_duration\n  [${__interval}]\n) by (event_data_name)", "hide": false, "queryType": "range", "refId": "D"}], "title": "Weekly calculation api response time", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 63}, "id": 14, "panels": [], "title": "Performance", "type": "row"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 600}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 0, "y": 64}, "id": 8, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "avg_over_time({kind=\"measurement\",app_id=\"${app_id}\"} |= \"ttfb\" |~ \"$app\" | logfmt | page_id=~\"${page_id}\" | unwrap ttfb [$__range]) by (app)", "legendFormat": "TTFB", "queryType": "range", "refId": "A"}], "title": "TTFB", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "#EAB839", "value": 1800}, {"color": "red", "value": 3000}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 4, "y": 64}, "id": 9, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "avg_over_time({kind=\"measurement\",app_id=\"${app_id}\"} |= \" fcp=\" |~ \"$app\" | logfmt | page_id=~\"${page_id}\" | unwrap fcp [$__range]) by (app)", "legendFormat": "FCP", "queryType": "range", "refId": "A"}], "title": "FCP", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "#EAB839", "value": 2500}, {"color": "red", "value": 4000}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 8, "y": 64}, "id": 10, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "avg_over_time({kind=\"measurement\",app_id=\"${app_id}\"} |= \" lcp=\" |~ \"$app\" | logfmt | page_id=~\"${page_id}\" | unwrap lcp [$__range]) by (app)", "legendFormat": "LCP", "queryType": "range", "refId": "A"}], "title": "LCP", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "max": 1, "min": 0, "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "#EAB839", "value": 0.1}, {"color": "red", "value": 0.3}]}, "unit": "none"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 12, "y": 64}, "id": 11, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "avg_over_time({kind=\"measurement\",app_id=\"${app_id}\"} |= \"cls\" |~ \"$app\" | logfmt | page_id=~\"${page_id}\" | unwrap cls [$__range]) by (app)", "legendFormat": "CLS", "queryType": "range", "refId": "A"}], "title": "CLS", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "#EAB839", "value": 100}, {"color": "red", "value": 300}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 3, "w": 4, "x": 16, "y": 64}, "id": 12, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "avg_over_time({kind=\"measurement\",app_id=\"${app_id}\"} |= \" fid=\" |~ \"$app\" | logfmt | page_id=~\"${page_id}\" | unwrap fid [$__range]) by (app)", "legendFormat": "FID", "queryType": "range", "refId": "A"}], "title": "FID", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 67}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "quantile_over_time(0.75, {kind=\"measurement\",app_id=\"${app_id}\"} |= \" ttfb=\" |~ \"$app\" | logfmt | page_id=~\"${page_id}\" | unwrap ttfb [5m]) by (app)", "legendFormat": "TTFB", "queryType": "range", "refId": "A"}, {"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "quantile_over_time(0.75, {kind=\"measurement\",app=\"$app\"} |= \" fcp=\" | logfmt | unwrap fcp [5m]) by (app)", "hide": false, "legendFormat": "FCP", "queryType": "range", "refId": "B"}, {"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "quantile_over_time(0.75, {kind=\"measurement\",app=\"$app\"} |= \" lcp=\" | logfmt | unwrap lcp [5m]) by (app)", "hide": false, "legendFormat": "LCP", "queryType": "range", "refId": "C"}], "title": "<PERSON>, p75", "type": "timeseries"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "#EAB839", "value": 0.1}, {"color": "red", "value": 0.25}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 8, "y": 67}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "quantile_over_time(0.75, {kind=\"measurement\",app_id=\"${app_id}\"} |= \" cls=\" |~ \"$app\" | logfmt | page_id=~\"${page_id}\" | unwrap cls [5m]) by (app)", "legendFormat": "CLS", "queryType": "range", "refId": "A"}], "title": "Cumulative Layout Shift, p75", "type": "timeseries"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "line"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "yellow", "value": 100}, {"color": "red", "value": 300}]}, "unit": "ms"}, "overrides": []}, "gridPos": {"h": 8, "w": 7, "x": 15, "y": 67}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "quantile_over_time(0.75, {kind=\"measurement\",app_id=\"${app_id}\"} |= \" fid=\" |~ \"$app\" | logfmt | page_id=~\"${page_id}\" | unwrap fid [5m]) by (app)", "legendFormat": "FID", "queryType": "range", "refId": "A"}], "title": "First Input Delay, p75", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 75}, "id": 16, "panels": [], "title": "Exceptions", "type": "row"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 1}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 2, "x": 0, "y": 76}, "id": 18, "options": {"colorMode": "value", "graphMode": "none", "justifyMode": "auto", "orientation": "auto", "percentChangeColorMode": "standard", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showPercentChange": false, "textMode": "auto", "wideLayout": true}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "sum(count_over_time({app_id=\"${app_id}\",kind=\"exception\"} |~ \"$app\" [$__range])) by (app)", "queryType": "range", "refId": "A"}], "title": "Total Exceptions", "type": "stat"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "errors"}, "properties": [{"id": "color", "value": {"fixedColor": "red", "mode": "fixed"}}]}, {"__systemRef": "hideSeriesFrom", "matcher": {"id": "byNames", "options": {"mode": "exclude", "names": ["errors"], "prefix": "All except:", "readOnly": true}}, "properties": [{"id": "custom.hideFrom", "value": {"legend": false, "tooltip": false, "viz": true}}]}]}, "gridPos": {"h": 8, "w": 12, "x": 2, "y": 76}, "id": 25, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": false}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "count_over_time({app_id=\"${app_id}\",kind=\"exception\"} |~ \"$app\" [$__interval])", "legendFormat": "errors", "queryType": "range", "refId": "A"}], "title": "Exceptions Over Time", "type": "timeseries"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 8, "x": 14, "y": 76}, "id": 20, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({kind=\"exception\", app_id=\"${app_id}\"} |~ \"$app\" | logfmt [$__range])) by (value))", "queryType": "instant", "refId": "A"}], "title": "Top Exceptions", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "value": "Error"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 9, "w": 14, "x": 0, "y": 84}, "id": 21, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({kind=\"exception\", app_id=\"${app_id}\"} |~ \"$app\" | logfmt [$__range])) by (page_url))", "queryType": "instant", "refId": "A"}], "title": "Top URLs by Exception Count", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "page_url": "URL", "value": "Error"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Version"}, "properties": [{"id": "custom.width", "value": 150}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Errors"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 9, "w": 8, "x": 14, "y": 84}, "id": 23, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count by (browser_name, browser_version) (count_over_time({kind=\"exception\", app_id=\"${app_id}\"} |~ \"$app\" | logfmt [$__range])))", "queryType": "instant", "refId": "A"}], "title": "Top Browsers by Exception Count", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "browser_name": "Browser", "browser_version": "Version", "page_url": "URL", "value": "Error"}}}], "type": "table"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 93}, "id": 27, "panels": [], "title": "Meta", "type": "row"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 10, "x": 0, "y": 94}, "id": 29, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({app_id=\"${app_id}\",kind=\"measurement\"} |= \" ttfb=\" |~ \"$app\" | logfmt [$__range])) by (browser_name, browser_version))", "queryType": "instant", "refId": "A"}], "title": "Popular Browsers", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "browser_name": "Browser", "browser_version": "Version"}}}], "type": "table"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "barWidthFactor": 0.6, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "errors"}, "properties": []}]}, "gridPos": {"h": 10, "w": 12, "x": 10, "y": 94}, "id": 30, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"hideZeros": false, "mode": "single", "sort": "none"}}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "count_over_time({app_id=\"${app_id}\",kind=\"measurement\"} |= \" ttfb=\" |~ \"$app\" [$__interval])", "legendFormat": "visits", "queryType": "range", "refId": "A"}], "title": "Visits", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 104}, "id": 32, "panels": [], "title": "Events", "type": "row"}, {"datasource": "${data_source}", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "cellOptions": {"type": "auto"}, "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": 0}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Count"}, "properties": [{"id": "custom.width", "value": 100}]}]}, "gridPos": {"h": 8, "w": 8, "x": 0, "y": 105}, "id": 35, "options": {"cellHeight": "sm", "footer": {"countRows": false, "fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Count"}]}, "pluginVersion": "12.1.0-89256.patch1-89350", "targets": [{"datasource": "${data_source}", "direction": "backward", "editorMode": "code", "expr": "topk(10, count(count_over_time({kind=\"event\", app_id=\"${app_id}\"} |~ \"$app\" | logfmt [$__range])) by (event_name))", "queryType": "instant", "refId": "A"}], "title": "Top Events", "transformations": [{"id": "organize", "options": {"excludeByName": {"Time": true}, "indexByName": {}, "renameByName": {"Value #A": "Count", "event_name": "Name", "value": "Error"}}}], "type": "table"}], "preload": false, "refresh": "", "schemaVersion": 41, "tags": [], "templating": {"list": [{"current": {"text": "367", "value": "367"}, "description": "366:live 367: stg", "label": "live/stg", "name": "app_id", "options": [{"selected": true, "text": "367", "value": "367"}, {"selected": false, "text": "366", "value": "366"}], "query": "367, 366", "type": "custom"}, {"current": {"text": "scm-siv-fragment", "value": "scm-siv-fragment"}, "hide": 2, "label": "fragment name", "name": "app", "query": "scm-siv-fragment", "skipUrlSync": true, "type": "constant"}, {"current": {"text": "", "value": ""}, "description": "eg: /siv/stock-management", "name": "page_id", "options": [{"selected": true, "text": "", "value": ""}], "query": "", "type": "textbox"}, {"current": {"text": ".*", "value": ".*"}, "description": "", "includeAll": false, "name": "country", "options": [{"selected": true, "text": ".*", "value": ".*"}, {"selected": false, "text": "IT", "value": "IT"}, {"selected": false, "text": "JP", "value": "JP"}, {"selected": false, "text": "BA", "value": "BA"}, {"selected": false, "text": "BJ", "value": "BJ"}, {"selected": false, "text": "CI", "value": "CI"}, {"selected": false, "text": "CR", "value": "CR"}, {"selected": false, "text": "DO", "value": "DO"}, {"selected": false, "text": "AU", "value": "AU"}, {"selected": false, "text": "DE", "value": "DE"}, {"selected": false, "text": "GB", "value": "GB"}, {"selected": false, "text": "NL", "value": "NL"}, {"selected": false, "text": "US", "value": "US"}, {"selected": false, "text": "BE", "value": "BE"}, {"selected": false, "text": "AT", "value": "AT"}, {"selected": false, "text": "CA", "value": "CA"}, {"selected": false, "text": "MR", "value": "MR"}, {"selected": false, "text": "FJ", "value": "FJ"}, {"selected": false, "text": "NO", "value": "NO"}, {"selected": false, "text": "CH", "value": "CH"}, {"selected": false, "text": "ML", "value": "ML"}, {"selected": false, "text": "FR", "value": "FR"}, {"selected": false, "text": "NZ", "value": "NZ"}, {"selected": false, "text": "CN", "value": "CN"}, {"selected": false, "text": "SE", "value": "SE"}, {"selected": false, "text": "ER", "value": "ER"}, {"selected": false, "text": "AO", "value": "AO"}, {"selected": false, "text": "CK", "value": "CK"}, {"selected": false, "text": "DK", "value": "DK"}, {"selected": false, "text": "AR", "value": "AR"}, {"selected": false, "text": "BO", "value": "BO"}, {"selected": false, "text": "BR", "value": "BR"}, {"selected": false, "text": "BG", "value": "BG"}, {"selected": false, "text": "CL", "value": "CL"}, {"selected": false, "text": "CO", "value": "CO"}, {"selected": false, "text": "HR", "value": "HR"}, {"selected": false, "text": "CY", "value": "CY"}, {"selected": false, "text": "CZ", "value": "CZ"}, {"selected": false, "text": "EC", "value": "EC"}, {"selected": false, "text": "EE", "value": "EE"}, {"selected": false, "text": "FI", "value": "FI"}, {"selected": false, "text": "GR", "value": "GR"}, {"selected": false, "text": "HU", "value": "HU"}, {"selected": false, "text": "IE", "value": "IE"}, {"selected": false, "text": "EU", "value": "EU"}, {"selected": false, "text": "LV", "value": "LV"}, {"selected": false, "text": "LT", "value": "LT"}, {"selected": false, "text": "MT", "value": "MT"}, {"selected": false, "text": "PY", "value": "PY"}, {"selected": false, "text": "PE", "value": "PE"}, {"selected": false, "text": "PO", "value": "PO"}, {"selected": false, "text": "PT", "value": "PT"}, {"selected": false, "text": "RO", "value": "RO"}, {"selected": false, "text": "SK", "value": "SK"}, {"selected": false, "text": "SI", "value": "SI"}, {"selected": false, "text": "KN", "value": "KN"}, {"selected": false, "text": "EG", "value": "EG"}, {"selected": false, "text": "ET", "value": "ET"}, {"selected": false, "text": "FO", "value": "FO"}, {"selected": false, "text": "GH", "value": "GH"}, {"selected": false, "text": "GT", "value": "GT"}, {"selected": false, "text": "GW", "value": "GW"}, {"selected": false, "text": "HN", "value": "HN"}, {"selected": false, "text": "ID", "value": "ID"}, {"selected": false, "text": "IL", "value": "IL"}, {"selected": false, "text": "IR", "value": "IR"}, {"selected": false, "text": "IS", "value": "IS"}, {"selected": false, "text": "JO", "value": "JO"}, {"selected": false, "text": "KE", "value": "KE"}, {"selected": false, "text": "KH", "value": "KH"}, {"selected": false, "text": "KP", "value": "KP"}, {"selected": false, "text": "KR", "value": "KR"}, {"selected": false, "text": "LK", "value": "LK"}, {"selected": false, "text": "MA", "value": "MA"}, {"selected": false, "text": "MG", "value": "MG"}, {"selected": false, "text": "MV", "value": "MV"}, {"selected": false, "text": "MX", "value": "MX"}, {"selected": false, "text": "MZ", "value": "MZ"}, {"selected": false, "text": "NC", "value": "NC"}, {"selected": false, "text": "NG", "value": "NG"}, {"selected": false, "text": "PH", "value": "PH"}, {"selected": false, "text": "PL", "value": "PL"}, {"selected": false, "text": "SG", "value": "SG"}, {"selected": false, "text": "SN", "value": "SN"}, {"selected": false, "text": "TN", "value": "TN"}, {"selected": false, "text": "UG", "value": "UG"}, {"selected": false, "text": "ZA", "value": "ZA"}, {"selected": false, "text": "ZM", "value": "ZM"}, {"selected": false, "text": "ZW", "value": "ZW"}, {"selected": false, "text": "TR", "value": "TR"}, {"selected": false, "text": "VN", "value": "VN"}, {"selected": false, "text": "IN", "value": "IN"}, {"selected": false, "text": "UY", "value": "UY"}, {"selected": false, "text": "VE", "value": "VE"}, {"selected": false, "text": "CG", "value": "CG"}, {"selected": false, "text": "GN", "value": "GN"}, {"selected": false, "text": "LU", "value": "LU"}, {"selected": false, "text": "RS", "value": "RS"}, {"selected": false, "text": "RU", "value": "RU"}, {"selected": false, "text": "TH", "value": "TH"}, {"selected": false, "text": "CF", "value": "CF"}, {"selected": false, "text": "GQ", "value": "GQ"}, {"selected": false, "text": "YE", "value": "YE"}, {"selected": false, "text": "ES", "value": "ES"}, {"selected": false, "text": "AL", "value": "AL"}, {"selected": false, "text": "DZ", "value": "DZ"}, {"selected": false, "text": "GD", "value": "GD"}, {"selected": false, "text": "JM", "value": "JM"}, {"selected": false, "text": "LA", "value": "LA"}, {"selected": false, "text": "MY", "value": "MY"}, {"selected": false, "text": "MM", "value": "MM"}, {"selected": false, "text": "NP", "value": "NP"}, {"selected": false, "text": "NI", "value": "NI"}, {"selected": false, "text": "PK", "value": "PK"}, {"selected": false, "text": "TW", "value": "TW"}, {"selected": false, "text": "UA", "value": "UA"}, {"selected": false, "text": "TO", "value": "TO"}, {"selected": false, "text": "TT", "value": "TT"}, {"selected": false, "text": "AM", "value": "AM"}, {"selected": false, "text": "MD", "value": "MD"}, {"selected": false, "text": "MK", "value": "MK"}, {"selected": false, "text": "TK", "value": "TK"}, {"selected": false, "text": "TV", "value": "TV"}, {"selected": false, "text": "HK", "value": "HK"}, {"selected": false, "text": "XA", "value": "XA"}, {"selected": false, "text": "TZ", "value": "TZ"}], "query": ".*,IT,\n    JP,\n    BA,\n    BJ,\n    CI,\n    CR,\n    DO,\n    AU,\n    DE,\n    GB,\n    NL,\n    US,\n    BE,\n    AT,\n    CA,\n    MR,\n    FJ,\n    NO,\n    CH,\n    ML,\n    FR,\n    NZ,\n    CN,\n    SE,\n    ER,\n    AO,\n    CK,\n    DK,\n    AR,\n    BO,\n    BR,\n    BG,\n    CL,\n    CO,\n    HR,\n    CY,\n    CZ,\n    EC,\n    EE,\n    FI,\n    GR,\n    HU,\n    IE,\n    EU,\n    LV,\n    LT,\n    MT,\n    PY,\n    PE,\n    PO,\n    PT,\n    RO,\n    SK,\n    SI,\n    KN,\n    EG,\n    ET,\n    FO,\n    GH,\n    GT,\n    GW,\n    HN,\n    ID,\n    IL,\n    IR,\n    IS,\n    JO,\n    KE,\n    KH,\n    KP,\n    KR,\n    LK,\n    MA,\n    MG,\n    MV,\n    MX,\n    MZ,\n    NC,\n    NG,\n    PH,\n    PL,\n    SG,\n    SN,\n    TN,\n    UG,\n    ZA,\n    ZM,\n    ZW,\n    TR,\n    VN,\n    IN,\n    UY,\n    VE,\n    CG,\n    GN,\n    LU,\n    RS,\n    RU,\n    TH,\n    CF,\n    GQ,\n    YE,\n    ES,\n    AL,\n    DZ,\n    GD,\n    JM,\n    LA,\n    MY,\n    MM,\n    NP,\n    NI,\n    PK,\n    TW,\n    UA,\n    TO,\n    TT,\n    AM,\n    MD,\n    MK,\n    TK,\n    TV,\n    HK,\n    XA,\n    TZ", "type": "custom"}, {"allowCustomValue": false, "current": {"text": "logs_all_internal", "value": ""}, "description": "eg: logs_all_internal", "name": "data_source", "options": [], "query": "loki", "refresh": 1, "regex": "", "type": "datasource"}]}, "time": {"from": "now-2d", "to": "now"}, "timepicker": {}, "timezone": "", "title": "IP FE Metrics", "uid": "bedab843-93ab-4c7f-acdd-43c6a94d247d", "version": 207}