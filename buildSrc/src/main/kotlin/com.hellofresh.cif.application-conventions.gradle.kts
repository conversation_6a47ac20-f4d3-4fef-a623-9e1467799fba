plugins {
    base
    id("com.hellofresh.cif.common-conventions")
    id("com.hellofresh.cif.jib-conventions")
}

tasks {
    val processApplicationDefaultProperties by registering {
        inputs.properties("projectName" to rootProject.name, "applicationName" to project.name)
        val out = processResources.map { it.destinationDir.resolve("application-default.properties") }
        outputs.file(out)
        doLast {
            // language=properties
            out.get().writeText(
                """
                # suppress inspection "UnusedProperty" for whole file
                project.name=${rootProject.name}
                application.name=${project.name}
                statusServer.port=8081
                statusServer.host=0.0.0.0
                statusServer.backlog=0
                prometheus.scrapeInterval=PT1M
                prometheus.descriptions=false
                """.trimIndent(),
            )
        }
    }

    processResources.configure {
        dependsOn(processApplicationDefaultProperties)
    }
}
