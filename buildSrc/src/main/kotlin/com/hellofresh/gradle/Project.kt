package com.hellofresh.gradle

import java.io.File
import java.util.Properties
import org.gradle.api.Project

private inline fun Project.realRootPath(name: String, typePredicate: (File) -> Boolean): File? {
    var dir = (gradle.parent ?: gradle).rootProject.rootDir
    do {
        val path = dir.resolve(name)
        if (typePredicate(path)) return path
        dir = dir.parentFile.takeUnless { it == dir } ?: return null
    } while (true)
}

internal fun Project.realRootDir(name: String): File? =
    realRootPath(name, File::isDirectory)

internal fun Project.realRootFile(name: String): File? =
    realRootPath(name, File::isFile)

internal fun Project.loadRealRootProperties(name: String): Properties? =
    realRootFile("$name.properties")?.bufferedReader()?.use {
        Properties().apply { load(it) }.ifEmpty { null }
    }
