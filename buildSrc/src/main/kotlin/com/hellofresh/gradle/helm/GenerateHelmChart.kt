@file:Suppress("detekt:all")
package com.hellofresh.gradle.helm

import com.fasterxml.jackson.annotation.JsonInclude.Include.NON_NULL
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.ObjectReader
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.gradle.DEPLOYMENT_GROUP
import com.hellofresh.gradle.replaceTokens
import java.io.File
import java.io.InputStream
import javax.inject.Inject
import org.gradle.api.DefaultTask
import org.gradle.api.InvalidUserDataException
import org.gradle.api.file.DirectoryProperty
import org.gradle.api.file.FileSystemOperations
import org.gradle.api.model.ObjectFactory
import org.gradle.api.provider.MapProperty
import org.gradle.api.provider.Property
import org.gradle.api.provider.ProviderFactory
import org.gradle.api.provider.SetProperty
import org.gradle.api.tasks.Input
import org.gradle.api.tasks.InputDirectory
import org.gradle.api.tasks.OutputDirectory
import org.gradle.api.tasks.SkipWhenEmpty
import org.gradle.api.tasks.TaskAction
import org.gradle.internal.exceptions.DefaultMultiCauseException
import org.gradle.kotlin.dsl.extra
import org.gradle.kotlin.dsl.mapProperty
import org.gradle.kotlin.dsl.property
import org.gradle.kotlin.dsl.setProperty

abstract class GenerateHelmChart @Inject constructor(
    objects: ObjectFactory,
    providers: ProviderFactory,
    private val fs: FileSystemOperations,
) : DefaultTask() {
    /**
     * See `settings.gradle.kts` in the root of the repository.
     */
    @get:Input
    val applicationId: Property<String> = objects.property<String>().convention(
        providers.provider {
            project.property("applicationId").toString()
        },
    )

    @get:Input
    val tiers: SetProperty<String> = objects.setProperty<String>().convention(
        providers.provider {
            @Suppress("UNCHECKED_CAST")
            project.property("tiers") as Iterable<String>
        },
    )

    @get:Input
    val properties: MapProperty<String, String> = objects.mapProperty<String, String>().value(
        providers.provider {
            @Suppress("UNCHECKED_CAST")
            project.extra.properties.filterValues { it is String } as Map<String, String>
        },
    )

    @get:InputDirectory
    @get:SkipWhenEmpty
    abstract val src: DirectoryProperty

    @get:OutputDirectory
    val dst: DirectoryProperty = objects.directoryProperty()
        .convention(applicationId.flatMap { project.rootProject.layout.buildDirectory.dir("helm/chart/$it") })

    init {
        group = DEPLOYMENT_GROUP
        description = "Generate the Helm charts of this project for the Kubernetes deployment."
    }

    @TaskAction
    fun run() {
        val applicationId = applicationId.get()
        val src = src.get()
        val dst = dst.get()
        val properties = properties.get()

        // We copy the entire contents of the source directory except for the
        // values YAML files as is and replace all tokens within them while
        // doing so.
        fs.copy {
            from(src)
            exclude("values*.yaml")
            replaceTokens(properties)
            into(dst)
        }

        val yaml = yamlMapper()
        val defaultFile = src.file("values.yaml").asFile
        val defaultYaml = defaultFile.readText()

        // The values YAML must be present separately for each tier we have
        // because the tier itself is a required property in the Chart
        // templates.
        tiers.get().forEach { tier ->
            // We should merge and replace in one go without writing to disk the
            // intermediate result of the merge.
            fs.copy {
                val tierFilename = "values-$tier.yaml"
                val tierFile = src.file(tierFilename).asFile
                val tmpFile = File(temporaryDir, tierFilename)
                yaml.writeValue(tmpFile, maybeMerge(yaml, defaultFile, tierFile, defaultYaml))
                from(tmpFile)
                replaceTokens(properties.toTierProperties(tier, applicationId))
                into(dst)
            }
        }
    }

    internal companion object {
        internal fun Map<String, String>.toTierProperties(tier: String, applicationId: String): Map<String, String> {
            val result = toMutableMap()
            val tierSuffix = "[$tier]"
            forEach { (k, v) ->
                if (k.endsWith(tierSuffix)) {
                    result[k.substring(0, k.length - tierSuffix.length)] = v
                }
            }
            result["tier"] = tier
            result["host"] = "$applicationId.$tier-k8s.hellofresh.io"
            return result
        }

        private operator fun Any?.get(property: String): Any? =
            asMap()?.get(property as Any)

        private operator fun Any?.set(key: String, value: Any?): Any? =
            asMap()?.put(key, value)

        @Suppress("UNCHECKED_CAST")
        private fun Any?.asMap(): MutableMap<String, Any?>? =
            if (this is MutableMap<*, *>) this as MutableMap<String, Any?> else null

        fun yamlMapper(): ObjectMapper =
            ObjectMapper(YAMLFactory())
                .setDefaultMergeable(true)
                .setSerializationInclusion(NON_NULL)
                .findAndRegisterModules()

        /**
         * Merges the supplied contents of [defaultYaml] with the data from the [tierFile] if
         * the file exists, otherwise simply returns the contents of the [defaultYaml].
         */
        fun maybeMerge(yamlMapper: ObjectMapper, defaultFile: File, tierFile: File, defaultYaml: String): Any {
            var values: Any? = yamlMapper.readValue(defaultYaml)
            val errors = mutableListOf<Exception>()
            if (tierFile.exists()) {
                values = tierFile.inputStream().use { merge(yamlMapper.readerForUpdating(values), it) }
                checkDeployments(tierFile, values, errors)
                if (errors.isNotEmpty()) {
                    errors.clear()
                    val tierValues: Any? = yamlMapper.readValue(tierFile)
                    checkDeployments(tierFile, tierValues, errors)
                    checkDeployments(defaultFile, yamlMapper.readValue(defaultYaml), errors)
                }
            } else {
                checkDeployments(tierFile, values, errors)
            }

            if (values == null) {
                errors += InvalidUserDataException("Default values and tier values if any are null")
            }

            if (errors.isNotEmpty()) {
                throw errors.singleOrNull() ?: DefaultMultiCauseException(
                    "Multiple issues were found in your Helm chart:",
                    errors
                )
            }

            return values as Any
        }

        fun checkDeployments(file: File, values: Any?, errors: MutableList<Exception>) {
            values["deployments"].asMap()?.mapNotNullTo(errors) { (k, v) ->
                checkResources(file, k, v)
            }
        }

        /**
         * Merges the [defaultValues] with the [tierValues].
         */
        fun merge(defaultValues: ObjectReader, tierValues: InputStream): Any? =
            defaultValues.readValue(tierValues)

        fun checkResources(file: File, name: Any?, deployment: Any?): Exception? {
            val resources = deployment["resources"]
            val requests = resources["requests"]

            if (resources == null || requests == null || requests["cpu"] == null || requests["memory"] == null) {
                return InvalidUserDataException(
                    "Missing required 'deployments.$name.resources.requests.(cpu|memory)' settings in: $file"
                )
            }

            if (resources["limits"] == null) {
                resources["limits"] = requests
            } else {
                val limits = resources["limits"]!!
                if (limits["memory"] == null) limits["memory"] = requests["memory"]
            }

            return null
        }
    }
}
