package com.hellofresh.gradle

import com.hellofresh.gradle.utils.toLowerCamelCase
import com.hellofresh.gradle.utils.toLowerDashCase
import com.hellofresh.gradle.utils.toLowerSnakeCase
import com.hellofresh.gradle.utils.toUpperCamelCase
import com.hellofresh.gradle.utils.toUpperDashCase
import com.hellofresh.gradle.utils.toUpperSnakeCase
import org.gradle.api.plugins.ExtraPropertiesExtension

internal inline fun ExtraPropertiesExtension.hasOrPut(key: String, supplier: () -> Any?) {
    if (!has(key)) set(key, supplier())
}

internal inline fun ExtraPropertiesExtension.hasThen(key: String, action: (k: String, v: Any?) -> Unit) {
    if (has(key)) action(key, get(key))
}

internal fun ExtraPropertiesExtension.putCaseFormats(key: String, value: String) {
    set("$key.upperCamelCase", value.toUpperCamelCase())
    set("$key.upperDashCase", value.toUpperDashCase())
    set("$key.upperSnakeCase", value.toUpperSnakeCase())
    set("$key.lowerCamelCase", value.toLowerCamelCase())
    set("$key.lowerDashCase", value.toLowerDashCase())
    set("$key.lowerSnakeCase", value.toLowerSnakeCase())
}

internal fun ExtraPropertiesExtension.putCaseLowerUpper(key: String, value: String) {
    set("$key.lower", value.lowercase())
    set("$key.upper", value.uppercase())
}
