plugins {
    id("com.google.cloud.tools.jib")
}

allprojects {
    val isLocal = System.getenv("HF_TIER")?.trim()?.lowercase() == "local"

    val toImage = if (isLocal) {
        "${rootProject.name}/$name:local"
    } else {
        "${project.property("jib.to.repository")!!}:$name-${System.getenv("VERSION") ?: "latest"}"
    }

    jib {
        from {
            image = project.property("jib.from.image")!!.toString()
        }
        to {
            image = toImage
        }
        container {
            project.findProperty("jib.container.jvmFlags")?.toString()?.split(' ')?.let {
                jvmFlags = it
            }
        }
    }
}
