package com.hellofresh.cif.sqr

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.lib.KrontabScheduler
import com.hellofresh.cif.lib.MeteredJob
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.dcbalancer.DcLoadBalancer
import com.hellofresh.cif.lib.dcbalancer.DcWeightLoadBalancer
import com.hellofresh.cif.lib.dcbalancer.DcWeightLoadBalancer.Companion.emptyConfiguration
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepositoryImpl
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.sqr.repository.DcWeightRepositoryImpl
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import com.hellofresh.cif.sqr.repository.WeeklyCalculationsRepository
import com.hellofresh.cif.sqr.service.SupplyQuantityRecommendationService
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeService
import com.hellofresh.cif.sqr.shortshelflife.SqrShortShelfLifeDcParam
import com.hellofresh.cif.sqr.shortshelflife.SqrShortShelfLifeParams
import com.hellofresh.cif.sqr.shortshelflife.repository.DailyCalculationsRepository
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendation.v1.SupplyQuantityRecommendationVal
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyKey
import com.hellofresh.proto.stream.ordering.supplyQuantityRecommendationDaily.v1.SupplyQuantityRecommendationDailyVal
import io.micrometer.core.instrument.MeterRegistry
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit.MINUTES
import org.apache.kafka.clients.producer.Producer
import org.apache.logging.log4j.kotlin.Logging

private const val STATUS_SERVER_HTTP_PORT = 8081
private const val NUMBER_OF_DATABASE_CONNECTIONS = 5

@SuppressWarnings("MemberNameEqualsClassName")
object Main : Logging {

    @JvmStatic
    fun main(args: Array<String>) {
        val meterRegistry = createMeterRegistry()
        val statsigFeatureFlagClient = statsigFeatureFlagClient()
        val readMetricsDSLContext = DBConfiguration.jooqReadOnlyDslContext(
            NUMBER_OF_DATABASE_CONNECTIONS,
            meterRegistry,
        )
        StatusServer.run(meterRegistry, STATUS_SERVER_HTTP_PORT)

        val readWriteMetricsDSLContext = DBConfiguration.jooqMasterDslContext(
            NUMBER_OF_DATABASE_CONNECTIONS,
            meterRegistry,
        )

        val kafkaProducer = shutdownNeeded {
            SupplyQuantityRecommendationService.createProducer(kafkaProducerConfig())
        }

        val kafkaProducerSsl = shutdownNeeded {
            SQRShortShelfLifeService.createProducer(kafkaProducerConfig())
        }

        val dcBalancer = dcBalancer(replicaCount(), podName(), readMetricsDSLContext).also { it.loadDcs() }

        val sqrJob = createSupplyQuantityRecommendationJob(
            meterRegistry,
            dcBalancer,
            readMetricsDSLContext,
            readWriteMetricsDSLContext,
            kafkaProducer,
        )

        val sqrShortShelfLifeJob = createSQRShortShelfLifeJob(
            readSqrShortShelfLifeParamsProperties(),
            meterRegistry,
            dcBalancer,
            readMetricsDSLContext,
            readWriteMetricsDSLContext,
            kafkaProducerSsl,
            statsigFeatureFlagClient,
        )

        BNLBufferCalculationSQSListener.launch(meterRegistry, readMetricsDSLContext, readWriteMetricsDSLContext)

        shutdownNeeded {
            KrontabScheduler(
                period = jobTimeMinutes(),
                timeUnit = MINUTES,
                executor = Executors.newSingleThreadExecutor(),
            )
        }.schedule {
            sqrJob.execute()
            sqrShortShelfLifeJob.execute()
        }
    }

    private fun jobTimeMinutes() = ConfigurationLoader.getStringOrFail("job.time_period_minutes").toInt()

    private fun replicaCount() = ConfigurationLoader.getIntegerOrFail("HF_REPLICA_COUNT")

    private fun podName() = ConfigurationLoader.getStringOrFail("HF_POD_NAME")

    @Suppress("LongParameterList")
    fun createSupplyQuantityRecommendationJob(
        meterRegistry: MeterRegistry,
        dcBalancer: DcLoadBalancer,
        readMetricsDSLContext: MetricsDSLContext,
        readWriteMetricsDSLContext: MetricsDSLContext,
        kafkaProducer: Producer<SupplyQuantityRecommendationKey, SupplyQuantityRecommendationVal>,
    ): MeteredJob {
        val supplyQuantityRecommendationService = SupplyQuantityRecommendationService(
            DcConfigService(meterRegistry, DcRepositoryImpl(readMetricsDSLContext)),
            WeeklyCalculationsRepository(readMetricsDSLContext),
            SupplyQuantityRecommendationConfigRepository(readMetricsDSLContext),
            SafetyStockRepository(readMetricsDSLContext),
            readWriteMetricsDSLContext,
            kafkaProducer,
        )

        return MeteredJob(meterRegistry, "supply-quantity-recommendation-job") {
            val runDcCodes = dcBalancer.loadDcs()
            logger.info("Starting sqr job with dcs:  $runDcCodes")
            supplyQuantityRecommendationService.run(runDcCodes)
            logger.info("Sqr job finished")
        }
    }

    private fun readSqrShortShelfLifeParamsProperties(): SqrShortShelfLifeParams {
        val includedMap = ConfigurationLoader.getMapWithSetValue("sqr.short-shelf-life.market-sku-categories.included")
            .mapValues { it.value.filter { skuCategory -> skuCategory.isNotBlank() }.toSet() }

        val excludedMap = ConfigurationLoader.getMapWithSetValue("sqr.short-shelf-life.market-sku-categories.excluded")
            .mapValues { it.value.filter { skuCategory -> skuCategory.isNotBlank() }.toSet() }

        return SqrShortShelfLifeParams(
            marketParam = (includedMap.keys + excludedMap.keys).distinct().associateWith {
                SqrShortShelfLifeDcParam(
                    skuCategoriesIncluded = includedMap[it] ?: emptySet(),
                    skuCategoriesExcluded = excludedMap[it] ?: emptySet(),
                )
            }
        )
    }

    @Suppress("LongParameterList")
    fun createSQRShortShelfLifeJob(
        params: SqrShortShelfLifeParams,
        meterRegistry: MeterRegistry,
        dcBalancer: DcLoadBalancer,
        readMetricsDSLContext: MetricsDSLContext,
        readWriteMetricsDSLContext: MetricsDSLContext,
        kafkaProducerSsl: Producer<SupplyQuantityRecommendationDailyKey, SupplyQuantityRecommendationDailyVal>,
        statsigFeatureFlagClient: StatsigFeatureFlagClient,
    ): MeteredJob {
        val sqrShortShelfLifeService = SQRShortShelfLifeService(
            params,
            readWriteMetricsDSLContext,
            DcConfigService(meterRegistry, DcRepositoryImpl(readMetricsDSLContext)),
            DailyCalculationsRepository(readMetricsDSLContext),
            SQRShortShelfLifeConfRepository(readMetricsDSLContext),
            PurchaseOrderRepositoryImpl(
                readMetricsDSLContext,
                DcConfigService(meterRegistry, DcRepositoryImpl(readMetricsDSLContext)),
                statsigFeatureFlagClient,
            ),
            kafkaProducerSsl,
        )

        return MeteredJob(meterRegistry, "sqr-short-shelf-life-job") {
            val runDcCodes = dcBalancer.loadDcs()
            logger.info("Starting sqr short shelf life job with dcs:  $runDcCodes")
            sqrShortShelfLifeService.run(runDcCodes)
            logger.info("Sqr short shelf life job finished")
        }
    }

    fun dcBalancer(
        replicaCount: Int,
        podName: String,
        metricsDSLContext: MetricsDSLContext,
    ): DcWeightLoadBalancer =
        DcWeightLoadBalancer(
            replicaCount,
            podName,
            DcWeightRepositoryImpl(metricsDSLContext),
            emptyConfiguration,
            listOf { dcCodes ->
                logger.info("SQR Job Loading Dcs ${dcCodes.size}: $dcCodes")
            },
        )

    private fun statsigFeatureFlagClient() = StatsigFactory.build(
        ::shutdownHook,
        sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
        userId = ConfigurationLoader.getStringOrFail("application.name"),
        isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
        hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
    )

    private fun kafkaProducerConfig() = ConfigurationLoader.loadKafkaProducerConfigurations()
}
