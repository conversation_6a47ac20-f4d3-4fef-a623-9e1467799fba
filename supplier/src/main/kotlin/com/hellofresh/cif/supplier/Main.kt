package com.hellofresh.cif.supplier

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.loadSchemaRegistryClientConfig
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.supplier.sku.SupplierSkuApp
import io.confluent.kafka.schemaregistry.client.CachedSchemaRegistryClient
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.confluent.kafka.schemaregistry.client.rest.RestService
import io.confluent.kafka.serializers.AbstractKafkaSchemaSerDeConfig
import kotlin.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.consumer.ConsumerConfig

private const val HTTP_PORT = 8081
private const val FALSE = "false"
private val parallelism = ConfigurationLoader.getIntegerOrDefault("parallelism", 1)

private val consumerConfig = ConfigurationLoader.loadKafkaConsumerConfigurations() +
    mapOf(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to FALSE)

private fun schemaRegistryClient(): SchemaRegistryClient = CachedSchemaRegistryClient(
    RestService(ConfigurationLoader.getStringOrFail("schema.registry.url")),
    AbstractKafkaSchemaSerDeConfig.MAX_SCHEMAS_PER_SUBJECT_DEFAULT,
    loadSchemaRegistryClientConfig(),
)

@Suppress("TooGenericExceptionCaught")
suspend fun main() {
    val meterRegistry = createMeterRegistry()
    val metricsDSLContext = DBConfiguration.jooqMasterDslContext(parallelism, meterRegistry)
    StatusServer.run(
        meterRegistry,
        HTTP_PORT
    )

    val pollConfig = PollConfig(
        Duration.parse(ConfigurationLoader.getStringOrFail("poll.timeout")),
        ConfigurationLoader.getStringOrFail("poll.interval_ms").toLong(),
        Duration.parse(ConfigurationLoader.getStringOrFail("process.timeout")),
    )
    withContext(Dispatchers.IO) {
        launch {
            SupplierApp(
                parallelism,
                consumerConfig,
                schemaRegistryClient(),
                pollConfig,
                metricsDSLContext,
                meterRegistry,
            ).runApp()
        }
        launch {
            SupplierSkuApp(
                parallelism,
                consumerConfig,
                schemaRegistryClient(),
                pollConfig,
                metricsDSLContext,
                meterRegistry,
            ).runApp()
        }
    }
}
