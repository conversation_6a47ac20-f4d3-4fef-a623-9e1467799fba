package com.hellofresh.cif.supplier.sku

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.supplier.model.SupplierSku
import com.hellofresh.cif.supplier.schema.tables.SupplierCulinarySku as SupplierCulinarySkuTable
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.jooq.Batch
import org.jooq.impl.DSL.insertInto

class PerformSupplierSkuUpsert(
    private val metricsDSLContext: MetricsDSLContext
) : suspend (ConsumerRecords<*, List<SupplierSku>>) -> Unit {

    override suspend fun invoke(records: ConsumerRecords<*, List<SupplierSku>>) {
        val batchQueryBuilder = BatchQueryBuilder(metricsDSLContext)
        records.flatMap { it.value() }
            .forEach { supplierSku ->
                supplierSku.let {
                    batchQueryBuilder
                        .bind(it)
                        .executeAsync()
                        .await()
                }
            }
    }

    /**
     * Provides a safe interface to build PreparedStatement
     */
    private class BatchQueryBuilder(dsl: MetricsDSLContext) {
        private val query = dsl.withTagName("insert-supplier-culinary-sku")
            .batch(
                SupplierCulinarySkuTable.SUPPLIER_CULINARY_SKU.run {
                    insertInto(this)
                        .columns(
                            ID,
                            SUPPLIER_ID,
                            CULINARY_SKU_ID,
                            MARKET,
                            STATUS
                        )
                        .values(null, null, null, "", "")
                        .onDuplicateKeyUpdate()
                        .set(ID, UUID.randomUUID())
                        .set(SUPPLIER_ID, UUID.randomUUID())
                        .set(CULINARY_SKU_ID, UUID.randomUUID())
                        .set(MARKET, "")
                        .set(STATUS, "")
                },
            )

        lateinit var runnableQuery: RunnableQuery

        fun bind(supplierSku: SupplierSku): RunnableQuery {
            if (!this::runnableQuery.isInitialized) {
                runnableQuery = RunnableQuery(query)
            }

            query.bind(
                supplierSku.id,
                supplierSku.supplierId,
                supplierSku.culinarySkuId,
                supplierSku.market,
                supplierSku.status,
                supplierSku.id,
                supplierSku.supplierId,
                supplierSku.culinarySkuId,
                supplierSku.market,
                supplierSku.status,
            )

            return runnableQuery
        }

        class RunnableQuery(val query: Batch) {
            fun executeAsync() = query.executeAsync()
        }
    }
}
