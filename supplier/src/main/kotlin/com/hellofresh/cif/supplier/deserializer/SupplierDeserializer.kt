package com.hellofresh.cif.supplier.deserializer

import com.hellofresh.cif.lib.kafka.serde.getValue
import com.hellofresh.cif.supplier.model.Supplier
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroDeserializer
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.apache.kafka.common.serialization.Deserializer

class SupplierDeserializer(schemaRegistryClient: SchemaRegistryClient) : Deserializer<List<Supplier>> {

    private val inner = KafkaAvroDeserializer(schemaRegistryClient)

    override fun close() {
        inner.close()
    }

    override fun configure(configs: MutableMap<String, *>?, isKey: <PERSON>olean) {
        inner.configure(configs, isKey)
    }

    @Suppress("VariableNaming")
    override fun deserialize(topic: String?, data: ByteArray?): List<Supplier> {
        val record = inner.deserialize(topic, data) as GenericRecord? ?: return emptyList()

        val id: String by record
        val name: String by record
        val parent_id: String by record

        return listOf(
            Supplier(
                id = UUID.fromString(id),
                name = name,
                parentId = if (parent_id.isBlank()) null else UUID.fromString(parent_id)
            )
        )
    }
}
