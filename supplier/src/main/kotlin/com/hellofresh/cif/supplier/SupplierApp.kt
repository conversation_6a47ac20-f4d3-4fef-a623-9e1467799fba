package com.hellofresh.cif.supplier

import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.supplier.deserializer.GlobalSupplierDeserializer
import com.hellofresh.cif.supplier.deserializer.SupplierDeserializer
import com.hellofresh.cif.supplier.model.Supplier
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.micrometer.core.instrument.MeterRegistry
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.common.serialization.UUIDDeserializer

class SupplierApp(
    private val parallelism: Int,
    private val consumerConfig: Map<String, String>,
    private val schemaRegistryClient: SchemaRegistryClient,
    private val pollConfig: PollConfig,
    private val metricsDSLContext: MetricsDSLContext,
    private val meterRegistry: MeterRegistry
) {
    private lateinit var facilityProcessor: CoroutinesProcessor<UUID, List<Supplier>>
    private lateinit var globalSupplyProcessor: CoroutinesProcessor<UUID, List<Supplier>>
    suspend fun runApp() {
        val performUpsert = PerformUpsert(metricsDSLContext)
        withContext(Dispatchers.IO) {
            repeat(parallelism) {
                launch {
                    facilityProcessor = CoroutinesProcessor(
                        pollConfig = pollConfig,
                        consumerProcessorConfig = facilityConsumerConfig(),
                        meterRegistry = meterRegistry,
                        process = performUpsert,
                        handleDeserializationException = DeserializationExceptionStrategy.create(
                            ConfigurationLoader.getStringOrFail("consumer.poison-pill.strategy"),
                            meterRegistry,
                        ),
                        recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                            meterRegistry,
                            "supplier_processor_write_failure",
                        ),
                    )
                    shutdownNeeded {
                        facilityProcessor.also {
                            HealthChecks.add(it)
                            StartUpChecks.add(it)
                        }
                    }.run()
                }
            }
            repeat(parallelism) {
                launch {
                    globalSupplyProcessor = CoroutinesProcessor(
                        pollConfig = pollConfig,
                        consumerProcessorConfig = globalSupplierConsumerConfig(),
                        meterRegistry = meterRegistry,
                        process = performUpsert,
                        handleDeserializationException = DeserializationExceptionStrategy.create(
                            ConfigurationLoader.getStringOrFail("consumer.poison-pill.strategy"),
                            meterRegistry,
                        ),
                        recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                            meterRegistry,
                            "supplier_processor_write_failure",
                        ),
                    )
                    shutdownNeeded {
                        globalSupplyProcessor.also {
                            HealthChecks.add(it)
                            StartUpChecks.add(it)
                        }
                    }.run()
                }
            }
        }
    }

    private fun facilityConsumerConfig() = ConsumerProcessorConfig(
        consumerConfig,
        UUIDDeserializer(),
        SupplierDeserializer(schemaRegistryClient),
        facilityTopics,
    )

    private fun globalSupplierConsumerConfig() = ConsumerProcessorConfig(
        consumerConfig,
        UUIDDeserializer(),
        GlobalSupplierDeserializer(schemaRegistryClient),
        globalSupplierTopics,
    )

    fun close() {
        facilityProcessor.close()
        globalSupplyProcessor.close()
    }
}
