package com.hellofresh.cif.supplier

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.supplier.model.Supplier
import com.hellofresh.cif.supplier.schema.tables.Supplier as SupplierTable
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.jooq.Batch
import org.jooq.impl.DSL.insertInto

class PerformUpsert(
    private val metricsDSLContext: MetricsDSLContext
) : suspend (ConsumerRecords<*, List<Supplier>>) -> Unit {

    override suspend fun invoke(records: ConsumerRecords<*, List<Supplier>>) {
        val batchQueryBuilder = BatchQueryBuilder(metricsDSLContext)
        records.flatMap { it.value() }
            .forEach { supplier ->
                supplier.let {
                    batchQueryBuilder
                        .bind(it)
                        .executeAsync()
                        .await()
                }
            }
    }

    /**
     * Provides a safe interface to build PreparedStatement
     */
    private class BatchQueryBuilder(dsl: MetricsDSLContext) {
        private val query = dsl.withTagName("insert-supplier")
            .batch(
                SupplierTable.SUPPLIER.run {
                    insertInto(this)
                        .columns(
                            ID,
                            NAME,
                            PARENT_ID,
                        )
                        .values(null, "", null)
                        .onDuplicateKeyUpdate()
                        .set(ID, UUID.randomUUID())
                        .set(NAME, "")
                        .set(PARENT_ID, UUID.randomUUID())
                },
            )

        lateinit var runnableQuery: RunnableQuery

        fun bind(supplier: Supplier): RunnableQuery {
            if (!this::runnableQuery.isInitialized) {
                runnableQuery = RunnableQuery(query)
            }

            query.bind(
                supplier.id,
                supplier.name,
                supplier.parentId,
                supplier.id,
                supplier.name,
                supplier.parentId,
            )

            return runnableQuery
        }

        class RunnableQuery(val query: Batch) {
            fun executeAsync() = query.executeAsync()
        }
    }
}
