package com.hellofresh.cif.supplier.sku

import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.supplier.deserializer.SupplierSkuDeserializer
import com.hellofresh.cif.supplier.model.SupplierSku
import com.hellofresh.cif.supplier.supplierSkuTopics
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.micrometer.core.instrument.MeterRegistry
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.common.serialization.UUIDDeserializer

class SupplierSkuApp(
    private val parallelism: Int,
    private val consumerConfig: Map<String, String>,
    private val schemaRegistryClient: SchemaRegistryClient,
    private val pollConfig: PollConfig,
    private val metricsDSLContext: MetricsDSLContext,
    private val meterRegistry: MeterRegistry
) {
    private lateinit var supplierSkusProcessor: CoroutinesProcessor<UUID, List<SupplierSku>>

    suspend fun runApp() {
        val updateSupplierSku = PerformSupplierSkuUpsert(metricsDSLContext)
        withContext(Dispatchers.IO) {
            repeat(parallelism) {
                launch {
                    supplierSkusProcessor = CoroutinesProcessor(
                        pollConfig = pollConfig,
                        consumerProcessorConfig = supplierSkuConsumer(),
                        meterRegistry = meterRegistry,
                        process = updateSupplierSku,
                        handleDeserializationException = DeserializationExceptionStrategy.create(
                            ConfigurationLoader.getStringOrFail("consumer.poison-pill.strategy"),
                            meterRegistry,
                        ),
                        recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                            meterRegistry,
                            "supplier_sku_processor_write_failure",
                        ),
                    )
                    shutdownNeeded {
                        supplierSkusProcessor.also {
                            HealthChecks.add(it)
                            StartUpChecks.add(it)
                        }
                    }.run()
                }
            }
        }
    }

    fun close() {
        supplierSkusProcessor.close()
    }

    private fun supplierSkuConsumer() = ConsumerProcessorConfig(
        consumerConfig,
        UUIDDeserializer(),
        SupplierSkuDeserializer(schemaRegistryClient),
        supplierSkuTopics,
    )
}
