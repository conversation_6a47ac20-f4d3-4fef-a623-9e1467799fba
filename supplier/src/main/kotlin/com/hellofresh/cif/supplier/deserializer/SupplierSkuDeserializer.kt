package com.hellofresh.cif.supplier.deserializer

import com.hellofresh.cif.lib.kafka.serde.getValue
import com.hellofresh.cif.supplier.model.SupplierSku
import io.confluent.kafka.schemaregistry.client.SchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroDeserializer
import java.util.UUID
import org.apache.avro.generic.GenericRecord
import org.apache.kafka.common.serialization.Deserializer

class SupplierSkuDeserializer(schemaRegistryClient: SchemaRegistryClient) : Deserializer<List<SupplierSku>> {

    private val inner = KafkaAvroDeserializer(schemaRegistryClient)

    override fun close() {
        inner.close()
    }

    override fun configure(configs: MutableMap<String, *>?, isKey: Boolean) {
        inner.configure(configs, isKey)
    }

    @Suppress("VariableNaming")
    override fun deserialize(topic: String?, data: ByteArray?): List<SupplierSku> {
        val record = inner.deserialize(topic, data) as GenericRecord? ?: return emptyList()
        val id: String by record
        val supplier_id: String by record
        val culinary_sku_id: String by record
        val market: String by record
        val status: String by record

        return listOf(
            SupplierSku(
                id = UUID.fromString(id),
                supplierId = UUID.fromString(supplier_id),
                culinarySkuId = UUID.fromString(culinary_sku_id),
                market = market,
                status = status
            )
        )
    }
}
