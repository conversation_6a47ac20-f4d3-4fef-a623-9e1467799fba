package com.hellofresh.cif.advanceshippingnotice

import com.google.type.DateTime
import com.hellofresh.cif.advanceshippingnotice.model.PoAsn
import com.hellofresh.cif.advanceshippingnotice.model.PoAsnSku
import com.hellofresh.cif.advanceshippingnotice.repo.AdvanceShippingNoticeRepository
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.lib.uom.toUOM
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.AsnItem
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.AsnItem.Quantity.CasePackaging.UnitOfMeasure.UNIT_OF_MEASURE_CASE
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.AsnItem.Quantity.CasePackaging.UnitOfMeasure.UNIT_OF_MEASURE_UNIT
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.AsnItem.Quantity.PackagingCase.CASE_PACKAGING
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.AsnItem.Quantity.PackagingCase.UNIT_PACKAGING
import com.hellofresh.sku.models.SkuSpecification
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.logging.log4j.kotlin.Logging

class AdvanceShippingNoticeService(
    private val advanceShippingNoticeRepository: AdvanceShippingNoticeRepository,
    private val dcConfigService: DcConfigService,
    private val skuSpecificationService: SkuSpecificationService
) {
    suspend fun processRecords(records: ConsumerRecords<String, PurchaseOrderAsn>) {
        records.asSequence()
            .filter { it.key().isNotBlank() }
            .map { it.value() }
            .filter { it.asnId.isNotBlank() }
            .forEach { asn -> processAsnRecord(asn) }
    }

    private suspend fun processAsnRecord(purchaseOrderAsn: PurchaseOrderAsn) {
        dcConfigService.dcConfigurations[purchaseOrderAsn.shipment.distributionCenter]?.also {
            if (purchaseOrderAsn.itemsList.isEmpty()) {
                return
            }
            val mappedPurchaseOrderAsn =
                mapPurchaseOrderAsn(purchaseOrderAsn, it.zoneId, skuSpecificationService.specifications)
            if (mappedPurchaseOrderAsn.skus.isNotEmpty()) {
                advanceShippingNoticeRepository.upsert(mappedPurchaseOrderAsn)
            } else {
                logger.warn("${mappedPurchaseOrderAsn.id} asn id having unsupported unit of measure for all skus.")
            }
        } ?: run {
            logger.warn("Dc not found ${purchaseOrderAsn.shipment.distributionCenter} for po ${purchaseOrderAsn.asnId}")
        }
    }

    internal fun mapPurchaseOrderAsn(
        purchaseOrderAsn: PurchaseOrderAsn,
        zoneId: ZoneId,
        skuSpecifications: Map<UUID, SkuSpecification>,
    ) = PoAsn(
        purchaseOrderAsn.asnId,
        UUID.fromString(purchaseOrderAsn.purchaseOrderId),
        purchaseOrderAsn.purchaseOrderNumber,
        purchaseOrderAsn.shipment.distributionCenter,
        convertToOffsetDateTime(purchaseOrderAsn.shipment.plannedDeliveryTime, zoneId),
        UUID.fromString(purchaseOrderAsn.supplier.id),
        purchaseOrderAsn.itemsList
            .asSequence()
            .filter(::isValidPackaging)
            .map { asnItem -> mapToPoAsnSku(purchaseOrderAsn.asnId, asnItem) }
            .filterNotNull()
            .filter {
                (skuSpecifications[it.skuId]?.uom == it.skuUOM).also { isEqual ->
                    if (!isEqual) {
                        logger.error("UOM mismatch for sku ${it.skuId} in ASN ${purchaseOrderAsn.asnId}")
                    }
                }
            }
            .groupingBy { it.skuId }
            .reduce { _, acc, sku ->
                acc.copy(shippedQuantity = acc.shippedQuantity + sku.shippedQuantity)
            }.values.toList(),
    )

    private fun mapToPoAsnSku(asnId: String, asnItem: AsnItem): PoAsnSku? {
        val orderSize = asnItem.shippedQuantity.orderSize
        val skuQuantity = when (asnItem.shippedQuantity.packagingCase) {
            UNIT_PACKAGING -> orderSize.toLong()
            CASE_PACKAGING -> when (asnItem.shippedQuantity.casePackaging.unit) {
                UNIT_OF_MEASURE_UNIT -> orderSize.toLong()
                UNIT_OF_MEASURE_CASE -> {
                    val shippedQuantity = asnItem.shippedQuantity.casePackaging.size.value.toBigDecimal()
                    if (shippedQuantity.stripTrailingZeros().scale() > 0) {
                        logger.warn(
                            "Shipped quantity received with fraction digits, dropping fraction: " +
                                "$shippedQuantity",
                        )
                    }
                    shippedQuantity.toLong() * orderSize
                }

                else -> {
                    logger.warn("Invalid packaging unit")
                    null
                }
            }

            else -> {
                logger.warn("Invalid packaging case")
                null
            }
        }
        val skuUOM = if (asnItem.shippedQuantity.packagingCase == UNIT_PACKAGING) {
            SkuUOM.UOM_UNIT
        } else {
            asnItem.shippedQuantity.casePackaging.unit.toUOM()
        }
        return skuQuantity?.let { PoAsnSku(asnId, UUID.fromString(asnItem.id), it, skuUOM) }
    }

    private fun isValidPackaging(sku: AsnItem) =
        when (sku.shippedQuantity.packagingCase) {
            UNIT_PACKAGING -> true
            CASE_PACKAGING ->
                when (sku.shippedQuantity.casePackaging.unit) {
                    UNIT_OF_MEASURE_UNIT, UNIT_OF_MEASURE_CASE -> true
                    else -> false
                }

            else -> false
        }

    private fun convertToOffsetDateTime(protobufDateTime: DateTime, zoneId: ZoneId) =
        LocalDateTime.of(
            protobufDateTime.year,
            protobufDateTime.month,
            protobufDateTime.day,
            protobufDateTime.hours,
            protobufDateTime.minutes,
            protobufDateTime.seconds,
            protobufDateTime.nanos,
        ).atZone(zoneId).toOffsetDateTime()

    companion object : Logging
}
