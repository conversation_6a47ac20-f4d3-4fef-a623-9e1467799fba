package com.hellofresh.cif.purchaseorder.model

import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState
import java.time.OffsetDateTime
import java.util.UUID

data class Po(
    val poRef: String,
    val poId: UUID?,
    val poNumber: String,
    val dc: String,
    val state: PoState,
    val status: PoStatus?,
    val expectedArrivalStartTime: OffsetDateTime,
    val expectedArrivalEndTime: OffsetDateTime,
    val supplierId: UUID,
    val supplierName: String?,
    val skus: List<PoSku>,
)

data class PoSku(val id: UUID, val quantity: Long, val skuUOM: SkuUOM)

enum class PoStatus {
    Planned,
    Sent,
    Accepted
}
