package com.hellofresh.cif.advanceshippingnotice.repo

import com.hellofresh.cif.advanceshippingnotice.model.PoAsn
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.purchaseorder.schema.Tables.ADVANCED_SHIPPING_NOTICE
import com.hellofresh.cif.purchaseorder.schema.Tables.ADVANCED_SHIPPING_NOTICE_SKU
import com.hellofresh.cif.purchaseorder.schema.enums.Uom
import com.hellofresh.cif.purchaseorder.schema.enums.Uom.UOM_UNIT
import java.math.BigDecimal
import java.util.UUID
import kotlinx.coroutines.future.await
import org.apache.logging.log4j.kotlin.Logging
import org.jooq.DSLContext

class AdvanceShippingNoticeRepositoryImpl(private val metricsDSLContext: MetricsDSLContext) :
    AdvanceShippingNoticeRepository {

    private val upsertPurchaseOrderAsnDuration = "upsert-purchase-order-asn"

    override suspend fun upsert(purchaseOrderAsn: PoAsn) {
        logger.info("Saving/Updating po asn: $purchaseOrderAsn")
        metricsDSLContext.withTagName(upsertPurchaseOrderAsnDuration).transactionAsync { tx ->
            val txDsl = tx.dsl()
            txDsl.insertInto(ADVANCED_SHIPPING_NOTICE)
                .columns(
                    ADVANCED_SHIPPING_NOTICE.ASN_ID,
                    ADVANCED_SHIPPING_NOTICE.PO_NUMBER,
                    ADVANCED_SHIPPING_NOTICE.PO_ID,
                    ADVANCED_SHIPPING_NOTICE.DC_CODE,
                    ADVANCED_SHIPPING_NOTICE.PLANNED_DELIVERY_TIME,
                    ADVANCED_SHIPPING_NOTICE.SUPPLIER_ID
                ).values(
                    purchaseOrderAsn.id,
                    purchaseOrderAsn.poNumber,
                    purchaseOrderAsn.poId,
                    purchaseOrderAsn.dc,
                    purchaseOrderAsn.plannedDeliveryTime,
                    purchaseOrderAsn.supplierId
                ).onDuplicateKeyUpdate()
                .set(ADVANCED_SHIPPING_NOTICE.ASN_ID, purchaseOrderAsn.id)
                .set(ADVANCED_SHIPPING_NOTICE.PO_NUMBER, purchaseOrderAsn.poNumber)
                .set(ADVANCED_SHIPPING_NOTICE.PO_ID, purchaseOrderAsn.poId)
                .set(ADVANCED_SHIPPING_NOTICE.DC_CODE, purchaseOrderAsn.dc)
                .set(ADVANCED_SHIPPING_NOTICE.PLANNED_DELIVERY_TIME, purchaseOrderAsn.plannedDeliveryTime)
                .set(ADVANCED_SHIPPING_NOTICE.SUPPLIER_ID, purchaseOrderAsn.supplierId)
                .execute()

            with(createInsertUpdateSkuBatch(txDsl)) {
                purchaseOrderAsn.skus.map { poAsnSku ->
                    bind(
                        poAsnSku.asnId,
                        poAsnSku.skuId,
                        poAsnSku.shippedQuantity,
                        toUom(poAsnSku.skuUOM),
                        poAsnSku.shippedQuantity,
                        toUom(poAsnSku.skuUOM),
                    )
                }
                execute()
            }

            txDsl.deleteFrom(ADVANCED_SHIPPING_NOTICE_SKU)
                .where(ADVANCED_SHIPPING_NOTICE_SKU.ASN_ID.eq(purchaseOrderAsn.id))
                .and(ADVANCED_SHIPPING_NOTICE_SKU.SKU_ID.notIn(purchaseOrderAsn.skus.map { it.skuId }))
                .execute()
        }.await()
    }
    private fun createInsertUpdateSkuBatch(dslContext: DSLContext) = dslContext.batch(
        dslContext.insertInto(ADVANCED_SHIPPING_NOTICE_SKU)
            .columns(
                ADVANCED_SHIPPING_NOTICE_SKU.ASN_ID,
                ADVANCED_SHIPPING_NOTICE_SKU.SKU_ID,
                ADVANCED_SHIPPING_NOTICE_SKU.SHIPPED_QUANTITY,
                ADVANCED_SHIPPING_NOTICE_SKU.UOM
            ).values(
                "",
                UUID(0, 0),
                BigDecimal.ZERO,
                Uom.UOM_UNIT
            ).onDuplicateKeyUpdate()
            .set(ADVANCED_SHIPPING_NOTICE_SKU.SHIPPED_QUANTITY, BigDecimal.ZERO)
            .set(ADVANCED_SHIPPING_NOTICE_SKU.UOM, Uom.UOM_UNIT)
    )

    private fun toUom(skuUOM: SkuUOM): Uom =
        when (skuUOM) {
            SkuUOM.UOM_UNIT -> UOM_UNIT

            SkuUOM.UOM_KG -> Uom.UOM_KG
            SkuUOM.UOM_LBS -> Uom.UOM_LBS
            SkuUOM.UOM_OZ -> Uom.UOM_OZ
            SkuUOM.UOM_GAL -> Uom.UOM_GAL
            SkuUOM.UOM_LITRE -> Uom.UOM_LITRE
            SkuUOM.UOM_UNRECOGNIZED, SkuUOM.UOM_UNSPECIFIED -> Uom.UOM_UNSPECIFIED
        }

    companion object : Logging
}
