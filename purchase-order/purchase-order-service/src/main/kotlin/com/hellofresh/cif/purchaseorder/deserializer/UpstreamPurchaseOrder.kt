package com.hellofresh.cif.purchaseorder.deserializer

import com.google.type.Decimal
import com.hellofresh.cif.models.SkuUOM
import java.time.OffsetDateTime

data class UpstreamPurchaseOrder(
    val revision: PurchaseOrderRevision,
    val id: String?,
    val distributionCenterCode: String,
    val state: PoState,
    val expectedArrivalStartTime: OffsetDateTime,
    val expectedArrivalEndTime: OffsetDateTime,
    val supplierId: String,
    val supplierName: String? = null,
    val sendTime: OffsetDateTime?,
    val updateTime: OffsetDateTime,
    val orderItems: List<PurchaseOrderItem>,
) {
    data class PurchaseOrderItem(
        val skuId: String,
        val quantity: Decimal,
        val packagingUnit: SkuUOM,
    )

    data class PurchaseOrderRevision(
        val formatted: String,
        val number: PoNumber,
        val type: PurchaseOrderType
    )

    enum class PurchaseOrderType {
        UNSPECIFIED,
        STANDARD,
        EMERGENCY,
        PROVISIONAL,
        UNRECOGNIZED,
    }

    data class PoNumber(
        val formatted: String
    )

    enum class PoState {
        Unspecified,
        Initiated,
        Approved,
        Rejected,
        Deleted
    }
}
