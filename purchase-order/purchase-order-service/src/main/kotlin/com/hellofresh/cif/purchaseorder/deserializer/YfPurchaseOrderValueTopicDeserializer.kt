package com.hellofresh.cif.purchaseorder.deserializer

import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.lib.uom.toUOM
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoNumber
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState.Approved
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState.Deleted
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState.Initiated
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState.Rejected
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PoState.Unspecified
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PurchaseOrderRevision
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PurchaseOrderType.EMERGENCY
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PurchaseOrderType.PROVISIONAL
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PurchaseOrderType.STANDARD
import com.hellofresh.cif.purchaseorder.deserializer.UpstreamPurchaseOrder.PurchaseOrderType.UNSPECIFIED
import com.hellofresh.dateUtil.models.toOffsetDateTime
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.PurchaseOrderItem
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.PurchaseOrderItem.PackagingCase.CASE_PACKAGING
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.PurchaseOrderItem.PackagingCase.PACKAGING_NOT_SET
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.PurchaseOrderItem.PackagingCase.UNIT_PACKAGING
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.State.STATE_APPROVED
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.State.STATE_DELETED
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.State.STATE_INITIATED
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.State.STATE_REJECTED
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.State.STATE_UNSPECIFIED
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.State.UNRECOGNIZED
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrderRevision.PurchaseOrderType
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrderRevision.PurchaseOrderType.PURCHASE_ORDER_TYPE_EMERGENCY
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrderRevision.PurchaseOrderType.PURCHASE_ORDER_TYPE_PROVISIONAL
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrderRevision.PurchaseOrderType.PURCHASE_ORDER_TYPE_STANDARD
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrderRevision.PurchaseOrderType.PURCHASE_ORDER_TYPE_UNSPECIFIED
import java.time.ZoneId
import org.apache.kafka.common.serialization.Deserializer

class YfPurchaseOrderValueTopicDeserializer(
    private val dcConfigService: DcConfigService
) : Deserializer<UpstreamPurchaseOrder> {

    override fun deserialize(p0: String?, data: ByteArray?): UpstreamPurchaseOrder {
        check(data != null) { "purchase order message value can not be null" }
        return with(
            PurchaseOrder.parseFrom(
                data,
            ),
        ) {
            getZoneId(dcConfigService, distributionCenterCode, revision.formatted).let {
                UpstreamPurchaseOrder(
                    revision = PurchaseOrderRevision(
                        formatted = revision.formatted,
                        number = PoNumber(revision.number.formatted),
                        type = mapType(revision.type),
                    ),
                    id = null,
                    distributionCenterCode = distributionCenterCode,
                    state = mapState(status),
                    expectedArrivalStartTime = expectedArrival.startTime.toOffsetDateTime(zoneId = it),
                    expectedArrivalEndTime = expectedArrival.endTime.toOffsetDateTime(zoneId = it),
                    supplierId = supplierId,
                    supplierName = supplierName,
                    sendTime = sendTime.toOffsetDateTime(zoneId = it),
                    updateTime = updateTime.toOffsetDateTime(zoneId = it),
                    orderItems = orderItemsList.map { orderItem ->
                        UpstreamPurchaseOrder.PurchaseOrderItem(
                            skuId = orderItem.skuId,
                            quantity = orderItem.quantity,
                            packagingUnit = mapPackagingUnit(orderItem),
                        )
                    },
                )
            }
        }
    }
}

private fun mapState(purchaseOrderStatus: PurchaseOrder.State) =
    when (purchaseOrderStatus) {
        STATE_INITIATED -> Initiated
        STATE_APPROVED -> Approved
        STATE_REJECTED -> Rejected
        STATE_DELETED -> Deleted
        STATE_UNSPECIFIED, UNRECOGNIZED -> Unspecified
    }

private fun mapType(purchaseOrderStatus: PurchaseOrderType) =
    when (purchaseOrderStatus) {
        PURCHASE_ORDER_TYPE_UNSPECIFIED -> UNSPECIFIED
        PURCHASE_ORDER_TYPE_PROVISIONAL -> PROVISIONAL
        PURCHASE_ORDER_TYPE_EMERGENCY -> EMERGENCY
        PURCHASE_ORDER_TYPE_STANDARD -> STANDARD
        PurchaseOrderType.UNRECOGNIZED -> UNSPECIFIED
    }

private fun mapPackagingUnit(item: PurchaseOrderItem): SkuUOM =
    when (item.packagingCase) {
        UNIT_PACKAGING -> SkuUOM.UOM_UNIT
        CASE_PACKAGING -> item.casePackaging.unit.toUOM()
        PACKAGING_NOT_SET -> SkuUOM.UOM_UNSPECIFIED
        null -> SkuUOM.UOM_UNSPECIFIED
    }

private fun getZoneId(dcConfigService: DcConfigService, dcCode: String, po: String): ZoneId =
    dcConfigService.dcConfigurations[dcCode]?.zoneId ?: throw IllegalArgumentException(
        "DC configuration not found for DC: $dcCode in PO: $po"
    )
