package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.models.purchaseorder.PoStatus
import com.hellofresh.cif.purchase_order_lib.schema.enums.PurchaseOrderStatus
import com.hellofresh.cif.purchase_order_lib.schema.enums.PurchaseOrderStatus.Accepted
import com.hellofresh.cif.purchase_order_lib.schema.enums.PurchaseOrderStatus.Planned
import com.hellofresh.cif.purchase_order_lib.schema.enums.PurchaseOrderStatus.Sent
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test

class PoStatusMapperTest {

    @Test
    fun `database enum is correctly mapped to domain status`() {
        PurchaseOrderStatus.entries.forEach { status ->
            val domainStatus = status.toPoStatus()
            when (status) {
                Planned -> assertEquals(PoStatus.PLANNED, domainStatus)
                Sent -> assertEquals(PoStatus.SENT, domainStatus)
                Accepted -> assertEquals(PoStatus.APPROVED, domainStatus)
            }
        }
    }
}
