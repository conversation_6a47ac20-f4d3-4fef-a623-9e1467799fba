package com.hellofresh.cif.purchaseorder

import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.purchase_order_lib.schema.enums.Uom
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.AdvancedShippingNoticeRecord
import com.hellofresh.cif.purchase_order_lib.schema.tables.records.AdvancedShippingNoticeSkuRecord
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

internal class PurchaseOrderAsnRepositoryImplTest : TestPrepare() {
    private val dcCode = "VE"
    private val skuId = UUID.randomUUID()
    private val expectedArrivalStartTime = OffsetDateTime.now(UTC)
    private val expectedArrivalEndTime = expectedArrivalStartTime.plusHours(2)
    private val expectedArrivalDate = expectedArrivalStartTime.toLocalDate()
    private val poRef = "2305DH046273_01"
    private val poId = UUID.randomUUID()
    private val poNumber = UUID.randomUUID().toString()
    private val poQuantity = 12345L
    private val asnId: String = "ASN12345"
    private val supplierId = UUID.randomUUID()
    private val pastDate1 = OffsetDateTime.now(UTC).withHour(5).withMinute(59).minusDays(1)
    private val pastDate2 = OffsetDateTime.now(UTC).withHour(5).withMinute(59).minusDays(20)
    private val pastDate3NotInRange = OffsetDateTime.now(UTC).withHour(23).withMinute(59).minusDays(22)
    private val defaultPurchaseOrder = PurchaseOrderRecord(
        poRef, poId, poNumber, dcCode, expectedArrivalStartTime,
        expectedArrivalEndTime,
        UUID.randomUUID(),
        UUID.randomUUID().toString(),
        skuId,
        poQuantity,
    )

    private val defaultPurchaseOrderAsn = AdvancedShippingNoticeRecord(
        asnId,
        poNumber,
        poId,
        dcCode,
        expectedArrivalStartTime.minusDays(1),
        supplierId,
        OffsetDateTime.now(ZoneId.of("UTC")),
        OffsetDateTime.now(ZoneId.of("UTC")),
    )

    private val defaultPurchaseOrderAsnSku = AdvancedShippingNoticeSkuRecord(
        asnId,
        skuId,
        poQuantity.toBigDecimal(),
        OffsetDateTime.now(ZoneId.of("UTC")),
        OffsetDateTime.now(ZoneId.of("UTC")),
        Uom.UOM_UNIT,
    )

    @Test
    fun `PoService returns list of purchase orders for multiple dcs, sku and days`() {
        val poQuantity1: Long = 10
        val expectedPurchaseOrder1 = PurchaseOrderRecord(
            poRef,
            poId,
            UUID.randomUUID().toString(),
            dcCode,
            expectedArrivalStartTime,
            expectedArrivalEndTime,
            UUID.randomUUID(),
            UUID.randomUUID().toString(),
            null,
            listOf(
                PurchaseOrderSkuRecord(skuId, poQuantity1),
                PurchaseOrderSkuRecord(UUID.randomUUID(), poQuantity1),
            ),
        )
        val expectedPurchaseOrder2 = expectedPurchaseOrder1.copy(
            poRef = poRef + "2",
            poId = UUID.randomUUID(),
            poNumber = UUID.randomUUID().toString(),
            dcCode = dcCode + "1",
            expectedDateStartTime = expectedArrivalStartTime.minusDays(1),
            expectedDateEndTime = expectedArrivalEndTime.minusDays(1),
            supplierId = UUID.randomUUID(),
            supplierName = UUID.randomUUID().toString(),
            skus = listOf(
                PurchaseOrderSkuRecord(skuId, poQuantity1),
                PurchaseOrderSkuRecord(UUID.randomUUID(), poQuantity1),
            ),
        )
        persistDcConfig(dcCode = expectedPurchaseOrder2.dcCode)
        val expectedPOs = listOf(expectedPurchaseOrder1, expectedPurchaseOrder2)
        persistPurchaseOrderAndRefreshPoInfoView(expectedPOs)

        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrdersWithAsns(
                skuId,
                setOf(expectedPurchaseOrder1.dcCode, expectedPurchaseOrder2.dcCode),
                setOf(DateRange(expectedArrivalDate.minusDays(1), expectedArrivalDate)),
            )
        }

        // then
        assertEquals(2, purchaseOrders.size)
        setOf(expectedPurchaseOrder1, expectedPurchaseOrder2)
            .forEach { expectedPO ->
                purchaseOrders.first { it.poReference == expectedPO.poRef }
                    .also {
                        assertEquals(2, it.purchaseOrderSkus.size)
                        assertEquals(expectedPO.poId, it.poId)
                    }
            }
    }

    @Test
    fun `returns POs by skuId, dcCode and date ranges that match having po, grn, asn using local dc time`() {
        val canadaDcCode = "BL"
        val zoneId = ZoneId.of("America/Winnipeg")
        val arrivalDateTime = OffsetDateTime.now(UTC)
        val purchaseOrderAsnForCanadaDc = AdvancedShippingNoticeRecord(
            asnId,
            poNumber,
            poId,
            canadaDcCode,
            arrivalDateTime.minusDays(1),
            supplierId,
            OffsetDateTime.now(UTC),
            OffsetDateTime.now(UTC),
        )
        val pastDate1 = arrivalDateTime.minusDays(1).atZoneSameInstant(zoneId).toOffsetDateTime()
        val pastDate2 = arrivalDateTime.minusDays(20).atZoneSameInstant(zoneId).toOffsetDateTime()
        val pastDate3NotInRange = arrivalDateTime.minusDays(22)

        val grnPastDate1 = createGrn(
            deliveryDate = pastDate1,
            dcCode = canadaDcCode,
            poNumber = poNumber,
            poRef = poRef,
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
        )
        val poPastDate1 =
            defaultPurchaseOrder.copy(dcCode = canadaDcCode, expectedDateStartTime = pastDate1)

        val poPastDate2 = defaultPurchaseOrder.copy(
            dcCode = canadaDcCode,
            poId = UUID.randomUUID(),
            poNumber = "PO2",
            poRef = "PO2",
            supplierId = UUID.randomUUID(),
            expectedDateStartTime = pastDate2,
        )
        val grnPastDate2 = grnPastDate1.copy(
            dcCode = canadaDcCode,
            deliveryDate = pastDate2,
            poNumber = poPastDate2.poNumber,
            poRef = poPastDate2.poRef,
        )

        val poPastDate3NotInRange = defaultPurchaseOrder.copy(
            dcCode = canadaDcCode,
            poId = UUID.randomUUID(),
            poNumber = "PO3",
            poRef = "PO3",
            supplierId = UUID.randomUUID(),
            expectedDateStartTime = pastDate3NotInRange,
        )
        val defaultPurchaseOrderAsnSku1 = createAsnSkuRecord(
            10000L,
            UUID.randomUUID(),
        )
        val defaultPurchaseOrderAsnSku2 = createAsnSkuRecord(
            20000L,
            UUID.randomUUID(),
        )
        val grnPastDate3NotInRange =
            grnPastDate1.copy(
                dcCode = canadaDcCode,
                deliveryDate = pastDate3NotInRange,
                poNumber = poPastDate3NotInRange.poNumber,
                poRef = poPastDate3NotInRange.poRef,
            )
        persistDcConfig(dcCode = canadaDcCode, market = "CA", zoneId = zoneId.id)
        persistPurchaseOrderAndRefreshPoInfoView(listOf(poPastDate1, poPastDate2, poPastDate3NotInRange))
        persistGoodsReceivedNotes(setOf(grnPastDate1, grnPastDate2, grnPastDate3NotInRange))
        persistPurchaseOrderAsn(
            purchaseOrderAsnForCanadaDc,
            listOf(
                defaultPurchaseOrderAsnSku,
                defaultPurchaseOrderAsnSku1,
                defaultPurchaseOrderAsnSku2,
            ),
        )

        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrdersWithAsns(
                skuId,
                setOf(canadaDcCode),
                setOf(
                    DateRange(pastDate1.toLocalDate(), pastDate1.toLocalDate()),
                    DateRange(pastDate2.toLocalDate(), pastDate2.toLocalDate()),
                ),
            )
        }

        // then
        assertEquals(2, purchaseOrders.size)
        purchaseOrders.first { it.number == poPastDate1.poNumber }.assertPurchaseOrder(grnPastDate1, poPastDate1)
        purchaseOrders.first { it.number == poPastDate2.poNumber }.assertPurchaseOrder(grnPastDate2, poPastDate2)
        val purchaseOrder = purchaseOrders.first { it.number == poPastDate1.poNumber }
        assertEquals(3, purchaseOrder.asns.size)
        purchaseOrder.asns
            .firstOrNull { it.id == asnId }
            ?.let { asn ->
                assertEquals(
                    purchaseOrderAsnForCanadaDc.plannedDeliveryTime.toLocalDate(),
                    asn.plannedDeliveryTime.toLocalDate(),
                )
                assertEquals(
                    defaultPurchaseOrderAsnSku.shippedQuantity.toLong(),
                    asn.shippedQuantity.getValue().toLong()
                )
            }
    }

    @Test
    fun `returns POs by skuId, dcCode and date ranges that match having po, grn, no asn`() {
        val grnPastDate1 = createGrn(
            deliveryDate = pastDate1,
            dcCode = dcCode,
            poNumber = poNumber,
            poRef = poRef,
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
        )
        val poPastDate1 =
            defaultPurchaseOrder.copy(expectedDateStartTime = pastDate1)

        val poPastDate2 = defaultPurchaseOrder.copy(
            poId = UUID.randomUUID(),
            poNumber = "PO2",
            poRef = "PO2_2",
            supplierId = UUID.randomUUID(),
            expectedDateStartTime = pastDate2,
        )
        val grnPastDate2 = grnPastDate1.copy(
            deliveryDate = pastDate2,
            poNumber = poPastDate2.poNumber,
            poRef = poPastDate2.poRef,
        )

        val poPastDate3NotInRange = defaultPurchaseOrder.copy(
            poId = UUID.randomUUID(),
            poNumber = "PO3",
            poRef = "PO3_3",
            supplierId = UUID.randomUUID(),
            expectedDateStartTime = pastDate3NotInRange,
        )

        val grnPastDate3NotInRange =
            grnPastDate1.copy(
                deliveryDate = pastDate3NotInRange,
                poNumber = poPastDate3NotInRange.poNumber,
                poRef = poPastDate3NotInRange.poRef,
            )

        persistPurchaseOrderAndRefreshPoInfoView(listOf(poPastDate1, poPastDate2, poPastDate3NotInRange))
        persistGoodsReceivedNotes(setOf(grnPastDate1, grnPastDate2, grnPastDate3NotInRange))

        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrdersWithAsns(
                skuId,
                setOf(dcCode),
                setOf(
                    DateRange(pastDate1.toLocalDate(), pastDate1.toLocalDate()),
                    DateRange(pastDate2.toLocalDate(), pastDate2.toLocalDate()),
                ),
            )
        }

        // then
        assertEquals(2, purchaseOrders.size)
        purchaseOrders.first { it.number == poPastDate1.poNumber }.assertPurchaseOrder(grnPastDate1, poPastDate1)
        purchaseOrders.first { it.number == poPastDate2.poNumber }.assertPurchaseOrder(grnPastDate2, poPastDate2)
    }

    @Test
    fun `returns POs by skuId, dcCode and date ranges that match having po, grn, asn`() {
        val grnPastDate1 = createGrn(
            deliveryDate = pastDate1,
            dcCode = dcCode,
            poNumber = poNumber,
            poRef = poRef,
            skuId = skuId,
            deliveryInfoStatus = CLOSED,
        )
        val poPastDate1 =
            defaultPurchaseOrder.copy(expectedDateStartTime = pastDate1)

        val poPastDate2 = defaultPurchaseOrder.copy(
            poId = UUID.randomUUID(),
            poNumber = "PO2",
            poRef = "PO2_2",
            supplierId = UUID.randomUUID(),
            expectedDateStartTime = pastDate2,
        )
        val grnPastDate2 = grnPastDate1.copy(
            deliveryDate = pastDate2,
            poNumber = poPastDate2.poNumber,
            poRef = poPastDate2.poRef,
        )

        val poPastDate3NotInRange = defaultPurchaseOrder.copy(
            poId = UUID.randomUUID(),
            poNumber = "PO3",
            poRef = "PO3_3",
            supplierId = UUID.randomUUID(),
            expectedDateStartTime = pastDate3NotInRange,
        )
        val defaultPurchaseOrderAsnSku1 = createAsnSkuRecord(
            10000L,
            UUID.randomUUID(),
        )
        val defaultPurchaseOrderAsnSku2 = createAsnSkuRecord(
            20000L,
            UUID.randomUUID(),
        )
        val grnPastDate3NotInRange =
            grnPastDate1.copy(
                deliveryDate = pastDate3NotInRange,
                poNumber = poPastDate3NotInRange.poNumber,
                poRef = poPastDate3NotInRange.poRef,
            )

        persistPurchaseOrderAndRefreshPoInfoView(listOf(poPastDate1, poPastDate2, poPastDate3NotInRange))
        persistGoodsReceivedNotes(setOf(grnPastDate1, grnPastDate2, grnPastDate3NotInRange))
        persistPurchaseOrderAsn(
            defaultPurchaseOrderAsn,
            listOf(
                defaultPurchaseOrderAsnSku,
                defaultPurchaseOrderAsnSku1,
                defaultPurchaseOrderAsnSku2,
            ),
        )

        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrdersWithAsns(
                skuId,
                setOf(dcCode),
                setOf(
                    DateRange(pastDate1.toLocalDate(), pastDate1.toLocalDate()),
                    DateRange(pastDate2.toLocalDate(), pastDate2.toLocalDate()),
                ),
            )
        }

        // then
        assertEquals(2, purchaseOrders.size)
        purchaseOrders.first { it.number == poPastDate1.poNumber }.assertPurchaseOrder(grnPastDate1, poPastDate1)
        purchaseOrders.first { it.number == poPastDate2.poNumber }.assertPurchaseOrder(grnPastDate2, poPastDate2)
        val purchaseOrder = purchaseOrders.first { it.number == poPastDate1.poNumber }
        assertEquals(3, purchaseOrder.asns.size)
        purchaseOrder.asns
            .firstOrNull { it.id == asnId }
            ?.let { asn ->
                assertEquals(
                    defaultPurchaseOrderAsn.plannedDeliveryTime.toLocalDate(),
                    asn.plannedDeliveryTime.toLocalDate(),
                )
                assertEquals(
                    defaultPurchaseOrderAsnSku.shippedQuantity.toLong(),
                    asn.shippedQuantity.getValue().toLong()
                )
            }
    }

    @Test
    fun `returns POs by skuId, dcCode and date ranges that match having po, asn, no grn`() {
        val poPastDate1 =
            defaultPurchaseOrder.copy(expectedDateStartTime = pastDate1)

        val poPastDate2 = defaultPurchaseOrder.copy(
            poId = UUID.randomUUID(),
            poRef = "PO2",
            poNumber = UUID.randomUUID().toString(),
            supplierId = UUID.randomUUID(),
            expectedDateStartTime = pastDate2,
        )

        val poPastDate3NotInRange = defaultPurchaseOrder.copy(
            poId = UUID.randomUUID(),
            poRef = "PO3",
            poNumber = UUID.randomUUID().toString(),
            supplierId = UUID.randomUUID(),
            expectedDateStartTime = pastDate3NotInRange,
        )
        val defaultPurchaseOrderAsnSku1 = createAsnSkuRecord(
            10000L,
            UUID.randomUUID(),
        )
        val defaultPurchaseOrderAsnSku2 = createAsnSkuRecord(
            20000L,
            UUID.randomUUID(),
        )
        persistPurchaseOrderAndRefreshPoInfoView(listOf(poPastDate1, poPastDate2, poPastDate3NotInRange))
        persistPurchaseOrderAsn(
            defaultPurchaseOrderAsn,
            listOf(
                defaultPurchaseOrderAsnSku,
                defaultPurchaseOrderAsnSku1,
                defaultPurchaseOrderAsnSku2,
            ),
        )
        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrdersWithAsns(
                skuId,
                setOf(dcCode),
                setOf(
                    DateRange(pastDate1.toLocalDate(), pastDate1.toLocalDate()),
                    DateRange(pastDate2.toLocalDate(), pastDate2.toLocalDate()),
                ),
            )
        }
        // then
        assertEquals(2, purchaseOrders.size)
        purchaseOrders.first { it.poReference == poPastDate1.poRef }.assertPurchaseOrder(null, poPastDate1)
        purchaseOrders.first { it.poReference == poPastDate2.poRef }.assertPurchaseOrder(null, poPastDate2)
        val purchaseOrder = purchaseOrders.first { it.poReference == poPastDate1.poRef }
        assertEquals(3, purchaseOrder.asns.size)
        purchaseOrder.asns
            .firstOrNull { it.id == asnId }
            ?.let { asn ->
                assertEquals(
                    defaultPurchaseOrderAsn.plannedDeliveryTime.toLocalDate(),
                    asn.plannedDeliveryTime.toLocalDate(),
                )
                assertEquals(
                    defaultPurchaseOrderAsnSku.shippedQuantity.toLong(),
                    asn.shippedQuantity.getValue().toLong()
                )
            }
    }

    @Test
    fun `returns POs by skuId, dcCode and date ranges that match with some grn without po asn`() {
        val grn = createGrn(
            dcCode = defaultPurchaseOrder.dcCode,
            poRef = "OTHER_POREF",
            skuId = defaultPurchaseOrder.skus.first().skuId,
            deliveryDate = defaultPurchaseOrder.expectedDateStartTime,
            deliveryInfoStatus = CLOSED,
        )

        persistPurchaseOrderAndRefreshPoInfoView(defaultPurchaseOrder)
        persistPurchaseOrderAsn(
            defaultPurchaseOrderAsn,
            listOf(defaultPurchaseOrderAsnSku),
        )
        persistGoodsReceivedNotes(setOf(grn))

        // when
        val purchaseOrders = runBlocking {
            purchaseOrderRepository.findPurchaseOrdersWithAsns(
                skuId,
                setOf(dcCode),
                setOf(
                    DateRange(
                        defaultPurchaseOrder.expectedDateStartTime.minusDays(1).toLocalDate(),
                        defaultPurchaseOrder.expectedDateStartTime.toLocalDate(),
                    ),
                ),
            )
        }
        // then
        assertEquals(2, purchaseOrders.size)
        purchaseOrders.first { it.poReference == grn.poRef }.assertPurchaseOrder(grn, null)

        val purchaseOrder = purchaseOrders.first { it.poReference == defaultPurchaseOrder.poRef }
        purchaseOrder.assertPurchaseOrder(null, defaultPurchaseOrder)
        assertEquals(1, purchaseOrder.asns.size)
        assertEquals(defaultPurchaseOrderAsnSku.skuId, purchaseOrder.asns.first().skuId)
        assertEquals(defaultPurchaseOrderAsnSku.asnId, purchaseOrder.asns.first().id)
    }

    private fun createAsnSkuRecord(shippedQuantity: Long, skuId: UUID) = AdvancedShippingNoticeSkuRecord(
        asnId,
        skuId,
        shippedQuantity.toBigDecimal(),
        OffsetDateTime.now(UTC),
        OffsetDateTime.now(ZoneId.of("UTC")),
        Uom.UOM_UNIT,
    )
}
