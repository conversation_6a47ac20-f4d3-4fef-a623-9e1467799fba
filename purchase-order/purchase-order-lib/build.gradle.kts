plugins {
    id("com.hellofresh.cif.common-conventions")
    hellofresh.`test-integration`
    alias(libs.plugins.jooq)
}

description = "Project to access Purchase order domain data"
group = "$group.${project.name}"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    api(projects.purchaseOrder.purchaseOrderModels)
    api(projects.lib.db)
    api(projects.lib.models)
    api(projects.lib.logging)
    api(projects.lib.logging)
    api(projects.lib)
    implementation(projects.lib.featureflags)
    api(libs.jackson.kotlin)
    api(libs.coroutines.core)
    api(libs.micrometer.core)
    api(projects.distributionCenterLib)

    testIntegrationImplementation(platform(libs.log4j.bom))
    testIntegrationImplementation(libs.log4j.api2)
    testIntegrationImplementation(libs.log4j.core)
    testIntegrationImplementation(libs.log4j.slf4j)
    testIntegrationImplementation(libs.log4j.json)
    testIntegrationImplementation(libs.log4j.kotlin)
    testImplementation(testFixtures(projects.lib.featureflags))

    testImplementation(libs.mockk)

    testIntegrationImplementation(libs.mockk)
    testIntegrationImplementation(projects.libTests)
    testIntegrationImplementation(libs.jooq.core)
}

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "purchase_orders_view|goods_received_note|purchase_order|purchase_order_sku|" +
                            "supplier|supplier_sku|advanced_shipping_notice|advanced_shipping_notice_sku|" +
                            "supplier_culinary_sku|sku_specification|sku_specification_view|dc_config|purchase_order_status|uom"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = false
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}
