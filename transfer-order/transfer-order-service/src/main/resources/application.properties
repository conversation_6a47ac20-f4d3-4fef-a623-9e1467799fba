application.name=transfer-order-service

parallelism=1

# will change the group id once everything is ready
group.id=csku-inventory-forecast.transfer-order-service.v1
auto.offset.reset=latest

# 5 min
max.poll.interval.ms=600000

# poll will either wait 5 seconds or for the 20KB or 500 records
fetch.min.bytes=20000
fetch.max.wait.ms=5000
max.poll.records=500

poll.interval_ms=20
poll.timeout=PT1S
process.timeout=PT30S

bootstrap.servers=localhost:29092
