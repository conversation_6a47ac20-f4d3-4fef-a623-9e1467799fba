plugins {
    id("com.hellofresh.cif.common-conventions")
    `test-functional`
    alias(libs.plugins.jooq)
}

description = "Project to access Transfer Order domain data"
group = "$group.transfer_order_lib"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    api(projects.lib.models)
    api(projects.lib.db)
    api(projects.distributionCenterLib)

    testImplementation(projects.libTests)
}

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes =
                            "transfer_order|transfer_order_skus|transfer_order_status|uom|sku_specification|dc_config" +
                            "|goods_received_note|transfer_orders_grn_view|sku_specification_view"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = false
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}
