package com.hellofresh.cif.demand.service

import com.hellofresh.cif.demand.repository.DemandRepository
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.s3.S3File
import com.hellofresh.cif.s3.S3Importer
import com.hellofresh.cif.skuinput.service.ShortSkuSpec
import com.hellofresh.cif.skuinput.service.SkuInputService
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import java.util.UUID
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class ForecastPackagingServiceTest {

    private val importer = mockk<S3Importer>(relaxed = true)
    private val demandRepository = mockk<DemandRepository>(relaxed = true)
    private val skuInputService = mockk<SkuInputService>(relaxed = true)
    private val forecastPackagingService = ForecastPackagingService(importer, demandRepository, skuInputService)

    @Test
    fun `should parse and save demand data when CSV is valid`() {
        // Given
        val s3File = S3File(bucket = "sku_level_demands/es_packaging_forecast", key = "es_forecast.csv")
        val csv = """
            forecast_date,hellofresh_week,production_date,dc,brand,sku_category,sku_size,quantity,sku_code,sku_name,
            13/02/2025 09:14,2025-W08,15/02/2025,SP,HF,box,L,14244,PCK-10-121326-1,HF Large box 2023,
        """.trimIndent().toByteArray()

        every { importer.fetchObjectContent(any(), any()) } returns csv.inputStream()
        coEvery { skuInputService.getSkuId("SP", "PCK-10-121326-1") } returns ShortSkuSpec(
            id = UUID.randomUUID(),
            uom = SkuUOM.UOM_UNIT,
        )

        // When
        runBlocking {
            forecastPackagingService.process(s3File)
        }

        // Then
        coVerify { demandRepository.insertDemandInBatch(match { it.isNotEmpty() }) }
    }
}
