package com.hellofresh.demand.models

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.lib.kafka.serde.objectMapper
import com.hellofresh.cif.models.SkuQuantity
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.random.Random
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.Test

internal class DemandsTest {
    @Test
    fun `returns the forecast demand for the given dc code`() {
        val skuId = UUID.randomUUID()
        val dcCode = "BV"
        val date = LocalDate.now(UTC)
        val demandsMap = listOf(
            Demand(
                skuId = skuId,
                dcCode = dcCode,
                date = date,
                forecastedQty = SkuQuantity.fromLong(200),
                actualConsumption = ActualConsumption(SkuQuantity.fromLong(500L), false),
            ),
            Demand(
                skuId = skuId,
                dcCode = "GR",
                date = date,
                forecastedQty = SkuQuantity.fromLong(500),
                actualConsumption = ActualConsumption(SkuQuantity.fromLong(500L), false),
            ),
        )
        val demandInput = Demands(demandsMap)

        val expectedDemand = SkuQuantity.fromLong(200)

        val demands = demandInput.getDemand(skuId, dcCode, date, UTC)
        assertEquals(expectedDemand, demands?.forecastedQty)
    }

    @Test
    fun `returns the actual demand for the given dc code if usable`() {
        val skuId = UUID.randomUUID()
        val dcCode1 = "BV"
        val dcCode2 = "GR"
        val date = LocalDate.now(UTC).minusDays(1)

        val demand1 = Demand(
            skuId = skuId,
            dcCode = dcCode1,
            date = date,
            forecastedQty = SkuQuantity.fromLong(200),
            actualConsumption = ActualConsumption(SkuQuantity.fromLong(500L), true),
        )
        val demand2 = Demand(
            skuId = skuId,
            dcCode = dcCode2,
            date = date,
            forecastedQty = SkuQuantity.fromLong(300),
            actualConsumption = ActualConsumption(SkuQuantity.fromLong(600L), false),
        )
        val demandInput = Demands(listOf(demand1, demand2))

        val demandResult1 = demandInput.getDemand(skuId, dcCode1, date, UTC)
        assertEquals(demand1.actualConsumptionQty, demandResult1?.actualConsumptionQty)
        assertEquals(demand1.actualConsumptionQty, demandResult1?.actualDemand)

        val demandResult2 = demandInput.getDemand(skuId, dcCode2, date, UTC)
        assertEquals(demand2.actualConsumptionQty, demandResult2?.actualConsumptionQty)
        assertEquals(demand2.forecastedQty.getValue(), demandResult2?.actualDemand?.getValue())
    }

    @Test
    fun `returns the actual demand for the past and forecast demand for the future date`() {
        val skuId = UUID.randomUUID()
        val dcCode = "BV"
        val pastDate = LocalDate.now(UTC).minusDays(5)
        val futureDate = LocalDate.now(UTC).plusDays(5)

        val demand1 = Demand(
            skuId = skuId,
            dcCode = dcCode,
            date = pastDate,
            forecastedQty = SkuQuantity.fromLong(200),
            actualConsumption = ActualConsumption(SkuQuantity.fromLong(500L), true),
        )

        val demand2 = Demand(
            skuId = skuId,
            dcCode = dcCode,
            date = futureDate,
            forecastedQty = SkuQuantity.fromLong(200),
            actualConsumption = ActualConsumption(SkuQuantity.fromLong(500L), true),
        )
        val demandInput = Demands(listOf(demand1, demand2))

        val demandResult1 = demandInput.getDemand(skuId, dcCode, pastDate, UTC)
        assertEquals(demand1.actualConsumptionQty, demandResult1!!.actualDemand)

        val demandResult2 = demandInput.getDemand(skuId, dcCode, futureDate, UTC)
        assertEquals(demand2.forecastedQty, demandResult2!!.forecastedQty)
    }

    @Test
    fun `backwards serialization compatibility for consumption details without crossdocking`() {
        val expectedConsumptionDetails = ConsumptionDetails(
            recipeBreakdowns = listOf(
                RecipeBreakdown(
                    UUID.randomUUID().toString(),
                    UUID.randomUUID().toString(),
                    Random.nextLong(),
                ),
            ),
            prekitting = Prekittings(
                `in` = listOf(Prekitting(Random.nextLong())),
                out = listOf(Prekitting(Random.nextLong())),

            ),
            substitutions = Substitutions(
                `in` = listOf(
                    Substitution(
                        UUID.randomUUID().toString(),
                        UUID.randomUUID().toString(),
                        Random.nextLong(),
                    ),
                ),
                out = listOf(
                    Substitution(
                        UUID.randomUUID().toString(),
                        UUID.randomUUID().toString(),
                        Random.nextLong(),
                    ),
                ),
            ),
        )
        val deserializedDetails = objectMapper.readValue<ConsumptionDetails>(
            objectMapper.writeValueAsString(expectedConsumptionDetails),
        )
        assertEquals(expectedConsumptionDetails, deserializedDetails)
        assertNull(deserializedDetails.crossDockings)
    }

    @Test
    fun `serialization compatibility for nullable and empty consumption details properties`() {
        val expectedConsumptionDetails = ConsumptionDetails(
            recipeBreakdowns = emptyList(),
            prekitting = null,
            substitutions = null,
            crossDockings = null,
        )
        val deserializedDetails = objectMapper.readValue<ConsumptionDetails>(
            objectMapper.writeValueAsString(expectedConsumptionDetails),
        )
        assertEquals(expectedConsumptionDetails, deserializedDetails)
    }
}
