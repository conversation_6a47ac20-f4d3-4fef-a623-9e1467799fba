package com.hellofresh.cif.sqs

import com.hellofresh.cif.s3.S3EventMessageParser
import com.hellofresh.cif.s3.S3File
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.just
import io.mockk.mockk
import kotlin.test.assertFailsWith
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class SQSMessageProxyTest {
    private val processor = mockk<MessageHandlerServiceInterface>()
    private val messageParser = S3EventMessageParser()
    private val sqsMessageProxy = SQSMessageProxy(processor, messageParser)
    private val bucketName = "testBucket"
    private val key = "test-key.csv"

    @Test
    fun `should invoke processor with parsed S3File from SQS message`() {
        val sqsMsg = Message.builder()
            .body("{\"Records\":[{\"s3\":{\"bucket\":{\"name\":\"$bucketName\"},\"object\":{\"key\":\"$key\"}}}]}")
            .build()

        coEvery { processor.name() } returns "test process"
        coEvery { processor.process(any()) } just Runs

        runBlocking {
            sqsMessageProxy.processMessage(sqsMsg)
        }

        coVerify(exactly = 1) { processor.process(any()) }
    }

    @Test
    fun `should fail when processor does not handle parsed S3File`() {
        val sqsMsg = Message.builder()
            .body("{\"Records\":[{\"s3\":{\"bucket\":{\"name\":\"\"},\"object\":{\"key\":\"$key\"}}}]}")
            .build()

        runBlocking {
            coEvery { processor.name() } returns "test process"
            assertFailsWith<InvalidFileNameException> {
                sqsMessageProxy.processMessage(sqsMsg)
            }
        }
    }

    @Test
    fun `should parse message but skip processor call`() {
        val sqsMsg = Message.builder()
            .body(
                "{\"Records\":[{\"eventName\":\"TestEvent\",\"s3\":{\"bucket\":{\"name\":\"$bucketName\"},\"object\":{\"key\":\"$key\"}}}]}"
            )
            .build()

        runBlocking {
            coEvery { processor.name() } returns "test process"
            sqsMessageProxy.processMessage(sqsMsg)
            coVerify(exactly = 0) { processor.process(S3File(bucketName, key)) }
        }
    }
}
