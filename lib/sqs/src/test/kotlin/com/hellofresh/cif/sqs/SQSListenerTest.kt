package com.hellofresh.cif.sqs

import io.mockk.Called
import io.mockk.Runs
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.just
import io.mockk.mockk
import kotlin.coroutines.cancellation.CancellationException
import kotlin.streams.asStream
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import software.amazon.awssdk.core.exception.AbortedException
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest
import software.amazon.awssdk.services.sqs.model.DeleteMessageResponse
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest
import software.amazon.awssdk.services.sqs.model.ReceiveMessageResponse

class SQSListenerTest {
    private val sqsMessageProxy = mockk<SQSMessageProxy>()
    private val sqsClient = mockk<SqsClient>()
    val sqsListener = SQSListener(sqsMessageProxy, sqsClient, "testUrl")
    private val sqsMsg = Message.builder()
        .body("{\"Records\":[{\"s3\":{\"bucket\":{\"name\":\"testBucket\"},\"object\":{\"key\":\"test-key.csv\"}}}]}")
        .build()

    @Test
    fun `should process and delete SQS message successfully`() = runBlocking {
        coEvery { sqsClient.receiveMessage(any<ReceiveMessageRequest>()) } returns ReceiveMessageResponse.builder()
            .messages(listOf(sqsMsg)).build()
        coEvery { sqsMessageProxy.processMessage(any()) } just Runs
        coEvery { sqsClient.deleteMessage(any<DeleteMessageRequest>()) } returns DeleteMessageResponse.builder().build()

        val job = launch(Dispatchers.Default) {
            sqsListener.run()
        }

        delay(100)

        sqsListener.close()
        job.join()

        coVerify { sqsClient.receiveMessage(any<ReceiveMessageRequest>()) }
        coVerify { sqsMessageProxy.processMessage(any()) }
        coVerify { sqsClient.deleteMessage(any<DeleteMessageRequest>()) }
    }

    @ParameterizedTest
    @MethodSource("exceptionProvider")
    fun `should handle exception during SQS message receive without failing process`(exception: Exception) =
        runBlocking {
            coEvery { sqsClient.receiveMessage(any<ReceiveMessageRequest>()) } throws exception

            val job = launch {
                sqsListener.run()
            }

            delay(100)
            sqsListener.close()
            job.join()

            coVerify { sqsClient.receiveMessage(any<ReceiveMessageRequest>()) }
            coVerify { sqsMessageProxy wasNot Called }
            coVerify { sqsClient.deleteMessage(any<DeleteMessageRequest>()) wasNot Called }
        }

    @Test
    fun `should handle parsing error of SQS message without failing process`() = runBlocking {
        coEvery { sqsClient.receiveMessage(any<ReceiveMessageRequest>()) } returns ReceiveMessageResponse.builder()
            .messages(listOf(sqsMsg)).build()
        coEvery { sqsMessageProxy.processMessage(any()) } throws InvalidFileNameException("invalid file name")

        val job = launch(Dispatchers.Default) {
            sqsListener.run()
        }

        delay(100)
        sqsListener.close()
        job.join()

        coVerify { sqsClient.receiveMessage(any<ReceiveMessageRequest>()) }
        coVerify { sqsMessageProxy.processMessage(any()) }
        coVerify { sqsClient.deleteMessage(any<DeleteMessageRequest>()) }
    }

    companion object {
        @JvmStatic
        fun exceptionProvider() = listOf(
            UnsupportedOperationException("Unsupported operation"),
            AbortedException.create("Thread was interrupted"),
            CancellationException("Coroutine was cancelled"),
        ).asSequence().asStream()
    }
}
