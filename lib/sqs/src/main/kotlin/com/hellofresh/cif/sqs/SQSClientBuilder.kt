package com.hellofresh.cif.sqs

import com.hellofresh.cif.config.ConfigurationLoader
import java.net.URI
import software.amazon.awssdk.auth.credentials.AnonymousCredentialsProvider
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sts.StsClient
import software.amazon.awssdk.services.sts.auth.StsAssumeRoleCredentialsProvider
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest

object SQSClientBuilder {
    fun getSqsClient(role: String? = null, sessionName: String = ""): SqsClient = when {
        ConfigurationLoader.isLocal() -> localSqsClient(ConfigurationLoader.getStringOrFail("aws.sqs.host"))
        role != null -> createSqsClientWithRoleArn(role, sessionName)
        else -> createSqsClientForMainIT()
    }

    private fun localSqsClient(sqsHost: String): SqsClient =
        SqsClient.builder()
            .endpointOverride(URI.create(sqsHost))
            .credentialsProvider(AnonymousCredentialsProvider.create())
            .region(Region.EU_WEST_1)
            .build()

    private fun createSqsClientForMainIT(): SqsClient =
        createSqsClient(DefaultCredentialsProvider.create())

    private fun createSqsClientWithRoleArn(roleArn: String, sessionName: String): SqsClient =
        createSqsClient(stsAssumeRoleCredentialsProvider(roleArn, sessionName))

    private fun createSqsClient(provider: AwsCredentialsProvider): SqsClient =
        SqsClient.builder()
            .region(Region.EU_WEST_1)
            .credentialsProvider(provider)
            .build()

    private fun stsAssumeRoleCredentialsProvider(roleArn: String, sessionName: String): AwsCredentialsProvider {
        val stsClient = StsClient.builder()
            .region(Region.EU_WEST_1)
            .build()
        val assumeRoleRequest = AssumeRoleRequest.builder()
            .roleArn(roleArn)
            .roleSessionName(sessionName)
            .build()

        return StsAssumeRoleCredentialsProvider.builder()
            .stsClient(stsClient)
            .refreshRequest(assumeRoleRequest)
            .build()
    }
}
