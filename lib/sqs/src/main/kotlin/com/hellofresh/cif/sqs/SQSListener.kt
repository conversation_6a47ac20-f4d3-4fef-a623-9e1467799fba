package com.hellofresh.cif.sqs

import java.util.concurrent.atomic.AtomicBoolean
import kotlin.coroutines.cancellation.CancellationException
import kotlin.coroutines.coroutineContext
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.isActive
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging
import software.amazon.awssdk.core.exception.AbortedException
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.DeleteMessageRequest
import software.amazon.awssdk.services.sqs.model.Message
import software.amazon.awssdk.services.sqs.model.ReceiveMessageRequest

private const val MAX_SQS_MESSAGES_TO_READ = 10
private const val SQS_POLL_WAIT_TIME_IN_SECONDS = 20
private const val ERROR_BACKOFF_DELAY_MS = 500L

class SQSListener(
    private val messageProxy: SQSMessageProxy,
    private val sqsClient: SqsClient,
    private val sqsUrl: String,
) : AutoCloseable {

    private val isActive = AtomicBoolean(true)

    @Suppress("TooGenericExceptionCaught")
    suspend fun run() {
        val name = makeName(sqsUrl)
        while (isActive.get() && coroutineContext.isActive) {
            try {
                logger.info("Listening to $name")
                val receiveMessageRequest = ReceiveMessageRequest.builder()
                    .queueUrl(sqsUrl)
                    .maxNumberOfMessages(MAX_SQS_MESSAGES_TO_READ)
                    .waitTimeSeconds(SQS_POLL_WAIT_TIME_IN_SECONDS)
                    .build()

                val messages = withContext(Dispatchers.IO) {
                    sqsClient.receiveMessage(receiveMessageRequest).messages()
                }

                logger.info("Total ${messages.count()} messages received from $name")

                for (message in messages) {
                    logger.info(
                        "Received SQS Message. SQSMessage: " +
                            "[id: ${message.messageId()}, name: $name, body: ${message.body()}]"
                    )
                    kotlin.runCatching {
                        messageProxy.processMessage(message)
                        // delete after message processed successfully
                        deleteSQSMessage(sqsClient, sqsUrl, message)
                    }.onSuccess {
                        logger.info(
                            "Completed processing the SQS messages. " +
                                "Message: [id: ${message.messageId()}, name: $name]."
                        )
                    }.onFailure { error ->
                        // delete after message processed failed
                        deleteSQSMessage(sqsClient, sqsUrl, message)
                        if (error is InvalidFileNameException) {
                            logger.warn("${error.message}")
                        } else {
                            logger.error("Error processing message: ${error.message}", error)
                        }
                    }
                }
            } catch (_: CancellationException) {
                logger.warn("Listener for $name was cancelled")
            } catch (_: AbortedException) {
                logger.warn("Listener for $name was interrupted")
            } catch (e: Exception) {
                logger.error("Failed to process $name SQS messages: ", e)
            }

            delay(ERROR_BACKOFF_DELAY_MS)
        }

        logger.info("SQSListener: $name finished gracefully")
    }

    private fun deleteSQSMessage(sqsClient: SqsClient, queueUrl: String, message: Message) {
        val name = makeName(sqsUrl)
        kotlin.runCatching {
            logger.info("Message: [id: ${message.messageId()}, name: $name] Deleting the SQS Message")
            val receiptHandle = message.receiptHandle()
            val deleteMessageRequest = DeleteMessageRequest.builder()
                .queueUrl(queueUrl)
                .receiptHandle(receiptHandle)
                .build()
            val result = sqsClient.deleteMessage(deleteMessageRequest)
            logger.info(".delete:receipt-handle = $receiptHandle, rc = ${result.sdkHttpResponse().statusCode()}")
        }.onFailure {
            logger.error("Error while deleting $name SQS messages: ${it.message}")
        }
    }

    private fun makeName(sqsUrl: String): String = sqsUrl.split("/").last()

    override fun close() {
        isActive.set(false)
        logger.info("SQSListener: ${makeName(sqsUrl)} was closed")
    }

    companion object : Logging
}
