package com.hellofresh.cif.s3

import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.net.URI
import java.time.Instant
import kotlin.test.assertEquals
import kotlin.time.Duration.Companion.seconds
import kotlin.time.toJavaDuration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import software.amazon.awssdk.core.ResponseInputStream
import software.amazon.awssdk.core.sync.RequestBody
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectAttributesRequest
import software.amazon.awssdk.services.s3.model.GetObjectAttributesResponse
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.GetObjectResponse
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.ListObjectsV2Response
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.services.s3.model.PutObjectResponse
import software.amazon.awssdk.services.s3.model.S3Object
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.s3.presigner.model.GetObjectPresignRequest
import software.amazon.awssdk.services.s3.presigner.model.PresignedGetObjectRequest
import software.amazon.awssdk.services.s3.presigner.model.PresignedPutObjectRequest
import software.amazon.awssdk.services.s3.presigner.model.PutObjectPresignRequest

class S3FileServiceTest {
    private val s3Client = mockk<S3Client>()
    private val s3Presigner = mockk<S3Presigner>()
    private val s3FileService = S3FileService(s3Client, s3Presigner)

    private val bucket = "test-bucket"
    private val prefix = "test-prefix"
    private val fileName = "fileName"
    private val key = "$prefix&$fileName"

    @AfterEach
    fun tearDown() {
        clearMocks(s3Client)
    }

    @Test
    fun `fetchObjectContent should return InputStream`() {
        val inputStream = mockk<ResponseInputStream<GetObjectResponse>>()

        every {
            s3Client.getObject(
                match<GetObjectRequest> {
                    it.bucket() == bucket && it.key() == key
                },
            )
        } returns inputStream

        val result = s3FileService.fetchObjectContent(bucket, key)

        assertEquals(inputStream, result)
    }

    @Test
    fun `fetchObjectSummaries should return list of keys`() {
        val lastModified = Instant.now()
        val summary1 = S3Object.builder()
            .key("test-key-1")
            .lastModified(lastModified).build()

        val summary2 =
            S3Object.builder()
                .key("test-key-2")
                .lastModified(lastModified).build()

        val result = mockk<ListObjectsV2Response>()
        every { result.contents() } returns listOf(summary1, summary2)
        every { s3Client.listObjectsV2(any<ListObjectsV2Request>()) } returns result

        val summaries = s3FileService.fetchObjectSummaries(bucket, prefix)

        val sampleData = listOf(
            FileObject(lastModified = lastModified, key = "test-key-1"),
            FileObject(lastModified = lastModified, key = "test-key-2"),
        )
        assertEquals(sampleData, summaries)
        verify { s3Client.listObjectsV2(any<ListObjectsV2Request>()) }
    }

    @Test
    fun `putObject should upload content`() {
        val stringContent = "theContent"

        val lastModified = Instant.now()
        every {
            s3Client.putObject(
                match<PutObjectRequest> {
                    it.bucket() == bucket && it.key() == key
                },
                any<RequestBody>(),
            )
        } returns PutObjectResponse.builder().build()

        every {
            s3Client.getObjectAttributes(
                match<GetObjectAttributesRequest> {
                    it.bucket() == bucket && it.key() == key
                },
            )
        } returns GetObjectAttributesResponse.builder().lastModified(lastModified).build()

        val putObject = s3FileService.putObject(bucket, key, stringContent)

        assertEquals(key, putObject.key)
        assertEquals(lastModified, putObject.lastModified)

        verify(exactly = 1) {
            s3Client.putObject(
                match<PutObjectRequest> {
                    it.bucket() == bucket && it.key() == key
                },
                match<RequestBody> {
                    stringContent == it.contentStreamProvider().newStream().readAllBytes().decodeToString()
                },
            )
        }
    }

    @Test
    fun `should get pre signed get url from object`() {
        val expiration = 30.seconds.toJavaDuration()
        val url = URI("http://testPresigned").toURL()
        val presignedGetObjectRequest = mockk<PresignedGetObjectRequest>()
        every { presignedGetObjectRequest.url() } returns url
        every {
            s3Presigner.presignGetObject(
                match<GetObjectPresignRequest> {
                    it.signatureDuration() == expiration &&
                        it.objectRequest.bucket() == bucket &&
                        it.objectRequest.key() == key
                },

            )
        } returns presignedGetObjectRequest

        assertEquals(
            url,
            s3FileService.getPresignedGetUrl(bucket, key, expiration),
        )
    }

    @Test
    fun `should get pre signed post url from object`() {
        val expiration = 30.seconds.toJavaDuration()
        val url = URI("http://testPresigned").toURL()
        val presignedPutObjectRequest = mockk<PresignedPutObjectRequest>()
        every { presignedPutObjectRequest.url() } returns url
        every {
            s3Presigner.presignPutObject(
                match<PutObjectPresignRequest> {
                    it.signatureDuration() == expiration &&
                        it.putObjectRequest().bucket() == bucket &&
                        it.putObjectRequest().key() == key
                },
            )
        } returns presignedPutObjectRequest

        assertEquals(
            url,
            s3FileService.getPreSignedPutUrl(
                bucket,
                expiration,
                key,
                mapOf("market" to "EU", "author_name" to "test", "author_email" to "<EMAIL>")
            ),
        )
    }
}
