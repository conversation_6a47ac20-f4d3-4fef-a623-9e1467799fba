plugins {
    id("com.hellofresh.cif.common-conventions")
}

dependencies {
    api(projects.lib.logging)
    api(libs.aws.java.sdk.s3)
    api(libs.aws.java.sdk.sts)
    api(projects.lib.fileConsumer)
    implementation(projects.lib.configuration)
    implementation(libs.jackson.kotlin)

    testImplementation(projects.libTests)
    testImplementation(libs.testcontainers.postgresql)
    testImplementation(libs.mockk)
    testImplementation(libs.coroutines.core)
    testImplementation(libs.coroutines.jdk8)
}
