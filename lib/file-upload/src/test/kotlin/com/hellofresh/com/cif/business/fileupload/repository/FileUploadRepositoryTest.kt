package com.hellofresh.com.cif.business.fileupload.repository

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.business.file_upload.schema.enums.FileType
import com.hellofresh.cif.business.file_upload.schema.enums.FileUploadStatus
import com.hellofresh.cif.business.file_upload.schema.tables.FileUploads.FILE_UPLOADS
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.com.cif.business.fileupload.model.FileUpload
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.Test
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

class FileUploadRepositoryTest {

    @Test
    fun `test file upload repository`() {
        // Add your test logic here
        val fileUpload = FileUpload(
            id = UUID.randomUUID(),
            filename = "test_file.txt",
            market = "UK",
            dcs = setOf("DC1", "DC2", "DC1"),
            authorName = "John Doe",
            authorEmail = "<EMAIL>",
            message = "File uploaded successfully",
            status = FileUploadStatus.IMPORTED,
            fileType = FileType.STOCK_INVENTORY,
        )
        fileUploadRepository.insert(dslContext = dsl, file = fileUpload)

        // For example, you can call methods from fileUploadRepository and assert the results
        val result = dsl.selectFrom(FILE_UPLOADS)
            .where(FILE_UPLOADS.ID.eq(fileUpload.id))
            .fetch()

        assertEquals(1, result.size)
        with(result[0]) {
            assertEquals(FileUploadStatus.IMPORTED, status)
            assertEquals("UK", market)
            assertEquals(fileUpload.filename, fileName)
            assertEquals(2, dcs.size)
            assertEquals(FileType.STOCK_INVENTORY, fileType)
        }
    }

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(FILE_UPLOADS).execute()
    }

    companion object {
        private val dataSource = getMigratedDataSource(nestedFolderCount = 2)

        private lateinit var dsl: MetricsDSLContext
        private lateinit var fileUploadRepository: FileUploadRepositoryImpl

        @JvmStatic
        @BeforeAll
        fun setUp() {
            // Set up the test environment
            val config = DefaultConfiguration()
                .set(dataSource)
                .set(POSTGRES)
                .set(Executors.newSingleThreadExecutor())

            dsl = DSL.using(
                config,
            ).withMetrics(SimpleMeterRegistry())
            fileUploadRepository = FileUploadRepositoryImpl()
        }
    }
}
