package com.hellofresh.cif.config

import java.nio.file.Files
import java.nio.file.Paths
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.locks.ReentrantLock
import kotlin.concurrent.withLock
import kotlin.io.path.notExists
import org.apache.commons.configuration.Configuration

private const val BOOTSTRAP_SERVERS = "bootstrap.servers"

private val KAFKA_CONSUMER_CONFIGURATIONS = listOf(
    "group.id",
    BOOTSTRAP_SERVERS,
    "fetch.max.wait.ms",
    "max.poll.records",
    "fetch.min.bytes",
    "max.poll.interval.ms",
    "auto.offset.reset",
)

private const val AIVEN_DOMAIN = ".aivencloud.com"

private const val PROJECT_NAME = "csku-inventory-forecast"

private const val TRIBE_NAME = "procurement"

@Suppress("TooManyFunctions")
abstract class AbstractConfigurationLoader(private val configuration: Configuration) {

    private val firstRun: AtomicBoolean = AtomicBoolean(true)
    private val lock = ReentrantLock()

    val aivenUsername: String
        get() = getStringOrDefault("HF_AIVEN_USERNAME", PROJECT_NAME)
    val aivenPassword: String
        get() = getStringOrFail("HF_AIVEN_PASSWORD")

    fun getEnvironment(): String = configuration.getString("HF_TIER")
    fun getStringOrFail(key: String, lazyMessage: () -> Any = { "$key property not found" }): String =
        requireNotNull(configuration.getString(key), lazyMessage)

    fun getStringOrDefault(key: String, defaultValue: String): String = configuration.getString(key, defaultValue)
    fun getStringIfPresent(key: String): String? = configuration.getString(key)
    fun getIntegerOrFail(key: String, lazyMessage: () -> Any = { "$key property not found" }): Int =
        requireNotNull(configuration.getInt(key), lazyMessage)

    fun getIntegerOrDefault(key: String, defaultValue: Int): Int = configuration.getInt(key, defaultValue)
    fun getIntegerIfPresent(key: String): Int? = configuration.getInteger(key, null)

    fun getList(key: String): List<String> = configuration.getStringArray(key).toList()
    fun getSet(key: String): Set<String> = getList(key).toSet()

    /**
     * Format Value
     * KEY1=V1,KEY2=V2|V3
     */
    fun <T> getMap(
        key: String,
        listTransform: ((List<String>) -> T)
    ): Map<String, T> =
        buildMap<String, T> {
            configuration.getProperties(key).forEach { k, v ->
                put(
                    k as String,
                    listTransform((v as String).split("|").toList()),
                )
            }
        }

    fun getMap(key: String): Map<String, List<String>> = getMap(key) { it }

    fun getMapWithSetValue(key: String): Map<String, Set<String>> =
        getMap(key) { it.toSet() }

    fun isStaging() = getEnvironment() == "staging"
    fun isLive() = getEnvironment() == "live"
    fun isLocal() = getEnvironment() == "local"

    fun loadKafkaConsumerConfigurations(): Map<String, String> {
        if (isLocal()) {
            return KAFKA_CONSUMER_CONFIGURATIONS.associateWith { configuration.getString(it) }
        }
        return KAFKA_CONSUMER_CONFIGURATIONS.associateWith {
            configuration.getString(it)
        }.toMap() + loadKafkaAuthConfigurations()
    }

    fun loadKafkaProducerConfigurations(): Map<String, String> {
        val bootstrapServers =
            checkNotNull(getStringIfPresent("HF_BOOTSTRAP_SERVERS") ?: getStringOrFail(BOOTSTRAP_SERVERS))
        val properties = mapOf(
            BOOTSTRAP_SERVERS to bootstrapServers,
        )
        return if (isLocal()) properties else properties + loadKafkaAuthConfigurations()
    }

    private fun loadKafkaAuthConfigurations(): Map<String, String> = mapOf(
        "sasl.jaas.config" to plainLogin(ConfigurationLoader.aivenUsername, ConfigurationLoader.aivenPassword),
        "sasl.mechanism" to "PLAIN",
        "security.protocol" to "SASL_SSL",
        "ssl.endpoint.identification.algorithm" to "",
        "ssl.truststore.location" to getAivenSslTruststoreLocation(),
        "ssl.truststore.password" to "${getEnvironment()}$AIVEN_DOMAIN",
    )

    fun getApplicationName() = getStringOrFail("application.name")

    fun getPrometheusConfig() =
        PrometheusConfig(
            projectName = "${TRIBE_NAME}_${PROJECT_NAME}",
            applicationName = getApplicationName(),
            prometheusDescription = getStringOrDefault("prometheus.descriptions", "false"),
            prometheusScrapeInterval = getStringOrDefault("prometheus.scrapeInterval", "PT1M"),
        )

    internal fun getAivenSslTruststoreLocation(): String {
        val resource = "kafka/${getEnvironment()}$AIVEN_DOMAIN/trust-store.jks"
        val truststorePath = Paths.get(System.getProperty("java.io.tmpdir"), resource.replace('/', '.'))
        if (truststorePath.notExists()) {
            val url = checkNotNull(ClassLoader.getSystemResource(resource)) {
                "Could not find required Aiven trust store: $resource. Aborting"
            }
            if (firstRun.compareAndSet(true, false)) {
                lock.withLock {
                    url.openStream().use { Files.copy(it, truststorePath) }
                }
            }
        }
        return truststorePath.toString()
    }

    private fun plainLogin(userName: String, password: String) =
        "org.apache.kafka.common.security.plain.PlainLoginModule required username=\"$userName\" password=\"$password\";"
}
