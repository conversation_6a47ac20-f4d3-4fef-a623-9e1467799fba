package com.hellofresh.cif.config

private const val DACH = "DACH"
private const val BENELUXFR = "BENELUXFR"
private const val DKSE = "DKSE"
const val UK_MARKET = "GB"
const val EU_MARKET = "EU"
object CountryMarketMapping {
    val countryMarketMap = mapOf(
        "DE" to DACH,
        "CH" to DACH,
        "NL" to BENELUXFR,
        "BE" to BENELUXFR,
        "LU" to BENELUXFR,
        "FR" to "FR",
        "IT" to "IT",
        "GB" to UK_MARKET,
        "CA" to "CA",
        "US" to "US",
        "NK" to "NR",
        "DK" to DKSE,
        "SE" to DKSE,
        "EU" to EU_MARKET,
        "JP" to "JP",
        "IE" to "IE",
        "ES" to "ES",
        "NZ" to "NZ",
        "AU" to "AU",
    )
}
