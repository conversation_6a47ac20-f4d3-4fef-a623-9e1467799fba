package com.hellofresh.cif.db.metrics

import com.hellofresh.cif.checks.CheckResult
import com.hellofresh.cif.checks.HealthCheck
import com.hellofresh.cif.db.DbDslCheck
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tags
import org.jooq.Configuration
import org.jooq.DSLContext
import org.jooq.ExecuteListenerProvider
import org.jooq.TransactionListenerProvider
import org.jooq.impl.DSL

const val CONFIGURATION_METRICS_TAGS_KEY = "configurationMetricTags"
const val NAME_TAG_KEY = "name"

class MetricsDSLContext internal constructor(
    val name: String,
    configuration: Configuration,
    private val meterRegistry: MeterRegistry,
    private val defaultTags: Tags = Tags.empty()
) : HealthCheck, DSLContext by DSL.using(deriveConfigurationWithListeners(configuration, meterRegistry, defaultTags)) {

    fun withTagName(name: String) = addTags(Tags.of(NAME_TAG_KEY, name))

    private fun addTags(tags: Tags) =
        MetricsDSLContext(
            name,
            this.configuration().derive()
                .apply {
                    data().compute(CONFIGURATION_METRICS_TAGS_KEY) { _, value ->
                        value?.let { (it as Tags).and(tags) } ?: tags
                    }
                },
            this.meterRegistry,
            this.defaultTags,
        )

    fun withMeteredConfiguration(configuration: Configuration, tagName: String) =
        withMeteredConfiguration(configuration).withTagName(tagName)

    fun withMeteredConfiguration(configuration: Configuration): MetricsDSLContext =
        MetricsDSLContext(name, configuration, this.meterRegistry, this.defaultTags)

    override suspend fun isHealthy() = CheckResult(name, DbDslCheck.isHealthy(this))

    companion object {

        private fun deriveConfigurationWithListeners(
            configuration: Configuration,
            meterRegistry: MeterRegistry,
            defaultTags: Tags
        ) =
            configuration.derive()
                .let { derivedConf ->
                    derivedConf.executeListenerProviders()
                        .find { it.provide() is JooqListener }
                        ?.let { derivedConf }
                        ?: derivedConf.deriveAppending(ExecuteListenerProvider { JooqListener(meterRegistry, defaultTags) })
                }.let { derivedConf ->
                    derivedConf.transactionListenerProviders()
                        .find { it.provide() is JooqListener }
                        ?.let { derivedConf }
                        ?: derivedConf.deriveAppending(TransactionListenerProvider { JooqListener(meterRegistry, defaultTags) })
                }
    }
}

fun DSLContext.withMetrics(registry: MeterRegistry, tags: Tags = Tags.empty(), name: String = "defaultName"): MetricsDSLContext =
    MetricsDSLContext(name, this.configuration(), registry, tags)
