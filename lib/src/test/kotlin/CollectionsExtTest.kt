package com.hellofresh.cif.lib

import kotlin.test.Test
import kotlin.test.assertEquals

class CollectionsExtTest {

    @Test
    fun `group by with set`() {
        val pairs = listOf(
            1 to 1,
            1 to 2,
            1 to 1,
            2 to 3,
            2 to 2,
            2 to 2,
        )

        val groupedPairs = pairs.groupByToSet { it.first }
        val groupedSeconds = pairs.groupByToSet({ it.first }) { it.second }

        assertEquals(setOf(1, 2), groupedPairs.keys)
        assertEquals(setOf(1 to 1, 1 to 2), groupedPairs[1])
        assertEquals(setOf(2 to 3, 2 to 2), groupedPairs[2])

        assertEquals(setOf(1, 2), groupedSeconds.keys)
        assertEquals(setOf(1, 2), groupedSeconds[1])
        assertEquals(setOf(3, 2), groupedSeconds[2])
    }

    @Test
    fun `group by first`() {
        val pairs = listOf(
            1 to 1,
            1 to 2,
            1 to 3,
            2 to 1,
            2 to 2,
            2 to 2,
        )

        val groupedPairs = pairs.groupByFirst()
        val groupedPairsSet = pairs.groupByFirstToSet()

        assertEquals(setOf(1, 2), groupedPairs.keys)
        assertEquals(listOf(1, 2, 3), groupedPairs[1])
        assertEquals(listOf(1, 2, 2), groupedPairs[2])

        assertEquals(setOf(1, 2), groupedPairsSet.keys)
        assertEquals(setOf(1, 2, 3), groupedPairsSet[1])
        assertEquals(setOf(1, 2), groupedPairsSet[2])
    }

    @Test
    fun `group by second`() {
        val pairs = listOf(
            1 to 1,
            1 to 2,
            1 to 3,
            2 to 1,
            2 to 2,
            2 to 2,
        )

        val groupedPairs = pairs.groupBySecond()
        val groupedPairsSet = pairs.groupBySecondToSet()

        assertEquals(setOf(1, 2, 3), groupedPairs.keys)
        assertEquals(listOf(1, 2), groupedPairs[1])
        assertEquals(listOf(1, 2, 2), groupedPairs[2])
        assertEquals(listOf(1), groupedPairs[3])

        assertEquals(setOf(1, 2, 3), groupedPairsSet.keys)
        assertEquals(setOf(1, 2), groupedPairsSet[1])
        assertEquals(setOf(1, 2), groupedPairsSet[2])
        assertEquals(setOf(1), groupedPairsSet[3])
    }
}
