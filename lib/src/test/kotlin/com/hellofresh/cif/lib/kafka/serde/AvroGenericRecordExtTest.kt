package com.hellofresh.cif.lib.kafka.serde

import io.confluent.kafka.schemaregistry.avro.AvroSchema
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFails
import kotlin.test.assertNull
import org.apache.avro.generic.GenericData.Record
import org.apache.avro.generic.GenericRecordBuilder
import org.intellij.lang.annotations.Language
import org.junit.jupiter.api.assertAll

internal class AvroGenericRecordExtTest {
    @Test fun `magic getValue function correctly handles existing fields with correct types`() {
        assertAll(
            {
                val someString: String by record
                assertEquals("value", someString)
            },
            {
                val someNullableString: String? by record
                assertNull(someNullableString)
            },
            {
                val someInt: Int by record
                assertEquals(42, someInt)
            },
            {
                val someUuid: UUID by record
                assertEquals(UUID(0, 1), someUuid)
            },
            {
                val someNestedRecord: Map<String, Any?> by record
                val someEnum: String by someNestedRecord
                assertEquals("B", someEnum)
            },
            {
                val someCollection: List<Int> by record
                assertEquals(listOf(1, 2, 3, 4), someCollection)
            },
        )
    }

    @Test fun `exception is thrown if non-existing field is requested`() {
        assertFails {
            val missingField: String by record
            println(missingField) // required or the code is optimized away
            record
        }
    }

    @Test fun `exception is thrown if types do not match`() {
        assertFails {
            val someString: Int by record
            println(someString) // required or the code is optimized away
            record
        }
    }

    @Test fun or() {
        assertAll(
            {
                val someString: String by record.or("default")
                assertEquals("value", someString)
            },
            {
                val someNullableString: String? by record.or("default")
                assertNull(someNullableString, "Avro default trumps custom default value")
            },
            {
                val someMissingString: String by record.or("default")
                assertEquals("default", someMissingString)
            },
            {
                val someMissingInt: Int by record.or(42)
                assertEquals(42, someMissingInt)
            },
            {
                val someMissingFloat: Float by record.or(4.2F)
                assertEquals(4.2F, someMissingFloat)
            },
        )
    }

    companion object {
        @Language("JSON")
        val schema = AvroSchema(
            """{
              "namespace": "com.hellofresh.avro",
              "name": "SomeRecord",
              "type": "record",
              "fields": [
                {
                  "name": "someString",
                  "type": "string"
                },
                {
                  "name": "someNullableString",
                  "type": ["null", "string"],
                  "default": null
                },
                {
                  "name": "someInt",
                  "type": "int"
                },
                {
                  "name": "someUuid",
                  "type": "string",
                  "logicalType": "uuid"
                },
                {
                  "name": "someNestedRecord",
                  "type": {
                    "name": "SomeNestedRecord",
                    "type": "record",
                    "fields": [
                      {
                        "name": "someEnum",
                        "type": {
                          "name": "SomeEnum",
                          "type": "enum",
                          "symbols": [
                            "A",
                            "B"
                          ]
                        }
                      }
                    ]
                  }
                },
                {
                  "name": "someCollection",
                  "type": {
                    "type": "array",
                    "items": {
                      "type": "int"
                    }
                  }
                }
              ]
            }""",
        )

        val record: Record = GenericRecordBuilder(schema.rawSchema()).apply {
            set("someString", "value")
            set("someInt", 42)
            set("someUuid", UUID(0, 1))
            set("someNestedRecord", mapOf("someEnum" to "B"))
            set("someCollection", listOf(1, 2, 3, 4))
        }.build()
    }
}
