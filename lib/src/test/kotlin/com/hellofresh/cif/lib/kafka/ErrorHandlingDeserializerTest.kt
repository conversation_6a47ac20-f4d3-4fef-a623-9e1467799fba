package com.hellofresh.cif.lib.kafka

import io.mockk.every
import io.mockk.mockk
import java.time.Instant
import java.util.Optional
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.common.header.Headers
import org.apache.kafka.common.header.internals.RecordHeaders
import org.apache.kafka.common.record.TimestampType
import org.apache.kafka.common.serialization.Deserializer
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class ErrorHandlingDeserializerTest {

    val defaultTopicName = "testTopicName"
    val defaultvalue = "testValue"

    @Test
    fun `error deserializer delegates deserialization`() {
        val delegateDeserializer = mockk<Deserializer<String>>()

        val byteArray = defaultvalue.toByteArray(Charsets.UTF_8)
        val headers = RecordHeaders()
        every { delegateDeserializer.deserialize(defaultTopicName, headers, byteArray) } returns defaultvalue

        assertEquals(
            defaultvalue,
            ErrorHandlingDeserializer(false, delegateDeserializer).deserialize(defaultTopicName, headers, byteArray),
        )

        assertNull(ErrorHandlingDeserializer.readDeserializeErrorHeader(createConsumerRecord(false, headers)))
    }

    @ParameterizedTest
    @CsvSource("true", "false")
    fun `error deserializer tags record with deserialization exception`(isKeySerialized: Boolean) {
        val delegateDeserializer = mockk<Deserializer<String>>()

        val headers = RecordHeaders()
        val byteArray = defaultvalue.toByteArray()
        every { delegateDeserializer.deserialize(defaultTopicName, headers, byteArray) } throws IllegalArgumentException()

        val result = ErrorHandlingDeserializer(
            isKeySerialized,
            delegateDeserializer
        ).deserialize(defaultTopicName, headers, byteArray)

        assertNull(result)
        assertNotNull(getHeaderValue(headers, isKeySerialized))

        val consumerRecord: ConsumerRecord<Any?, Any?> = createConsumerRecord(isKeySerialized, headers)

        val deserializationErrorMessage = ErrorHandlingDeserializer.readDeserializeErrorHeader(consumerRecord)

        assertNotNull(deserializationErrorMessage)
    }

    private fun createConsumerRecord(isKeySerialized: Boolean, headers: Headers) =
        ConsumerRecord<Any?, Any?>(
            defaultTopicName,
            0,
            0L,
            Instant.now().toEpochMilli(),
            TimestampType.CREATE_TIME,
            0,
            0,
            if (isKeySerialized) null else defaultvalue,
            if (!isKeySerialized) null else defaultvalue,
            headers,
            Optional.empty(),
        )

    private fun getHeaderValue(headers: Headers, isKeySerialized: Boolean) =
        headers.lastHeader(
            if (isKeySerialized) {
                KEY_DESERIALIZER_EXCEPTION_HEADER
            } else {
                VALUE_DESERIALIZER_EXCEPTION_HEADER
            },
        ).value()
}
