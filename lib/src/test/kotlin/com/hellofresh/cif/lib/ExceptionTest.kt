package com.hellofresh.cif.lib

import java.io.FileNotFoundException
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import org.junit.jupiter.api.assertThrows

class ExceptionTest {

    @Test fun `throws exception when not handled`() {
        assertThrows<FileNotFoundException> {
            runCatching<RuntimeException, Int>({ throw FileNotFoundException("") }, {})
        }
    }

    @Test fun `handles exception and returns null`() {
        var a = 0
        val ret = runCatching<RuntimeException, Int>({ throw IllegalStateException("") }, { a = 10 })
        assertNull(ret)
        assertEquals(10, a)
    }

    @Test fun `returns the value if no exception`() {
        val ret = runCatching<RuntimeException, Int>({ 10 }, {})
        assertEquals(10, ret)
    }
}
