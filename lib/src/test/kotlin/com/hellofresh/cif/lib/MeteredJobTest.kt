package com.hellofresh.cif.lib

import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlinx.coroutines.runBlocking

class MeteredJobTest {

    @Test fun `record success`() {
        val registry = SimpleMeterRegistry()
        runBlocking {
            MeteredJob(registry, "testJob") {
                //
            }
                .execute()
        }
        val success = registry.find("component_success").counter()
        val failure = registry.find("component_failure").counter()

        assertNotNull(success)
        assertEquals(1, success.count().toInt())

        assertNotNull(failure)
        assertEquals(0, failure.count().toInt())
    }

    @Test fun `record failure`() {
        val registry = SimpleMeterRegistry()
        runBlocking {
            MeteredJob(registry, "testJob") {
                error("some failure")
            }
                .execute()
        }
        val success = registry.find("component_success").counter()
        val failure = registry.find("component_failure").counter()

        assertNotNull(success)
        assertEquals(0, success.count().toInt())

        assertNotNull(failure)
        assertEquals(1, failure.count().toInt())
    }

    @Test fun `record duration of both failed and successful run`() {
        val registry = SimpleMeterRegistry()
        runBlocking {
            MeteredJob(registry, "testJob") {
                //
            }
                .execute()
        }
        runBlocking {
            MeteredJob(registry, "testJob") {
                error("some failure")
            }.execute()
        }
        val timer = registry.find("import_duration").timer()

        assertNotNull(timer)
        assertEquals(2, timer.count())
    }
}
