package com.hellofresh.cif.lib.dcbalancer

import com.hellofresh.cif.lib.dcbalancer.DcWeightLoadBalancer.Companion.Configuration
import com.hellofresh.cif.lib.dcbalancer.DcWeightLoadBalancer.Companion.emptyConfiguration
import io.mockk.coEvery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import kotlin.random.Random
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class DcWeightLoadBalancerTest {

    private val dcConsumer1 = mockk<DcConsumer>(relaxed = true)
    private val dcConsumer2 = mockk<DcConsumer>(relaxed = true)
    private val dcWeightRepository = mockk<DcWeightRepository>()
    private val loadSplitter = mockk<DcWeightRoundRobinLoadSplitter>()

    @Test
    fun `returns filtered Dcs from DB and call dc consumers`() {
        val dcCode1 = "dc1"
        val dcCode2 = "dc2"

        val dcConfigs = setOf(
            DcWeight(dcCode1, Random.nextLong()),
            DcWeight(dcCode2, Random.nextLong()),
        )
        coEvery { dcWeightRepository.fetchDcsWithWeight() } returns dcConfigs
        val expectedDcs = setOf(dcCode1)
        every { loadSplitter.filterDcConfigs(dcConfigs) } returns expectedDcs

        val dcService = DcWeightLoadBalancer(
            loadSplitter,
            dcWeightRepository,
            emptyConfiguration,
            listOf(dcConsumer1, dcConsumer2),
        )

        val loadDcs = dcService.loadDcs()

        assertEquals(expectedDcs, loadDcs)
        assertEquals(expectedDcs, dcService.loadedDcCodes)
        verify(exactly = 1) { dcConsumer1(expectedDcs) }
        verify(exactly = 1) { dcConsumer2(expectedDcs) }
    }

    @Test
    fun `don't call consumers when there is no new dc`() {
        val dcCode1 = "dc1"

        val dcConfigs1 = setOf(DcWeight(dcCode1, Random.nextLong()))

        coEvery { dcWeightRepository.fetchDcsWithWeight() } returns dcConfigs1
        every { loadSplitter.filterDcConfigs(any()) } answers {
            this.firstArg<Set<DcWeight>>().map { it.dcCode }.toSet()
        }
        val expectedDcs = setOf(dcCode1)

        val dcService = DcWeightLoadBalancer(
            loadSplitter,
            dcWeightRepository,
            emptyConfiguration,
            listOf(dcConsumer1, dcConsumer2)
        )

        assertEquals(expectedDcs, dcService.loadDcs())
        assertEquals(expectedDcs, dcService.loadedDcCodes)
        assertEquals(expectedDcs, dcService.loadDcs())
        assertEquals(expectedDcs, dcService.loadedDcCodes)
        verify(exactly = 1) { dcConsumer1(expectedDcs) }
        verify(exactly = 1) { dcConsumer2(expectedDcs) }
    }

    @Test
    fun `call consumers again when there is a new dc`() {
        val dcCode1 = "dc1"
        val dcCode2 = "dc2"

        val dcConfigs1 = setOf(DcWeight(dcCode1, Random.nextLong()))
        val dcConfigs2 = setOf(DcWeight(dcCode2, Random.nextLong()))

        coEvery { dcWeightRepository.fetchDcsWithWeight() } returns dcConfigs1 andThen dcConfigs2
        every { loadSplitter.filterDcConfigs(any()) } answers {
            this.firstArg<Set<DcWeight>>().map { it.dcCode }.toSet()
        }

        val dcService = DcWeightLoadBalancer(
            loadSplitter,
            dcWeightRepository,
            emptyConfiguration,
            listOf(dcConsumer1, dcConsumer2)
        )

        val expectedDcCode1 = setOf(dcCode1)
        assertEquals(expectedDcCode1, dcService.loadDcs())
        assertEquals(expectedDcCode1, dcService.loadedDcCodes)
        val expectedDcCode2 = setOf(dcCode2)
        assertEquals(expectedDcCode2, dcService.loadDcs())
        assertEquals(expectedDcCode2, dcService.loadedDcCodes)

        verify(exactly = 1) { dcConsumer1(expectedDcCode1) }
        verify(exactly = 1) { dcConsumer2(expectedDcCode1) }
        verify(exactly = 1) { dcConsumer1(expectedDcCode2) }
        verify(exactly = 1) { dcConsumer2(expectedDcCode2) }
    }

    @Test
    fun `returns filtered Dcs from DB excluding configuration`() {
        val dcCode1 = "dc1"
        val dcCode2 = "dc2"

        val dcConfigs = setOf(
            DcWeight(dcCode1, Random.nextLong()),
            DcWeight(dcCode2, Random.nextLong()),
        )
        coEvery { dcWeightRepository.fetchDcsWithWeight() } returns dcConfigs
        val expectedDcs = setOf(dcCode2)
        every {
            loadSplitter.filterDcConfigs(
                match { it.size == 1 && it.first().dcCode == dcCode2 },
            )
        } returns expectedDcs

        val dcService = DcWeightLoadBalancer(
            loadSplitter,
            dcWeightRepository,
            Configuration(includedDcs = emptySet(), excludedDcs = setOf(dcCode1)),
            listOf(dcConsumer1, dcConsumer2),
        )

        val loadDcs = dcService.loadDcs()

        assertEquals(expectedDcs, loadDcs)
        assertEquals(expectedDcs, dcService.loadedDcCodes)
        verify(exactly = 1) { dcConsumer1(expectedDcs) }
        verify(exactly = 1) { dcConsumer2(expectedDcs) }
    }

    @Test
    fun `just included Dcs are used`() {
        val dcCode = "dc1"

        val dcConfigs = setOf(
            DcWeight(dcCode, Random.nextLong()),
            DcWeight("other", Random.nextLong()),
        )
        coEvery { dcWeightRepository.fetchDcsWithWeight() } returns dcConfigs
        val expectedDcs = setOf(dcCode)

        val dcService = DcWeightLoadBalancer(
            loadSplitter,
            dcWeightRepository,
            Configuration(includedDcs = expectedDcs, excludedDcs = emptySet()),
            listOf(dcConsumer1, dcConsumer2),
        )

        val loadDcs = dcService.loadDcs()

        assertEquals(expectedDcs, loadDcs)
        assertEquals(expectedDcs, dcService.loadedDcCodes)

        verify(exactly = 0) { loadSplitter.filterDcConfigs(any()) }
        verify(exactly = 1) { dcConsumer1(expectedDcs) }
        verify(exactly = 1) { dcConsumer2(expectedDcs) }
    }
}
