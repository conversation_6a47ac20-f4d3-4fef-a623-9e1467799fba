package com.hellofresh.cif.lib

import com.hellofresh.cif.checks.CheckResult
import com.hellofresh.cif.checks.Checks
import io.micrometer.core.instrument.Counter
import io.micrometer.prometheus.PrometheusConfig
import io.micrometer.prometheus.PrometheusMeterRegistry
import io.restassured.RestAssured.given
import kotlin.random.Random
import kotlin.test.BeforeTest
import kotlin.test.Test
import org.hamcrest.Matchers
import org.hamcrest.core.IsNot.not

class StatusServerTest {
    var port: Int? = null

    @BeforeTest fun setup() {
        port = Random(System.currentTimeMillis()).nextInt(10000, 20000)
    }

    @Test fun `liveness check is gives ok`() {
        StatusServer.run(null, port!!)
        given().port(port!!).get("/health").then().statusCode(200)
    }

    @Test fun `liveness check returns unavailable`() {
        StatusServer.run(null, port!!, healthCheck = Checks("").add { CheckResult("", false) })
        given().port(port!!).get("/health").then().statusCode(not(200))
    }

    @Test fun `startup probe check gives ok`() {
        StatusServer.run(null, port!!)
        given().port(port!!).get("/startup").then().statusCode(200)
    }

    @Test fun `startup probe check returns unavailable`() {
        StatusServer.run(null, port!!, startUpCheck = Checks("").add { CheckResult("", false) })
        given().port(port!!).get("/startup").then().statusCode(not(200))
    }

    @Test fun `metrics are not available if meterRegistry is not given`() {
        StatusServer.run(null, port!!)
        given().port(port!!).get("/prometheus").then().statusCode(404)
    }

    @Test fun `metrics are given in prometheus format`() {
        val registry = PrometheusMeterRegistry(PrometheusConfig.DEFAULT)
        Counter.builder("test")
            .tag("key", "value")
            .register(registry)
            .increment(10.toDouble())

        StatusServer.run(registry, port!!)
        given().port(port!!).get("/prometheus")
            .then()
            .statusCode(200)
            .body(
                Matchers.stringContainsInOrder("test_total{key=\"value\",} 10.0"),
            )
    }
}
