package com.hellofresh.cif.lib.dcbalancer

import kotlinx.coroutines.runBlocking
import org.apache.logging.log4j.kotlin.Logging

typealias DcConsumer = (Set<String>) -> Unit

class DcWeightLoadBalancer(
    private val dcWeightRoundRobinLoadSplitter: DcWeightRoundRobinLoadSplitter,
    private val dcWeightRepository: DcWeightRepository,
    private val configuration: Configuration = emptyConfiguration,
    private val dcLoadConsumers: List<DcConsumer> = emptyList(),
) : DcLoadBalancer {

    override var loadedDcCodes = emptySet<String>()

    constructor(
        replicaCount: Int,
        podName: String,
        dcWeightRepository: DcWeightRepository,
        configuration: Configuration = emptyConfiguration,
        dcLoadConsumers: List<DcConsumer>,
    ) : this(
        DcWeightRoundRobinLoadSplitter(replicaCount, podName),
        dcWeightRepository,
        configuration,
        dcLoadConsumers,
    )

    override fun loadDcs(): Set<String> =
        runBlocking { dcWeightRepository.fetchDcsWithWeight() }
            .split()
            .also { dcCodes ->
                val pendingDcCodes = dcCodes.filter { !loadedDcCodes.contains(it) }.toSet()
                if (pendingDcCodes.isNotEmpty()) {
                    kotlin.runCatching {
                        dcLoadConsumers.forEach { it(pendingDcCodes) }
                    }.onFailure {
                        logger.error("Problem consuming dcs", it)
                    }
                }
                loadedDcCodes = dcCodes
            }

    private fun Set<DcWeight>.split() =
        if (configuration.includedDcs.isNotEmpty()) {
            configuration.includedDcs.filter { includedDc ->
                this.any { it.dcCode == includedDc }
            }.toSet()
        } else {
            dcWeightRoundRobinLoadSplitter
                .filterDcConfigs(
                    this.filter {
                        configuration.excludedDcs.isEmpty() ||
                            !configuration.excludedDcs.contains(it.dcCode)
                    }
                        .toSet(),
                )
        }

    companion object : Logging {
        val emptyConfiguration = Configuration(emptySet(), emptySet())

        data class Configuration(
            val includedDcs: Set<String>,
            val excludedDcs: Set<String>,
        )
    }
}
