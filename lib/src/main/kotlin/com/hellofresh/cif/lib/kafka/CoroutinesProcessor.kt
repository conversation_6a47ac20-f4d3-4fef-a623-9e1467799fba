package com.hellofresh.cif.lib.kafka

import com.hellofresh.cif.checks.CheckResult
import com.hellofresh.cif.checks.HealthCheck
import com.hellofresh.cif.lib.kafka.ErrorHandlingDeserializer.Companion.readDeserializeErrorHeader
import io.github.resilience4j.kotlin.retry.executeSuspendFunction
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Timer
import io.micrometer.core.instrument.binder.kafka.KafkaClientMetrics
import java.time.Duration
import java.util.concurrent.TimeUnit.MILLISECONDS
import java.util.concurrent.atomic.AtomicBoolean
import java.util.regex.Pattern
import kotlin.system.measureTimeMillis
import kotlin.time.toJavaDuration
import kotlinx.coroutines.delay
import kotlinx.coroutines.withTimeout
import org.apache.kafka.clients.consumer.Consumer
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.common.KafkaException
import org.apache.kafka.common.TopicPartition
import org.apache.kafka.common.errors.RecordDeserializationException
import org.apache.kafka.common.errors.WakeupException
import org.apache.kafka.common.serialization.Deserializer
import org.apache.logging.log4j.Level
import org.apache.logging.log4j.kotlin.Logging

/**
 * Blocks the calling thread as it continuously polls and [transform] the Kafka
 * records. More throughput could be achieved by running multiple instances of
 * this processor in different threads.

 * It does not handle any errors, and relies on [transform] to handle the
 * exceptions during processing. Any unhandled exception will lead to the
 * thread dying.
 *
 * Similarly, exceptions arising during polling will kill the process. These
 * exceptions could be due to deserialization and other exceptions from Kafka
 * which are irrecoverable.
 *
 * */

class CoroutinesProcessor<K, V>(
    private val pollConfig: PollConfig,
    private val consumer: Consumer<K, V>,
    meterRegistry: MeterRegistry,
    private val process: suspend (ConsumerRecords<K, V>) -> Unit,
    private val recordProcessingExceptionStrategy: RecordProcessingExceptionStrategy,
    private val handleDeserializationException: DeserializationExceptionStrategy = LogAndIgnoreExceptions(Level.ERROR)
) : AutoCloseable, HealthCheck {

    private val closed = AtomicBoolean(false)
    private val isLive = AtomicBoolean(false)

    private val taskDuration = Timer.builder("task_duration")
        .description("Record the elapsed time of the execution of the task.")
        .register(meterRegistry)

    init {
        KafkaClientMetrics(consumer).bindTo(meterRegistry)
    }

    constructor(
        pollConfig: PollConfig,
        consumerProcessorConfig: ConsumerProcessorConfig<K, V>,
        meterRegistry: MeterRegistry,
        process: suspend (ConsumerRecords<K, V>) -> Unit,
        recordProcessingExceptionStrategy: RecordProcessingExceptionStrategy,
        handleDeserializationException: DeserializationExceptionStrategy
    ) : this(
        pollConfig,
        consumer(consumerProcessorConfig),
        meterRegistry,
        process,
        recordProcessingExceptionStrategy,
        handleDeserializationException,
    )

    @Suppress("TooGenericExceptionCaught")
    suspend fun run() {
        try {
            val pollTimeout = pollConfig.pollTimeout.toJavaDuration()
            while (!closed.get()) {
                pollAndProcessRecords(pollTimeout)
            }
        } catch (e: WakeupException) {
            // Mostly happening when closing consumer, throwing error if it wasnt close before
            if (!closed.get()) throw e
        } catch (e: Throwable) {
            logger.warn("Consumer thread will exit: ${consumer.getConsumerDescription()}", e)
        } finally {
            isLive.set(false)
            consumer.close()
        }
    }

    internal suspend fun pollAndProcessRecords(pollTimeout: Duration) {
        val records = pollFromKafka(pollTimeout)
        if (!records.isEmpty) {
            processRecords(records)
        } else {
            delay(pollConfig.pollInterval)
        }
    }

    private suspend fun processRecords(records: ConsumerRecords<K, V>) {
        val timeInMillis = measureTimeMillis {
            // throws any exception other than timeout

            withTimeout(pollConfig.processTimeout) {
                @Suppress("TooGenericExceptionCaught")
                try {
                    if (recordProcessingExceptionStrategy is RetryAndContinueProcessing) {
                        recordProcessingExceptionStrategy.retry.executeSuspendFunction { process.invoke(records) }
                    } else {
                        process.invoke(records)
                    }

                    consumer.commitAsync()
                } catch (e: Exception) {
                    recordProcessingExceptionStrategy.invoke(e)
                }
            }
        }
        taskDuration.record(timeInMillis, MILLISECONDS)
    }

    private fun pollFromKafka(pollTimeout: Duration): ConsumerRecords<K, V> {
        val records = try {
            consumer.poll(pollTimeout)
        } catch (e: RecordDeserializationException) {
            logger.error("Deserialization error not properly handled, ErrorHandlingDeserializer must be used", e)
            handleDeserializationException(e)
            ConsumerRecords(emptyMap())
        }.mapNotNull { record ->
            val exceptionMessage: String? = readDeserializeErrorHeader(record)
            if (exceptionMessage != null) {
                handleDeserializationException(KafkaException(exceptionMessage))
                null
            } else {
                record
            }
        }
            .groupBy { TopicPartition(it.topic(), it.partition()) }
            .let { ConsumerRecords(it) }

        // Its live after poll is success
        isLive.set(true)
        return records
    }

    override fun close() {
        closed.set(true)
        consumer.wakeup()
    }

    override suspend fun isHealthy() = CheckResult("Topic Consumer: ${consumer.getConsumerDescription()}", isLive.get())

    private fun Consumer<*, *>.getConsumerDescription() =
        this.metrics().keys
            .filter { metricName -> metricName.tags().containsKey(TOPIC_TAG) && metricName.tags().containsKey(PARTITION_TAG) }
            .groupBy { metricName -> metricName.tags().getValue(TOPIC_TAG) }
            .map { (topic, metrics) ->
                "$topic - ${
                    (
                        metrics.map {
                            it.tags().get(
                                PARTITION_TAG,
                            )
                        }.distinct().joinToString(",")
                        )
                }"
            }
            .joinToString(separator = " | ")
            .ifEmpty { "Unknown" }

    companion object : Logging {

        private const val TOPIC_TAG = "topic"
        private const val PARTITION_TAG = "partition"

        fun <K, V> consumer(consumerProcessorConfig: ConsumerProcessorConfig<K, V>) =
            KafkaConsumer(
                consumerProcessorConfig.configurations,
                ErrorHandlingDeserializer(true, consumerProcessorConfig.keyDeserializer),
                ErrorHandlingDeserializer(false, consumerProcessorConfig.valueDeserializer),
            ).also {
                if (consumerProcessorConfig.topicsPattern != null) {
                    it.subscribe(consumerProcessorConfig.topicsPattern)
                } else {
                    it.subscribe(consumerProcessorConfig.topics)
                }
            }
    }
}

data class ConsumerProcessorConfig<K, V>(
    val configurations: Map<String, String>,
    val keyDeserializer: Deserializer<K>,
    val valueDeserializer: Deserializer<V>,
    val topics: List<String>,
    val topicsPattern: Pattern? = null
) {
    init {
        require(topics.isNotEmpty() || topicsPattern != null) { "Either topic list or pattern must be informed" }
    }
}
