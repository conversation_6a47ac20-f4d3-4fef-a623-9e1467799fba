package com.hellofresh.cif.lib.metrics

import com.hellofresh.cif.checks.CheckResult
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import io.micrometer.core.instrument.binder.jvm.ClassLoaderMetrics
import io.micrometer.core.instrument.binder.jvm.JvmGcMetrics
import io.micrometer.core.instrument.binder.jvm.JvmMemoryMetrics
import io.micrometer.core.instrument.binder.jvm.JvmThreadMetrics
import io.micrometer.core.instrument.binder.system.ProcessorMetrics
import io.micrometer.core.instrument.binder.system.UptimeMetrics
import io.micrometer.core.instrument.config.validate.Validated
import io.micrometer.prometheus.HistogramFlavor
import io.micrometer.prometheus.HistogramFlavor.Prometheus
import io.micrometer.prometheus.PrometheusConfig
import io.micrometer.prometheus.PrometheusMeterRegistry
import java.time.Duration

class HelloFreshMeterRegistry(
    /**
     * The project name is used as prefix for all meters.
     *
     * The value of this should be the same as the name of the GitHub repository
     * as this ensures uniqueness among all our projects. Consequently it also
     * matches the name of the Gradle root project, which also acts as the
     * source for this value if you use automatic construction through the
     * service library.
     */
    projectName: String,
    /**
     * The application name is added as a tag with the `application` key to all
     * meters.
     *
     * The value of this should be the same as the name of the artifact, or in
     * other words, that of the Gradle project. The latter acts as the source
     * for this value if you use automatic construction through the service
     * library.
     */
    applicationName: String,
    /**
     * Whether meter descriptions should be included in the Prometheus scrape
     * output, or not.
     *
     * Turning this off minimizes the amount of data that is sent to Prometheus
     * upon each scrap. However, there are obviously also no descriptions
     * available for the individual meters.
     */
    includeDescriptions: Boolean,
    /**
     * The interval at which our Prometheus will be scraping us.
     *
     * This is used to optimize window statistics like max.
     */
    scrapeInterval: Duration,
) : PrometheusMeterRegistry(
    object : PrometheusConfig {
        override fun histogramFlavor(): HistogramFlavor = Prometheus
        override fun descriptions(): Boolean = includeDescriptions
        override fun step(): Duration = scrapeInterval
        override fun get(key: String): String? = null
        override fun validate(): Validated<Any?> = Validated.none()
    },
) {
    init {
        config().apply {
            meterFilter(HfMeterFilter(projectName, applicationName))
        }

        ClassLoaderMetrics().bindTo(this)
        JvmGcMetrics().bindTo(this)
        JvmMemoryMetrics().bindTo(this)
        JvmThreadMetrics().bindTo(this)
        ProcessorMetrics().bindTo(this)
        UptimeMetrics().bindTo(this)
    }
}

fun createMeterRegistry(): HelloFreshMeterRegistry {
    val prometheusConfig = ConfigurationLoader.getPrometheusConfig()
    return HelloFreshMeterRegistry(
        prometheusConfig.projectName,
        prometheusConfig.applicationName,
        prometheusConfig.prometheusDescription.toBoolean(),
        Duration.parse(prometheusConfig.prometheusScrapeInterval),
    ).also {
        with({ CheckResult("MeterRegistry", !it.isClosed) }) {
            StartUpChecks.add(this)
            HealthChecks.add(this)
        }
    }
}
