package com.hellofresh.cif.lib

import java.time.Instant
import org.apache.kafka.clients.consumer.ConsumerRecord

/**
 * See [Record.instantTimestamp]
 */
@Suppress("MagicNumber")
fun <K, V> ConsumerRecord<K, V>.instantTimestamp(): Instant = this.timestamp().let {
    check(it > Instant.now().toEpochMilli() / 10) {
        "Record timestamp is in seconds: $it, key: ${this.key()}"
    }

    Instant.ofEpochMilli(it)
}
