package com.hellofresh.cif.lib.kafka.serde

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import com.google.protobuf.Message
import java.nio.ByteBuffer
import java.util.UUID
import org.apache.avro.generic.GenericContainer
import org.apache.kafka.common.serialization.Deserializer
import org.apache.kafka.common.serialization.Serde
import org.apache.kafka.common.serialization.Serdes
import org.apache.kafka.common.serialization.Serializer
import org.apache.kafka.common.utils.Bytes

/**
 * Some [Serde]s are stateful (e.g. Avro) and we thus have to create new ones
 * every time we need them.
 */
typealias SerdeSupplier<T> = () -> Serde<T>

/**
 * Gets the [Serde] for the given type [T].
 */
inline fun <reified T> serde(): Serde<T> =
    serde(T::class.java, ::jacksonTypeRef)

/**
 * Gets the [Serializer] for the given type [T].
 */
inline fun <reified T> serializer(): Serializer<T> =
    serde<T>().serializer()

/**
 * Gets the [Deserializer] for the given type [T].
 */
inline fun <reified T> deserializer(): Deserializer<T> =
    serde<T>().deserializer()

@PublishedApi
@Suppress("ComplexMethod", "UNCHECKED_CAST")
internal fun <T> serde(t: Class<T>, tr: () -> TypeReference<T>): Serde<T> =
    when {
        ByteArray::class.java.isAssignableFrom(t) -> Serdes.ByteArray()
        ByteBuffer::class.java.isAssignableFrom(t) -> Serdes.ByteBuffer()
        Bytes::class.java.isAssignableFrom(t) -> Serdes.Bytes()
        Double::class.java.isAssignableFrom(t) -> Serdes.Double()
        Float::class.java.isAssignableFrom(t) -> Serdes.Float()
        Int::class.java.isAssignableFrom(t) -> Serdes.Integer()
        Long::class.java.isAssignableFrom(t) -> Serdes.Long()
        Short::class.java.isAssignableFrom(t) -> Serdes.Short()
        String::class.java.isAssignableFrom(t) -> Serdes.String()
        UUID::class.java.isAssignableFrom(t) -> Serdes.UUID()
        Void::class.java.isAssignableFrom(t) -> Serdes.Void()
        Message::class.java.isAssignableFrom(t) -> ProtoSerde((t as Class<Message>).protoParser)
        GenericContainer::class.java.isAssignableFrom(t) -> error(
            "Unsupported Avro $t (note that you cannot use Avro's " +
                "SpecificRecord because it does not support the schema " +
                "evolution of Avro through the schema registry)",
        )

        else -> JacksonSerde(tr())
    } as Serde<T>
