package com.hellofresh.cif.lib.metrics

import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import java.util.concurrent.ConcurrentHashMap
import kotlin.reflect.KClass

/**
 * Record the execution of the given [block] via the supplied [meterRegistry].
 *
 * The name of this function is very generic as we might want to extend its
 * functionality in the future by collecting additional metrics of the given
 * [block].
 *
 * @see recordSuccess
 * @see recordFailure
 */
inline fun <reified C : Any, R> C.record(meterRegistry: MeterRegistry, block: C.() -> R): R =
    try {
        val result = block()
        meterRegistry.recordSuccess<C>()
        result
    } catch (re: Throwable) {
        meterRegistry.recordFailure<C>()
        throw re
    }

// -----------------------------------------------------------------------------

/**
 * Increments the success meter of the given [component].
 *
 * Assuming your component is `com.acme.Foo` then the resulting counter will be
 * available in the Prometheus scrape as follows:
 *
 * ```
 * # HELP ${project.name}_component_success_total
 * # TYPE ${project.name}_component_success_total counter
 * ${project.name}_component_success_total{application="${application.name}",name="com.acme.Foo",} 1.0
 * ```
 */
fun MeterRegistry.recordSuccess(component: KClass<*>, vararg tags: Tag) =
    component(this, SUCCESS, component, tags.toList())

fun MeterRegistry.recordSuccess(componentName: String, vararg tags: Tag) =
    component(this, SUCCESS, componentName, tags.toList())

inline fun <reified C : Any> MeterRegistry.recordSuccess() =
    recordSuccess(C::class)

// -----------------------------------------------------------------------------

/**
 * Increments the failure meter of the given [component].
 *
 * Assuming your component is `com.acme.Foo` then the resulting counter will be
 * available in the Prometheus scrape as follows:
 *
 * ```
 * # HELP ${project.name}_component_failure_total
 * # TYPE ${project.name}_component_failure_total counter
 * ${project.name}_component_failure_total{application="${application.name}",name="com.acme.Foo",} 1.0
 * ```
 */
fun MeterRegistry.recordFailure(component: KClass<*>, vararg tags: Tag) =
    component(this, FAILURE, component, tags.toList())

fun MeterRegistry.recordFailure(componentName: String, vararg tags: Tag) =
    component(this, FAILURE, componentName, tags.toList())

inline fun <reified C : Any> MeterRegistry.recordFailure() =
    recordFailure(C::class)

// -----------------------------------------------------------------------------

private const val SUCCESS = "success"
private const val FAILURE = "failure"

/**
 * We keep an O(1) map around of the component counters so that we do not have
 * to go through all meters that were registered. This is especially important
 * because the IDs of the meters run through filters and we might not be able
 * to find them again based on the name that is available to us here.
 */
private val componentCounters = ConcurrentHashMap<String, Counter>()

private fun component(it: MeterRegistry, result: String, componentName: String, tags: List<Tag>) {
    assert(result == SUCCESS || result == FAILURE) {
        "Result must be either `$SUCCESS` or `$FAILURE`, got: $result"
    }

    componentCounters.getOrPut("$componentName.$result") {
        it.counter("component_$result", tags + Tag.of("name", componentName))
    }.increment()
}

private fun component(it: MeterRegistry, result: String, component: KClass<*>, tags: List<Tag>) {
    component(it, result, component.java.name, tags)
}
