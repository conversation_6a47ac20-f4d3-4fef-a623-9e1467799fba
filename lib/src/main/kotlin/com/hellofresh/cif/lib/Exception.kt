package com.hellofresh.cif.lib

/**
 * Executes [onFailure] if an error is thrown of type [T].
 *
 * @return output of the lambda [fn] or _null_ if an error of not type [T]
 * occur.
 */
inline fun <reified T : Throwable, R> runCatching(fn: () -> R, onFailure: (T) -> Unit): R? =
    runCatching { fn() }
        .onFailure {
            if (it is T) {
                onFailure(it)
            } else {
                throw it
            }
        }
        .getOrNull()
