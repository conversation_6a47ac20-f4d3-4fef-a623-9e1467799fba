package com.hellofresh.cif.lib.kafka

import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.common.header.Headers
import org.apache.kafka.common.header.internals.RecordHeader
import org.apache.kafka.common.serialization.Deserializer
import org.apache.logging.log4j.kotlin.Logging

private const val DESERIALIZER_EXCEPTION_HEADER_PREFIX = "deserializerException"
const val KEY_DESERIALIZER_EXCEPTION_HEADER = DESERIALIZER_EXCEPTION_HEADER_PREFIX + "Key"
const val VALUE_DESERIALIZER_EXCEPTION_HEADER = DESERIALIZER_EXCEPTION_HEADER_PREFIX + "Value"

class ErrorHandlingDeserializer<T>(private val isKeyDeserializer: Boolean, private val delegate: Deserializer<T>) : Deserializer<T> by delegate {

    override fun deserialize(topic: String, headers: Headers, data: ByteArray?): T? =
        runCatching {
            if (this.isKeyDeserializer) {
                headers.remove(KEY_DESERIALIZER_EXCEPTION_HEADER)
            } else {
                headers.remove(VALUE_DESERIALIZER_EXCEPTION_HEADER)
            }
            this.delegate.deserialize(topic, headers, data)
        }.recoverCatching { e ->
            processDeserializationException(topic, this.isKeyDeserializer, headers, e)
            return null
        }.getOrThrow()

    companion object : Logging {

        internal fun processDeserializationException(topic: String, isKey: Boolean, headers: Headers, cause: Throwable) {
            headers.add(
                RecordHeader(
                    if (isKey) KEY_DESERIALIZER_EXCEPTION_HEADER else VALUE_DESERIALIZER_EXCEPTION_HEADER,
                    createDeserializationErrorMessage(topic, isKey, headers, cause).toByteArray(Charsets.UTF_8),
                ),
            )
        }

        private fun createDeserializationErrorMessage(topic: String, isKey: Boolean, headers: Headers, cause: Throwable) =
            "Failed to deserialize ${if (isKey) "Key" else "Value body"} in topic $topic with headers ${printHeaders(
                headers
            )}, cause: ${cause.message}"

        private fun printHeaders(headers: Headers) =
            runCatching {
                "Headers( " +
                    headers.joinToString(",") { "(key: ${it.key()}, value: ${String(it.value(), Charsets.UTF_8)})" } +
                    ')'
            }.getOrElse { "Error reading headers" }

        fun <K, V> readDeserializeErrorHeader(record: ConsumerRecord<K?, V?>): String? {
            var deserializationErrorMessage: String? = null

            if (record.key() == null) {
                deserializationErrorMessage = deserializeHeader(record.headers(), KEY_DESERIALIZER_EXCEPTION_HEADER)
            }

            if (record.value() == null) {
                deserializationErrorMessage = deserializeHeader(record.headers(), VALUE_DESERIALIZER_EXCEPTION_HEADER)
            }
            return deserializationErrorMessage
        }

        private fun deserializeHeader(headers: Headers, headerKey: String): String? =
            runCatching {
                headers.lastHeader(headerKey)
                    ?.value()
                    ?.let {
                        String(it, Charsets.UTF_8)
                    }
            }.onFailure {
                logger.error("Failed to deserialize header $headerKey Deserialization Exception message")
            }.getOrElse { "Could not read deserialization error message" }
    }
}
