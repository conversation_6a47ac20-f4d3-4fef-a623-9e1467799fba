package com.hellofresh.cif.lib.kafka.serde

import kotlin.reflect.KProperty
import kotlin.reflect.KType
import kotlin.reflect.full.starProjectedType
import org.apache.avro.Schema.Field
import org.apache.avro.generic.GenericEnumSymbol
import org.apache.avro.generic.GenericRecord
import org.apache.avro.util.Utf8

/**
 * The more complicated logic is not being inlined by us because we do expect
 * that most callers will be successful with the inlined code and do not require
 * the extended variation, except for strings, those will always run into it.
 */
@PublishedApi
@Suppress("UNCHECKED_CAST")
internal fun <T> convert(field: Field, value: Any?, c: Class<out T>, t: KType): T =
    when {
        // This is an important step that must come before the Utf8/String check
        // because we need to protect us from returning null if the caller
        // requested a non-null type. This does exactly that, if it's null and
        // the requested type can be null we directly return.
        value == null && field.schema().isNullable -> null as T

        // Av<PERSON> is using a special Utf8 class to represent Strings. It is
        // basically a StringBuilder but one that enforces that all byte
        // sequences must seem like valid UTF-8 byte sequences. This is nice,
        // but users do not know about this and thus we have this special
        // casing here so that they can simply request String.
        //
        // The second special case we have here is for enums. Avro's type is
        // completely useless to us because it provides us, besides the string
        // representation of the enum member, the schema. However, we are never
        // interested in parsing the schema and only want the string so that we
        // can parse it.
        (value is Utf8 || value is GenericEnumSymbol<*>) && String::class.java.isAssignableFrom(c) -> value.toString() as T

        else -> error(
            "Could not convert ${field.name()} from ${if (value == null) "null" else value::class.java} to $t"
        )
    }

inline operator fun <reified T> GenericRecord.getValue(receiver: Nothing?, property: KProperty<*>): T =
    get(property.name).let {
        if (it is T) {
            it
        } else {
            convert(schema.getField(property.name), it, T::class.java, T::class.starProjectedType)
        }
    }

interface DefaultValueDelegate<T> {
    operator fun getValue(receiver: Nothing?, property: KProperty<*>): T
}

inline fun <reified T> GenericRecord.or(default: T): DefaultValueDelegate<T> =
    object : DefaultValueDelegate<T> {
        override fun getValue(receiver: Nothing?, property: KProperty<*>): T =
            if (hasField(property.name)) {
                <EMAIL>(receiver, property)
            } else {
                default
            }
    }
