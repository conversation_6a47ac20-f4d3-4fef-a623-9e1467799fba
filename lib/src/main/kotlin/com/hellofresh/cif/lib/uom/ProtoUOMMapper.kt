package com.hellofresh.cif.lib.uom

import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.SkuUOM.UOM_GAL
import com.hellofresh.cif.models.SkuUOM.UOM_KG
import com.hellofresh.cif.models.SkuUOM.UOM_LBS
import com.hellofresh.cif.models.SkuUOM.UOM_LITRE
import com.hellofresh.cif.models.SkuUOM.UOM_OZ
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT as SKU_UOM_UNIT
import com.hellofresh.cif.models.SkuUOM.UOM_UNRECOGNIZED
import com.hellofresh.cif.models.SkuUOM.UOM_UNSPECIFIED
import com.hellofresh.proto.stream.distributionCenter.inbound.goodsReceivedNote.v1.GoodsReceivedNoteValue.UnitOfMeasure
import com.hellofresh.proto.stream.supply.advanceShippingNotice.v2.PurchaseOrderAsn.AsnItem.Quantity.CasePackaging.UnitOfMeasure as AsnUnitOfMeasure
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderItem.CasePackaging.UOM
import com.hellofresh.proto.stream.supply.procurement.purchaseOrder.v1.PurchaseOrder.PurchaseOrderItem.CasePackaging.UOM.UOM_UNIT
import com.hellofresh.proto.stream.ye.supply.procurement.purchaseOrder.v2.PurchaseOrder.PurchaseOrderItem.CasePackaging.UOM as YfUom

fun UOM.toUOM(): SkuUOM =
    when (this) {
        UOM_UNIT -> SKU_UOM_UNIT
        UOM.UOM_UNSPECIFIED -> UOM_UNSPECIFIED
        UOM.UOM_KG -> UOM_KG
        UOM.UOM_LBS -> UOM_LBS
        UOM.UOM_OZ -> UOM_OZ
        UOM.UOM_GAL -> UOM_GAL
        UOM.UOM_LITRE -> UOM_LITRE
        UOM.UNRECOGNIZED -> UOM_UNRECOGNIZED
    }

fun YfUom.toUOM(): SkuUOM =
    when (this) {
        YfUom.UOM_UNIT -> SKU_UOM_UNIT
        YfUom.UOM_UNSPECIFIED -> UOM_UNSPECIFIED
        YfUom.UOM_KG -> UOM_KG
        YfUom.UOM_LBS -> UOM_LBS
        YfUom.UOM_OZ -> UOM_OZ
        YfUom.UOM_GAL -> UOM_GAL
        YfUom.UOM_LITRE -> UOM_LITRE
        YfUom.UNRECOGNIZED -> UOM_UNRECOGNIZED
    }

/**
 * Here the UNIT_OF_MEASURE_CASE is considered as UOM_UNIT
 * because we consider the palletizedQuantity while processing the grn messages
 * hence UOM_UNIT.
 */

fun UnitOfMeasure.toUOM(): SkuUOM =
    when (this) {
        UnitOfMeasure.UNIT_OF_MEASURE_UNSPECIFIED -> UOM_UNSPECIFIED
        UnitOfMeasure.UNIT_OF_MEASURE_UNIT -> SkuUOM.UOM_UNIT
        UnitOfMeasure.UNIT_OF_MEASURE_CASE -> SkuUOM.UOM_UNIT
        UnitOfMeasure.UNIT_OF_MEASURE_KG -> UOM_KG
        UnitOfMeasure.UNIT_OF_MEASURE_LBS -> UOM_LBS
        UnitOfMeasure.UNIT_OF_MEASURE_OZ -> UOM_OZ
        UnitOfMeasure.UNIT_OF_MEASURE_GAL -> UOM_GAL
        UnitOfMeasure.UNIT_OF_MEASURE_LITRE -> UOM_LITRE
        UnitOfMeasure.UNRECOGNIZED, UnitOfMeasure.UNIT_OF_MEASURE_OTHER -> UOM_UNRECOGNIZED
    }

fun AsnUnitOfMeasure.toUOM(): SkuUOM =
    when (this) {
        AsnUnitOfMeasure.UNIT_OF_MEASURE_UNSPECIFIED -> UOM_UNSPECIFIED

        AsnUnitOfMeasure.UNIT_OF_MEASURE_UNIT -> SkuUOM.UOM_UNIT
        AsnUnitOfMeasure.UNIT_OF_MEASURE_CASE -> SkuUOM.UOM_UNIT
        AsnUnitOfMeasure.UNIT_OF_MEASURE_KG -> UOM_KG
        AsnUnitOfMeasure.UNIT_OF_MEASURE_LBS -> UOM_LBS
        AsnUnitOfMeasure.UNIT_OF_MEASURE_OZ -> UOM_OZ
        AsnUnitOfMeasure.UNIT_OF_MEASURE_GAL -> UOM_GAL
        AsnUnitOfMeasure.UNIT_OF_MEASURE_LITRE -> UOM_LITRE
        AsnUnitOfMeasure.UNRECOGNIZED, AsnUnitOfMeasure.UNIT_OF_MEASURE_OTHER -> UOM_UNRECOGNIZED
    }
