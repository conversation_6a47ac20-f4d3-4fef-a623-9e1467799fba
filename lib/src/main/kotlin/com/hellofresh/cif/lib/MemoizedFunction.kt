package com.hellofresh.cif.lib

class MemoizedFunction<in IN, out OUT>(val fn: ((IN) -> OUT)) : ((IN) -> OUT) {
    private val cache: MutableMap<IN, OUT> = mutableMapOf()
    fun clearAll() {
        cache.clear()
    }

    fun clear(param: IN) = cache.remove(param)

    override fun invoke(param: IN): OUT = cache[param] ?: let {
        val r = fn(param)
        cache.putIfAbsent(param, r)
        r
    }
}

fun <IN, OUT> ((IN) -> OUT).memoize() = MemoizedFunction(this)
