package com.hellofresh.cif.lib.kafka

import io.github.resilience4j.core.IntervalFunction
import io.github.resilience4j.kotlin.retry.RetryConfig
import io.github.resilience4j.kotlin.retry.RetryRegistry
import io.github.resilience4j.retry.Retry
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import java.time.Duration
import org.apache.logging.log4j.kotlin.Logging

fun interface RecordProcessingExceptionStrategy : (Throwable) -> Unit {
    companion object : Logging
}

class FailProcessing(val registry: MeterRegistry, errorMetricTag: String) : RecordProcessingExceptionStrategy {
    private val errorMetric = Counter.builder(errorMetricTag).register(registry)
    override fun invoke(exc: Throwable) {
        RecordProcessingExceptionStrategy.logger.error(
            "Records processing failed, aborting. Reason ${exc.message}",
            exc
        )
        errorMetric.increment()
        throw exc
    }
}

class IgnoreAndContinueProcessing(val registry: MeterRegistry, errorMetricTag: String) :
    RecordProcessingExceptionStrategy {
    private val errorMetric = Counter.builder(errorMetricTag).register(registry)
    override fun invoke(exc: Throwable) {
        RecordProcessingExceptionStrategy.logger.warn(
            "Records processing failed, ignoring. Reason ${exc.message}",
            exc
        )
        errorMetric.increment()
    }
}

private const val INITIAL_RETRY_INTERVAL_MS = 200L

class RetryAndContinueProcessing(
    private val numRetries: Int,
    val registry: MeterRegistry,
    errorMetricTag: String
) : RecordProcessingExceptionStrategy {
    private val errorMetric = Counter.builder(errorMetricTag).register(registry)
    val retry: Retry = RetryRegistry {
        withRetryConfig(
            RetryConfig {
                maxAttempts(numRetries)
                intervalFunction(
                    IntervalFunction.ofExponentialBackoff(
                        Duration.ofMillis(INITIAL_RETRY_INTERVAL_MS),
                        2.0
                    )
                )
            }
        )
    }.retry("coroutines-processor-retry")

    override fun invoke(exc: Throwable) {
        RecordProcessingExceptionStrategy.logger.warn(
            "Records processing failed, ignoring. Reason ${exc.message}",
            exc
        )
        errorMetric.increment()
    }
}
