package com.hellofresh.cif.lib.dcbalancer

class DcWeightRoundRobinLoadSplitter(private val replicaCount: Int, private val podName: String) : DcLoadSplitter {
    override fun filterDcConfigs(dcConfigs: Set<DcWeight>): Set<String> {
        val podOrdinalIndex = podOrdinalIndex(podName)
        val replicaCount = replicaCount
        return dcConfigs.sortedByDescending { it.weight }.filterIndexed { index, _ ->
            (index % replicaCount == podOrdinalIndex)
        }.map { it.dcCode }.toSet()
    }

    private fun podOrdinalIndex(podName: String) = podName.split("-").last().toInt()
}
