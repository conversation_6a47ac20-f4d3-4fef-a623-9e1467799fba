package com.hellofresh.cif.lib.kafka.serde

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonTypeRef
import org.apache.kafka.common.serialization.Deserializer
import org.apache.kafka.common.serialization.Serdes.WrapperSerde
import org.apache.kafka.common.serialization.Serializer

val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()

class JacksonSerde<T>(
    serializer: JacksonSerializer<T>,
    deserializer: JacksonDeserializer<T>,
) : WrapperSerde<T>(serializer, deserializer) {
    constructor(
        typeReference: TypeReference<T>,
    ) : this(JacksonSerializer(objectMapper), JacksonDeserializer(objectMapper, typeReference))
}

class JacksonSerializer<T>(private val mapper: ObjectMapper) : Serializer<T> {

    override fun serialize(
        topic: String,
        data: T?,
    ) = data?.let(mapper::writeValueAsBytes)
}

class JacksonDeserializer<T>(
    private val mapper: ObjectMapper,
    private val typeReference: TypeReference<T>,
) : Deserializer<T> {
    constructor(
        typeReference: TypeReference<T>,
    ) : this(objectMapper, typeReference)

    override fun deserialize(
        topic: String,
        data: ByteArray?,
    ) = data?.let { mapper.readValue(it, typeReference) }

    companion object {
        inline operator fun <reified T> invoke() = JacksonDeserializer(jacksonTypeRef<T>())
        inline operator fun <reified T> invoke(mapper: ObjectMapper) = JacksonDeserializer(mapper, jacksonTypeRef<T>())
    }
}
