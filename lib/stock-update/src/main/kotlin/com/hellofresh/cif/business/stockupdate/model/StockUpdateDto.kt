package com.hellofresh.cif.business.stockupdate.model

import com.hellofresh.cif.models.SkuQuantity
import java.time.LocalDate
import java.util.UUID

data class StockUpdateDto(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate,
    val quantity: SkuQuantity,
    val reason: Reason,
    val week: String,
    val reasonDetail: String?,
    val authorName: String?,
    val authorEmail: String?,
    val deleted: Boolean,
    val version: Int? = null,
)

enum class Reason(val value: String) {

    WMS_ISSUE("WMS Issue"),

    RECOVERY_BOX("Recovery Box"),

    STOCK_RECOUNT("Stock Recount"),

    SUBSTITUTION("Substitution"),

    WASTE("Waste"),

    INTERNAL_USAGE("Internal Usage"),

    OTHER("Other");

    companion object {
        fun fromValue(value: String): Reason =
            values().find { it.value == value } ?: OTHER
    }
}
