package com.hellofresh.cif.business.stockupdate.model

import com.hellofresh.cif.models.SkuQuantity
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID

data class StockUpdate(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate,
    val week: String,
    val quantity: SkuQuantity,
    val reason: String,
    val reasonDetail: String?,
    val authorName: String?,
    val authorEmail: String,
    val version: Int,
    val createdAt: LocalDateTime,
    val deleted: Boolean,
) {
    companion object
}
