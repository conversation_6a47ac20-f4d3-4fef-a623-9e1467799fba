package com.hellofresh.cif.business.stockupdate.repository

import com.hellofresh.cif.business.stock_update.schema.Tables.STOCK_UPDATE
import com.hellofresh.cif.business.stock_update.schema.tables.records.StockUpdateRecord
import com.hellofresh.cif.business.stockupdate.model.SkuQuantityMapper.mapToDbUom
import com.hellofresh.cif.business.stockupdate.model.StockUpdateDto
import org.jooq.DSLContext
import org.jooq.InsertResultStep

class StockUpdateRepositoryImpl : StockUpdateRepositoryInterface {

    override fun upsertStockUpdate(
        dslContext: DSLContext,
        stockUpdates: List<StockUpdateDto>,
    ): InsertResultStep<StockUpdateRecord> =
        insertStockUpdateQuery(dslContext, stockUpdates)

    private fun mapToStockUpdateRecord(stockUpdates: List<StockUpdateDto>): List<StockUpdateRecord> =
        stockUpdates.map { stockUpdate ->
            StockUpdateRecord().apply {
                skuId = stockUpdate.skuId
                dcCode = stockUpdate.dcCode
                version = stockUpdate.version
                date = stockUpdate.date
                week = stockUpdate.week
                quantity = stockUpdate.quantity.getValue()
                uom = mapToDbUom(stockUpdate.quantity.unitOfMeasure)
                reason = stockUpdate.reason.value
                reasonDetail = stockUpdate.reasonDetail
                authorName = stockUpdate.authorName
                authorEmail = stockUpdate.authorEmail
                deleted = stockUpdate.deleted
            }
        }

    private fun insertStockUpdateQuery(dslContext: DSLContext, stockUpdates: List<StockUpdateDto>): InsertResultStep<StockUpdateRecord> {
        val stockUpdateRecords = mapToStockUpdateRecord(stockUpdates)
        return dslContext
            .insertInto(STOCK_UPDATE)
            .set(stockUpdateRecords)
            .returning()
    }
}
