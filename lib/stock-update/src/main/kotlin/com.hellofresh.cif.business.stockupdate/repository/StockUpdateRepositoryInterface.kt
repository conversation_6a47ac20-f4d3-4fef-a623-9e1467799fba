package com.hellofresh.cif.business.stockupdate.repository

import com.hellofresh.cif.business.stock_update.schema.tables.records.StockUpdateRecord
import com.hellofresh.cif.business.stockupdate.model.StockUpdateDto
import org.jooq.DSLContext
import org.jooq.InsertResultStep

interface StockUpdateRepositoryInterface {
    fun upsertStockUpdate(
        dslContext: DSLContext,
        stockUpdates: List<StockUpdateDto>
    ): InsertResultStep<StockUpdateRecord>
}
