package com.hellofresh.cif.featureflags

import com.hellofresh.cif.featureflags.FeatureFlag.NoAclUnusableInventory
import com.hellofresh.cif.featureflags.FeatureFlag.StopPublishingDemandsForClients

class StatsigTestFeatureFlagClient(var fixtures: Set<FeatureFlag>) : StatsigFeatureFlagClient {

    override fun isEnabledFor(featureFlag: FeatureFlag, default: Boolean?): Boolean =
        when (fixtures.toList().firstOrNull()) {
            is StopPublishingDemandsForClients -> {
                var isMatching = false
                val featureFlagSet = featureFlag.contextData.toSet()

                for (fixture in fixtures) {
                    if (fixture.contextData.any { it in featureFlagSet }) {
                        isMatching = true
                        break
                    }
                }
                isMatching
            }
            is NoAclUnusableInventory -> fixtures.contains(featureFlag)
            else -> fixtures.contains(featureFlag)
        }
}
