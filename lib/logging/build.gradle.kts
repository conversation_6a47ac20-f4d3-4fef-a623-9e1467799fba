plugins {
    id("com.hellofresh.cif.common-conventions")
}

dependencies {
    api(platform(libs.log4j.bom))
    api(libs.log4j.api2)
    api(libs.log4j.core)
    api(libs.log4j.slf4j)
    api(libs.log4j.json)
    api(libs.log4j.kotlin)

    implementation(libs.slack.appender)
    implementation(libs.okhttp) {
        because("Required by Slack Appender")
    }

    modules {
        module("com.hellofresh:logging") {
            replacedBy("com.hellofresh.cif.lib:logging")
        }
    }
}
