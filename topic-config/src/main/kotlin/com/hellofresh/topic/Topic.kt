package com.hellofresh.topic

import com.hellofresh.cif.lib.kafka.serde.SerdeSupplier
import com.hellofresh.cif.lib.kafka.serde.serde
import org.apache.kafka.common.internals.Topic.validate

class Topic<K, V>(
    val prefix: String,
    val version: Int,
    private val keySerdeSupplier: SerdeSupplier<K>,
    private val valSerdeSupplier: SerdeSupplier<V>,
) {
    val name = "$prefix.v$version"
    val keySerde get() = keySerdeSupplier()
    val valSerde get() = valSerdeSupplier()

    init {
        validate(name)
        require(version > 0) { "Version must be greater than zero, got: $version" }
    }

    companion object {
        inline operator fun <reified K, reified V> invoke(
            prefix: String,
            version: Int,
        ) = Topic(prefix, version, { serde<K>() }, { serde<V>() })
    }
}
