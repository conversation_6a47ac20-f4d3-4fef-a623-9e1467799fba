package com.hellofresh.topicConfig

import java.util.Properties
import org.apache.logging.log4j.kotlin.Logging

class TopicConfig private constructor() {
    companion object : Logging {
        private val configMap = loadMultiple("topic-versions")

        fun getTopicVersion(name: String): Int {
            val topicVersionStr = configMap[name]
            require(
                topicVersionStr != null
            ) { "Topic $name requires a version. Add it to topic-versions.properties first." }
            val topicVersionInt = topicVersionStr.toString().toIntOrNull()
            require(topicVersionInt != null) { "Version $topicVersionStr cannot be cast to Int for topic $name" }
            return topicVersionInt
        }

        /**
         * Load and merge the properties files having the same name
         * but different classpath
         */
        private fun loadMultiple(name: String): Properties =
            Properties().apply {
                val resource = name.plus(".properties")
                ClassLoader.getSystemResources(resource)?.let {
                    it.asIterator().forEach { url -> load(url.openStream()) }
                } ?: logger.error { "Could not find any .properties for $resource" }
            }
    }
}
