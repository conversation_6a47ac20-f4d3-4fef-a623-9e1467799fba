package com.hellofresh.cif.distributionCenterLib

import InfraPreparation.getDataSourceConfig
import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.distribution_center_lib.schema.Tables
import com.hellofresh.cif.distribution_center_lib.schema.tables.records.DcConfigRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.unmockkAll
import java.time.DayOfWeek
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.BeforeEach

class DcConfigServiceFunctionalTest {

    @Test fun smokeTest() {
        val expected = (0..3).map {
            DcConfigRecord().apply {
                dcCode = UUID.randomUUID().toString()
                cleardown = MONDAY.name
                productionStart = FRIDAY.name
                market = UUID.randomUUID().toString()
                zoneId = ZoneOffset.UTC.id
                hasCleardown = true
                enabled = true
                createdAt = LocalDateTime.now(ZoneOffset.UTC)
                updatedAt = LocalDateTime.now(ZoneOffset.UTC)
                recordTimestamp_ = LocalDateTime.now(ZoneOffset.UTC)
                poCutoffTime = LocalTime.now(ZoneOffset.UTC).withNano(0)
            }
        }
        dsl.batchInsert(expected).execute()
        assertEquals(
            expected.associateBy { it.dcCode }
                .mapValues {
                    with(it.value) {
                        DistributionCenterConfiguration(
                            dcCode,
                            DayOfWeek.valueOf(productionStart),
                            DayOfWeek.valueOf(cleardown),
                            market,
                            ZoneId.of(zoneId),
                            enabled,
                            hasCleardown,
                            wmsType = WmsSystem.UNRECOGNIZED,
                            poCutoffTime,
                            brands = emptyList(),
                        )
                    }
                },
            DcConfigService(SimpleMeterRegistry()).dcConfigurations,
        )
    }

    companion object {
        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
        lateinit var dsl: MetricsDSLContext
        private val dataSource = getMigratedDataSource()

        @BeforeAll
        @JvmStatic
        fun init() {
            with(getDataSourceConfig()) {
                System.setProperty("HF_INVENTORY_DB_HOST", hostName)
                System.setProperty("HF_INVENTORY_READONLY_DB_USERNAME", userName)
                System.setProperty("HF_INVENTORY_READONLY_DB_PASSWORD", password)
            }
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newSingleThreadExecutor())
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            DcRepositoryImpl(dsl)
        }

        @BeforeEach
        fun clear() {
            dsl.deleteFrom(Tables.DC_CONFIG).execute()
            unmockkAll()
        }
    }
}
