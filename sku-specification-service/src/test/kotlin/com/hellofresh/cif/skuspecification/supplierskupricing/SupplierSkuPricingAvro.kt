package com.hellofresh.cif.skuspecification.supplierskupricing

import com.hellofresh.cif.lib.kafka.avro.AvroBuilder
import com.hellofresh.cif.skuspecification.CskuDeserializerTest.Companion
import io.confluent.kafka.schemaregistry.avro.AvroSchema
import io.confluent.kafka.schemaregistry.client.MockSchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroSerializer
import java.util.UUID

object SupplierSkuPricingAvro {
    private val supplierSkuPricingAvroSchema =
        Companion::class.java.classLoader.getResourceAsStream("pricing.avsc")?.readAllBytes()?.let {
            String(it, Charsets.UTF_8)
        }
    private val avroSchema = AvroSchema(supplierSkuPricingAvroSchema)
    private val avroSerializer = KafkaAvroSerializer(MockSchemaRegistryClient()).apply {
        configure(mapOf("schema.registry.url" to "mock://localhost:0"), false)
    }
    val supplierSkuPricingSchemaRegistryClient = MockSchemaRegistryClient().apply {
        register("test-topic", avroSchema, 1, 1)
    }

    @Suppress("LongParameterList")
    fun avroRecordOf(
        id: UUID,
        supplierSkuId: UUID,
        leadTime: Int?,
        enabled: Boolean,
        market: String,
        startDate: String,
        endDate: String,
    ): ByteArray {
        val record = AvroBuilder(avroSchema.rawSchema()).apply {
            set("id", id.toString())
            set("supplier_sku_id", supplierSkuId.toString())
            set("lead_time", leadTime)
            set("enabled", enabled)
            set("market", market)
            set("start_date", startDate)
            set("end_date", endDate)
        }
            .setRest()
            .build()
        return avroSerializer.serialize("", record)
    }
}
