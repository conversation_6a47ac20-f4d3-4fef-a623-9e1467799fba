package com.hellofresh.cif.skuspecification.youfoodz

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.skuSpecification.schema.Tables.SKU_SPECIFICATION_YF
import com.hellofresh.cif.skuSpecification.schema.tables.records.SkuSpecificationYfRecord
import com.hellofresh.cif.skuspecification.model.SkuSpecification
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

class YfSkuSpecificationRepositoryImplTest {

    @AfterEach
    fun afterEach() {
        dsl.deleteFrom(SKU_SPECIFICATION_YF).execute()
    }

    @Test
    fun `youfoodz sku specification is persisted`() {
        val yfCulinarySku1 = SkuSpecification(
            id = UUID.randomUUID(),
            market = "MARKET",
            category = "cat",
            code = UUID.randomUUID().toString(),
            name = UUID.randomUUID().toString(),
            coolingType = "Cool",
            packaging = "PACK1",
            parentId = UUID.randomUUID(),
            acceptableCodeLife = 11, null, uom = SkuUOM.UOM_UNIT,
        )

        val yfCulinarySku2 = SkuSpecification(
            id = UUID.randomUUID(),
            market = "MARKET2",
            category = "PRO",
            code = UUID.randomUUID().toString(),
            name = UUID.randomUUID().toString(),
            coolingType = "AMB",
            packaging = "PACK2",
            parentId = null,
            acceptableCodeLife = 11,
            fumigationAllowed = null,
            uom = SkuUOM.UOM_GAL,
        )

        runBlocking {
            yfSkuSpecificationRepository.saveSkuSpecification(listOf(yfCulinarySku1, yfCulinarySku2))
        }

        val yfSkuSpecificationRecords = dsl.selectFrom(SKU_SPECIFICATION_YF).fetch()

        assertYfSkuSpecificationRecord(yfCulinarySku1, yfSkuSpecificationRecords.first { it.id == yfCulinarySku1.id })
        assertYfSkuSpecificationRecord(yfCulinarySku2, yfSkuSpecificationRecords.first { it.id == yfCulinarySku2.id })
    }

    @Test
    fun `youfoodz sku specification is upserted`() {
        val yfCulinarySku = SkuSpecification(
            id = UUID.randomUUID(),
            market = "MARKET",
            category = "cat",
            code = UUID.randomUUID().toString(),
            name = UUID.randomUUID().toString(),
            coolingType = "Cool",
            packaging = "PACK1",
            parentId = UUID.randomUUID(),
            acceptableCodeLife = 11, null, uom = SkuUOM.UOM_UNIT,
        )

        val upsertedYfCulinarySku = yfCulinarySku.copy(
            market = "MARKET2",
            category = "PRO",
            code = UUID.randomUUID().toString(),
            name = UUID.randomUUID().toString(),
            coolingType = "AMB",
            packaging = "PACK2",
            parentId = null,
            acceptableCodeLife = 11, uom = SkuUOM.UOM_GAL,
        )

        runBlocking {
            yfSkuSpecificationRepository.saveSkuSpecification(listOf(yfCulinarySku))
            yfSkuSpecificationRepository.saveSkuSpecification(listOf(upsertedYfCulinarySku))
        }

        val yfSkuSpecificationRecords = dsl.selectFrom(SKU_SPECIFICATION_YF).fetch()

        assertEquals(1, yfSkuSpecificationRecords.size)
        assertYfSkuSpecificationRecord(
            upsertedYfCulinarySku,
            yfSkuSpecificationRecords.first { it.id == yfCulinarySku.id },
        )
    }

    private fun assertYfSkuSpecificationRecord(skuSpecification: SkuSpecification, skuSpecificationYfRecord: SkuSpecificationYfRecord) {
        assertEquals(skuSpecification.id, skuSpecificationYfRecord.id)
        assertEquals(skuSpecification.name, skuSpecificationYfRecord.name)
        assertEquals(skuSpecification.code, skuSpecificationYfRecord.code)
        assertEquals(skuSpecification.market, skuSpecificationYfRecord.market)
        assertEquals(skuSpecification.category, skuSpecificationYfRecord.category)
        assertEquals(skuSpecification.packaging, skuSpecificationYfRecord.packaging)
        assertEquals(skuSpecification.coolingType, skuSpecificationYfRecord.coolingType)
        assertEquals(skuSpecification.parentId, skuSpecificationYfRecord.parentId)
        assertEquals(skuSpecification.acceptableCodeLife, skuSpecificationYfRecord.acceptableCodeLife)
        assertEquals(skuSpecification.uom.name, skuSpecificationYfRecord.uom.name)
    }

    companion object {

        private lateinit var dsl: MetricsDSLContext
        private lateinit var yfSkuSpecificationRepository: YfSkuSpecificationRepository

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            val dataSource = getMigratedDataSource()
            val dbConfiguration = DefaultConfiguration().apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
            }
            dsl = dbConfiguration.dsl().withMetrics(SimpleMeterRegistry())
            yfSkuSpecificationRepository = YfSkuSpecificationRepositoryImpl(dsl)
        }
    }
}
