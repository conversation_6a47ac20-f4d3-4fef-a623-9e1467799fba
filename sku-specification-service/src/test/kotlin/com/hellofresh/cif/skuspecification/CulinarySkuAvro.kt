package com.hellofresh.cif.skuspecification

import com.hellofresh.cif.lib.kafka.avro.AvroBuilder
import com.hellofresh.cif.skuspecification.CskuDeserializerTest.Companion
import com.hellofresh.sku.models.SkuSpecification
import io.confluent.kafka.schemaregistry.avro.AvroSchema
import io.confluent.kafka.schemaregistry.client.MockSchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroSerializer
import java.util.UUID

object CulinarySkuAvro {
    private val cskuAvroSchema = Companion::class.java.classLoader.getResourceAsStream(
        "culinarysku.avsc",
    )?.readAllBytes()?.let {
        String(it, Charsets.UTF_8)
    }
    private val avroSchema = AvroSchema(cskuAvroSchema)
    private val avroSerializer = KafkaAvroSerializer(MockSchemaRegistryClient()).apply {
        configure(mapOf("schema.registry.url" to "mock://localhost:0"), false)
    }
    val culinarySkuSchemaRegistryClient = MockSchemaRegistryClient().apply {
        register("test-topic", avroSchema, 1, 1)
    }

    const val defaultCategory = "PTN"
    const val defaultName = "Sku name"
    const val defaultPackagingType = "packaging"
    const val defaultCoolingType = "cooling"

    fun avroCskuOf(skuId: UUID, skuSpec: SkuSpecification) = avroCskuOf(
        id = skuId.toString(),
        market = skuSpec.market,
        code = skuSpec.skuCode,
        name = skuSpec.name,
        packagingType = skuSpec.packaging,
        coolingType = skuSpec.coolingType,
        category = skuSpec.category,
        parentId = skuSpec.parentId.toString(),
        acceptableCodeLife = skuSpec.acceptableCodeLife,
    )

    @Suppress("LongParameterList")
    fun avroCskuOf(
        id: String = "00000000-0000-0000-0000-000000000000",
        market: String = "TEST",
        code: String = "CODE",
        name: String = defaultName,
        packagingType: String = defaultPackagingType,
        coolingType: String = defaultCoolingType,
        category: String = defaultCategory,
        parentId: String? = "",
        acceptableCodeLife: Int = 0,
        uom: String? = null,
        fumigation: Boolean? = null,
        brands: List<String> = emptyList(),
    ): ByteArray {
        val record = AvroBuilder(avroSchema.rawSchema()).apply {
            set("event_type", "entity_created")
            set("id", id)
            set("market", market)
            set("code", code)
            set("name", name)
            set("category", category)
            set("brands", brands)
            set("packaging") {
                set("type", packagingType)
            }
            set("cooling_type") {
                set("name", coolingType)
            }
            set("code_check_req", false)
            if (parentId != null) {
                set("third_pw_parent_id", parentId)
            }
            set("acceptable_code_life", acceptableCodeLife)
            if (uom != null) {
                set("extras", """{"extras":"{\"wms_uom\": \"$uom\"}"}""")
            }
            if (fumigation != null) {
                set("extras", """{"extras":"{\"fumigated\": $fumigation}"}""")
            }
        }
            .setRest()
            .build()
        return avroSerializer.serialize("", record)
    }
}
