package com.hellofresh.cif.skuspecification.suppliersku

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.skuSpecification.schema.tables.SupplierSku.SUPPLIER_SKU
import com.hellofresh.cif.skuSpecification.schema.tables.records.SupplierSkuRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

class SupplierSkuRepositoryImplTest {

    @AfterEach
    fun after() {
        dsl.deleteFrom(SUPPLIER_SKU).execute()
    }

    @Test
    fun `should store multiple supplier skus`() {
        // given
        val supplierSku1 = SupplierSku(UUID.randomUUID(), UUID.randomUUID(), "Active", 19)
        val supplierSku2 = SupplierSku(UUID.randomUUID(), UUID.randomUUID(), "Inactive", 22)

        // when
        runBlocking { supplierSkuRepo.saveSupplierSkus(setOf(supplierSku1, supplierSku2)) }

        // then
        val dbRecords = getAllSupplierSkus()
        assertEquals(2, dbRecords.size)
        assertEquals(supplierSku1.mlor, dbRecords.first { it.skuId == supplierSku1.skuId }.mlorDays)
        assertEquals(supplierSku1.status, dbRecords.first { it.skuId == supplierSku1.skuId }.status)
        assertEquals(supplierSku2.mlor, dbRecords.first { it.skuId == supplierSku2.skuId }.mlorDays)
        assertEquals(supplierSku2.status, dbRecords.first { it.skuId == supplierSku2.skuId }.status)
    }

    @Test
    fun `should update an existing supplier sku`() {
        // given
        val skuId = UUID.randomUUID()
        val supplierSkuId = UUID.randomUUID()
        persistSupplierSku(skuId, supplierSkuId, mlorDays = 19)

        // when
        val supplierSkuUpd = SupplierSku(skuId, supplierSkuId, mlor = 11, status = "Inactive")
        runBlocking { supplierSkuRepo.saveSupplierSkus(setOf(supplierSkuUpd)) }

        // then
        val dbRecords = getAllSupplierSkus()
        assertEquals(1, dbRecords.size)
        val dbRecord = dbRecords.first()
        assertEquals(supplierSkuUpd.mlor, dbRecord.mlorDays)
        assertEquals(supplierSkuUpd.status, dbRecord.status)
        assertTrue(dbRecord.updatedAt > dbRecord.createdAt)
    }

    @Test
    fun `should delete a supplier sku when a subsequent update has mlor=zero`() {
        val skuId = UUID.randomUUID()
        val supplierSkuId = UUID.randomUUID()
        persistSupplierSku(skuId, supplierSkuId, mlorDays = 9)

        // when
        val supplierSkuUpd = SupplierSku(skuId, supplierSkuId, mlor = 0, status = "Active")
        runBlocking { supplierSkuRepo.saveSupplierSkus(setOf(supplierSkuUpd)) }

        // then
        val dbRecords = getAllSupplierSkus()
        assertTrue(dbRecords.isEmpty())
    }

    @Test
    fun `should not save a supplierSku with mlor=0`() {
        val supplierSkuUpd = SupplierSku(UUID.randomUUID(), UUID.randomUUID(), mlor = 0, status = "Active")
        runBlocking { supplierSkuRepo.saveSupplierSkus(setOf(supplierSkuUpd)) }
    }

    @Test
    fun `should fetch the smallest active mlor for a supplier sku with multiple records`() {
        // given
        val skuId = UUID.randomUUID()
        val supplierSkuSmallestActiveMlor = 10
        persistSupplierSku(skuId, mlorDays = supplierSkuSmallestActiveMlor + 1)
        persistSupplierSku(skuId, mlorDays = supplierSkuSmallestActiveMlor)
        persistSupplierSku(skuId, mlorDays = supplierSkuSmallestActiveMlor - 1, status = "Inactive")

        // when
        val mlor = runBlocking { supplierSkuRepo.getMlorDays(skuId) }

        // then
        assertEquals(supplierSkuSmallestActiveMlor, mlor)
    }

    @Test
    fun `should return a null mlor for a supplier sku not having Active status`() {
        // given
        val skuId = UUID.randomUUID()
        SupplierSku(skuId, UUID.randomUUID(), "Inactive", 9)

        // when
        val mlor = runBlocking { supplierSkuRepo.getMlorDays(skuId) }

        // then
        assertNull(mlor)
    }

    private fun persistSupplierSku(
        skuId: UUID,
        supplierSkuId: UUID = UUID.randomUUID(),
        mlorDays: Int,
        status: String = "Active"
    ) {
        dsl.batchInsert(
            SupplierSkuRecord().apply {
                this.supplierSkuId = supplierSkuId
                this.skuId = skuId
                this.mlorDays = mlorDays
                this.status = status
            },
        ).execute()
    }

    private fun getAllSupplierSkus() = dsl.selectFrom(SUPPLIER_SKU).fetch()

    companion object {
        private val dataSource = getMigratedDataSource()
        private lateinit var dsl: MetricsDSLContext
        private lateinit var supplierSkuRepo: SupplierSkuRepository

        @BeforeAll
        @JvmStatic
        fun init() {
            dsl = DSL.using(
                DefaultConfiguration()
                    .apply {
                        setSQLDialect(SQLDialect.POSTGRES)
                        setDataSource(dataSource)
                    },
            ).withMetrics(SimpleMeterRegistry())
            supplierSkuRepo = SupplierSkuRepositoryImpl(dsl)
        }
    }
}
