package com.hellofresh.cif.skuspecification.supplierskupricing

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.skuSpecification.schema.tables.SupplierSkuPricing.SUPPLIER_SKU_PRICING
import com.hellofresh.cif.skuSpecification.schema.tables.records.SupplierSkuPricingRecord
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

class SupplierSkuPricingRepositoryImplTest {
    private val today = LocalDate.now()

    @AfterEach
    fun afterEach() {
        dsl.deleteFrom(SUPPLIER_SKU_PRICING).execute()
    }

    @Test
    fun `should store multiple supplier sku pricing`() {
        // given
        val supplierSkuPricing1 =
            SupplierSkuPricing(UUID.randomUUID(), UUID.randomUUID(), 9, true, "it", today, today.plusMonths(10))
        val supplierSkuPricing2 =
            SupplierSkuPricing(UUID.randomUUID(), UUID.randomUUID(), 10, true, "it", today, today.plusMonths(10))

        // when
        runBlocking { supplierSkuPricingRepo.saveSupplierSkuPricing(setOf(supplierSkuPricing1, supplierSkuPricing2)) }

        // then
        val dbRecords = getAllSupplierSkuPricing()
        assertEquals(2, dbRecords.size)
        assertEquals(supplierSkuPricing1.leadTime, dbRecords.first { it.id == supplierSkuPricing1.id }.leadTime)
        assertEquals(supplierSkuPricing1.enabled, dbRecords.first { it.id == supplierSkuPricing1.id }.enabled)
        assertEquals(supplierSkuPricing2.leadTime, dbRecords.first { it.id == supplierSkuPricing2.id }.leadTime)
        assertEquals(supplierSkuPricing2.enabled, dbRecords.first { it.id == supplierSkuPricing2.id }.enabled)
        assertEquals(supplierSkuPricing2.startDate, dbRecords.first { it.id == supplierSkuPricing2.id }.startDate)
        assertEquals(supplierSkuPricing2.endDate, dbRecords.first { it.id == supplierSkuPricing2.id }.endDate)
    }

    @Test
    fun `should update an existing supplier sku pricing`() {
        // given
        val id = UUID.randomUUID()
        val supplierSkuId = UUID.randomUUID()
        persistSupplierSku(id, supplierSkuId, leadTime = 10)

        // when
        val supplierSkuPricingUpd =
            SupplierSkuPricing(
                id,
                supplierSkuId,
                leadTime = 11,
                enabled = true,
                market = "ca",
                today,
                today.plusMonths(10),
            )
        runBlocking { supplierSkuPricingRepo.saveSupplierSkuPricing(setOf(supplierSkuPricingUpd)) }

        // then
        val dbRecords = getAllSupplierSkuPricing()
        assertEquals(1, dbRecords.size)
        val dbRecord = dbRecords.first()
        assertEquals(supplierSkuPricingUpd.leadTime, dbRecord.leadTime)
        assertEquals(supplierSkuPricingUpd.enabled, dbRecord.enabled)
        assertEquals(supplierSkuPricingUpd.market, dbRecord.market)
        assertTrue(dbRecord.updatedAt > dbRecord.createdAt)
    }

    @Test
    fun `should not save a supplierSkuPricing , if the supplier sku pricing is disabled`() {
        val supplierSkuPricing = SupplierSkuPricing(
            UUID.randomUUID(),
            UUID.randomUUID(),
            leadTime = 0,
            enabled = false,
            "ca",
            today,
            today.plusMonths(10),
        )
        runBlocking { supplierSkuPricingRepo.saveSupplierSkuPricing(setOf(supplierSkuPricing)) }
        val dbRecords = getAllSupplierSkuPricing()
        assertEquals(0, dbRecords.size)
    }

    private fun persistSupplierSku(
        id: UUID,
        supplierSkuId: UUID = UUID.randomUUID(),
        leadTime: Int = 0,
        enabled: Boolean = false,
        market: String = ""
    ) {
        dsl.batchInsert(
            SupplierSkuPricingRecord().apply {
                this.id = id
                this.supplierSkuId = supplierSkuId
                this.leadTime = leadTime
                this.enabled = enabled
                this.market = market
                this.startDate = today
                this.endDate = today.plusMonths(10)
            },
        ).execute()
    }

    private fun getAllSupplierSkuPricing() = dsl.selectFrom(SUPPLIER_SKU_PRICING).fetch()

    companion object {
        private val dataSource = getMigratedDataSource()
        private lateinit var dsl: MetricsDSLContext
        private lateinit var supplierSkuPricingRepo: SupplierSkuPricingRepository

        @BeforeAll
        @JvmStatic
        fun init() {
            dsl = DSL.using(
                DefaultConfiguration()
                    .apply {
                        setSQLDialect(SQLDialect.POSTGRES)
                        setDataSource(dataSource)
                    },
            ).withMetrics(SimpleMeterRegistry())
            supplierSkuPricingRepo = SupplierSkuPricingRepositoryImpl(dsl)
        }
    }
}
