package com.hellofresh.cif.skuspecification.suppliersku

import InfraPreparation.createKafkaProducer
import InfraPreparation.getMigratedDataSource
import InfraPreparation.startKafkaAndCreateTopics
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.skuSpecification.schema.tables.SupplierSku.SUPPLIER_SKU
import com.hellofresh.cif.skuSpecification.schema.tables.records.SupplierSkuRecord
import com.hellofresh.cif.skuspecification.suppliersku.SupplierSkuAvro.avroRecordOf
import com.hellofresh.cif.skuspecification.suppliersku.SupplierSkuAvro.supplierSkuSchemaRegistryClient
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Duration
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.ByteArraySerializer
import org.apache.kafka.common.serialization.Serdes
import org.awaitility.Awaitility
import org.jooq.Result
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.testcontainers.containers.KafkaContainer

class SupplierSkuIntegrationTest {

    @AfterEach
    fun afterEach() {
        dsl.deleteFrom(SUPPLIER_SKU).execute()
    }

    @Test
    fun `should consume from SupplierSku topic and store in the DB`() {
        // given
        val skuId = UUID.randomUUID()
        val supplierSkuRecordValueBytes = avroRecordOf(skuId, UUID.randomUUID(), "Active", 42)
        val record: ProducerRecord<String, ByteArray> =
            ProducerRecord(supplierSkuTopic, skuId.toString(), supplierSkuRecordValueBytes)

        // when
        supplierSkuTopicProducer.send(record)

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                supplierSkuApp.runApp()
            }

            lateinit var supplierSkuRecords: Result<SupplierSkuRecord>
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    supplierSkuRecords = dsl.selectFrom(SUPPLIER_SKU).where(SUPPLIER_SKU.SKU_ID.eq(skuId)).fetch()
                    supplierSkuRecords.isNotEmpty
                }.let {
                    assertEquals(42, supplierSkuRecords.first().mlorDays)
                }
            app.cancel()
        }
    }

    companion object {
        private lateinit var supplierSkuTopicProducer: KafkaProducer<String, ByteArray>
        private lateinit var consumerConfig: Map<String, String>
        private lateinit var supplierSkuApp: SupplierSkuApp

        private lateinit var kafka: KafkaContainer
        private lateinit var dsl: MetricsDSLContext
        private lateinit var supplierSkuRepository: SupplierSkuRepository
        private const val supplierSkuTopic = "public.planning.suppliersku.nutrition.v1"

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            kafka = startKafkaAndCreateTopics(
                listOf(supplierSkuTopic),
            )

            supplierSkuTopicProducer = createKafkaProducer(
                kafka, Serdes.String().serializer(),
                ByteArraySerializer(),
            )
            consumerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                    ConsumerConfig.GROUP_ID_CONFIG to "csku-inventory-forecast.sku-specification-service-test-integration.v1",
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
                )
            val dataSource = getMigratedDataSource()
            val dbConfiguration = DefaultConfiguration().apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
            }
            dsl = dbConfiguration.dsl().withMetrics(SimpleMeterRegistry())
            supplierSkuRepository = SupplierSkuRepositoryImpl(dsl)
            val pollConfig = PollConfig(
                Duration.ofSeconds(1).toKotlinDuration(),
                100,
                Duration.ofSeconds(15).toKotlinDuration(),
            )
            supplierSkuApp = SupplierSkuApp(
                SimpleMeterRegistry(),
                dsl,
                pollConfig = pollConfig,
                consumerConfig = consumerConfig,
                schemaRegistryClient = supplierSkuSchemaRegistryClient,
            )
        }
    }
}
