package com.hellofresh.cif.skuspecification

import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.skuspecification.youfoodz.YfSkuSpecificationRepositoryImpl
import com.hellofresh.proto.stream.ye.planning.yfCulinarySku.v1.YfCulinarySku
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.util.UUID
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.clients.producer.internals.FutureRecordMetadata
import org.apache.kafka.common.TopicPartition
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue

class YfSkuSpecificationServiceTest {
    private val kafkaProducerMock: KafkaProducer<String, YfCulinarySku> = mockk(relaxed = true)
    private val yfSkuSpecificationRepository: YfSkuSpecificationRepositoryImpl = mockk(relaxed = true)
    private val yfSkuSpecificationService = YfSkuSpecificationService(yfSkuSpecificationRepository)

    @Test
    fun `should save a sku specification record`() {
        // given
        val skuId = UUID.randomUUID()
        val records = ConsumerRecords(
            mapOf(
                TopicPartition("test", 0) to
                    listOf(
                        ConsumerRecord(
                            "test", 0, 0,
                            skuId.toString(),
                            YfCulinarySku.newBuilder()
                                .setId(skuId.toString())
                                .setMarket("market")
                                .setCategory("cat")
                                .setCode("cod")
                                .setName("nam")
                                .setAcceptableCodeLife(0)
                                .setBaseUom(YfCulinarySku.UOM.UOM_UNIT)
                                .build(),
                        ),
                    ),
            ),
        )

        val slot = slot<ProducerRecord<String, YfCulinarySku>>()
        every {
            kafkaProducerMock.send(capture(slot))
        } returns mockk<FutureRecordMetadata>()

        // when
        runBlocking {
            yfSkuSpecificationService.save(records)
            // then
            verify(exactly = 1) {
                runBlocking {
                    yfSkuSpecificationRepository.saveSkuSpecification(
                        withArg { savedList ->
                            assertTrue(savedList.size == 1)
                            val savedSku = savedList[0]
                            assertEquals(YOUFOODZ_COOLING_TYPE, savedSku.coolingType)
                            assertEquals("nam", savedSku.name)
                            assertEquals(YOUFOODZ_PACKAGING, savedSku.packaging)
                            assertEquals("cod", savedSku.code)
                            assertEquals("cat", savedSku.category)
                            assertEquals(null, savedSku.parentId)
                            assertEquals(0, savedSku.acceptableCodeLife)
                            assertEquals(UOM_UNIT, savedSku.uom)
                            assertEquals("market", savedSku.market)
                        },
                    )
                }
            }
        }
    }
}
