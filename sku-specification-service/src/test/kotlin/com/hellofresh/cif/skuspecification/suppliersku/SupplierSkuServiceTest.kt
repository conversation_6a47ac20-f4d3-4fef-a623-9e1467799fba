package com.hellofresh.cif.skuspecification.suppliersku

import io.mockk.coVerify
import io.mockk.mockk
import java.util.UUID
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.common.TopicPartition

class SupplierSkuServiceTest {
    private val supplierSkuRepositoryMock: SupplierSkuRepository = mockk(relaxed = true)
    private val supplierSkuService = SupplierSkuService(supplierSkuRepositoryMock)

    @Test
    fun `should save non null SupplierSku records`() {
        // given
        val skuId = UUID.randomUUID()
        val mlorDays = 9
        val supplierSku = SupplierSku(skuId, UUID.randomUUID(), "Active", mlorDays)

        // when
        runBlocking {
            supplierSkuService.saveSupplierSkus(
                ConsumerRecords(
                    mapOf(
                        TopicPartition("test", 0) to
                            listOf(ConsumerRecord("test", 0, 0, UUID.randomUUID().toString(), supplierSku))
                    )
                )
            )
        }

        // then
        coVerify { supplierSkuRepositoryMock.saveSupplierSkus(setOf(supplierSku)) }
    }

    @Test
    fun `should skip records with null value`() {
        // when
        runBlocking {
            supplierSkuService.saveSupplierSkus(
                ConsumerRecords(
                    mapOf(
                        TopicPartition("test", 0) to
                            listOf(ConsumerRecord("test", 0, 0, UUID.randomUUID().toString(), null))
                    )
                )
            )
        }

        // then
        coVerify(exactly = 0) { supplierSkuRepositoryMock.saveSupplierSkus(any()) }
    }
}
