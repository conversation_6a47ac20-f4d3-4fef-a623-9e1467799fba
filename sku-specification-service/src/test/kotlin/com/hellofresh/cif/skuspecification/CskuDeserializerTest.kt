package com.hellofresh.cif.skuspecification

import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.skuspecification.CulinarySkuAvro.avroCskuOf
import com.hellofresh.cif.skuspecification.CulinarySkuAvro.defaultCategory
import com.hellofresh.cif.skuspecification.CulinarySkuAvro.defaultCoolingType
import com.hellofresh.cif.skuspecification.CulinarySkuAvro.defaultName
import com.hellofresh.cif.skuspecification.CulinarySkuAvro.defaultPackagingType
import com.hellofresh.cif.skuspecification.model.SkuSpecification
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFails
import org.junit.jupiter.api.assertDoesNotThrow

internal class CskuDeserializerTest {
    private val skuDeserializer = SkuDeserializer(CulinarySkuAvro.culinarySkuSchemaRegistryClient)

    @Test
    fun `exception is thrown if data is null`() {
        assertFails {
            skuDeserializer.deserialize("test", null)
        }
    }

    @Test
    fun `exception is thrown if id is invalid`() {
        assertFails {
            skuDeserializer.deserialize("test", avroCskuOf(id = "invalid"))
        }
    }

    @Test
    fun `exception is not thrown if market is invalid`() {
        assertDoesNotThrow {
            skuDeserializer.deserialize("test", avroCskuOf(market = " \t "))
        }
    }

    @Test
    fun `exception is thrown if name is invalid`() {
        assertFails {
            skuDeserializer.deserialize("test", avroCskuOf(name = ""))
        }
    }

    @Test
    fun `deserialization succeeds if third_pw_parent_id is empty`() {
        val cskuId = UUID.fromString("00000000-0000-0000-0000-000000000000")
        val expected =
            SkuSpecification(cskuId, "DACH", defaultCategory, "CODE", defaultName, defaultCoolingType, defaultPackagingType, null, 0, null, SkuUOM.UOM_UNIT)
        val actual = skuDeserializer.deserialize(
            "test",
            avroCskuOf("00000000-0000-0000-0000-000000000000", "DACH", "CODE", parentId = ""),
        )

        assertEquals(expected, actual)
    }

    @Test
    fun `deserialization succeeds if third_pw_parent_id is missing`() {
        val cskuId = UUID.fromString("00000000-0000-0000-0000-000000000000")
        val expected =
            SkuSpecification(cskuId, "DACH", defaultCategory, "CODE", defaultName, defaultCoolingType, defaultPackagingType, null, 0, null, SkuUOM.UOM_UNIT)
        val actual = skuDeserializer.deserialize(
            "test",
            avroCskuOf("00000000-0000-0000-0000-000000000000", "DACH", "CODE", parentId = null),
        )

        assertEquals(expected, actual)
    }

    @Test
    fun `deserialization succeeds when fumigated is available in extras`() {
        val cskuId = UUID.fromString("00000000-0000-0000-0000-000000000000")
        val expected =
            SkuSpecification(cskuId, "DACH", defaultCategory, "CODE", defaultName, defaultCoolingType, defaultPackagingType, null, 0, true, SkuUOM.UOM_UNIT)
        val actual = skuDeserializer.deserialize(
            "test",
            avroCskuOf("00000000-0000-0000-0000-000000000000", "DACH", "CODE", parentId = null, fumigation = true),
        )

        assertEquals(expected, actual)
    }

    @Test
    fun `deserialization succeeds if uom is present`() {
        val cskuId = UUID.fromString("00000000-0000-0000-0000-000000000000")
        val expected =
            SkuSpecification(cskuId, "DACH", defaultCategory, "CODE", defaultName, defaultCoolingType, defaultPackagingType, null, 0, null, SkuUOM.UOM_LBS)
        val actual = skuDeserializer.deserialize(
            "test",
            avroCskuOf("00000000-0000-0000-0000-000000000000", "DACH", "CODE", parentId = "", uom = "lbs"),
        )

        assertEquals(expected, actual)
    }

    @Test
    fun `deserialization succeeds if data ischemas valid`() {
        val cskuId = UUID.fromString("00000000-0000-0000-0000-000000000000")
        val parentId = UUID.randomUUID()
        val expected =
            SkuSpecification(
                cskuId,
                "DACH",
                defaultCategory,
                "CODE",
                defaultName,
                defaultCoolingType,
                defaultPackagingType,
                parentId,
                0,
                null,
                SkuUOM.UOM_UNIT,
                listOf("Brand1", "Brand2"),
            )
        val actual = skuDeserializer.deserialize(
            "test",
            avroCskuOf(
                "00000000-0000-0000-0000-000000000000",
                "DACH",
                "CODE",
                parentId = parentId.toString(),
                brands = listOf("Brand1", "Brand2")
            ),
        )

        assertEquals(expected, actual)
    }

    companion object
}
