package com.hellofresh.cif.skuspecification

import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.sku.models.SkuSpecification
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.clients.producer.internals.FutureRecordMetadata
import org.apache.kafka.common.TopicPartition

class SkuSpecificationServiceTest {
    private val kafkaProducerMock: KafkaProducer<String, SkuSpecification> = mockk(relaxed = true)
    private val skuSpecificationService = SkuSpecificationService(kafkaProducerMock)

    @Test
    fun `should publish a sku specification record`() {
        // given
        val skuId = UUID.randomUUID()
        val records = ConsumerRecords(
            mapOf(
                TopicPartition("test", 0) to
                    listOf(
                        ConsumerRecord(
                            "test", 0, 0,
                            skuId.toString(),
                            com.hellofresh.cif.skuspecification.model.SkuSpecification(
                                skuId, "market", "cat", "cod", "nam", "ct",
                                "pkg", UUID.randomUUID(), 0, null, UOM_UNIT,
                            ),
                        ),
                    ),
            ),
        )

        val slot = slot<ProducerRecord<String, SkuSpecification>>()
        every {
            kafkaProducerMock.send(capture(slot))
        } returns mockk<FutureRecordMetadata>()

        // when
        skuSpecificationService.publish(records)

        // then
        verify(exactly = 1) { kafkaProducerMock.send(any()) }
        val publishedRecord = slot.captured
        assertSkuSpecificationResult(skuId, publishedRecord, records)
    }

    @Test
    fun `should publish an sku specification record including safety stock values`() {
        // given
        val skuId = UUID.randomUUID()
        val records = ConsumerRecords(
            mapOf(
                TopicPartition("test", 0) to
                    listOf(
                        ConsumerRecord(
                            "test", 0, 0,
                            skuId.toString(),
                            com.hellofresh.cif.skuspecification.model.SkuSpecification(
                                skuId, "eu", "cat", "PRO-00-105596-1", "name", "ct",
                                "pkg", UUID.randomUUID(), 0, null, UOM_UNIT
                            ),
                        ),
                    ),
            ),
        )

        val slot = slot<ProducerRecord<String, SkuSpecification>>()
        every {
            kafkaProducerMock.send(capture(slot))
        } returns mockk<FutureRecordMetadata>()

        // when
        skuSpecificationService.publish(records)

        // then
        verify(exactly = 1) { kafkaProducerMock.send(any()) }
        val publishedRecord = slot.captured
        assertSkuSpecificationResult(skuId, publishedRecord, records)
    }

    private fun assertSkuSpecificationResult(
        skuId: UUID,
        publishedRecord: ProducerRecord<String, SkuSpecification>,
        records: ConsumerRecords<String, com.hellofresh.cif.skuspecification.model.SkuSpecification>
    ) {
        assertEquals(1, records.count())
        val data = records.first().value()
        assertEquals(skuId.toString(), publishedRecord.key())
        assertEquals(data.id.toString(), publishedRecord.key())
        assertEquals(data.market, publishedRecord.value().market)
        assertEquals(data.code, publishedRecord.value().skuCode)
        assertEquals(data.coolingType, publishedRecord.value().coolingType)
        assertEquals(data.category, publishedRecord.value().category)
        assertEquals(data.acceptableCodeLife, publishedRecord.value().acceptableCodeLife)
        assertEquals(data.name, publishedRecord.value().name)
        assertEquals(data.packaging, publishedRecord.value().packaging)
        assertEquals(data.parentId, publishedRecord.value().parentId)
    }
}
