package com.hellofresh.cif.skuspecification.supplierskupricing

import InfraPreparation.createKafkaProducer
import InfraPreparation.getMigratedDataSource
import InfraPreparation.startKafkaAndCreateTopics
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.skuSpecification.schema.tables.SupplierSkuPricing.SUPPLIER_SKU_PRICING
import com.hellofresh.cif.skuSpecification.schema.tables.records.SupplierSkuPricingRecord
import com.hellofresh.cif.skuspecification.supplierskupricing.SupplierSkuPricingAvro.avroRecordOf
import com.hellofresh.cif.skuspecification.supplierskupricing.SupplierSkuPricingAvro.supplierSkuPricingSchemaRegistryClient
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Duration
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.ByteArraySerializer
import org.apache.kafka.common.serialization.Serdes
import org.awaitility.Awaitility
import org.jooq.Result
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll
import org.testcontainers.containers.KafkaContainer

class SupplierSkuPricingIntegrationTest {

    @Test
    fun `should consume from SupplierSku pricing topic and store in the DB`() {
        // given
        val id = UUID.randomUUID()
        val supplierSkuPricingRecordValueBytes = avroRecordOf(
            id,
            UUID.randomUUID(),
            10,
            true,
            "ca",
            "2023-09-26T00:00:00Z",
            "2023-09-27T00:00:00Z",
        )
        val record: ProducerRecord<String, ByteArray> =
            ProducerRecord(supplierSkuPricingTopic, id.toString(), supplierSkuPricingRecordValueBytes)

        // when
        supplierSkuPricingTopicProducer.send(record)

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                supplierSkuPricingApp.runApp()
            }

            lateinit var supplierSkuPricingRecords: Result<SupplierSkuPricingRecord>
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    supplierSkuPricingRecords = dsl.selectFrom(SUPPLIER_SKU_PRICING)
                        .where(SUPPLIER_SKU_PRICING.ID.eq(id)).fetch()
                    supplierSkuPricingRecords.isNotEmpty
                }.let {
                    assertEquals(10, supplierSkuPricingRecords.first().leadTime)
                    assertEquals("2023-09-26", supplierSkuPricingRecords.first().startDate.toString())
                    assertEquals("2023-09-27", supplierSkuPricingRecords.first().endDate.toString())
                }
            app.cancel()
        }
    }

    @AfterEach
    fun afterEach() {
        dsl.deleteFrom(SUPPLIER_SKU_PRICING).execute()
    }

    companion object {
        private lateinit var supplierSkuPricingTopicProducer: KafkaProducer<String, ByteArray>
        private lateinit var consumerConfig: Map<String, String>
        private lateinit var supplierSkuPricingApp: SupplierSkuPricingApp

        private lateinit var kafka: KafkaContainer
        private lateinit var dsl: MetricsDSLContext
        private lateinit var supplierSkuPricingRepository: SupplierSkuPricingRepository
        private const val supplierSkuPricingTopic = "public.planning.suppliersku.pricing.v1"

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            kafka = startKafkaAndCreateTopics(
                listOf(supplierSkuPricingTopic),
            )

            supplierSkuPricingTopicProducer = createKafkaProducer(
                kafka, Serdes.String().serializer(),
                ByteArraySerializer(),
            )
            consumerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                    ConsumerConfig.GROUP_ID_CONFIG to "csku-inventory-forecast.sku-specification-service-test-integration.v1",
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
                )
            val dataSource = getMigratedDataSource()
            val dbConfiguration = DefaultConfiguration().apply {
                setSQLDialect(POSTGRES)
                setDataSource(dataSource)
            }
            dsl = dbConfiguration.dsl().withMetrics(SimpleMeterRegistry())
            supplierSkuPricingRepository = SupplierSkuPricingRepositoryImpl(dsl)
            val pollConfig = PollConfig(
                Duration.ofSeconds(1).toKotlinDuration(),
                100,
                Duration.ofSeconds(15).toKotlinDuration(),
            )
            supplierSkuPricingApp = SupplierSkuPricingApp(
                SimpleMeterRegistry(),
                dsl,
                pollConfig = pollConfig,
                consumerConfig = consumerConfig,
                schemaRegistryClient = supplierSkuPricingSchemaRegistryClient,
            )
        }
    }
}
