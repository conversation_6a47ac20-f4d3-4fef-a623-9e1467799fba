package com.hellofresh.cif.skuspecification.suppliersku

import com.hellofresh.cif.lib.kafka.avro.AvroBuilder
import com.hellofresh.cif.skuspecification.CskuDeserializerTest.Companion
import io.confluent.kafka.schemaregistry.avro.AvroSchema
import io.confluent.kafka.schemaregistry.client.MockSchemaRegistryClient
import io.confluent.kafka.serializers.KafkaAvroSerializer
import java.util.UUID

object SupplierSkuAvro {
    private val supplierSkuAvroSchema =
        Companion::class.java.classLoader.getResourceAsStream("nutrition.avsc")?.readAllBytes()?.let {
            String(it, Charsets.UTF_8)
        }
    private val avroSchema = AvroSchema(supplierSkuAvroSchema)
    private val avroSerializer = KafkaAvroSerializer(MockSchemaRegistryClient()).apply {
        configure(mapOf("schema.registry.url" to "mock://localhost:0"), false)
    }
    val supplierSkuSchemaRegistryClient = MockSchemaRegistryClient().apply {
        register("test-topic", avroSchema, 1, 1)
    }

    fun avroRecordOf(
        skuId: UUID,
        supplierSkuId: UUID,
        status: String,
        mlorDays: Int?
    ): ByteArray {
        val extras = mlorDays
            ?.let { SupplierSkuDeserializer.objectMapper.writeValueAsString(mapOf("min_shelf_life_on_deliver" to mlorDays)) }
            ?: "{}"
        val record = AvroBuilder(avroSchema.rawSchema()).apply {
            set("culinary_sku_id", skuId.toString())
            set("supplier_sku_id", supplierSkuId.toString())
            set(
                "extras",
                extras
            )
            set("supplier_sku_status", status)
        }
            .setRest()
            .build()
        return avroSerializer.serialize("", record)
    }
}
