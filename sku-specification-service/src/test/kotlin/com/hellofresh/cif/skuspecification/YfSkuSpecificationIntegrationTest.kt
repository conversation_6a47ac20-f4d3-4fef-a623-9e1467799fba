package com.hellofresh.cif.skuspecification

import InfraPreparation.createKafkaProducer
import InfraPreparation.getMigratedDataSource
import InfraPreparation.startKafkaAndCreateTopics
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.skuSpecification.schema.Tables.SKU_SPECIFICATION_YF
import com.hellofresh.proto.stream.ye.planning.yfCulinarySku.v1.YfCulinarySku
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Duration
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.ByteArraySerializer
import org.apache.kafka.common.serialization.Serdes
import org.awaitility.Awaitility
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.testcontainers.containers.KafkaContainer

class YfSkuSpecificationIntegrationTest {
    @Test
    fun `should consume from raw YF culinary topic persist messages in db`() {
        val skuId = UUID.randomUUID()
        val yfCulinarySku = YfCulinarySku.newBuilder()
            .setId(skuId.toString())
            .setMarket("MKT")
            .setCategory("cat")
            .setCode("SPI")
            .setName("name")
            .setAcceptableCodeLife(1)
            .setBaseUom(YfCulinarySku.UOM.UOM_UNIT)
            .build()

        val record: ProducerRecord<String, ByteArray> = ProducerRecord(
            yfCulinaryTopic,
            skuId.toString(),
            yfCulinarySku.toByteArray(),
        )
        culinarySkuTopicProducer.send(record)

        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }

            val dbRecord = dsl.selectFrom(SKU_SPECIFICATION_YF)
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    dbRecord.count() == 1
                }.let {
                    assertEquals(yfCulinarySku.code, dbRecord.first().code)
                    assertEquals(yfCulinarySku.name, dbRecord.first().name)
                    assertEquals(yfCulinarySku.category, dbRecord.first().category)
                    assertEquals(yfCulinarySku.market.lowercase(), dbRecord.first().market)
                    assertEquals(YOUFOODZ_COOLING_TYPE, dbRecord.first().coolingType)
                    assertEquals(YOUFOODZ_PACKAGING, dbRecord.first().packaging)
                    assertEquals(null, dbRecord.first().parentId)
                }

            app.cancel()
        }
    }

    companion object {
        private lateinit var culinarySkuTopicProducer: KafkaProducer<String, ByteArray>
        private lateinit var consumerConfig: SkuSpecificationConsumerConfig
        private lateinit var app: YfSkuSpecificationApp
        private val dataSource = getMigratedDataSource()
        private lateinit var dsl: MetricsDSLContext

        private lateinit var kafka: KafkaContainer
        private const val yfCulinaryTopic = "public.ye.planning.yf-culinary-sku.v1"

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            kafka = startKafkaAndCreateTopics(
                listOf(
                    yfCulinaryTopic,
                ),
            )

            culinarySkuTopicProducer = createKafkaProducer(
                kafka, Serdes.String().serializer(),
                ByteArraySerializer(),
            )
            dsl = DSL.using(
                DefaultConfiguration()
                    .apply {
                        setSQLDialect(SQLDialect.POSTGRES)
                        setDataSource(dataSource)
                        setExecutor(Executors.newSingleThreadExecutor())
                    },
            ).withMetrics(SimpleMeterRegistry())

            consumerConfig = mapOf(
                ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                ConsumerConfig.GROUP_ID_CONFIG to "csku-inventory-forecast.sku-specification-service-test-integration.v1",
                ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
            )
            val pollConfig = PollConfig(
                Duration.ofSeconds(1).toKotlinDuration(),
                100,
                Duration.ofSeconds(15).toKotlinDuration(),
            )
            app = YfSkuSpecificationApp(
                SimpleMeterRegistry(),
                consumerConfig = consumerConfig,
                dslContext = dsl,
                pollConfig = pollConfig,
            )
        }
    }
}
