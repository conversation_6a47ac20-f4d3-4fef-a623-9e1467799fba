package com.hellofresh.cif.skuspecification.supplierskuprincing

import com.hellofresh.cif.skuspecification.supplierskupricing.SupplierSkuPricingAvro
import com.hellofresh.cif.skuspecification.supplierskupricing.SupplierSkuPricingAvro.avroRecordOf
import com.hellofresh.cif.skuspecification.supplierskupricing.SupplierSkuPricingDeserializer
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull

class SupplierSkuPricingDeserializerTest {
    private val supplierSkuPricingDeserializer = SupplierSkuPricingDeserializer(
        SupplierSkuPricingAvro.supplierSkuPricingSchemaRegistryClient
    )
    private val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ssX")

    @Test
    fun `should deserialize supplier sku pricing avro value`() {
        // given
        val id = UUID.randomUUID()
        val supplierSkuId = UUID.randomUUID()
        val leadTime = 9
        val enabled = true
        val market = "ca"
        val startDate = "2023-09-28T00:00:00Z"
        val endDate = "2023-12-31T00:00:00Z"
        val supplierSkuPricingAvro = avroRecordOf(id, supplierSkuId, leadTime, enabled, market, startDate, endDate)

        // when
        val supplierSkuResult = supplierSkuPricingDeserializer.deserialize("test", supplierSkuPricingAvro)

        // then
        assertNotNull(supplierSkuResult)
        assertEquals(id, supplierSkuResult.id)
        assertEquals(supplierSkuId, supplierSkuResult.supplierSkuId)
        assertEquals(leadTime, supplierSkuResult.leadTime)
        assertEquals(enabled, supplierSkuResult.enabled)
        assertEquals(market, supplierSkuResult.market)
        assertEquals(LocalDate.parse(startDate, formatter), supplierSkuResult.startDate)
        assertEquals(LocalDate.parse(endDate, formatter), supplierSkuResult.endDate)
    }
}
