package com.hellofresh.cif.skuspecification

import InfraPreparation.createKafkaConsumer
import InfraPreparation.createKafkaProducer
import InfraPreparation.startKafkaAndCreateTopics
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.serde.JacksonDeserializer
import com.hellofresh.cif.skuspecification.CulinarySkuAvro.culinarySkuSchemaRegistryClient
import com.hellofresh.sku.models.SKU_SPECIFICATION_TOPIC
import com.hellofresh.sku.models.SkuSpecification
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.Duration
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.assertEquals
import kotlin.time.toKotlinDuration
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.runBlocking
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.clients.consumer.KafkaConsumer
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.kafka.common.serialization.ByteArraySerializer
import org.apache.kafka.common.serialization.Serdes
import org.apache.kafka.common.serialization.StringDeserializer
import org.awaitility.Awaitility
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test
import org.testcontainers.containers.KafkaContainer

class SkuSpecificationIntegrationTest {

    @Test
    fun `should consume from raw culinary topic and publish to sku specification topic`() {
        // given
        val skuId = UUID.randomUUID()
        val skuSpec = SkuSpecification("cool", "name", "pkg", "SPI-", "SPI", UUID.randomUUID(), 1, "mkt")
        val culinarySkuAvroBytes = CulinarySkuAvro.avroCskuOf(skuId, skuSpec)
        val record: ProducerRecord<String, ByteArray> = ProducerRecord(
            culinaryTopic,
            skuId.toString(),
            culinarySkuAvroBytes
        )

        // when
        culinarySkuTopicProducer.send(record)

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }

            var records = skuSpecTopicConsumer.poll(Duration.ofMillis(10))
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    records = skuSpecTopicConsumer.poll(Duration.ofMillis(10))
                    records.count() == 1
                }.let {
                    assertEquals(skuSpec, records.first().value())
                }

            app.cancel()
        }
    }

    @Test
    fun `should consume from raw culinary topic and publish to the sku specification to topic`() {
        // given
        val skuId = UUID.randomUUID()
        val skuCodeWithSafetyStockEnabled = "PRO-00-105596-1"
        val safetyStockEnabledMarket = "eu"
        val skuSpec = SkuSpecification(
            "cool",
            "name",
            "pkg",
            skuCodeWithSafetyStockEnabled,
            "SPI",
            UUID.randomUUID(),
            1,
            safetyStockEnabledMarket
        )
        val culinarySkuAvroBytes = CulinarySkuAvro.avroCskuOf(skuId, skuSpec)
        val record: ProducerRecord<String, ByteArray> = ProducerRecord(
            culinaryTopic,
            skuId.toString(),
            culinarySkuAvroBytes
        )

        // when
        culinarySkuTopicProducer.send(record)

        // then
        runBlocking {
            val app = launch(Executors.newSingleThreadExecutor().asCoroutineDispatcher()) {
                app.runApp()
            }

            var records = skuSpecTopicConsumer.poll(Duration.ofMillis(10))
            Awaitility
                .await()
                .atMost(Duration.ofSeconds(20))
                .until {
                    records = skuSpecTopicConsumer.poll(Duration.ofMillis(10))
                    records.count() == 1
                }.let {
                    assertEquals(skuSpec.coolingType, records.first().value().coolingType)
                    assertEquals(skuSpec.name, records.first().value().name)
                    assertEquals(skuSpec.packaging, records.first().value().packaging)
                    assertEquals(skuSpec.category, records.first().value().category)
                    assertEquals(skuSpec.acceptableCodeLife, records.first().value().acceptableCodeLife)
                    assertEquals(skuSpec.market, records.first().value().market)
                }

            app.cancel()
        }
    }

    companion object {
        private lateinit var culinarySkuTopicProducer: KafkaProducer<String, ByteArray>
        private lateinit var skuSpecTopicConsumer: KafkaConsumer<String, SkuSpecification>
        private lateinit var consumerConfig: SkuSpecificationConsumerConfig
        private lateinit var producerConfig: SkuSpecificationConsumerConfig
        private lateinit var app: SkuSpecificationApp

        private lateinit var kafka: KafkaContainer
        private const val culinaryTopic = "public.planning.culinarysku.v1"
        private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            kafka = startKafkaAndCreateTopics(
                listOf(SKU_SPECIFICATION_TOPIC, culinaryTopic),
            )

            culinarySkuTopicProducer = createKafkaProducer(
                kafka, Serdes.String().serializer(),
                ByteArraySerializer(),
            )
            skuSpecTopicConsumer = createKafkaConsumer(
                "random", SKU_SPECIFICATION_TOPIC,
                StringDeserializer(), JacksonDeserializer<SkuSpecification>(objectMapper),
            )
            consumerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                    ConsumerConfig.GROUP_ID_CONFIG to "csku-inventory-forecast.sku-specification-service-test-integration.v1",
                    ConsumerConfig.AUTO_OFFSET_RESET_CONFIG to "earliest",
                )
            producerConfig =
                mapOf(
                    ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG to kafka.bootstrapServers,
                )
            val pollConfig = PollConfig(
                Duration.ofSeconds(1).toKotlinDuration(),
                100,
                Duration.ofSeconds(15).toKotlinDuration(),
            )
            app = SkuSpecificationApp(
                SimpleMeterRegistry(),
                consumerConfig = consumerConfig,
                producerConfig = producerConfig,
                schemaRegistryClient = culinarySkuSchemaRegistryClient,
                pollConfig = pollConfig,
            )
        }
    }
}
