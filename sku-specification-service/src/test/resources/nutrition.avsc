{"name": "nutrition", "namespace": "com.hellofresh.planning.suppliersku.nutrition", "type": "record", "fields": [{"name": "event_type", "type": {"name": "event_types", "type": "enum", "symbols": ["entity_created", "entity_updated"]}}, {"name": "supplier_sku_id", "type": {"type": "string", "logicalType": "uuid"}, "doc": "Global unique identifier for supplierskus, common across all systems, and unique across all countries"}, {"name": "supplier_sku_status", "type": "string", "doc": "Status of the supplier SKU", "default": ""}, {"name": "culinary_sku_id", "type": {"type": "string", "logicalType": "uuid"}, "doc": "Global unique identifier for culinary skus, common across all systems, and unique across all countries", "default": ""}, {"name": "kilo_calories", "type": "double"}, {"name": "kilo_joules", "type": "double"}, {"name": "fats", "type": "double"}, {"name": "saturated_fats", "type": "double"}, {"name": "carbohydrates", "type": "double"}, {"name": "sugars", "type": "double"}, {"name": "proteins", "type": "double"}, {"name": "salt", "type": "double"}, {"name": "dietary_fiber", "type": "double"}, {"name": "cholesterol", "type": "double"}, {"name": "sodium", "type": "double"}, {"name": "potassium", "type": "double"}, {"name": "calcium", "type": "double"}, {"name": "iron", "type": "double"}, {"name": "trans_fats", "type": "double"}, {"name": "weight_nut_calc", "type": "double", "default": 0.0}, {"name": "traces_of_allergens", "type": {"type": "array", "items": {"name": "trace_of_allergen", "type": "record", "fields": [{"name": "name", "type": "string"}, {"name": "id", "type": "int"}, {"name": "type", "type": "string", "default": ""}]}}, "default": []}, {"name": "allergens", "type": {"type": "array", "items": {"name": "allergen", "type": "record", "fields": [{"name": "name", "type": "string"}, {"name": "id", "type": "int"}, {"name": "type", "type": "string", "default": ""}, {"name": "sub_allergens", "type": {"type": "array", "items": {"name": "sub_allergen", "type": "record", "fields": [{"name": "name", "type": "string"}, {"name": "id", "type": "int"}, {"name": "type", "type": "string", "default": ""}]}}, "default": []}]}}, "default": []}, {"name": "ingredient_statement", "type": "string", "default": ""}, {"name": "product_brand", "type": "string", "default": ""}, {"name": "country_of_origin", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "health_mark", "type": "string", "default": ""}, {"name": "animal_welfare", "type": "string", "default": ""}, {"name": "five_a_day", "type": "string"}, {"name": "dietary_preference", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "label_info", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "extras", "type": "string", "default": ""}, {"name": "created_at", "type": "string"}, {"name": "updated_at", "type": "string"}]}