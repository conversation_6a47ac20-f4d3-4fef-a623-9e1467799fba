{"name": "culinarysku", "namespace": "com.hellofresh.planning.culinarysku", "type": "record", "fields": [{"name": "event_type", "type": {"name": "event_types", "type": "enum", "symbols": ["entity_created", "entity_updated"]}}, {"name": "id", "type": "string", "logicalType": "uuid"}, {"name": "market", "type": "string"}, {"name": "name", "type": "string"}, {"name": "category", "type": "string"}, {"name": "subcategory", "type": "string"}, {"name": "locale", "type": "string"}, {"name": "code", "type": "string"}, {"name": "status", "type": "string"}, {"name": "brands", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "type", "type": "string"}, {"name": "code_check_req", "type": "boolean"}, {"name": "acceptable_code_life", "type": "int", "default": 0}, {"name": "crush_class", "type": "int", "default": 0}, {"name": "ingredient_id", "type": "string", "default": ""}, {"name": "recipe_card_unit", "type": "string", "default": "", "doc": "recipe card unit in english"}, {"name": "recipe_card_translation", "type": "string", "default": "", "doc": "recipe card translation in local language based on market"}, {"name": "recipe_card_quantity", "type": "float", "default": 0, "doc": "recipe card quantity"}, {"name": "quantity", "type": {"name": "quantity", "type": "record", "fields": [{"name": "amount", "type": "float"}, {"name": "unit", "type": "string"}]}}, {"name": "temperature", "type": {"name": "temperature", "type": "record", "fields": [{"name": "min", "type": "int"}, {"name": "max", "type": "int"}, {"name": "unit", "type": "string"}], "default": {}}}, {"name": "packaging", "type": {"name": "packaging", "type": "record", "fields": [{"name": "quantity", "type": "float"}, {"name": "type", "type": "string", "default": ""}, {"name": "unit", "type": "string"}, {"name": "size", "type": "float"}]}}, {"name": "cooling_type", "type": {"name": "cooling_type", "type": "record", "fields": [{"name": "name", "type": "string"}, {"name": "code", "type": "int"}]}}, {"name": "third_pw_parent_id", "type": "string", "default": "", "doc": "database id of the culinary SKU that is used for ordering from third party warehouse"}, {"name": "product_types", "type": {"type": "array", "items": "string"}, "default": []}, {"name": "extras", "type": "string", "default": ""}, {"name": "created_at", "type": "string"}, {"name": "updated_at", "type": "string"}, {"name": "created_by", "type": "string", "default": "", "doc": "email of the user that created the csku"}, {"name": "updated_by", "type": "string", "default": "", "doc": "email of the user that last updated the csku"}, {"name": "co2_emission_factor", "type": "float", "default": 0, "doc": "carbon emission factor (CO2e/kg)"}]}