package com.hellofresh.sku.models

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonValue
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.hellofresh.cif.models.SkuUOM
import java.util.UUID
import org.apache.kafka.common.serialization.Deserializer
import org.apache.kafka.common.serialization.Serde
import org.apache.kafka.common.serialization.Serializer

typealias SkuIdAndSpecification = Pair<UUID, SkuSpecification>

/**
 * [SkuId] = Stock Keeping Unit Identifier
 */
data class SkuId(@get:JsonValue val value: UUID) {
    companion object SkuIdSerde : Serde<SkuId> {
        override fun serializer() = Serializer<SkuId> { _, skuId ->
            skuId?.value?.toString()?.toByteArray(Charsets.UTF_8)
        }

        override fun deserializer() = Deserializer { _, bytes ->
            bytes?.let { SkuId(UUID.fromString(String(bytes, Charsets.UTF_8))) }
        }
    }
}

const val DEFAULT_MAX_DAYS_BEFORE_EXPIRY = 5L

/**
 * [SkuSpecification] holds relevant information about a SKU that can be mapped either
 * from an [SkuId].
 */
@JsonIgnoreProperties(ignoreUnknown = true)
@JsonNaming(SnakeCaseStrategy::class)
data class SkuSpecification(
    val coolingType: String,
    val name: String,
    val packaging: String,
    val skuCode: String,
    val category: String,
    val parentId: UUID? = null,
    val acceptableCodeLife: Int,
    val market: String,
    val fumigationAllowed: Boolean? = null,
    val uom: SkuUOM = SkuUOM.UOM_UNIT,
    val brands: List<String> = emptyList(),
) {
    companion object {
        fun daysBeforeExpiry(acceptableCodeLife: Int) = if (acceptableCodeLife <= 0) {
            DEFAULT_MAX_DAYS_BEFORE_EXPIRY
        } else {
            acceptableCodeLife.toLong()
        }
    }
}
