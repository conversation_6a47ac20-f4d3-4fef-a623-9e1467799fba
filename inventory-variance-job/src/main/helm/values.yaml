---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
tag: '@dockerTag@'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

cronJobs:
  cronjob:
    repository: '@dockerRepository@'
    pullPolicy: Always
    schedule: "0 * * * *"
    concurrencyPolicy: "Forbid"
    resources:
       requests:
          memory: '1Gi'
          cpu: '500m'
       limits:
          memory: '1Gi'
          cpu: '1'
    env:
       HF_INVENTORY_DB_HOST: 'vault:@tier@/key-value/data/inventory#DB_REPLICA_HOST'
       HF_INVENTORY_READONLY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#READONLY_DB_PASSWORD'
       HF_INVENTORY_READONLY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#READONLY_DB_USERNAME'
       HF_INVENTORY_DB_MASTER_HOST: 'vault:@tier@/key-value/data/inventory#DB_MASTER_HOST'
       HF_INVENTORY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#DB_PASSWORD'
       HF_INVENTORY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#DB_USERNAME'
       HF_STATSIG_SDK_KEY: 'vault:@tier@/key-value/data/misc#STATSIG_SDK_KEY'

configMap:
  HF_TIER: '@tier@'
  SLACK_ALERT_CHANNEL: '@slackAlertChannel@-@tier@'
  SLACK_WEBHOOK: 'vault:common/key-value/data/misc#SLACK_URL'


prometheus-pushgateway:
    fullnameOverride: "@applicationId@-prometheus-pushgateway"
    image:
        repository: docker.io/prom/pushgateway
    podLabels:
        app:  "@applicationId@-prometheus-pushgateway"
        environment: '@tier@'
        tribe: '@tribe@'
        squad: '@squad@'
    resources:
        limits:
            memory: 200Mi
            cpu: 100m
        requests:
            cpu: 100m
            memory: 200Mi
    service:
        type: ClusterIP
        port: 9091
        targetPort: 9091
    ingress:
        enabled: true
        className: edge-stack-private
        hosts:
            - "@applicationId@-prometheus-pushgateway.@<EMAIL>"
    serviceMonitor:
        enabled: true
        namespace: scm
        additionalLabels:
            prometheus: kube-prometheus
