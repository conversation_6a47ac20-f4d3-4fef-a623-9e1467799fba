package com.hellofresh.cif.inventoryvariance

import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryRepository
import com.hellofresh.cif.inventoryvariance.repository.CalculationData
import com.hellofresh.cif.inventoryvariance.repository.CalculationVarianceRepository
import com.hellofresh.cif.inventoryvariance.repository.InventoryVarianceRepository
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.SkuInventory
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import com.hellofresh.inventory.models.variance.DailyInventoryVarianceData
import com.hellofresh.inventory.models.variance.InventoryVariance
import com.hellofresh.sku.models.SkuSpecification
import java.math.BigDecimal
import java.math.RoundingMode.HALF_EVEN
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.temporal.ChronoUnit
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging

private const val PERCENTAGE = 100.0
private val HUNDRED = BigDecimal("100")

class InventoryVarianceService(
    private val dcConfigService: DcConfigService,
    private val inventoryRepository: InventoryRepository,
    private val calculationVarianceRepository: CalculationVarianceRepository,
    private val inventoryVarianceRepository: InventoryVarianceRepository,
    private val skuSpecificationService: SkuSpecificationService,
    statsigFeatureFlagClient: StatsigFeatureFlagClient,
) {

    private val usableInventoryEvaluator = UsableInventoryEvaluator(statsigFeatureFlagClient)

    // Run by default for the current week only
    internal suspend fun fillInventoryVariance(weeksInPast: Int = 0) {
        dcConfigService.dcConfigurations.values
            .filter { it.enabled }
            .forEach { distributionCenter ->
                repeat(weeksInPast + 1) { past ->
                    // Make it configurable
                    val referenceDate = LocalDate.now(distributionCenter.zoneId).minusWeeks(past.toLong())
                    val dcWeek = DcWeek(referenceDate, distributionCenter.productionStart)
                    logger.info("Calculating Inventory Variance for dc ${distributionCenter.dcCode} and week $dcWeek")
                    val inventoryVariances = calculateInventoryVariance(distributionCenter, dcWeek)
                    inventoryVarianceRepository.upsert(inventoryVariances)
                }
            }
    }

    internal suspend fun calculateInventoryVariance(
        distributionCenter: DistributionCenterConfiguration,
        dcWeek: DcWeek
    ): List<InventoryVariance> =
        distributionCenter.cleardown.let { cleardownDay ->
            withContext(Dispatchers.IO) {
                val dateRange = getDateRange(distributionCenter, dcWeek)
                val previousCleardownDate = getCleardownDate(dateRange, cleardownDay.minus(1))

                val deferredCalculationData = async {
                    calculationVarianceRepository.fetchSkuCalculationData(distributionCenter.dcCode, dateRange)
                }
                val deferredInventoryData = async {
                    inventoryRepository.fetchBy(setOf(distributionCenter.dcCode), dateRange)
                }

                deferredCalculationData.await()
                    .let { calculations ->
                        val skuInventoryDataMap = deferredInventoryData.await().flatMap { snapshot ->
                            snapshot.skus.map { skuInventory ->
                                SkuInventorySnapshot(
                                    skuInventory = skuInventory,
                                    inventorySnapshot = snapshot,
                                )
                            }
                        }.groupBy {
                            it.skuInventory.skuId
                        }
                        calculations.groupBy { it.skuId }
                            .mapNotNull { (skuId, value) ->
                                skuSpecificationService.specifications[skuId]?.let { skuSpec ->
                                    val (cleardownVariance, liveVariance) = calculateVariance(
                                        skuSpec,
                                        previousCleardownDate,
                                        value,
                                        skuInventoryDataMap[skuId],
                                    )

                                    InventoryVariance(
                                        skuId = skuId,
                                        dcCode = distributionCenter.dcCode,
                                        dcWeek = dcWeek.toString(),
                                        cleardownVariance = cleardownVariance,
                                        liveVariance = liveVariance,
                                        dailyInventoryVarianceData = toDailyInventoryVarianceData(
                                            skuSpec,
                                            value,
                                            skuInventoryDataMap[skuId],
                                        ),
                                    )
                                }
                            }
                    }
            }
        }

    private fun toDailyInventoryVarianceData(
        skuSpec: SkuSpecification,
        inventoryVarianceData: List<CalculationData>,
        inventorySnapshots: List<SkuInventorySnapshot>?
    ) =
        inventoryVarianceData.map {
            DailyInventoryVarianceData(
                date = it.date,
                inventoryQty = findUsableInventoryQuantity(skuSpec, it.date, inventorySnapshots),
                cleardownClosingStock = it.cleardownClosingStock,
                liveClosingStock = it.liveClosingStock,
            )
        }

    private fun calculateVariance(
        skuSpecification: SkuSpecification,
        previousCleardownDate: LocalDate,
        value: List<CalculationData>,
        inventorySnapshots: List<SkuInventorySnapshot>?
    ): Pair<SkuQuantity, SkuQuantity> {
        val stockVarianceReportData = value.find { stockVarianceReportData ->
            stockVarianceReportData.date == previousCleardownDate
        } ?: return SkuQuantity.ZERO to SkuQuantity.ZERO

        val cleardownInventoryQty = findUsableInventoryQuantity(
            skuSpecification,
            previousCleardownDate,
            inventorySnapshots,
        )

        return calculateVariancePercent(
            cleardownInventoryQty, stockVarianceReportData.cleardownClosingStock,
        ) to calculateVariancePercent(
            cleardownInventoryQty, stockVarianceReportData.liveClosingStock,
        )
    }

    private fun findUsableInventoryQuantity(
        skuSpecification: SkuSpecification,
        date: LocalDate,
        skuInventorySnapshots: List<SkuInventorySnapshot>?
    ): SkuQuantity? {
        val skuInventorySnapshot = skuInventorySnapshots?.find {
            it.inventorySnapshot.snapshotTime.toLocalDate() == date
        } ?: return null

        val usableInventory = skuInventorySnapshot.skuInventory.inventory.filter { i ->
            usableInventoryEvaluator.isUsable(
                dc = skuInventorySnapshot.inventorySnapshot.dcCode,
                date = date,
                inventory = i,
                acl = skuSpecification.acceptableCodeLife,
                skuCategory = skuSpecification.category,
            ).usable
        }
        val totalQuantity = usableInventory.sumOf { it.qty.getValue() }
        val skuId = skuInventorySnapshot.skuInventory.skuId
        return if (usableInventory.isEmpty()) {
            SkuQuantity.fromLong(0, skuSpecificationService.specifications[skuId]!!.uom)
        } else {
            SkuQuantity.fromBigDecimal(totalQuantity, usableInventory.first().qty.unitOfMeasure)
        }
    }

    private fun calculateVariancePercent(inventoryQty: SkuQuantity?, closingStock: SkuQuantity): SkuQuantity {
        val uom = closingStock.unitOfMeasure
        if (inventoryQty == null) {
            return SkuQuantity.fromBigDecimal(BigDecimal.ZERO, uom)
        }
        val inventoryQtyBigDecimal = inventoryQty.getValue()
        val closingStockBigDecimal = closingStock.getValue()
        val variance = toMaxHundredPercent(
            if (inventoryQtyBigDecimal.compareTo(BigDecimal.ZERO) != 0) {
                inventoryQtyBigDecimal.subtract(closingStockBigDecimal).divide(inventoryQtyBigDecimal, 2, HALF_EVEN)
                    .multiply(BigDecimal(PERCENTAGE))
                    .abs()
            } else {
                HUNDRED
            },
        )

        return SkuQuantity.fromBigDecimal(variance, uom)
    }

    private fun toMaxHundredPercent(variancePercent: BigDecimal) =
        if (variancePercent > HUNDRED) HUNDRED else variancePercent

    private fun getCleardownDate(dateRange: DateRange, dayOfWeek: DayOfWeek): LocalDate {
        val days = ChronoUnit.DAYS.between(dateRange.fromDate, dateRange.toDate)
        return (0..days).map { d ->
            dateRange.fromDate.plusDays(d)
        }.first {
            it.dayOfWeek == dayOfWeek
        }
    }

    private fun getDateRange(
        distributionCenter: DistributionCenterConfiguration,
        dcWeek: DcWeek
    ) = run {
        val productionStart = distributionCenter.productionStart
        val zoneId = distributionCenter.zoneId
        val startDate = dcWeek.getStartDateInDcWeek(productionStart, zoneId)
        val endDate = dcWeek.getLastDateInDcWeek(productionStart, zoneId)
        DateRange(startDate, endDate)
    }

    data class SkuInventorySnapshot(
        val skuInventory: SkuInventory,
        val inventorySnapshot: InventorySnapshot
    )

    companion object : Logging
}
