package com.hellofresh.cif.inventoryvariance.repository

import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.inventory_variance_job.schema.Tables.CALCULATION
import com.hellofresh.cif.inventory_variance_job.schema.Tables.LIVE_INVENTORY_CALCULATION
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_GAL
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_KG
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_LBS
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_LITRE
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_OZ
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_UNRECOGNIZED
import com.hellofresh.cif.inventory_variance_job.schema.enums.Uom.UOM_UNSPECIFIED
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.Record6
import org.jooq.impl.DSL

class CalculationVarianceRepository(
    private val metricsDSLContext: MetricsDSLContext,
) {

    suspend fun fetchSkuCalculationData(dcCode: String, dateRange: DateRange): List<CalculationData> =
        metricsDSLContext.withTagName("inventory-variance-live-data")
            .transactionResultAsync { contextConfig ->
                contextConfig.dsl().select(
                    CALCULATION.CSKU_ID,
                    CALCULATION.DATE,
                    CALCULATION.CLOSING_STOCK,
                    CALCULATION.UOM,
                    LIVE_INVENTORY_CALCULATION.CLOSING_STOCK,
                    CALCULATION.DEMANDED,
                )
                    .from(CALCULATION)
                    .join(LIVE_INVENTORY_CALCULATION).on(
                        CALCULATION.CSKU_ID.eq(LIVE_INVENTORY_CALCULATION.CSKU_ID)
                            .and(CALCULATION.DC_CODE.eq(LIVE_INVENTORY_CALCULATION.DC_CODE))
                            .and(CALCULATION.DATE.eq(LIVE_INVENTORY_CALCULATION.DATE)),
                    ).where(
                        CALCULATION.DC_CODE.eq(dcCode)
                            .and(CALCULATION.DATE.between(dateRange.fromDate, dateRange.toDate))
                            .and(CALCULATION.CSKU_ID.`in`(atLeastDemandExistsForOneDayInWeek(dcCode, dateRange))),
                    ).fetch()
                    .map { toCalculationData(it) }
            }.await()

    private fun atLeastDemandExistsForOneDayInWeek(dcCode: String, dateRange: DateRange) =
        DSL.selectDistinct(CALCULATION.CSKU_ID)
            .from(CALCULATION)
            .where(
                CALCULATION.DC_CODE.eq(dcCode)
                    .and(CALCULATION.DATE.between(dateRange.fromDate, dateRange.toDate))
                    .and(CALCULATION.DEMANDED.greaterThan(BigDecimal.ZERO)),
            )

    private fun toCalculationData(
        record: Record6<UUID, LocalDate, BigDecimal, Uom, BigDecimal, BigDecimal>
    ) = CalculationData(
        skuId = record[CALCULATION.CSKU_ID],
        date = record[CALCULATION.DATE],
        cleardownClosingStock = SkuQuantity.fromBigDecimal(
            record[CALCULATION.CLOSING_STOCK],
            record[CALCULATION.UOM].toSkuUOM(),
        ),
        liveClosingStock = SkuQuantity.fromBigDecimal(
            record[LIVE_INVENTORY_CALCULATION.CLOSING_STOCK],
            record[LIVE_INVENTORY_CALCULATION.UOM].toSkuUOM(),
        ),
    )
}

data class CalculationData(
    val skuId: UUID,
    val date: LocalDate,
    val cleardownClosingStock: SkuQuantity,
    val liveClosingStock: SkuQuantity
)

private fun Uom.toSkuUOM(): SkuUOM = when (this) {
    UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
    UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
    UOM_UNIT -> SkuUOM.UOM_UNIT
    UOM_KG -> SkuUOM.UOM_KG
    UOM_LBS -> SkuUOM.UOM_LBS
    UOM_GAL -> SkuUOM.UOM_GAL
    UOM_LITRE -> SkuUOM.UOM_LITRE
    UOM_OZ -> SkuUOM.UOM_OZ
}
