package com.hellofresh.cif.inventoryvariance

import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFactory
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventoryvariance.repository.CalculationVarianceRepository
import com.hellofresh.cif.inventoryvariance.repository.InventoryVarianceRepository
import com.hellofresh.cif.lib.MeteredJob
import com.hellofresh.cif.lib.metrics.HelloFreshMeterRegistry
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.shutdown.shutdownHook
import com.hellofresh.cif.skuSpecificationLib.SkuSpecificationService
import io.prometheus.client.exporter.PushGateway
import kotlin.system.exitProcess
import org.apache.logging.log4j.kotlin.logger

const val JOOQ_POOL_THREADS = 1

private val pushGatewayEndpoint = ConfigurationLoader.getStringOrFail("metrics.gateway")
private val pastWeeks = ConfigurationLoader.getIntegerOrDefault("PAST_WEEKS", 0)

val logger = logger("com.hellofresh.cif.inventoryvariance.main")
suspend fun main() {
    val meterRegistry = createMeterRegistry()

    val readOnlyDSLContext = DBConfiguration.jooqReadOnlyDslContext(JOOQ_POOL_THREADS, meterRegistry)
    val masterDSLContext = DBConfiguration.jooqMasterDslContext(JOOQ_POOL_THREADS, meterRegistry)

    val dcConfigService = DcConfigService(meterRegistry)
    val inventoryRepository = InventoryRepositoryImpl(readOnlyDSLContext)
    val calculationVarianceRepository = CalculationVarianceRepository(readOnlyDSLContext)
    val inventoryVarianceRepository = InventoryVarianceRepository(masterDSLContext)
    val statsigFeatureFlagClient = StatsigFactory.build(
        ::shutdownHook,
        sdkKey = ConfigurationLoader.getStringOrFail("HF_STATSIG_SDK_KEY"),
        userId = ConfigurationLoader.getStringOrFail("application.name"),
        isOffline = ConfigurationLoader.getStringIfPresent("statsig.offline")?.toBoolean() ?: false,
        hfTier = ConfigurationLoader.getStringOrFail("HF_TIER"),
    )

    val inventoryVarianceService = InventoryVarianceService(
        dcConfigService = dcConfigService,
        inventoryRepository = inventoryRepository,
        calculationVarianceRepository = calculationVarianceRepository,
        inventoryVarianceRepository = inventoryVarianceRepository,
        statsigFeatureFlagClient = statsigFeatureFlagClient,
        skuSpecificationService = SkuSpecificationService(meterRegistry),
    )

    MeteredJob(meterRegistry, "inventory job") { inventoryVarianceService.fillInventoryVariance(pastWeeks) }
        .execute()

    pushMetrics(meterRegistry)

    exitProcess(0)
}

private fun pushMetrics(meterRegistry: HelloFreshMeterRegistry) {
    runCatching {
        val prometheusPushGateway = PushGateway(pushGatewayEndpoint)
        prometheusPushGateway.pushAdd(meterRegistry.prometheusRegistry, ConfigurationLoader.getApplicationName())
    }.onFailure {
        logger.error("Problem pushing prometheus metrics", it)
    }
}
