plugins {
    id("com.hellofresh.cif.application-conventions")
}

group = "$group.${project.name}"
description = "Contains end to end tests for the mvp"
extra["deploy"] = false

dependencies {
    implementation(projects.dateUtilModels)
    implementation(projects.distributionCenterModels)
    implementation(projects.skuModels)
    implementation(projects.supplyQuantityRecommendationLib)
    implementation(libs.hellofresh.service)
    implementation(libs.hellofresh.schemaregistry) {
        exclude(group = "com.google.api.grpc", module = "proto-google-common-protos")
    }
    implementation(libs.protobuf.grpc)
    implementation(libs.hikaricp)
    implementation(libs.coroutines.core)
    implementation(libs.postgresql.driver)
    implementation(libs.awaitility)
    implementation(libs.ktor.client.cio)
    implementation(libs.ktor.server.auth.jwt)
    implementation(libs.kotlin.test)
}

val exec by tasks.registering(JavaExec::class) {
    classpath = sourceSets.main.get().runtimeClasspath
    mainClass.set("com.hellofresh.cif.endToEndTest.MainKt")
    allJvmArgs = project.property("gradle.javaExec.jvmFlags")
        .toString()
        .split("\\s+".toRegex())
    environment("HF_PROFILES", "e2e,kafka")
}
