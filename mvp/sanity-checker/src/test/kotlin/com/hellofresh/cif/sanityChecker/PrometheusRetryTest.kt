package com.hellofresh.cif.sanityChecker

import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.prometheus.client.Collector
import io.prometheus.client.Gauge
import io.prometheus.client.exporter.PushGateway
import java.io.IOException
import kotlin.test.Test

class PrometheusRetryTest {
    private val gaugeMock = mockk<Gauge>()
    private val pushGatewayMock = mockk<PushGateway>()
    private val metrics = PrometheusMetrics(gaugeMock, pushGatewayMock)

    @Test
    @Suppress("SwallowedException")
    fun `should retry if pushGateway is unavailable`() {
        every { pushGatewayMock.pushAdd(any<Collector>(), any(), any()) } throws IOException("unavailable")

        try {
            metrics.push("metric")
        } catch (e: IOException) {
            verify(
                exactly = MAX_PUSH_METRICS_SUBMIT_RETRIES
            ) { pushGatewayMock.pushAdd(any<Collector>(), any(), any()) }
        }
    }
}
