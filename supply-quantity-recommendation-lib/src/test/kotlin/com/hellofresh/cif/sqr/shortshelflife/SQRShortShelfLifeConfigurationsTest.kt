package com.hellofresh.cif.sqr.shortshelflife

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConfigurations.Companion.default
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class SQRShortShelfLifeConfigurationsTest {

    private val today = LocalDate.now(UTC)
    private val distributionCenterConfiguration = DistributionCenterConfiguration.Companion.default()
        .copy(
            productionStart = today.dayOfWeek,
            zoneId = UTC,
        )

    private val dcCode = distributionCenterConfiguration.dcCode

    @Test
    fun `returns matching configuration for sku dc and date`() {
        val skuId = UUID.randomUUID()

        val sqrSSlConfigurations = listOf(
            SQRShortShelfLifeConf(
                dcCode,
                skuId,
                today,
                ONE,
                TEN,
                false
            ),
            SQRShortShelfLifeConf(
                dcCode,
                skuId,
                today.plusWeeks(1),
                ONE + ONE,
                TEN + TEN,
                false
            ),
        ).associateBy { it.date }

        val configurations = SQRShortShelfLifeConfigurations(sqrSSlConfigurations.values.toList(), emptySet())

        assertEquals(sqrSSlConfigurations[today], configurations.getConfiguration(dcCode, skuId, today))
        assertEquals(
            sqrSSlConfigurations[today.plusWeeks(1)],
            configurations.getConfiguration(dcCode, skuId, today.plusWeeks(1)),
        )
    }

    @Test
    fun `returns default configuration for non existing sku`() {
        val today = LocalDate.now()
        val skuId = UUID.randomUUID()
        val sqrConfiguration =
            SQRShortShelfLifeConfigurations(
                emptyList(),
                emptySet(),
            ).getConfiguration(dcCode, skuId, today)

        assertEquals(default(dcCode, skuId, today), sqrConfiguration)
    }

    @Test
    fun `returns matching sqr configuration for sku and past day week`() {
        val skuId = UUID.randomUUID()
        val today = LocalDate.now()

        val sqrSSlConfigurations = listOf(
            SQRShortShelfLifeConf(
                dcCode,
                skuId,
                today.minusWeeks(1),
                ONE,
                TEN,
                false
            ),
            SQRShortShelfLifeConf(
                dcCode,
                skuId,
                today.plusWeeks(2),
                ONE + ONE,
                TEN + TEN,
                false
            ),
        ).associateBy { it.date }

        val configurations =
            SQRShortShelfLifeConfigurations(
                sqrSSlConfigurations.values.toList(),
                setOf(distributionCenterConfiguration)
            )

        assertEquals(
            sqrSSlConfigurations[today.minusWeeks(1)]!!,
            configurations.getConfiguration(dcCode, skuId, today),
        )
    }

    @Test
    fun `returns default configuration for current week without same day as requested`() {
        val skuId = UUID.randomUUID()
        val today = LocalDate.now()

        val sqrSSlConfigurations = listOf(
            SQRShortShelfLifeConf(
                dcCode,
                skuId,
                today.plusDays(1),
                ONE,
                TEN,
                false
            ),
        ).associateBy { it.date }

        val configurations =
            SQRShortShelfLifeConfigurations(
                sqrSSlConfigurations.values.toList(),
                setOf(distributionCenterConfiguration)
            )

        assertEquals(
            default(dcCode, skuId, today),
            configurations.getConfiguration(dcCode, skuId, today),
        )
    }

    @Test
    fun `returns default configuration for past week without same day as requested`() {
        val skuId = UUID.randomUUID()
        val today = LocalDate.now()

        val sqrSSlConfigurations = listOf(
            SQRShortShelfLifeConf(
                dcCode,
                skuId,
                today.plusDays(1).minusWeeks(1),
                ONE,
                TEN,
                false
            ),
        ).associateBy { it.date }

        val configurations =
            SQRShortShelfLifeConfigurations(
                sqrSSlConfigurations.values.toList(),
                setOf(distributionCenterConfiguration)
            )

        assertEquals(
            default(dcCode, skuId, today),
            configurations.getConfiguration(dcCode, skuId, today),
        )
    }

    @Test
    fun `returns matching week day configuration for last available week`() {
        val skuId = UUID.randomUUID()
        val today = LocalDate.now()

        val sqrSSlConfigurations = listOf(
            SQRShortShelfLifeConf(
                dcCode,
                skuId,
                today.plusDays(1).minusWeeks(1),
                ONE,
                TEN,
                false
            ),
            SQRShortShelfLifeConf(
                dcCode,
                skuId,
                today.minusWeeks(1),
                ONE,
                TEN,
                false
            ),
        ).associateBy { it.date }

        val configurations =
            SQRShortShelfLifeConfigurations(
                sqrSSlConfigurations.values.toList(),
                setOf(distributionCenterConfiguration)
            )

        assertEquals(
            sqrSSlConfigurations[today.minusWeeks(1)]!!,
            configurations.getConfiguration(dcCode, skuId, today),
        )
    }

    @Test
    fun `returns default configuration if there is no date match (current and past weeks)`() {
        val skuId = UUID.randomUUID()
        val today = LocalDate.now()

        val sqrSSlConfigurations = listOf(
            SQRShortShelfLifeConf(
                dcCode,
                skuId,
                today.plusWeeks(2),
                ONE,
                TEN,
                false
            ),
        ).associateBy { it.date }

        val configurations = SQRShortShelfLifeConfigurations(sqrSSlConfigurations.values.toList(), emptySet())

        assertEquals(default(dcCode, skuId, today), configurations.getConfiguration(dcCode, skuId, today))
    }
}
