---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
tag: '@dockerTag@'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

deployments:
  app:
    replicaCount: 1
    minAvailable: 0
    deploymentStrategy:
      type: Recreate
    containerPorts:
      http: 8080
      http-actuator: 8081
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    resources:
      requests:
        memory: '512Mi'
        cpu: '300m'
      limits:
        memory: '512Mi'
        cpu: '1'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    podAnnotations: { }
    env:
      HF_INVENTORY_DB_MASTER_HOST: 'vault:@tier@/key-value/data/inventory#DB_MASTER_HOST'
      HF_INVENTORY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#DB_PASSWORD'
      HF_INVENTORY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#DB_USERNAME'
      HF_AIVEN_PASSWORD: 'vault:@tier@/key-value/data/kafka/csku-inventory-forecast#password'
      HF_INVENTORY_DB_HOST: 'vault:@tier@/key-value/data/inventory#DB_REPLICA_HOST'
      HF_INVENTORY_READONLY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#READONLY_DB_PASSWORD'
      HF_INVENTORY_READONLY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#READONLY_DB_USERNAME'
    hpa:
      enabled: false
    spotInstance:
      preferred: true
    startupProbe:
      httpGet:
        path: /startup
        port: 8081
      initialDelaySeconds: 10
      timeoutSeconds: 3
      failureThreshold: 15
      periodSeconds: 10
    livenessProbe:
      httpGet:
        path: /health
        port: 8081
      initialDelaySeconds: 60
      periodSeconds: 15
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3

services:
  app:
    enablePrometheus: true
    metricPortName: 'http-actuator'
    metricPath: '/prometheus'
    enabled: true
    type: ClusterIP
    ports:
      http: 80
      http-actuator: 8081

configMap:
  HF_TIER: '@tier@'
  SLACK_ALERT_CHANNEL: '@slackAlertChannel@-@tier@'
  SLACK_WEBHOOK: 'vault:common/key-value/data/misc#SLACK_URL'
