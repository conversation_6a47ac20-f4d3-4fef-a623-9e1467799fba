parallelism=Number of consumer concurrently polling and processing demand. This number should be less than the number of input partitions.

poll.timeout=Duration to wait when poll for new records from kafka brokers
poll.interval_ms=Minimum duration to wait between 2 poll requests
process.timeout=Maximum duration to wait for the process to complete

inventory.db.master-host=inventory DB host
inventory.db.username=inventory DB username
inventory.db.password=inventory DB password
schema.registry.url=Aiven schema registry URL
