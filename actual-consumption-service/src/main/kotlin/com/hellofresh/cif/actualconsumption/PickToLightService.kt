package com.hellofresh.cif.actualconsumption

import com.hellofresh.cif.actualConsumption.schema.tables.records.Pick_2LightRecord
import com.hellofresh.cif.actualconsumption.model.Pick2Light
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import org.apache.kafka.clients.consumer.ConsumerRecords

private val CITY_TO_DC_MAPPING = mapOf(
    "Milano" to "IT",
    "Banbury" to "GR",
    "Nuneaton" to "BV",
    "Derby" to "TO",
    "Verden" to "VE",
    "Barleben" to "BX",
    "Koelliken" to "CH",
    "Summerlea" to "OH",
    "Vancouver" to "BL",
    "Edmonton" to "AE",
    "Timberlea" to "OE",
    "Perth" to "PH",
    "Perth2" to "PH",
    "Melbourne" to "ML",
    "Sydney" to "SY",
    "Bjuv" to "SK",
    "Cloudberry" to "SK",
    "Oslo" to "MO",
    "Lisses" to "LI",
    "Dublin" to "IE",
    "<PERSON><PERSON><PERSON>wijk" to "DH",
    "Auckland" to "NZ",
    "Bodega" to "SP",
)

class PickToLightService(
    private val p2lRepo: PickToLightRepository,
    private val dcConfigService: DcConfigService
) {
    suspend fun processRecords(records: ConsumerRecords<String, Pick2Light>) {
        val pick2lightrecords = records
            .map {
                val data = it.value()
                Pick_2LightRecord(
                    it.key(),
                    data.offsetDateTime,
                    data.data.pickRow.quantity.toShort(),
                    dcConfigService.dcConfigurations[CITY_TO_DC_MAPPING[data.city]]?.market,
                    CITY_TO_DC_MAPPING[data.city],
                    data.data.pickRow.cskuCode,
                )
            }.filter {
                it.cskuCode != null &&
                    it.cskuCode != "" &&
                    it.market != null &&
                    it.dcCode != null
            }
        if (pick2lightrecords.isNotEmpty()) p2lRepo.batchInsert(pick2lightrecords)
    }
}
