package com.hellofresh.cif.actualconsumption

import com.google.common.util.concurrent.ThreadFactoryBuilder
import com.hellofresh.cif.actualconsumption.model.Pick2Light
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.DBConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.lib.StatusServer
import com.hellofresh.cif.lib.kafka.ConsumerProcessorConfig
import com.hellofresh.cif.lib.kafka.CoroutinesProcessor
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategy
import com.hellofresh.cif.lib.kafka.DeserializationExceptionStrategyType.LOG_ERROR_IGNORE
import com.hellofresh.cif.lib.kafka.IgnoreAndContinueProcessing
import com.hellofresh.cif.lib.kafka.PollConfig
import com.hellofresh.cif.lib.kafka.serde.JacksonDeserializer
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.shutdown.shutdownHook
import io.micrometer.core.instrument.MeterRegistry
import java.util.concurrent.Executors
import kotlin.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.apache.kafka.common.serialization.StringDeserializer

private const val PICK_2_LIGHT_TOPIC = "intermediate.warehouse.pick-2-light.pick_time"
private const val STATUS_SERVER_HTTP_PORT = 8081
suspend fun main() {
    val meterRegistry = createMeterRegistry()
    val app = ActualConsumptionApplication(meterRegistry)
    StatusServer.run(
        meterRegistry,
        STATUS_SERVER_HTTP_PORT,
    )
    withContext(Dispatchers.IO) {
        repeat(ConfigurationLoader.getIntegerOrDefault("parallelism", 1)) {
            launch { app.runApp() }
        }
    }
}

@Suppress("MagicNumber")
class ActualConsumptionApplication(private val meterRegistry: MeterRegistry) {

    private val parallelism = getParallelismConfig()
    val metricsDSLContext = DBConfiguration.jooqMasterDslContext(parallelism, meterRegistry)
    private val pickToLightRepositoryImpl = PickToLightRepositoryImpl(metricsDSLContext)
    private val dcConfigService = DcConfigService(meterRegistry)
    private val pickToLightService = PickToLightService(pickToLightRepositoryImpl, dcConfigService)
    private val coroutineDispatcher = Executors.newFixedThreadPool(
        parallelism,
        ThreadFactoryBuilder().setNameFormat("producer-thread-%d").build(),
    ).asCoroutineDispatcher()

    suspend fun runApp() {
        val pollTimeout =
            Duration.parse(
                requireNotNull(
                    ConfigurationLoader.getStringOrFail("poll.timeout"),
                ) { "poll.timeout property not found" },
            )
        val pollInterval = ConfigurationLoader.getIntegerOrDefault("poll.interval_ms", 20).toLong()
        val processTimeout =
            Duration.parse(
                requireNotNull(
                    ConfigurationLoader.getStringOrFail("process.timeout"),
                ) { "process.timeout property not found" },
            )
        val consumerConfig: Map<String, String> = ConfigurationLoader.loadKafkaConsumerConfigurations() +
            mapOf(ConsumerConfig.ENABLE_AUTO_COMMIT_CONFIG to "false")
        val consumerProcessorConfig = ConsumerProcessorConfig(
            consumerConfig,
            StringDeserializer(),
            JacksonDeserializer<Pick2Light>(),
            listOf(PICK_2_LIGHT_TOPIC),
        )
        withContext(coroutineDispatcher) {
            repeat(parallelism) {
                launch {
                    CoroutinesProcessor(
                        pollConfig = PollConfig(
                            pollTimeout,
                            pollInterval,
                            processTimeout,
                        ),
                        consumerProcessorConfig = consumerProcessorConfig,
                        meterRegistry = meterRegistry,
                        process = pickToLightService::processRecords,
                        handleDeserializationException = DeserializationExceptionStrategy.create(
                            LOG_ERROR_IGNORE,
                            meterRegistry,
                        ),
                        recordProcessingExceptionStrategy = IgnoreAndContinueProcessing(
                            meterRegistry,
                            "pick2light_write_failure",
                        ),
                    ).also {
                        shutdownHook(it)
                        HealthChecks.add(it)
                        StartUpChecks.add(it)
                    }.run()
                }
            }
        }
    }

    private fun getParallelismConfig() = ConfigurationLoader.getIntegerOrDefault("parallelism", 1)
}
