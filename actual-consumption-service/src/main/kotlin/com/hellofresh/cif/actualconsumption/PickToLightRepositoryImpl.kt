package com.hellofresh.cif.actualconsumption

import com.hellofresh.cif.actualConsumption.schema.tables.Pick_2Light
import com.hellofresh.cif.actualConsumption.schema.tables.records.Pick_2LightRecord
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import java.time.OffsetDateTime
import java.time.ZoneOffset.UTC
import java.util.concurrent.CompletionStage
import kotlinx.coroutines.future.await
import org.jooq.Batch
import org.jooq.impl.DSL.insertInto

class PickToLightRepositoryImpl(private val metricsDSLContext: MetricsDSLContext) : PickToLightRepository {

    override suspend fun batchInsert(records: List<Pick_2LightRecord>) {
        BatchQueryBuilder(metricsDSLContext).let {
            records
                .map { v -> it.bind(v) }
                .lastOrNull()
                ?.executeAsync()
        }?.await()
    }

    /**
     * Provides a safe interface to build PreparedStatement
     */
    private class BatchQueryBuilder(dsl: MetricsDSLContext) {
        private val query = dsl.withTagName("insert-pick-to-light")
            .batch(
                Pick_2Light.PICK_2_LIGHT.run {
                    insertInto(this)
                        .columns(
                            KAFKA_MESSAGE_KEY,
                            CREATED_AT,
                            QUANTITY,
                            MARKET,
                            DC_CODE,
                            CSKU_CODE,
                        )
                        .values("", OffsetDateTime.now(UTC), 0, "", "", "")
                        .onDuplicateKeyUpdate()
                        .set(KAFKA_MESSAGE_KEY, "")
                        .set(CREATED_AT, OffsetDateTime.now(UTC))
                        .set(QUANTITY, 0)
                        .set(MARKET, "")
                        .set(DC_CODE, "")
                        .set(CSKU_CODE, "")
                },
            )

        lateinit var runnableQuery: RunnableQuery

        fun bind(pick2lightrecord: Pick_2LightRecord): RunnableQuery {
            if (!this::runnableQuery.isInitialized) {
                runnableQuery = RunnableQuery(query)
            }

            query.bind(
                pick2lightrecord.kafkaMessageKey,
                pick2lightrecord.createdAt,
                pick2lightrecord.quantity,
                pick2lightrecord.market,
                pick2lightrecord.dcCode,
                pick2lightrecord.cskuCode,
                pick2lightrecord.kafkaMessageKey,
                pick2lightrecord.createdAt,
                pick2lightrecord.quantity,
                pick2lightrecord.market,
                pick2lightrecord.dcCode,
                pick2lightrecord.cskuCode,
            )
            return runnableQuery
        }

        class RunnableQuery(val query: Batch) {
            fun executeAsync(): CompletionStage<IntArray> = query.executeAsync()
        }
    }
}
