plugins {
    id("com.hellofresh.cif.common-conventions")
    alias(libs.plugins.jooq)
}

description = "Project to access Demand domain data"
group = "$group.demand.lib"

dependencies {
    jooqGenerator(libs.postgresql.driver)

    api(projects.demandModels)
    api(projects.lib)
    api(projects.lib.models)
    api(projects.lib.db)
    api(projects.lib.logging)
    api(projects.lib.featureflags)
    api(libs.coroutines.core)
    api(libs.jackson.kotlin)
    api(libs.coroutines.core)
    api(libs.coroutines.jdk8)

    testImplementation(projects.inventoryModels)
    testImplementation(libs.jackson.databind)
    testImplementation(libs.jackson.jsr310)
    testImplementation(projects.libTests)
    testImplementation(testFixtures(projects.lib.featureflags))
}

copy {
    duplicatesStrategy = DuplicatesStrategy.INCLUDE
}

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "demand|actual_consumption|pick_2_light" +
                            "|actual_consumption_inventory_activity|inventory_activity|inventory_activity_type" +
                            "|dc_config|uom|sku_specification|sku_specification_view|actual_consumption_view"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = false
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}
