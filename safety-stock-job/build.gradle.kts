plugins {
    id("com.hellofresh.cif.application-conventions")
    `test-functional`
    hellofresh.`test-fixtures`
    alias(libs.plugins.jooq)
}

description = "Calculates the safety stock and persist into DB."
group = "$group.safety.stock.job"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "safety_stocks|safety_stock_multiplier|sku_risk_rating|safety_stock_buffer|safety_stock_import"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)

    implementation(projects.skuInputsLib)
    implementation(projects.safetyStock.safetyStockLib)
    implementation(projects.supplyQuantityRecommendationLib)
    implementation(projects.lib.db)
    implementation(projects.lib)
    implementation(projects.lib.s3)
    implementation(projects.lib.sqs)
    implementation(projects.skuSpecificationLib)
    implementation(projects.distributionCenterLib)
    implementation(libs.apache.commons.math)
    implementation(libs.caffeine.core)

    testImplementation(testFixtures(projects.safetyStockJob))
    testImplementation(testFixtures(projects.distributionCenterModels))
    testImplementation(testFixtures(projects.safetyStock.safetyStockLib))
    testImplementation(testFixtures(projects.lib.featureflags))
    testFunctionalImplementation(projects.libTests)
    testFunctionalImplementation(libs.mockk)
}
