import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.safetystock.safetymultiplier.service.SafetyStockImport
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.Random
import java.util.UUID

fun SafetyStockImport.Companion.random(
    dcCode: String = "DC",
    skuId: UUID = UUID.randomUUID(),
    week: String = DcWeek(LocalDate.now().plusWeeks(1), DayOfWeek.MONDAY).value
) =
    SafetyStockImport(
        dcCode = dcCode,
        skuId = skuId,
        week = week,
        safetyStock = Random().nextLong(1000000),
    )
