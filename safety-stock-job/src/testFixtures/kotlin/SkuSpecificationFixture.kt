import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.sku.models.SkuSpecification

val SkuSpecification.Companion.default
    get() = SkuSpecification(
        coolingType = "None",
        name = "Default Sku",
        packaging = "Box",
        skuCode = "SPI-00-50271-5",
        category = "Proteins",
        parentId = null,
        acceptableCodeLife = 0,
        market = "DACH",
        uom = SkuUOM.UOM_UNIT,
        fumigationAllowed = null
    )
