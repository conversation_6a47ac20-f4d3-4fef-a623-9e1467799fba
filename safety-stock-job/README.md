# Safety Stock Job

## How to run the safety stock job locally for testing with docker compose ?

1. docker-compose up inventory-postgres db-migration
2. docker-compose up sqs-service
3. docker-compose up s3-service
4. Insert the dc_config records from live database to local database.
5. Insert the sku_specification records from live database to local database.
6. Start the safety-stock-job
7. Should be able to process the safety-stock-multiplier S3 files.
8. Should be able to view the records created in safety_stock_multiplier table.

