package com.hellofresh.cif.outputdemandforecast.service

import com.fasterxml.jackson.dataformat.cbor.databind.CBORMapper
import com.github.benmanes.caffeine.cache.Caffeine
import com.google.type.Decimal
import com.hellofresh.calculator.models.CskuInventoryForecastKey
import com.hellofresh.calculator.models.CskuInventoryForecastVal
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.StopPublishingDemandsForClients
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.models.SkuUOM.UOM_GAL
import com.hellofresh.cif.models.SkuUOM.UOM_KG
import com.hellofresh.cif.models.SkuUOM.UOM_LBS
import com.hellofresh.cif.models.SkuUOM.UOM_LITRE
import com.hellofresh.cif.models.SkuUOM.UOM_OZ
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.cif.models.SkuUOM.UOM_UNRECOGNIZED
import com.hellofresh.cif.models.SkuUOM.UOM_UNSPECIFIED
import com.hellofresh.dateUtil.models.toProtoDate
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastKey
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal.UOM
import com.hellofresh.proto.stream.ordering.skuInventoryDemandForecast.v1.SkuInventoryDemandForecastVal.WeekDate
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics
import java.math.BigDecimal
import java.time.Duration
import org.apache.kafka.clients.consumer.ConsumerRecords
import org.apache.kafka.clients.producer.Producer
import org.apache.kafka.clients.producer.ProducerRecord
import org.apache.logging.log4j.kotlin.Logging

const val DEMAND_FORECAST_TOPIC_NAME = "public.ordering.sku-inventory-demand-forecast.v1"

private const val MAX_FILTER_SIZE = 2_000_000L
private const val MAX_FILTER_IDLE_HOURS = 48L

class OutputDemandForecastService(
    meterRegistry: MeterRegistry,
    private val producer: Producer<SkuInventoryDemandForecastKey, SkuInventoryDemandForecastVal>,
    maxFilterSize: Long = MAX_FILTER_SIZE,
    maxFilterIdleTimeHours: Long = MAX_FILTER_IDLE_HOURS,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
    private val dcConfigService: DcConfigService,
) {

    private val outputFilter = Caffeine.newBuilder()
        .maximumSize(maxFilterSize)
        .expireAfterAccess(Duration.ofHours(maxFilterIdleTimeHours))
        .recordStats()
        .build<CskuInventoryForecastKey, ByteArray>().also {
            CaffeineCacheMetrics.monitor(meterRegistry, it, "demand-forecast-output-filter-cache")
        }

    fun processRecords(records: ConsumerRecords<CskuInventoryForecastKey, CskuInventoryForecastVal>) {
        records.asSequence()
            .map { it.key() to convertToValue(it.value()) }
            .filter(::isNewRecord)
            .onEach { (key, value) ->
                val market = dcConfigService.dcConfigurations[key.dcCode]?.market ?: run {
                    logger.warn("Error: market value not found for the dcCode ${key.dcCode}")
                    ""
                }

                if (isDcEnabledToPublish(key.dcCode, market, statsigFeatureFlagClient)) {
                    val outputKey = convertToOutputKey(key)
                    val outputValue = convertToOutputValue(value)
                    producer.send(ProducerRecord(DEMAND_FORECAST_TOPIC_NAME, outputKey, outputValue))
                } else {
                    logger.debug("The demand-forecast data is not sent to dc code = ${key.dcCode}, since its disabled")
                }
            }.also {
                producer.flush()
                it.forEach { pair -> updateRecordFilter(pair) }
            }
    }

    private fun isDcEnabledToPublish(
        dcCode: String,
        market: String,
        statsigFeatureFlagClient: StatsigFeatureFlagClient
    ): Boolean =
        !statsigFeatureFlagClient.isEnabledFor(
            StopPublishingDemandsForClients(
                setOf(
                    ContextData(MARKET, market),
                    ContextData(DC, dcCode),
                )
            )
        )

    private fun convertToOutputKey(
        key: CskuInventoryForecastKey
    ) = SkuInventoryDemandForecastKey.newBuilder()
        .apply {
            skuId = key.cskuId.toString()
            distributionCenterBobCode = key.dcCode
            date = key.date.toProtoDate()
        }.build()

    private fun isNewRecord(record: Pair<CskuInventoryForecastKey, SkuInventoryDemandForecast>) =
        outputFilter.getIfPresent(record.first)
            ?.let {
                !it.contentEquals(serialize(record.second))
            } ?: true

    private fun updateRecordFilter(record: Pair<CskuInventoryForecastKey, SkuInventoryDemandForecast>) {
        outputFilter.put(record.first, serialize(record.second))
    }

    companion object : Logging {

        private val cborMapper = CBORMapper().findAndRegisterModules()

        internal fun BigDecimal.toProtoDecimal() = Decimal.newBuilder().setValue(
            this.stripTrailingZeros().toPlainString()
        ).build()

        private fun serialize(skuInventoryDemandForecast: SkuInventoryDemandForecast): ByteArray = cborMapper.writeValueAsBytes(
            skuInventoryDemandForecast,
        )

        internal fun convertToValue(
            value: CskuInventoryForecastVal
        ): SkuInventoryDemandForecast =
            SkuInventoryDemandForecast(
                productionWeek = DcWeek(value.productionWeek),
                forecastedDemandedQty = value.demanded,
                forecastedNeededQty = value.dailyNeeds,
                productionWeekStartStock = value.productionWeekStartStock,
                uom = value.uom,
            )

        internal fun convertToOutputValue(
            skuInventoryDemandForecast: SkuInventoryDemandForecast
        ): SkuInventoryDemandForecastVal =
            SkuInventoryDemandForecastVal.newBuilder()
                .apply {
                    setProductionWeek(
                        WeekDate.newBuilder().apply {
                            year = skuInventoryDemandForecast.productionWeek.year
                            week = skuInventoryDemandForecast.productionWeek.week
                        },
                    )

                    unit = skuInventoryDemandForecast.uom.convertToUOM()
                    forecastedDemandedQty = skuInventoryDemandForecast.forecastedDemandedQty.toProtoDecimal()
                    forecastedNeededQty = skuInventoryDemandForecast.forecastedNeededQty.toProtoDecimal()
                    productionWeekStartStock = skuInventoryDemandForecast.productionWeekStartStock.toProtoDecimal()
                }.build()

        fun SkuUOM.convertToUOM(): UOM =
            when (this) {
                UOM_UNSPECIFIED, UOM_UNRECOGNIZED -> UOM.UOM_UNSPECIFIED
                UOM_UNIT -> UOM.UOM_UNIT
                UOM_KG -> UOM.UOM_KG
                UOM_LBS -> UOM.UOM_LBS
                UOM_OZ -> UOM.UOM_OZ
                UOM_LITRE -> UOM.UOM_LITRE
                UOM_GAL -> UOM.UOM_GAL
            }
    }
}

internal data class SkuInventoryDemandForecast(
    val productionWeek: DcWeek,
    val forecastedDemandedQty: BigDecimal,
    val forecastedNeededQty: BigDecimal,
    val productionWeekStartStock: BigDecimal,
    val uom: SkuUOM
)
