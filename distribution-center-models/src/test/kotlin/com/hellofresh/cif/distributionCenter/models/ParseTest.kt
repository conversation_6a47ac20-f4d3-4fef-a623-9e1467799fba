package com.hellofresh.cif.distributionCenter.models

import com.hellofresh.cif.lib.kafka.serde.serde
import kotlin.test.assertEquals

data class ParseTest<K, V>(
    val actualVal: String,
    val actualKey: String,
    val expectedKey: K,
    val expectedValue: V,
    val exception: <PERSON><PERSON>an,
) {

    inline fun <reified K1 : K, reified V1 : V> runTest() {
        val sK = serde<K1>()
        val sV = serde<V1>()

        if (!exception) {
            val gotK = sK.deserializer().deserialize("test", actualKey.toByteArray())
            assertEquals(expectedKey, gotK)

            val gotV = sV.deserializer().deserialize("test", actualVal.toByteArray())
            assertEquals(expectedValue, gotV)
        }
    }
}
