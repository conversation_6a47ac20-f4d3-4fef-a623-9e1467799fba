package com.hellofresh.cif.distributionCenter.models

import java.time.DayOfWeek
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.DayOfWeek.SATURDAY
import java.time.DayOfWeek.THURSDAY
import java.time.DayOfWeek.WEDNESDAY
import java.time.LocalDate
import java.time.ZoneId
import java.util.stream.Stream
import kotlin.test.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

internal class DcWeekTest {

    @ParameterizedTest
    @MethodSource("getDifferentDCWeekInputs")
    fun `should be able to get the last date in DC week`(dcWeekInput: DCWeekInput) {
        val dcWeek = DcWeek(dcWeekInput.week)
        val lastDateInDcWeek = dcWeek.getLastDateInDcWeek(dcWeekInput.dayOfWeek, ZoneId.of("UTC"))
        assertEquals(LocalDate.parse(dcWeekInput.expectedLastDateInDcWeek), lastDateInDcWeek)
    }

    companion object {
        @JvmStatic
        @Suppress("unused")
        private fun getDifferentDCWeekInputs() = Stream.of(
            DCWeekInput(
                "2022-W45",
                MONDAY,
                "2022-11-06",
                "2022-10-31",
            ),
            DCWeekInput(
                "2022-W45",
                FRIDAY,
                "2022-11-10",
                "2022-11-04",
            ),
            DCWeekInput(
                "2022-W47",
                WEDNESDAY,
                "2022-11-22",
                "2022-11-16",
            ),
            DCWeekInput(
                "2023-W01",
                FRIDAY,
                "2023-01-05",
                "2022-12-30",
            ),
            DCWeekInput(
                "2023-W02",
                THURSDAY,
                "2023-01-11",
                "2023-01-05",
            ),
            DCWeekInput(
                "2023-W09",
                SATURDAY,
                "2023-03-03",
                "2023-02-25",
            ),
            DCWeekInput(
                "2024-W01",
                THURSDAY,
                "2024-01-03",
                "2023-12-28",
            )

        )
    }
}

data class DCWeekInput(
    val week: String,
    val dayOfWeek: DayOfWeek,
    val expectedLastDateInDcWeek: String,
    val expectedStartDateInDcWeek: String
)
