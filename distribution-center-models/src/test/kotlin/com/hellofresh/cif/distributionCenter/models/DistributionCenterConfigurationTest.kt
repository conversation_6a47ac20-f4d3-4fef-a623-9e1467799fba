package com.hellofresh.cif.distributionCenter.models

import java.time.DayOfWeek.THURSDAY
import java.time.LocalDate
import java.time.ZoneId
import kotlin.test.Test
import kotlin.test.assertEquals
import org.junit.jupiter.api.assertThrows

class DistributionCenterConfigurationTest {

    @Test fun `functions regarding cleardown throws exceptions if cleardown doesn't exist`() {
        val dc = DistributionCenterConfiguration(
            dcCode = "dcCode",
            productionStart = THURSDAY,
            cleardown = THURSDAY,
            zoneId = ZoneId.of("Europe/Berlin"),
            market = "BENELUXFR",
            enabled = false,
            hasCleardown = false,
            wmsType = WmsSystem.WMS_SYSTEM_FCMS,
            brands = emptyList(),
        )

        assertThrows<IllegalStateException> { dc.checkCleardown() }
        assertThrows<IllegalStateException> { dc.getLatestCleardown() }
        assertThrows<IllegalStateException> { dc.isBeforeLatestCleardown(LocalDate.now()) }
        assertThrows<IllegalStateException> { dc.isLatestCleardown(LocalDate.now()) }
    }

    @Test
    fun `return current week from dc`() {
        val dc = DistributionCenterConfiguration(
            dcCode = "dcCode",
            productionStart = THURSDAY,
            cleardown = THURSDAY,
            zoneId = ZoneId.of("Europe/Berlin"),
            market = "BENELUXFR",
            enabled = false,
            hasCleardown = false,
            wmsType = WmsSystem.WMS_SYSTEM_FCMS,
        )

        assertEquals(ProductionWeek(dc.getLatestProductionStart(), dc.productionStart), dc.getCurrentWeek())
        assertEquals(ProductionWeek(LocalDate.now(dc.zoneId), dc.productionStart), dc.getCurrentWeek())
    }

    @Test
    fun `return week from for given date`() {
        val dc = DistributionCenterConfiguration(
            dcCode = "dcCode",
            productionStart = THURSDAY,
            cleardown = THURSDAY,
            zoneId = ZoneId.of("Europe/Berlin"),
            market = "BENELUXFR",
            enabled = false,
            hasCleardown = false,
            wmsType = WmsSystem.WMS_SYSTEM_FCMS,
            brands = emptyList(),
        )

        val pastWeekDay = LocalDate.now(dc.zoneId).minusWeeks(1)
        assertEquals(ProductionWeek(pastWeekDay, dc.productionStart), dc.getWeek(pastWeekDay))
        val futureWeekDay = LocalDate.now(dc.zoneId).minusWeeks(2)
        assertEquals(ProductionWeek(futureWeekDay, dc.productionStart), dc.getWeek(futureWeekDay))
    }
}
