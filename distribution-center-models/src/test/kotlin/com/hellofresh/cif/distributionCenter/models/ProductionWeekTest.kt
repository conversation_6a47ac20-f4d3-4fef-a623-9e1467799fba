package com.hellofresh.cif.distributionCenter.models

import java.time.DayOfWeek.MONDAY
import java.time.ZoneOffset
import kotlin.test.assertFalse
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class ProductionWeekTest {

    @Test
    fun `production week range iterates over all weeks`() {
        val weekNumberFrom = 20
        val weekNumberTo = 30

        val dcWeekFrom = DcWeek("2023-W$weekNumberFrom")
        val dcWeekTo = DcWeek("2023-W$weekNumberTo")

        val weekFrom = ProductionWeek(dcWeekFrom.getStartDateInDcWeek(MONDAY, ZoneOffset.UTC), MONDAY)
        val weekTo = ProductionWeek(dcWeekTo.getStartDateInDcWeek(MONDAY, ZoneOffset.UTC), MONDAY)

        val weekRange = weekFrom.rangeTo(weekTo).iterator()

        (weekNumberFrom..weekNumberTo).forEach {
            assertEquals(DcWeek("2023-W$it"), weekRange.next().dcWeek)
        }
        assertFalse(weekRange.hasNext())
    }

    @Test
    fun `production week range iterates over all weeks with open range`() {
        val weekNumberFrom = 20
        val weekNumberTo = 30

        val dcWeekFrom = DcWeek("2023-W$weekNumberFrom")
        val dcWeekTo = DcWeek("2023-W$weekNumberTo")

        val weekFrom = ProductionWeek(dcWeekFrom.getStartDateInDcWeek(MONDAY, ZoneOffset.UTC), MONDAY)
        val weekTo = ProductionWeek(dcWeekTo.getStartDateInDcWeek(MONDAY, ZoneOffset.UTC), MONDAY)

        val weekRange = weekFrom.rangeUntil(weekTo).iterator()

        (weekNumberFrom..<weekNumberTo).forEach {
            assertEquals(DcWeek("2023-W$it"), weekRange.next().dcWeek)
        }
        assertFalse(weekRange.hasNext())
    }
}
