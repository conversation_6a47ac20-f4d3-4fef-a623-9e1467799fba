package com.hellofresh.cif.distributionCenter.models

import java.time.DayOfWeek
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDate
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

internal class WeekTest {

    @Test fun `week is generated into a hellofresh valid string format`() {
        (2020..2030).forEach {
            assertEquals(
                "$it-W01",
                DcWeek(LocalDate.of(it, 1, 1), FRIDAY).value,
            )
        }
    }

    @ParameterizedTest
    @CsvSource(
        "2021-06-16,SUNDAY,2021-W24",
        "2021-06-09,WEDNESDAY,2021-W24",
        "2021-06-13,MONDAY,2021-W24",
        "2021-06-12,MONDAY,2021-W24",
        "2021-06-11,MONDAY,2021-W24",
        "2021-06-10,MONDAY,2021-W24",
        "2021-06-09,MONDAY,2021-W24",
        "2021-06-08,MONDAY,2021-W24",
        "2021-06-07,MONDAY,2021-W24",
        "2021-01-05,WEDNESDAY,2021-W01",
        "2021-01-04,WEDNESDAY,2021-W01",
        "2021-01-03,WEDNESDAY,2021-W01",
        "2021-01-02,WEDNESDAY,2021-W01",
        "2021-01-01,WEDNESDAY,2021-W01",
        "2020-12-31,WEDNESDAY,2021-W01",
        "2020-12-30,WEDNESDAY,2021-W01",
        "2021-07-23,SATURDAY,2021-W29",
        "2021-07-22,SATURDAY,2021-W29",
        "2021-07-21,SATURDAY,2021-W29",
        "2021-07-20,SATURDAY,2021-W29",
        "2021-07-19,SATURDAY,2021-W29",
        "2021-07-18,SATURDAY,2021-W29",
        "2021-07-17,SATURDAY,2021-W29",
    )
    fun `convert correctly from localDate to HF production week`(
        localDate: LocalDate,
        productionStart: DayOfWeek,
        expectedHFWeek: String,
    ) {
        assertEquals(expectedHFWeek, DcWeek(localDate, productionStart).value)
    }
}
