package com.hellofresh.cif.calculator.models

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.LocationType
import java.time.LocalDate
import java.time.LocalDateTime
import kotlin.random.Random

fun CalculationInventory.Companion.random() = with(Random(LocalDateTime.now().nano)) {
    CalculationInventory(
        qty = SkuQuantity.fromLong(nextLong(1000)),
        expiryDate = LocalDate.now().plusDays(10 + nextLong(100)),
        locationType = LocationType.entries.filter { it.isUsable() }.random(),
    )
}
