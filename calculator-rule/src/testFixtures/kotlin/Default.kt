package com.hellofresh.cif.calculator.models

import com.hellofresh.cif.calculator.calculations.rules.TARGET_SAFETY_STOCK_DEFAULT_STRATEGY
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID

fun CalculationInventory.Companion.default() = CalculationInventory(
    qty = SkuQuantity.fromLong(0),
    expiryDate = LocalDate.MAX.minusDays(10),
    locationType = LOCATION_TYPE_STAGING,
)

fun DayCalculationResult.Companion.default() =
    DayCalculationResult(
        UOM_UNIT,
        UUID.fromString("4b3e119a-2528-4b6d-a7cc-31fa5daba45b"),
        "DC",
        LocalDate.now(
            ZoneOffset.UTC,
        ),
        SkuQuantity.fromLong(0), SkuQuantity.fromLong(1), SkuQuantity.fromLong(2), SkuQuantity.fromLong(3),
        setOf(
            "A",
        ),
        SkuQuantity.fromLong(
            4,
        ),
        setOf(
            "B",
        ),
        SkuQuantity.fromLong(
            5,
        ),
        SkuQuantity.fromLong(6), SkuQuantity.fromLong(7), SkuQuantity.fromLong(8), "2022-W41",
        SkuQuantity.fromLong(9), netNeeds = SkuQuantity.fromLong(10),
        strategy = TARGET_SAFETY_STOCK_DEFAULT_STRATEGY,
    )
