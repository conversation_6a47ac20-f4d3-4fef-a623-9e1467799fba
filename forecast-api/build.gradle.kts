@Suppress("DSL_SCOPE_VIOLATION")
plugins {
    id("com.hellofresh.cif.application-conventions")
    hellofresh.`test-fixtures`
    `test-functional`
    alias(libs.plugins.jooq)
    alias(libs.plugins.openapi)
    alias(libs.plugins.gradle.docker.compose)
}

description = "API to query the calculations"
group = "$group.api"

jooq {
    version.set("3.19.8")
    configurations {
        create("main") {
            jooqConfiguration.apply {
                val dbPort = System.getProperty("INVENTORY_DB_JOOQ_PORT")
                jdbc.apply {
                    url = "*********************************************"
                    user = "cif"
                    password = "123456"
                }
                generator.apply {
                    name = "org.jooq.codegen.DefaultGenerator"
                    database.apply {
                        name = "org.jooq.meta.postgres.PostgresDatabase"
                        inputSchema = "public"
                        includes = "sku_specification|sku_specification_view|dc_config|inventory|stock_update" +
                            "|calculation|pre_production_calculation|demand|subbed_type|actual_consumption|pick_2_light" +
                            "|live_inventory_calculation|live_inventory_pre_production_calculation|note" +
                            "|purchase_order|purchase_order_sku|supplier|purchase_orders_view|po_calculations_view" +
                            "|inventory_variance|calculation_substitution_view|supplier_culinary_sku" +
                            "|supplier_sku|supplier_details_view|supplier_sku_pricing|inventory_cleardown_trigger" +
                            "|pre_prod_calculation_substitution_view" +
                            "|supply_quantity_recommendation_conf|supply_quantity_recommendation|safety_stocks|safety_stock_conf" +
                            "|file_export_request|export_status|uom|sku_risk_rating" +
                            "|sqr_short_shelf_life|sqr_short_shelf_life_conf|file_uploads|file_upload_status|file_type"
                        isIncludeSequences = false
                        isIncludePrimaryKeys = true
                        isIncludeUniqueKeys = false
                        isIncludeForeignKeys = false
                        isIncludeCheckConstraints = false
                        isIncludeIndexes = false
                    }

                    generate.apply {
                        isRecords = true
                        isPojos = false
                        isFluentSetters = true
                    }
                    target.apply {
                        packageName = "$group.schema"
                    }
                    strategy.name = "org.jooq.codegen.DefaultGeneratorStrategy"
                }
            }
        }
    }
}

dependencies {
    jooqGenerator(libs.postgresql.driver)
    api(projects.inventoryModels)
    api(projects.demandLib)

    implementation(libs.coroutines.core)
    implementation(projects.calculatorRule)
    implementation(projects.calculatorModels)
    implementation(projects.demandModels)
    implementation(projects.dateUtilModels)
    implementation(projects.distributionCenterLib)
    implementation(projects.inventory.inventoryLib)
    implementation(projects.skuModels)
    implementation(projects.skuInputsLib)
    implementation(projects.purchaseOrder.purchaseOrderLib)
    implementation(projects.lib.db)
    implementation(projects.lib.s3)
    implementation(projects.lib.featureflags)
    implementation(projects.distributionCenterLib)
    implementation(projects.supplyQuantityRecommendationLib)
    implementation(projects.safetyStock.safetyStockLib)
    implementation(projects.transferOrder.transferOrderLib)

    implementation(libs.hellofresh.schemaregistry) {
        exclude(group = "com.google.api.grpc", module = "proto-google-common-protos")
    }
    implementation(libs.protobuf.grpc)
    implementation(libs.caffeine.core)
    implementation(libs.jackson.databind)
    implementation(libs.jackson.jsr310)
    implementation(libs.jackson.kotlin)
    implementation(libs.ktor.client.encoding)
    implementation(libs.ktor.server.callid)
    implementation(libs.ktor.server.compression)
    implementation(libs.ktor.core)
    implementation(libs.ktor.server.auth.jwt)
    implementation(libs.ktor.jackson)
    implementation(libs.ktor.content.negotiation)
    implementation(libs.ktor.netty)
    implementation(libs.ktor.client.cio)
    implementation(libs.ktor.micrometer)
    implementation(libs.postgresql.driver)
    implementation(libs.apache.commonscsv)

    testFixturesImplementation(libs.jooq.core)
    testFixturesImplementation(libs.jackson.databind)
    testFixturesImplementation(libs.jackson.kotlin)
    testFixturesImplementation(libs.jackson.jsr310)

    testImplementation(libs.mockk)
    testImplementation(libs.ktor.test) {
        exclude("junit", "junit")
        exclude("ch.qos.logback", "logback-classic")
    }
    testImplementation(testFixtures(projects.lib.featureflags))
    testImplementation(testFixtures(projects.distributionCenterModels))
    testImplementation(testFixtures(projects.calculatorRule))
    testImplementation(testFixtures(projects.skuModels))

    testFunctionalImplementation(libs.ktor.test) {
        exclude("junit", "junit")
        exclude("ch.qos.logback", "logback-classic")
    }
    testFunctionalImplementation(projects.lib.db)
    testFunctionalImplementation(libs.restassured.validator)
    testFunctionalImplementation(libs.flyway.core)
    testFunctionalImplementation(libs.postgresql.driver)
    testFunctionalImplementation(libs.testcontainers.postgresql)
    testFunctionalImplementation(libs.testcontainers.junit)
    testFunctionalImplementation(projects.libTests)
    testFunctionalImplementation(testFixtures(projects.inventoryModels))

    testFunctionalRuntimeOnly(projects.inventoryDb)

    modules {
        module("com.hellofresh:logging") {
            replacedBy("com.hellofresh.cif.lib:logging")
        }
    }
}

val generatedSourcesDir = "${project.layout.buildDirectory.get()}/generated/openapi"
sourceSets.getByName("main").java {
    srcDir("$generatedSourcesDir/src/main/kotlin")
}

tasks {
    openApiGenerate.configure {
        this.generatorName.set("kotlin-server")
        this.inputSpec.set("$projectDir/src/main/resources/openapi.yaml")
        this.outputDir.set(generatedSourcesDir)
        this.modelPackage.set("com.hellofresh.cif.api.calculation.generated.model")

        this.configOptions.put("featureLocations", "false")
        this.configOptions.put("featureMetrics", "false")
        this.configOptions.put("serializationLibrary", "jackson")
        this.configOptions.put("interfaceOnly", "true")
        this.configOptions.put("enumPropertyNaming", "UPPERCASE")
        this.typeMappings.put("double", "java.math.BigDecimal")
        this.typeMappings.put("date-time", "java.time.OffsetDateTime")
        this.additionalProperties.put("useTags", true)
        this.globalProperties.put("apis", "false")
        this.globalProperties.put("models", "")
        outputs.cacheIf { false }
    }
    openApiValidate.configure {
        this.inputSpec.set("$projectDir/src/main/resources/openapi.yaml")
        outputs.cacheIf { false }
    }
    compileKotlin.configure {
        dependsOn("openApiValidate", "openApiGenerate")
    }
}
