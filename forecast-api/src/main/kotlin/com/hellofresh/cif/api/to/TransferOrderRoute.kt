package com.hellofresh.cif.api.to

import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.getAllOrDefault
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.api.to.TransferOrderResponseMapper.mapToTransferOrderDetailResponse
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.routing
import java.time.LocalDate
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

private const val TRANSFER_ORDER_PATH = "/transfer-orders"

fun Routing.transferOrder(
    transferOrdersService: TransferOrderService,
    dcConfigService: DcConfigService,
    timeout: Duration
) =
    authenticate {
        get(TRANSFER_ORDER_PATH) {
            runCatching {
                TransferOrdersReq(
                    skuId = UUID.fromString(call.parameters.getOrThrow("skuId").trim()),
                    dcCodes = call.parameters.getAllOrThrow("dcCode").map { it.trim().uppercase() }.toSet(),
                    weeks = call.parameters.getAllOrDefault("weeks").map { DcWeek(it) }.toSet(),
                    fromDate = call.parameters["fromDate"]?.let { LocalDate.parse(it) },
                    toDate = call.parameters["toDate"]?.let { LocalDate.parse(it) },
                )
            }.onFailure { exception ->
                call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
            }.onSuccess {
                withTimeout(timeout) {
                    runCatching {
                        mapToTransferOrderDetailResponse(
                            transferOrdersService.findTransferOrders(it),
                            dcConfigService
                        )
                    }
                }.onSuccess {
                    call.respond(HttpStatusCode.OK, it)
                }.onFailure {
                    call.handleResponseError(it)
                }
            }
        }
    }

fun transferOrderModule(
    transferOrdersService: TransferOrderService,
    dcConfigService: DcConfigService,
    timeout: Duration
): Application.() -> Unit = {
    routing {
        transferOrder(transferOrdersService, dcConfigService, timeout).also {
            configureSerialization(it)
        }
    }
}
