package com.hellofresh.cif.api.calculation.csvexport

import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.CalculationsService
import com.hellofresh.cif.api.calculation.csvexport.CalculationViewType.DAILY
import com.hellofresh.cif.api.calculation.csvexport.CalculationViewType.WEEKLY
import com.hellofresh.cif.api.fileexport.FileExportService
import com.hellofresh.cif.api.fileexport.repository.FileExportRequest
import com.hellofresh.cif.api.schema.enums.ExportStatus
import com.hellofresh.cif.lib.recordSuspended
import com.hellofresh.cif.s3.S3FileService
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.Tag
import io.micrometer.core.instrument.Tags
import io.micrometer.core.instrument.Timer
import java.time.Instant
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration
import org.apache.logging.log4j.kotlin.Logging

interface CalculationCsvGenerator {

    suspend fun generateCsvFile(
        calculationViewType: CalculationViewType,
        calculationRequest: CalculationRequest,
        fileExportRequest: FileExportRequest,
    ): FileExportRequest
}

@Suppress("LongParameterList")
class CalculationCsvGeneratorImpl(
    private val fileExportService: FileExportService,
    private val s3FileService: S3FileService,
    private val calculationsService: CalculationsService,
    private val purchaseOrderCSVService: PurchaseOrderCSVService,
    private val calculationsCsvConverter: CalculationsCsvConverter,
    private val meterRegistry: MeterRegistry,
    bucketSuffix: String
) : CalculationCsvGenerator {

    private val bucketName = "cif-stock-files-export$bucketSuffix"
    private val prefix = "calculation-csv-exports"

    private fun csvGeneratorDurationTimer(
        calculationViewType: CalculationViewType,
        calculationRequest: CalculationRequest

    ) = Timer.builder("calculation.export.csv").description("Record the elapsed time of csv generations").tags(
        Tags.of(
            listOf(
                Tag.of("type", calculationViewType.name),
                Tag.of("weeks", calculationRequest.weeks.size.toString()),
                Tag.of("dcs", calculationRequest.dcCodes.size.toString()),
            ),
        ),
    ).register(meterRegistry)

    private fun buildKey(requestId: UUID, calculationViewType: CalculationViewType, dcs: List<String>): String {
        val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        return "$prefix/" +
            "${yearMonthFormatter.format(now)}/" +
            "$requestId/" +
            "${fileNamePrefix(calculationViewType)}stock_production" +
            "_${dcs.distinct().sorted().joinToString("_")}" +
            "_${now.truncatedTo(ChronoUnit.SECONDS)}" + ".csv"
    }

    private fun fileNamePrefix(calculationViewType: CalculationViewType) = when (calculationViewType) {
        DAILY -> "daily"
        WEEKLY -> "weekly"
    }

    override suspend fun generateCsvFile(
        calculationViewType: CalculationViewType,
        calculationRequest: CalculationRequest,
        fileExportRequest: FileExportRequest,
    ): FileExportRequest = recordSuspended(
        csvGeneratorDurationTimer(calculationViewType, calculationRequest),
    ) {
        runCatching {
            logger.info(
                "Start csv file generation ID ${fileExportRequest.requestId} for: $calculationViewType - $calculationRequest",
            )

            val csvContent = generateCsvContent(calculationViewType, calculationRequest)
            val key = buildKey(fileExportRequest.requestId, calculationViewType, calculationRequest.dcCodes)

            val putObject = s3FileService.putObject(
                bucketName,
                key,
                csvContent,
                mapOf(
                    "dcs" to calculationRequest.dcCodes.joinToString(","),
                    "weeks" to calculationRequest.weeks.joinToString(","),
                    "requestId" to fileExportRequest.requestId.toString(),
                ),
            )

            val url = s3FileService.getPresignedGetUrl(
                bucketName,
                putObject.key,
                expirationDuration,
            )

            fileExportRequest.copy(
                status = ExportStatus.Completed,
                fileUrl = url.toString(),
            )
        }
    }.getOrElse {
        logger.error("Error generating CSV File for: $fileExportRequest", it)
        fileExportRequest.copy(
            status = ExportStatus.Failed,
        )
    }.let {
        requireNotNull(
            fileExportService.updateFileExportRequest(it),
        ) {
            logger.error("Trying to update missing file export request $it")
        }
    }

    private suspend fun generateCsvContent(calculationViewType: CalculationViewType, calculationRequest: CalculationRequest): String {
        val purchaseOrders = purchaseOrderCSVService.fetchPurchaseOrders(calculationRequest)

        return when (calculationViewType) {
            DAILY -> {
                val dailyCalculations = calculationRequest.weeks.flatMap {
                    calculationsService.getDailyCalculations(
                        calculationRequest.copy(weeks = listOf(it)),
                    ).calculationPage
                }
                calculationsCsvConverter.convertDailyToCsv(dailyCalculations, purchaseOrders)
            }

            WEEKLY -> {
                val weeklyCalculations = calculationRequest.weeks.flatMap {
                    calculationsService.getWeeklyCalculations(
                        calculationRequest.copy(weeks = listOf(it)),
                    ).calculationPage
                }
                calculationsCsvConverter.convertWeeklyToCsv(weeklyCalculations, purchaseOrders)
            }
        }
    }

    companion object : Logging {
        private val yearMonthFormatter = DateTimeFormatter.ofPattern("yyyy-MM")
            .withZone(ZoneOffset.UTC)

        private val expirationDuration = 5.minutes.toJavaDuration()
    }
}
