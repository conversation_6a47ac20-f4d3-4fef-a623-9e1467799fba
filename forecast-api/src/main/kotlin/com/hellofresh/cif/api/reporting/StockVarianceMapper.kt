package com.hellofresh.cif.api.reporting

import com.hellofresh.cif.api.calculation.generated.model.DayOfWeekResponse
import com.hellofresh.cif.api.calculation.generated.model.InboundVarianceValueResponse
import com.hellofresh.cif.api.calculation.generated.model.InventoryValueResponse
import com.hellofresh.cif.api.calculation.generated.model.PeriodResponse
import com.hellofresh.cif.api.calculation.generated.model.SkuResponse
import com.hellofresh.cif.api.calculation.generated.model.SkuStockVarianceResponse
import com.hellofresh.cif.api.calculation.generated.model.SkuStockVarianceResponseConsumption
import com.hellofresh.cif.api.calculation.generated.model.SkuStockVarianceResponseStock
import com.hellofresh.cif.api.calculation.generated.model.StockInboundVarianceReportingResponse
import com.hellofresh.cif.api.calculation.generated.model.StockLiveVariance
import com.hellofresh.cif.api.calculation.generated.model.StockLiveVarianceReportingResponse
import com.hellofresh.cif.api.calculation.generated.model.StockVarianceResponse
import com.hellofresh.cif.api.calculation.generated.model.StockVarianceResponseDc
import com.hellofresh.cif.api.calculation.generated.model.TotalCleardownVariancePercent
import com.hellofresh.cif.api.configuration.Sku
import com.hellofresh.cif.models.purchaseorder.StockInboundVarianceSku
import java.math.BigDecimal
import java.math.RoundingMode.HALF_UP

private val HUNDRED = BigDecimal("100")
fun mapStockVarianceResponse(dcCode: String, variances: List<StockVariance>): StockVarianceResponse {
    if (variances.isEmpty()) {
        return StockVarianceResponse(StockVarianceResponseDc(dcCode), emptyList())
    }
    val variancesResponse = variances.map { variance ->
        val consumption = SkuStockVarianceResponseConsumption(
            calculated = variance.calculatedConsumption,
            theoretical = variance.weekConsumption,
            missing = variance.missing,
        )
        val skuStockVarianceResponseStock = SkuStockVarianceResponseStock(
            opening = variance.openingStockPreviousCleardown,
            closing = variance.openingStockLatestCleardown,
            waste = variance.weekUnusable,
        )
        SkuStockVarianceResponse(
            sku = toSkuResponse(variance.sku),
            consumption = consumption,
            inbound = variance.weekActualInbound,
            stock = skuStockVarianceResponseStock,
            skuCategories = variance.skuCategory,
        )
    }
    val anyVariance = variances.first()
    val reportingPeriodResponse =
        PeriodResponse(anyVariance.previousCleardownWeek.value, anyVariance.currentCleardownWeek.value)
    val dcDetailsResponse =
        StockVarianceResponseDc(dcCode, DayOfWeekResponse.valueOf(variances.first().cleardownDay.name))
    return StockVarianceResponse(dc = dcDetailsResponse, skus = variancesResponse, period = reportingPeriodResponse)
}

fun mapLiveStockVarianceResponse(
    liveStockReport: List<StockLiveVarianceSku>
): StockLiveVarianceReportingResponse = StockLiveVarianceReportingResponse(
    stockLiveVariances = liveStockReport.map {
        StockLiveVariance(
            skuId = it.skuId,
            skuCode = it.skuCode,
            skuName = it.skuName,
            skuCategories = it.skuCategories,
            data = it.dailyInventoryVarianceData.map { v ->
                InventoryValueResponse(
                    date = v.date,
                    closingStock = v.cleardownClosingStock.getValue(),
                    liveClosingStock = v.liveClosingStock.getValue(),
                    inventoryQty = v.inventoryQty?.getValue(),
                )
            }.sortedBy { i -> i.date },
            productionCleardownVariancePercent = it.cleardownVariance,
            liveCleardownVariancePercent = it.liveVariance,
        )
    }.sortedBy { it.skuId },
    totalCleardownVariancePercent = calculateTotalCleardownVariancePercent(liveStockReport),
)

fun mapInboundVarianceResponse(
    inboundVarianceReport: List<StockInboundVarianceSku>
): StockInboundVarianceReportingResponse = StockInboundVarianceReportingResponse(
    inboundVariances = inboundVarianceReport.map {
        InboundVarianceValueResponse(
            poRef = it.poRef,
            asnId = it.asnId,
            supplierId = it.supplierId,
            supplierName = it.supplierName,
            skuId = it.skuId,
            skuName = it.skuName,
            skuCode = it.skuCode,
            skuCategories = it.skuCategory,
            poVsInbound = it.poVsInbound?.getValue()?.toLong(),
            asnVsInbound = it.asnVsInbound?.getValue()?.toLong(),
        )
    }.sortedBy { it.skuName },
    poVsInboundAverage = averagePercent(inboundVarianceReport) { it.poVsInbound?.getValue()?.toLong() },
    asnVsInboundAverage = averagePercent(inboundVarianceReport) { it.asnVsInbound?.getValue()?.toLong() },
)

private fun toMaxHundredPercent(variancePercent: BigDecimal) =
    if (variancePercent > HUNDRED) HUNDRED else variancePercent

private fun calculateTotalCleardownVariancePercent(stockLiveVarianceSkus: List<StockLiveVarianceSku>): TotalCleardownVariancePercent {
    val sumOfProductionCleardownVariance = BigDecimal(
        stockLiveVarianceSkus.sumOf { it.cleardownVariance },
    )

    val sumOfLiveCleardownVariance = BigDecimal(
        stockLiveVarianceSkus.sumOf { it.liveVariance },
    )

    val stockLiveVarianceSkusSize = BigDecimal(stockLiveVarianceSkus.size)
    val averageProductionCleardownVariancePercent = if (stockLiveVarianceSkusSize != BigDecimal.ZERO) {
        sumOfProductionCleardownVariance.divide(stockLiveVarianceSkusSize, 0, HALF_UP)
    } else {
        HUNDRED
    }

    val averageLiveCleardownVariancePercent = if (stockLiveVarianceSkusSize != BigDecimal(0)) {
        sumOfLiveCleardownVariance.divide(stockLiveVarianceSkusSize, 0, HALF_UP)
    } else {
        BigDecimal("100")
    }
    return TotalCleardownVariancePercent(
        toMaxHundredPercent(averageProductionCleardownVariancePercent).toLong(),
        toMaxHundredPercent(averageLiveCleardownVariancePercent).toLong(),
    )
}

private fun averagePercent(
    stockInboundVarianceSkus: List<StockInboundVarianceSku>,
    varianceSelector: (StockInboundVarianceSku) -> Long?
): Long? {
    val filteredStockInboundVariances = stockInboundVarianceSkus.mapNotNull { varianceSelector(it) }

    if (filteredStockInboundVariances.isEmpty()) {
        return null
    }
    val sumOfInboundVariances = BigDecimal(
        filteredStockInboundVariances.sum(),
    )
    val stockInboundVarianceSkusSize = BigDecimal(filteredStockInboundVariances.size)
    val averagePercent = if (stockInboundVarianceSkusSize != BigDecimal.ZERO) {
        sumOfInboundVariances.divide(stockInboundVarianceSkusSize, 0, HALF_UP)
    } else {
        HUNDRED
    }
    return averagePercent.toLong()
}

private fun toSkuResponse(sku: Sku) = SkuResponse(sku.skuCode, sku.skuId, sku.skuName)
