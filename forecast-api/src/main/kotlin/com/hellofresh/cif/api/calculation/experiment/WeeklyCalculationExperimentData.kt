package com.hellofresh.cif.api.calculation.experiment

import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import java.math.BigDecimal
import java.util.UUID

data class WeeklyCalculationExperimentData(
    val dcCode: String,
    val skuId: UUID,
    val weeks: Set<String>,
    val calculatorMode: CalculatorMode,
    val experiments: Map<DcWeek, BigDecimal>
) {
    fun calculationExperiments(dcConfig: DistributionCenterConfiguration) = experiments.mapKeys {
        CalculationKey(
            skuId,
            dcCode,
            it.key.getLastDateInDcWeek(dcConfig.productionStart, dcConfig.zoneId),
        )
    }
}
