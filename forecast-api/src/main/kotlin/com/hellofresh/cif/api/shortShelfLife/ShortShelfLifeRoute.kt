package com.hellofresh.cif.api.shortShelfLife

import com.hellofresh.cif.api.calculation.generated.model.ShortShelfLifeUpdateRequest
import com.hellofresh.cif.api.calculation.generated.model.SslSqrSimulationRequest
import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getLoggedInUserInfo
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeSimulation
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeSimulationDetail
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.authenticate
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.routing
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

private const val DC_CODE_PARAM = "dcCode"
private const val SKU_ID_PARAM = "skuId"

fun Routing.shortShelfLife(
    shortShelfLifeService: ShortShelfLifeService,
    timeoutInMillis: Long,
) = authenticate {
    get("/sqr-short-shelf-life/{$DC_CODE_PARAM}") {
        withTimeout(timeoutInMillis) {
            kotlin.runCatching {
                val dcCode = call.parameters.getOrThrow("dcCode").uppercase()
                val weeks = call.parameters.getAllOrThrow("week").map(::DcWeek).toSet()
                shortShelfLifeService.getShortShelfLife(dcCode, weeks)
            }.onFailure {
                call.handleResponseError(it)
            }.onSuccess { success ->
                call.respond(HttpStatusCode.OK, success)
            }
        }
    }

    post("/sqr-short-shelf-life/simulation/{$DC_CODE_PARAM}/{$SKU_ID_PARAM}") {
        withTimeout(timeoutInMillis) {
            kotlin.runCatching {
                val dcCode = call.parameters.getOrThrow(DC_CODE_PARAM)
                val skuId = UUID.fromString(call.parameters.getOrThrow(SKU_ID_PARAM))
                val sslSimulationInputs = getShortShelfLifeInputs(call)
                shortShelfLifeService.simulateShortShelfLife(
                    dcCode,
                    skuId,
                    sslSimulationInputs,
                ).shortShelfLifeSimulationResponse
            }.onFailure {
                call.handleResponseError(it)
            }.onSuccess { success ->
                call.respond(HttpStatusCode.OK, success)
            }
        }
    }

    put("/sqr-short-shelf-life/{$DC_CODE_PARAM}/{$SKU_ID_PARAM}") {
        withTimeout(timeoutInMillis) {
            kotlin.runCatching {
                val dcCode = call.parameters.getOrThrow(DC_CODE_PARAM).uppercase()
                val skuId = UUID.fromString(call.parameters.getOrThrow(SKU_ID_PARAM))
                val user = call.getLoggedInUserInfo()
                val request = call.receive<ShortShelfLifeUpdateRequest>()

                shortShelfLifeService.updateShortShelfLife(dcCode, skuId, request, user)
            }.onFailure {
                call.handleResponseError(it)
            }.onSuccess { success ->
                call.respond(HttpStatusCode.OK, success)
            }
        }
    }
}

private suspend fun getShortShelfLifeInputs(applicationCall: ApplicationCall): List<ShortShelfLifeSimulationDetail> =
    applicationCall.receive<SslSqrSimulationRequest>().data?.map { result ->
        ShortShelfLifeSimulationDetail(
            week = result.week,
            touchlessOrderingEnabled = result.touchlessOrdering,
            sqrs = result.sqrs.map { sqr ->
                ShortShelfLifeSimulation(
                    date = sqr.date,
                    stockUpdates = sqr.stockUpdates,
                    bufferPercentage = sqr.bufferPercentage,
                    bufferAdditional = sqr.bufferAdditional,
                    uom = sqr.uom,
                )
            },
        )
    } ?: emptyList()

fun shortShelfLifeModule(
    sslService: ShortShelfLifeService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        shortShelfLife(
            sslService,
            timeout.inWholeMilliseconds,
        )
            .also {
                configureSerialization(it)
            }
    }
}
