package com.hellofresh.cif.api.to

import com.hellofresh.cif.api.calculation.generated.model.Delivery
import com.hellofresh.cif.api.calculation.generated.model.TransferOrderResponseSkuItem
import com.hellofresh.cif.api.calculation.generated.model.TransferOrderStatus
import com.hellofresh.cif.api.calculation.generated.model.TransferOrdersDetailResponse
import com.hellofresh.cif.api.calculation.generated.model.TransferOrdersDetailResponseTransferOrdersInner
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.transferorder.model.DeliveryInfoStatus
import com.hellofresh.cif.transferorder.model.TransferOrder
import com.hellofresh.cif.transferorder.model.TransferOrderSku
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_CANCELLED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_DELETED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_DELIVERED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_ORDERED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.STATE_RESERVED
import com.hellofresh.cif.transferorder.model.TransferOrderStatus.UNRECOGNIZED
import java.time.ZoneId

object TransferOrderResponseMapper {
    fun mapToTransferOrderDetailResponse(
        transferOrders: List<TransferOrder>,
        dcConfigService: DcConfigService
    ): TransferOrdersDetailResponse {
        val inner = transferOrders.map { to ->
            val distributionCenter = requireNotNull(
                dcConfigService.dcConfigurations[to.sourceDc]
            ) { "Dc Config Must exist when returning transfer orders: ${to.sourceDc}" }
            val skus = to.transferOrderSkus.map { mapToTransferOrderResponseSkuItem(it, distributionCenter.zoneId) }

            TransferOrdersDetailResponseTransferOrdersInner(
                transferOrderNumber = to.transferOrderNumber,
                sourceDcCode = to.sourceDc,
                destinationDcCode = to.destinationDc,
                status = mapToTransferOrderStatus(to.status),
                inboundStartTime = to.expectedDeliveryTimeslot?.startTime
                    ?.toOffsetDateTime(),
                inboundEndTime = to.expectedDeliveryTimeslot?.endTime
                    ?.toOffsetDateTime(),
                week = to.week,
                skus = skus
            )
        }

        return TransferOrdersDetailResponse(inner)
    }

    private fun mapToTransferOrderResponseSkuItem(
        sku: TransferOrderSku,
        dcZonedId: ZoneId
    ): TransferOrderResponseSkuItem {
        val deliveries = sku.deliveries.map {
            Delivery(
                id = it.id,
                state = mapToDeliveryState(it.state),
                quantity = it.quantity.getValue().toInt(),
                time = it.deliveryTime.atZone(dcZonedId).toOffsetDateTime(),
            )
        }

        return TransferOrderResponseSkuItem(
            skuId = sku.skuId,
            supplierId = sku.supplierId,
            supplierName = sku.supplierName,
            deliveries = deliveries,
        )
    }

    private fun mapToTransferOrderStatus(status: com.hellofresh.cif.transferorder.model.TransferOrderStatus) =
        when (status) {
            STATE_RESERVED -> TransferOrderStatus.RESERVED
            STATE_ORDERED -> TransferOrderStatus.ORDERED
            STATE_DELIVERED -> TransferOrderStatus.DELIVERED
            STATE_CANCELLED -> TransferOrderStatus.CANCELLED
            STATE_DELETED -> TransferOrderStatus.DELETED
            UNRECOGNIZED -> error("Unrecognized transfer order status: $status")
        }

    private fun mapToDeliveryState(state: DeliveryInfoStatus): Delivery.State = when (state) {
        DeliveryInfoStatus.OPEN -> Delivery.State.OPEN
        DeliveryInfoStatus.CLOSED -> Delivery.State.CLOSED
    }
}
