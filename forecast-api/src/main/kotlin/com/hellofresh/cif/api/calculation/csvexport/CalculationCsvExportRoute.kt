package com.hellofresh.cif.api.calculation.csvexport

import com.hellofresh.cif.api.calculation.AllPages
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.csvexport.CalculationViewType.DAILY
import com.hellofresh.cif.api.calculation.csvexport.CalculationViewType.WEEKLY
import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.fileexport.mapToFileExportResponse
import com.hellofresh.cif.api.ktor.configureSerialization
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun calculationCsvExportModule(
    calculationCsvExportService: CalculationCsvExportService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        calculationCsvExport(
            calculationCsvExportService,
            timeout.inWholeMilliseconds,
        ).also {
            configureSerialization(it)
        }
    }
}

fun Routing.calculationCsvExport(
    csvExportService: CalculationCsvExportService,
    timeoutInMillis: Long,
) =
    authenticate {
        route("/export") {
            get("/calculation/dailyView") {
                withTimeout(timeoutInMillis) {
                    kotlin.runCatching {
                        kotlin.runCatching {
                            mapToFileExportResponse(
                                csvExportService.createCsvExport(DAILY, getCalculationRequest(call)),
                            )
                        }.onSuccess { result ->
                            call.respond(HttpStatusCode.OK, result)
                        }.onFailure {
                            call.handleResponseError(it.cause?.cause ?: it)
                        }
                    }.onFailure {
                        call.handleResponseError(it.cause?.cause ?: it)
                    }
                }
            }
            get("/calculation/weeklyView") {
                withTimeout(timeoutInMillis) {
                    kotlin.runCatching {
                        kotlin.runCatching {
                            mapToFileExportResponse(
                                csvExportService.createCsvExport(WEEKLY, getCalculationRequest(call)),
                            )
                        }.onSuccess { result ->
                            call.respond(HttpStatusCode.OK, result)
                        }.onFailure {
                            call.handleResponseError(it.cause?.cause ?: it)
                        }
                    }.onFailure {
                        call.handleResponseError(it.cause?.cause ?: it)
                    }
                }
            }
        }
    }

private fun getCalculationRequest(call: ApplicationCall) =
    CalculationRequest.from(call.parameters).copy(pageRequest = AllPages)
