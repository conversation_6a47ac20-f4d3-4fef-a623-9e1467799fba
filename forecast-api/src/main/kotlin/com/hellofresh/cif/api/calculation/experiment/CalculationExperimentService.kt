package com.hellofresh.cif.api.calculation.experiment

import com.hellofresh.cif.api.calculation.generated.model.WeeklyCalculationExperimentResponse
import com.hellofresh.cif.api.sku.SkuQuantityMapper
import com.hellofresh.cif.api.stockupdate.StockUpdateCalculationService
import com.hellofresh.cif.api.stockupdate.StockUpdateResults
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService

class CalculationExperimentService(
    private val stockUpdateCalculationService: StockUpdateCalculationService,
    private val dcConfigService: DcConfigService,
) {
    suspend fun getCalculationExperiment(dailyCalculationExperimentData: DailyCalculationExperimentData) =
        stockUpdateCalculationService.runStockUpdatesWithoutUom(
            dailyCalculationExperimentData.dcCode,
            dailyCalculationExperimentData.skuId,
            dailyCalculationExperimentData.weeks,
            dailyCalculationExperimentData.calculatorMode,
            dailyCalculationExperimentData.calculationExperiments(),
        )

    suspend fun getWeeklyCalculationExperiment(weeklyCalculationExperimentData: WeeklyCalculationExperimentData): List<WeeklyCalculationExperimentResponse> {
        val experiments = dcConfigService.dcConfigurations[weeklyCalculationExperimentData.dcCode]
            ?.let { weeklyCalculationExperimentData.calculationExperiments(it) }
            ?: emptyMap()

        val results = stockUpdateCalculationService.runStockUpdatesWithoutUom(
            weeklyCalculationExperimentData.dcCode,
            weeklyCalculationExperimentData.skuId,
            weeklyCalculationExperimentData.weeks,
            weeklyCalculationExperimentData.calculatorMode,
            experiments,
        )
        return convertToWeeklyCalculationResults(results)
    }

    private fun convertToWeeklyCalculationResults(
        results: StockUpdateResults
    ): List<WeeklyCalculationExperimentResponse> {
        require(results.calculations.isNotEmpty()) {
            ("daily calculation results set cannot be empty")
        }
        val weeklyStockUpdates = results.stockUpdates.mapNotNull { (key, value) ->
            dcConfigService.dcConfigurations[key.dcCode]?.let {
                DcWeek(key.date, it.productionStart) to value
            }
        }.toMap()

        return results.calculations
            .groupBy { it.cskuId to it.productionWeek }
            .map { (_, dayCalculationResults) ->
                val sortedDayCalculationResults = dayCalculationResults.sortedBy { it.date }
                val firstDayCalculationResult = sortedDayCalculationResults.first()
                val openingStock = firstDayCalculationResult.openingStock
                val closingStock = sortedDayCalculationResults.last().closingStock

                dayCalculationResults.map {
                    WeeklyCalculationExperimentResponse(
                        experiment = weeklyStockUpdates[
                            DcWeek(
                                dayCalculationResults.first().productionWeek,
                            ),
                        ]?.getValue()?.toInt(),
                        uom = SkuQuantityMapper.mapSkuUOMToUomEnum(it.uom),
                        incoming = it.expectedInbound.getValue().toInt(),
                        inbound = it.actualInbound.getValue().toInt(),
                        unusable = it.unusable.getValue().toInt(),
                        consumption = it.demanded.getValue().toInt(),
                        actualConsumption = it.actualConsumption.getValue().toInt(),
                        closing = closingStock.getValue().toInt(),
                        opening = openingStock.getValue().toInt(),
                        week = dayCalculationResults.first().productionWeek,
                    )
                }.reduce { agg, item ->
                    agg.copy(
                        experiment = weeklyStockUpdates[
                            DcWeek(
                                dayCalculationResults.first().productionWeek,
                            ),
                        ]?.getValue()?.toInt(),
                        incoming = agg.incoming + item.incoming,
                        inbound = agg.inbound + item.inbound,
                        unusable = agg.unusable + item.unusable,
                        consumption = agg.consumption + item.consumption,
                        actualConsumption = agg.actualConsumption.plus(item.actualConsumption),
                    )
                }
            }
    }
}
