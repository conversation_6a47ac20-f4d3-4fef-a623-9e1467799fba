package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.InventoryRefreshType.CLEARDOWN
import com.hellofresh.cif.api.calculation.InventoryRefreshType.LIVE
import com.hellofresh.cif.api.getAllOrDefault
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getOrDefault
import com.hellofresh.cif.api.ktor.RequestValidationException
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import java.util.UUID

const val PO_DUE_IN_MIN = -7
const val PO_DUE_IN_MAX = 10

interface CalculationFiltersParams {
    val dcCodes: List<String>
    val weeks: List<String>
    val skuCodes: List<String>
    val skuCategories: List<String>
    val additionalFilters: Set<AdditionalFilter>
    val consumptionDaysAhead: Int
    val inventoryRefreshType: InventoryRefreshType
    val locationInBox: List<String>
    val supplierIds: List<UUID>
    val activeSupplierNames: Set<String>
    val poDueInMin: Int?
    val poDueInMax: Int?
    val closingStockLessThanOrEqual: Long?
}

data class CalculationFilterRequest(
    override val dcCodes: List<String>,
    override val weeks: List<String>,
    override val skuCodes: List<String> = emptyList(),
    override val skuCategories: List<String> = emptyList(),
    override val additionalFilters: Set<AdditionalFilter> = emptySet(),
    override val consumptionDaysAhead: Int,
    override val inventoryRefreshType: InventoryRefreshType = CLEARDOWN,
    override val locationInBox: List<String> = emptyList(),
    override val supplierIds: List<UUID> = emptyList(),
    override val activeSupplierNames: Set<String> = emptySet(),
    override val poDueInMin: Int? = null,
    override val poDueInMax: Int? = null,
    override val closingStockLessThanOrEqual: Long? = null,
) : CalculationFiltersParams {

    companion object {

        fun from(parameters: Parameters) = CalculationFilterRequest(
            dcCodes = parameters.getAllOrThrow("dcCode"),
            weeks = parameters.getAllOrThrow("weeks"),
            skuCodes = parameters.getAllOrDefault("skuCode"),
            skuCategories = parameters.getAllOrDefault("skuCategory"),
            additionalFilters = parameters.getAllOrDefault("additionalFilters")
                .map { AdditionalFilter.valueOf(it.trim().uppercase()) }
                .toSet(),
            consumptionDaysAhead = parseConsumptionDaysAhead(parameters),
            inventoryRefreshType = parseInventoryRefreshType(parameters),
            locationInBox = parameters.getAllOrDefault("locationInBox"),
            supplierIds = parameters.getAllOrDefault("supplierId").map { UUID.fromString(it) },
            activeSupplierNames = parameters.getAllOrDefault("activeSupplierName").map { it }.toSet(),
            poDueInMin = parameters["poDueInGreaterThanOrEqual"]?.toInt()?.validateRange(
                "poDueInGreaterThanOrEqual",
                PO_DUE_IN_MIN,
                PO_DUE_IN_MAX,
            ),
            poDueInMax = parameters["poDueInLessThanOrEqual"]?.toInt()?.validateRange(
                "poDueInLessThanOrEqual",
                PO_DUE_IN_MIN,
                PO_DUE_IN_MAX,
            ),
            closingStockLessThanOrEqual = parameters["closingStockLessThan"]?.toLong(),
        )

        internal fun parseConsumptionDaysAhead(parameters: Parameters): Int {
            val consumptionDaysAhead = parameters.getOrDefault("consumptionDaysAhead", "0").toInt()
            return when {
                consumptionDaysAhead < 0 ->
                    throw RequestValidationException(
                        "Please enter value greater than zero",
                        HttpStatusCode.BadRequest,
                    )

                consumptionDaysAhead > 1 ->
                    throw RequestValidationException(
                        "Calculations for $consumptionDaysAhead days ahead is not yet implemented",
                        HttpStatusCode.NotImplemented,
                    )

                else -> consumptionDaysAhead
            }
        }

        internal fun parseInventoryRefreshType(parameters: Parameters) =
            when (val inventoryRefreshTypeParam = parameters.getOrDefault("inventoryRefreshType", "cleardown")) {
                "cleardown" -> CLEARDOWN
                "live" -> LIVE
                else -> error("unknown inventory refresh type: $inventoryRefreshTypeParam")
            }
    }
}

fun Int.validateRange(name: String, min: Int, max: Int): Int {
    if (this < min) {
        throw RequestValidationException(name, reason = "value should be greater than or equal to $min")
    }

    if (this > max) {
        throw RequestValidationException(name, reason = "value should be less than or equal to $max")
    }

    return this
}
