package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.generated.model.ActiveSupplierResponse
import com.hellofresh.cif.api.calculation.generated.model.CalculationFiltersResponse
import com.hellofresh.cif.api.calculation.generated.model.DailyCalculationsResponse
import com.hellofresh.cif.api.calculation.generated.model.SkuResponse
import com.hellofresh.cif.api.calculation.generated.model.SupplierResponse
import com.hellofresh.cif.api.calculation.generated.model.WeeklyCalculationsResponse
import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.ktor.customJackson
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.authenticate
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun Routing.calculation(
    calculationsService: CalculationsService,
    calculationResponseMapper: CalculationResponseMapper,
    timeoutInMillis: Long
) = authenticate {
    route("/calculation") {
        get("/dailyView") {
            handleCalculations(this.call, timeoutInMillis) { request ->
                val daily = calculationsService.getDailyCalculations(request)
                DailyCalculationsResponse(
                    calculations = daily.calculationPage.map {
                        calculationResponseMapper.toDailyCalculationResponse(
                            it,
                        )
                    },
                    page = request.pageRequest.number,
                    totalPages = daily.totalPages,
                    totalSkusAtRiskCount = daily.totalSkuAtRiskCount,
                    totalSkusCount = daily.totalSkuCount,
                )
            }
        }
        get("/weeklyView") {
            handleCalculations(this.call, timeoutInMillis) { request ->
                val weekly = calculationsService.getWeeklyCalculations(request)
                WeeklyCalculationsResponse(
                    calculations = weekly.calculationPage.map {
                        calculationResponseMapper.toWeeklyCalculationResponse(it)
                    },
                    page = request.pageRequest.number,
                    totalPages = weekly.totalPages,
                    totalSkusAtRiskCount = weekly.totalSkuAtRiskCount,
                    totalSkusCount = weekly.totalSkuCount,
                )
            }
        }
        get("/filters") {
            handleCalculationFilters(this.call, timeoutInMillis) { request ->
                val calculationFiltersData = calculationsService.getFilterCalculations(request)
                CalculationFiltersResponse(
                    skuSet = calculationFiltersData.skuSet.map { SkuResponse(it.skuCode, it.skuId, it.skuName) },
                    ingredientCategories = calculationFiltersData.ingredientCategories.toList(),
                    locationInBox = calculationFiltersData.locationInBox.toList(),
                    suppliers = calculationFiltersData.suppliers.map {
                        SupplierResponse(it.id, it.name)
                    }.sortedBy { it.name },
                    activeSuppliers = calculationFiltersData.activeSuppliers.map {
                        ActiveSupplierResponse(it.name)
                    }
                        .sortedBy { it.name },
                )
            }
        }
    }
}

fun calculationRoutingModule(
    calculationsService: CalculationsService,
    calculationResponseMapper: CalculationResponseMapper,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        calculation(
            calculationsService = calculationsService,
            calculationResponseMapper = calculationResponseMapper,
            timeoutInMillis = timeout.inWholeMilliseconds,
        ).install(ContentNegotiation) {
            customJackson()
        }
    }
}

private suspend fun handleCalculations(
    call: ApplicationCall,
    timeoutInMillis: Long,
    fetch: suspend (request: CalculationRequest) -> Any
) {
    handle(call, timeoutInMillis) {
        val calculationRequest = CalculationRequest.from(call.parameters)
        fetch(calculationRequest)
    }
}

private suspend fun handleCalculationFilters(
    call: ApplicationCall,
    timeoutInMillis: Long,
    fetch: suspend (request: CalculationFilterRequest) -> Any
) {
    handle(call, timeoutInMillis) {
        fetch(CalculationFilterRequest.from(call.parameters))
    }
}

private suspend fun handle(
    call: ApplicationCall,
    timeoutInMillis: Long,
    block: suspend (call: ApplicationCall) -> Any
) {
    withTimeout(timeoutInMillis) {
        runCatching {
            block(call)
        }.onSuccess {
            call.respond(HttpStatusCode.OK, it)
        }.onFailure {
            call.handleResponseError(it)
        }
    }
}

enum class AdditionalFilter {
    WITH_INVENTORY_SHORTAGE,
    WITH_INBOUND_SHORTAGE,
    WITH_CONSUMPTION_ONLY,
    WITH_NO_CONSUMPTION_ONLY,
    WITH_PROJECTED_WASTE,
    WITH_SKU_SUBBED_IN_ONLY,
    WITH_SKU_SUBBED_OUT_ONLY,
    WITH_NET_NEEDS_ONLY;

    fun filterName() = this.name.lowercase()
}
