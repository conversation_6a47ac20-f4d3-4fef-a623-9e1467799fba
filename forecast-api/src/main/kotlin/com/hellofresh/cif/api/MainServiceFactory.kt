package com.hellofresh.cif.api

import com.hellofresh.cif.api.calculation.CalculationsService
import com.hellofresh.cif.api.calculation.csvexport.CalculationCsvExportService
import com.hellofresh.cif.api.calculation.csvexport.CalculationCsvGeneratorImpl
import com.hellofresh.cif.api.calculation.csvexport.CalculationsCsvConverter
import com.hellofresh.cif.api.calculation.csvexport.PurchaseOrderCSVService
import com.hellofresh.cif.api.calculation.db.CalculationsRepositoryImpl
import com.hellofresh.cif.api.calculation.stockupdate.CalculationsPendingStockUpdateService
import com.hellofresh.cif.api.fileexport.FileExportService
import com.hellofresh.cif.api.note.NoteRepositoryImpl
import com.hellofresh.cif.api.note.NoteService
import com.hellofresh.cif.api.shortShelfLife.ShortShelfLifeService
import com.hellofresh.cif.api.shortShelfLife.repository.ShortShelfLifeRepositoryImpl
import com.hellofresh.cif.api.stockupdate.StockUpdateApiService
import com.hellofresh.cif.api.stockupdate.StockUpdateCalculationService
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.s3.S3FileService
import com.hellofresh.cif.shutdown.shutdownNeeded
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import io.micrometer.core.instrument.MeterRegistry

@Suppress("LongParameterList")
fun createCalculationService(
    meterRegistry: MeterRegistry,
    dcConfigService: DcConfigService,
    readOnlyDslContext: MetricsDSLContext,
    usableInventoryEvaluator: UsableInventoryEvaluator,
    stockUpdateApiService: StockUpdateApiService,
    statsigFeatureFlagClient: StatsigFeatureFlagClient,
) =
    CalculationsService(
        meterRegistry,
        dcConfigService,
        usableInventoryEvaluator,
        CalculationsPendingStockUpdateService(
            dcConfigService,
            stockUpdateApiService,
            statsigFeatureFlagClient,
        ),
        CalculationsRepositoryImpl(
            readOnlyDslContext,
            isProdCalculation = true,
        ),
        CalculationsRepositoryImpl(
            readOnlyDslContext,
            isProdCalculation = false,
        ),
        statsigFeatureFlagClient,
    )

fun calculationsCsvConverter(dcConfigService: DcConfigService, statsigFeatureFlagClient: StatsigFeatureFlagClient) =
    CalculationsCsvConverter(
        dcConfigService,
        statsigFeatureFlagClient,
        ConfigurationLoader.getSet("csv.brands.consumption.markets"),
    )

@Suppress("LongParameterList")
fun calculationCsvFileService(
    fileExportService: FileExportService,
    calculationsService: CalculationsService,
    purchaseOrderRepository: PurchaseOrderRepository,
    dcConfigService: DcConfigService,
    calculationsCsvConverter: CalculationsCsvConverter,
    meterRegistry: MeterRegistry
) =
    CalculationCsvExportService(
        fileExportService,
        CalculationCsvGeneratorImpl(
            fileExportService,
            getS3FileService(),
            calculationsService,
            PurchaseOrderCSVService(purchaseOrderRepository, dcConfigService),
            calculationsCsvConverter,
            meterRegistry,
            getS3BucketSuffix(),
        ),
        ConfigurationLoader.getStringOrFail("calculation.csv.parallelism").toInt(),
    )

fun getS3BucketSuffix() = if (ConfigurationLoader.isLive()) "-live" else "-staging"

fun getS3FileService() =
    shutdownNeeded {
        if (ConfigurationLoader.isLocal()) {
            S3FileService(ConfigurationLoader.getStringOrFail("aws.s3.host"))
        } else {
            S3FileService()
        }
    }

fun noteService(readWriteDslContext: MetricsDSLContext, readOnlyDslContext: MetricsDSLContext) =
    NoteService(
        NoteRepositoryImpl(
            readWriteDslContext,
            readOnlyDslContext,
        ),
    )

@Suppress("LongParameterList")
fun shortShelfLifeService(
    readWriteDslContext: MetricsDSLContext,
    readOnlyDslContext: MetricsDSLContext,
    stockUpdateApiService: StockUpdateApiService,
    dcConfigService: DcConfigService,
    stockUpdateCalculationService: StockUpdateCalculationService,
    sqrShortShelfLifeConfRepository: SQRShortShelfLifeConfRepository,
    stockUpdateService: StockUpdateService,
) = ShortShelfLifeService(
    shortShelfLifeRepositoryImpl = ShortShelfLifeRepositoryImpl(
        readWriteDslContext,
        readOnlyDslContext,
    ),
    stockUpdateApiService = stockUpdateApiService,
    dcConfigService = dcConfigService,
    stockUpdateCalculationService = stockUpdateCalculationService,
    sqrShortShelfLifeConfRepository = sqrShortShelfLifeConfRepository,
    stockUpdateService = stockUpdateService,
)
