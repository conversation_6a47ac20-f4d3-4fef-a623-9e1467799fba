package com.hellofresh.cif.api.note

import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.util.UUID

interface NoteRepository {
    @Suppress("LongParameterList")
    suspend fun createNote(
        skuId: UUID,
        authorEmail: String,
        authorName: String?,
        dcCodes: Set<String>,
        dcWeeks: Set<DcWeek>,
        text: String
    ): Note

    @Suppress("LongParameterList")
    suspend fun updateNote(
        id: UUID,
        authorName: String?,
        authorEmail: String,
        dcWeeks: Set<DcWeek>,
        text: String
    ): Note

    suspend fun deleteNote(
        id: UUID,
        authorName: String?,
        authorEmail: String
    ): Int

    suspend fun getNotes(findNotesRequest: FindNotesRequest): List<Note>
}
