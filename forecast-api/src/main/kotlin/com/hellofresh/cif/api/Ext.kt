package com.hellofresh.cif.api

import com.hellofresh.cif.api.ktor.RequestValidationException
import com.hellofresh.cif.api.user.LoggedInUserInfo
import com.hellofresh.cif.api.user.getLoggedInUser
import io.ktor.http.Parameters
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.principal

fun Parameters.getOrThrow(name: String) = get(name)
    ?.let { it.ifBlank { throw RequestValidationException(name) } }
    ?: throw RequestValidationException(name)

fun Parameters.getOrDefault(name: String, default: String) = get(name)
    ?.let { it.ifBlank { default } }
    ?: default

fun Parameters.getAllOrDefault(name: String, default: List<String> = emptyList()) = getAll(name)
    ?.ifEmpty { default }
    ?: default

fun Parameters.getAllOrThrow(name: String) = getAll(name)
    ?.ifEmpty { throw RequestValidationException(name) }
    ?: throw RequestValidationException(name)

fun ApplicationCall.getLoggedInUserInfo(): LoggedInUserInfo {
    val jwt = this.principal<JWTPrincipal>()
    requireNotNull(jwt) { "JWT Token doesn't exist" }
    return jwt.getLoggedInUser()
}
