package com.hellofresh.cif.api.ingredientdepletion

import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.SkuUOM
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL

class IngredientDepletionRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
) : IngredientDepletionRepository {

    override suspend fun getIngredientDepletionData(
        dcCodes: List<String>,
        week: String,
        brand: String
    ): IngredientDepletionResponseDto =
        metricsDSLContext.withTagName("fetch-ingredient-depletion-summary")
            .select(
                CALCULATION.DC_CODE,
                SKU_SPECIFICATION_VIEW.CODE,
                SKU_SPECIFICATION_VIEW.NAME,
                SKU_SPECIFICATION_VIEW.CATEGORY,
                SKU_SPECIFICATION_VIEW.UOM,
            )
            .from(CALCULATION)
            .join(SKU_SPECIFICATION_VIEW).on(
                CALCULATION.CSKU_ID.eq(SKU_SPECIFICATION_VIEW.ID),
            )
            .where(
                CALCULATION.DC_CODE.`in`(dcCodes)
                    .and(CALCULATION.PRODUCTION_WEEK.eq(week))
                    .and(
                        DSL.value(brand).eq(DSL.any(SKU_SPECIFICATION_VIEW.BRANDS)),
                    ),
            )
            .fetchAsync()
            .thenApply { result ->
                IngredientDepletionResponseDto(
                    ingredientSummary = result.map { record ->
                        IngredientSummaryDto(
                            site = record.get(CALCULATION.DC_CODE, String::class.java),
                            skuCode = record.get(SKU_SPECIFICATION_VIEW.CODE, String::class.java),
                            skuName = record.get(SKU_SPECIFICATION_VIEW.NAME, String::class.java),
                            skuCategory = record.get(SKU_SPECIFICATION_VIEW.CATEGORY),
                            uom = mapUomToSkuUOM(record.get(SKU_SPECIFICATION_VIEW.UOM)),
                        )
                    },
                )
            }.await()

    companion object {
        fun mapUomToSkuUOM(uom: Uom): SkuUOM =
            when (uom) {
                Uom.UOM_UNSPECIFIED -> SkuUOM.UOM_UNSPECIFIED
                Uom.UOM_UNIT -> SkuUOM.UOM_UNIT
                Uom.UOM_KG -> SkuUOM.UOM_KG
                Uom.UOM_LBS -> SkuUOM.UOM_LBS
                Uom.UOM_GAL -> SkuUOM.UOM_GAL
                Uom.UOM_LITRE -> SkuUOM.UOM_LITRE
                Uom.UOM_OZ -> SkuUOM.UOM_OZ
                Uom.UOM_UNRECOGNIZED -> SkuUOM.UOM_UNRECOGNIZED
            }
    }
}
