package com.hellofresh.cif.api.ktor

import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import java.time.Duration
import java.util.Date

private const val TEN_MINUTES = 10L
fun jwtToken(
    secret: String,
    issuer: String,
    userEmail: String,
    userName: String? = null,
    expireDuration: Duration = Duration.ofMinutes(TEN_MINUTES)
): String = JWT.create()
    .withSubject(userEmail)
    .withIssuer(issuer)
    .apply { userName?.let { withClaim("name", userName) } }
    .withClaim("email", userEmail)
    .withClaim("metadata", mapOf("name" to userName))
    .withExpiresAt(Date(System.currentTimeMillis() + expireDuration.toMillis()))
    .sign(Algorithm.HMAC256(secret))
