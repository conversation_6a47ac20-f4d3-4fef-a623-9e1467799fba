package com.hellofresh.cif.api.calculation.projectedWaste

import com.hellofresh.cif.api.calculation.AllPages
import com.hellofresh.cif.api.calculation.CalculationsPage
import com.hellofresh.cif.api.calculation.PageRequest
import com.hellofresh.cif.api.calculation.ProjectedWasteCalculationFilterRequest
import com.hellofresh.cif.api.calculation.ProjectedWasteView
import com.hellofresh.cif.api.calculation.generated.model.ProjectedWasteResponse
import com.hellofresh.cif.api.calculation.generated.model.ProjectedWastesResponse
import com.hellofresh.cif.api.calculation.generated.model.SkuResponse
import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.ktor.customJackson
import io.ktor.http.ContentType.Text
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.server.application.install
import io.ktor.server.auth.authenticate
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.request.accept
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun Routing.projectedWaste(
    projectedWasteCalculationService: ProjectedWasteCalculationService,
    timeoutInMillis: Long
) = authenticate {
    route("/projectedWaste") {
        get("") {
            handleProjectedWasteCalculations(this.call, timeoutInMillis) { request ->
                val calculationsForProjectedWaste =
                    projectedWasteCalculationService.prepareProjectedWasteDetails(request)
                mapProjectedWasteResponse(
                    calculationsForProjectedWaste,
                    request.pageRequest,
                )
            }
        }
    }
}

private suspend fun handleProjectedWasteCalculations(
    call: ApplicationCall,
    timeoutInMillis: Long,
    fetch: suspend (request: ProjectedWasteCalculationFilterRequest) -> Any
) {
    handle(call, timeoutInMillis) {
        val projectedWasteCalculationRequest = ProjectedWasteCalculationFilterRequest.from(call.parameters)
        val updatedRequest = if (Text.CSV.toString() == call.request.accept()) {
            projectedWasteCalculationRequest.copy(pageRequest = AllPages)
        } else {
            projectedWasteCalculationRequest
        }
        fetch(updatedRequest)
    }
}

private suspend fun handle(
    call: ApplicationCall,
    timeoutInMillis: Long,
    block: suspend (call: ApplicationCall) -> Any
) {
    withTimeout(timeoutInMillis) {
        runCatching {
            block(call)
        }.onSuccess {
            call.respond(HttpStatusCode.OK, it)
        }.onFailure {
            call.handleResponseError(it)
        }
    }
}

fun projectedWasteModule(
    projectedWasteCalculationService: ProjectedWasteCalculationService,
    projectedWasteCalculationCsvConverter: ProjectedWasteCalculationCsvConverter,
    timeout: Duration
): Application.() -> Unit = {
    routing {
        projectedWaste(
            projectedWasteCalculationService,
            timeout.inWholeMilliseconds,
        ).install(ContentNegotiation) {
            customJackson()
            register(Text.CSV, projectedWasteCalculationCsvConverter)
        }
    }
}

private fun mapProjectedWasteResponse(
    calculationsPages: CalculationsPage<ProjectedWasteView>,
    pageRequest: PageRequest,
): ProjectedWastesResponse =
    ProjectedWastesResponse(
        projectedWasteResponses = calculationsPages.calculationPage
            .flatMap { projectedWasteView ->
                projectedWasteView.unusableInventory.map { unusableInventory ->
                    ProjectedWasteResponse(
                        dcCode = projectedWasteView.dcCode,
                        sku = SkuResponse(
                            skuCode = projectedWasteView.skuCode,
                            skuId = projectedWasteView.cskuId,
                            skuName = projectedWasteView.skuName,
                        ),
                        quantity = unusableInventory.inventory.qty.toLong(),
                        date = unusableInventory.inventory.expiryDate,
                        isUsable = unusableInventory.usable,
                        unusableStockReason = unusableInventory.unusableReason,
                    )
                }
            },
        page = pageRequest.number,
        totalPages = calculationsPages.totalPages,
    )
