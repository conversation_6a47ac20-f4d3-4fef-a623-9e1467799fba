package com.hellofresh.cif.api.fileupload.repository

import com.hellofresh.cif.api.calculation.generated.model.FileUploadResponse
import com.hellofresh.cif.api.schema.enums.FileType
import com.hellofresh.cif.api.schema.enums.FileUploadStatus
import com.hellofresh.cif.api.schema.enums.FileUploadStatus.ERROR
import com.hellofresh.cif.api.schema.enums.FileUploadStatus.IMPORTED
import com.hellofresh.cif.api.schema.tables.FileUploads.FILE_UPLOADS
import com.hellofresh.cif.api.schema.tables.records.FileUploadsRecord
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import java.time.ZoneOffset
import kotlinx.coroutines.future.await

private const val FILE_UPLOADS_LIMIT = 20

class FileUploadRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
) : FileUploadRepository {
    override suspend fun fetchFileUploads(market: String, fileType: String): List<FileUploadResponse> =
        metricsDSLContext.withTagName("fetch-file-uploads")
            .selectFrom(FILE_UPLOADS)
            .where(FILE_UPLOADS.MARKET.eq(market))
            .and(FILE_UPLOADS.FILE_TYPE.eq(getFileType(fileType)))
            .orderBy(FILE_UPLOADS.CREATED_AT.desc())
            .limit(FILE_UPLOADS_LIMIT)
            .fetchAsync()
            .thenApply { it.map(::mapToFileUpload) }
            .await()

    private fun mapToFileUpload(record: FileUploadsRecord): FileUploadResponse =
        FileUploadResponse(
            id = record.id,
            fileName = record.fileName,
            market = record.market,
            dcCodes = record.dcs.toList(),
            authorName = record.authorName,
            authorEmail = record.authorEmail,
            createdAt = record.createdAt.atOffset(ZoneOffset.UTC),
            message = record.message ?: "",
            status = mapToFileUploadResponseStatus(record.status),
            fileType = mapToFileTypeResponse(record.fileType)
        )

    private fun mapToFileUploadResponseStatus(status: FileUploadStatus) =
        when (status) {
            IMPORTED -> FileUploadResponse.Status.IMPORTED
            ERROR -> FileUploadResponse.Status.ERROR
        }

    private fun mapToFileTypeResponse(fileType: FileType) =
        when (fileType) {
            FileType.STOCK_UPDATE -> FileUploadResponse.FileType.STOCK_UPDATE
            FileType.STOCK_INVENTORY -> FileUploadResponse.FileType.STOCK_INVENTORY
        }

    private fun getFileType(fileType: String): FileType = when (fileType) {
        FileUploadResponse.FileType.STOCK_UPDATE.value -> FileType.STOCK_UPDATE
        FileUploadResponse.FileType.STOCK_INVENTORY.value -> FileType.STOCK_INVENTORY
        else -> error("Unknown file type: $fileType")
    }
}
