package com.hellofresh.cif.api.supplyQuantityRecommendation.repository

import com.hellofresh.cif.api.schema.Tables.STOCK_UPDATE
import com.hellofresh.cif.api.sku.SkuQuantityMapper.mapToSkuUom
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.StockUpdate
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL

class StockUpdatesReadRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
) : StockUpdatesReadRepository {
    override suspend fun fetchStockUpdates(dcCode: String, dateRange: DateRange): List<StockUpdate> {
        val lastVersionStockUpdateTable =
            DSL.select()
                .distinctOn(
                    STOCK_UPDATE.DC_CODE,
                    STOCK_UPDATE.DATE,
                    STOCK_UPDATE.SKU_ID,
                )
                .from(STOCK_UPDATE)
                .where(
                    STOCK_UPDATE.DC_CODE.eq(dcCode).and(
                        STOCK_UPDATE.DATE.between(dateRange.fromDate, dateRange.toDate),
                    ),
                )
                .orderBy(
                    STOCK_UPDATE.DC_CODE,
                    STOCK_UPDATE.DATE,
                    STOCK_UPDATE.SKU_ID,
                    STOCK_UPDATE.VERSION.desc(),
                )
                .asTable("lastVersionWithDeleted")

        return metricsDSLContext.withTagName("fetch-stock-updates-using-dccode-week")
            .select(
                lastVersionStockUpdateTable.field(STOCK_UPDATE.DC_CODE),
                lastVersionStockUpdateTable.field(STOCK_UPDATE.SKU_ID),
                lastVersionStockUpdateTable.field(STOCK_UPDATE.DATE),
                lastVersionStockUpdateTable.field(STOCK_UPDATE.WEEK),
                lastVersionStockUpdateTable.field(STOCK_UPDATE.QUANTITY),
                lastVersionStockUpdateTable.field(STOCK_UPDATE.UOM),
            )
            .from(lastVersionStockUpdateTable)
            .where(lastVersionStockUpdateTable.field(STOCK_UPDATE.DELETED)?.isFalse)
            .fetchAsync()
            .thenApply { result ->
                result.map { record ->
                    StockUpdate(
                        dcCode = record[lastVersionStockUpdateTable.field(STOCK_UPDATE.DC_CODE)],
                        skuId = record.get(lastVersionStockUpdateTable.field(STOCK_UPDATE.SKU_ID)),
                        date = record.get(lastVersionStockUpdateTable.field(STOCK_UPDATE.DATE)),
                        week = record[lastVersionStockUpdateTable.field(STOCK_UPDATE.WEEK)],
                        quantity = SkuQuantity.fromBigDecimal(
                            record[lastVersionStockUpdateTable.field(STOCK_UPDATE.QUANTITY)],
                            mapToSkuUom(record[lastVersionStockUpdateTable.field(STOCK_UPDATE.UOM)]),
                        ),
                    )
                }
            }
            .await()
    }
}
