package com.hellofresh.cif.api.calculation

import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.api.calculation.db.toSkuUom
import com.hellofresh.cif.api.calculation.generated.model.UnusableStockByType
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.demand.models.DemandType
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import java.math.BigDecimal
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import kotlin.math.abs
import org.jetbrains.annotations.VisibleForTesting

object CalculationServiceMapper {
    @VisibleForTesting
    fun toDailyCalculation(
        record: CalculationRecord,
        usableInventoryEvaluator: UsableInventoryEvaluator,
        calculationStatus: CalculationStatus = CalculationStatus.PROCESSED
    ) = with(record) {
        DailyView(
            productionDay = ProductionDay(
                week = productionWeek,
                day = date,
            ),
            sku = Sku(
                skuId = cskuId,
                skuCode = code,
                skuCategories = category,
                skuName = name,
            ),
            calculation = getCalculation(record, usableInventoryEvaluator, calculationStatus)
        )
    }

    private fun getCalculation(
        record: CalculationRecord,
        usableInventoryEvaluator: UsableInventoryEvaluator,
        calculationStatus: CalculationStatus
    ) = with(record) {
        val demandDetails = getTotalDemandDetails(consumptionDetails)
        Calculation(
            uom = uom.toSkuUom(),
            usableStock = openingStock,
            unusableStock = expired,
            incomingPos = expectedInbound,
            inbound = actualInbound,
            pos = getAllPoNumber(record),
            consumption = demanded,
            dailyNeed = dailyNeeds,
            closingStock = closingStock,
            actualConsumption = actualConsumption,
            dcCode = this.dcCode,
            skuAtRisk = this.skuAtRisk,
            safetyStock = this.safetyStock,
            strategy = this.strategy,
            safetyStockNeeds = this.safetyStockNeeds,
            storageStock = this.storageStock,
            stagingStock = this.stagingStock,
            poDueIn = poDueIn,
            netNeeds = netNeeds,
            substituted = calculateSubstituted(
                record.subbed?.name,
                record.substitutedInQty,
                record.substitutedOutQty,
            ),
            brandConsumptions = getBrandConsumption(record.consumptionDetails),
            prekitting = getPrekitting(record.consumptionDetails),
            unusableStockDetails = calculateUnusableStockDetails(
                record.dcCode,
                record.date,
                record.acceptableCodeLife,
                record.category,
                record.unusableStockDetails,
                usableInventoryEvaluator,
            ),
            stockUpdate = this.stockUpdate,
            status = calculationStatus,
            fumigatedDemand = demandDetails[DemandType.FUMIGATED],
            regularDemand = demandDetails[DemandType.REGULAR],
            expectedInboundTo = expectedInboundTo ?: emptySet(),
            expectedInboundToQty = expectedInboundToQty ?: ZERO,
            actualInboundTo = actualInboundTo ?: emptySet(),
            actualInboundToQty = actualInboundToQty ?: ZERO,
            expectedOutboundTo = expectedOutboundTo ?: emptySet(),
            expectedOutboundToQty = expectedOutboundToQty ?: ZERO,
        )
    }

    private fun getTotalDemandDetails(consumptionDetails: ConsumptionDetails?): Map<DemandType?, Long> {
        val recipeBreakdownDemandDetails = groupAndSumByDemandType(
            consumptionDetails?.recipeBreakdowns,
            { it.demandType },
            { it.qty },
        )
        val substitutedOutDemandDetails = groupAndSumByDemandType(
            consumptionDetails?.substitutions?.out,
            { it.demandType },
            { it.qty },
        )
        val substitutedInDemandDetails = groupAndSumByDemandType(
            consumptionDetails?.substitutions?.`in`,
            { it.demandType },
            { it.qty },
        )
        val preKittingOutDemandDetails = groupAndSumByDemandType(
            consumptionDetails?.prekitting?.out,
            { it.demandType },
            { it.qty },
        )
        val preKittingInDemandDetails = groupAndSumByDemandType(
            consumptionDetails?.prekitting?.`in`,
            { it.demandType },
            { it.qty },
        )

        return (
            recipeBreakdownDemandDetails.keys +
                substitutedOutDemandDetails.keys +
                substitutedInDemandDetails.keys +
                preKittingOutDemandDetails.keys +
                preKittingInDemandDetails.keys
            )
            .associateWith { key ->
                (recipeBreakdownDemandDetails[key] ?: 0L) +
                    (substitutedInDemandDetails[key] ?: 0L) -
                    (substitutedOutDemandDetails[key] ?: 0L) +
                    (preKittingInDemandDetails[key] ?: 0L) -
                    (preKittingOutDemandDetails[key] ?: 0L)
            }
    }

    private fun <T> groupAndSumByDemandType(
        items: List<T>?,
        demandTypeSelector: (T) -> DemandType?,
        qtySelector: (T) -> Long
    ): Map<DemandType?, Long> =
        items?.groupBy(demandTypeSelector)?.mapValues { (_, values) -> values.sumOf(qtySelector) } ?: emptyMap()

    private fun getAllPoNumber(calculationRecord: CalculationRecord) =
        (calculationRecord.expectedInboundPo ?: emptySet()) + (calculationRecord.actualInboundPo ?: emptySet())

    @Suppress("LongParameterList")
    fun calculateUnusableStockDetails(
        dcCode: String,
        date: LocalDate,
        acceptableCodeLife: Int,
        category: String,
        unusableStockDetails: List<ForecastInventory>?,
        usableInventoryEvaluator: UsableInventoryEvaluator
    ): List<UnusableStockByType>? =
        unusableStockDetails
            ?.mapNotNull { unusableStockDetail ->
                unusableStockDetail.locationType?.let { locationType ->
                    usableInventoryEvaluator.isUsable(
                        dcCode,
                        date,
                        locationType,
                        unusableStockDetail.expiryDate,
                        acceptableCodeLife,
                        category,
                    ).unusableReason to unusableStockDetail.qty
                }
            }?.groupBy({ (unusableReason, _) -> unusableReason }) { it.second }
            ?.mapNotNull { (type, quantities) ->
                type?.let {
                    UnusableStockByType(
                        type = type,
                        qty = BigDecimal(quantities.sumOf { it.toLong() }),
                    )
                }
            }

    fun getPrekitting(consumptionDetails: ConsumptionDetails?): Long? =
        consumptionDetails?.prekitting?.`in`?.sumOf { it.qty }

    fun getBrandConsumption(consumptionDetails: ConsumptionDetails?): List<BrandConsumption> =
        consumptionDetails
            ?.recipeBreakdowns
            ?.groupBy { it.brand }
            ?.map { (brand, breakdowns) ->
                BrandConsumption(
                    brand = brand,
                    qty = breakdowns.sumOf { it.qty },
                )
            }
            ?: emptyList()

    fun calculateSubstituted(subbedType: String?, substitutedInQty: Long?, substitutedOutQty: Long?): Long? =
        when {
            subbedType == null || substitutedOutQty == null || substitutedInQty == null ||
                subbedType == "NONE" -> null

            substitutedOutQty > 0 && substitutedInQty > 0 -> abs(
                substitutedOutQty - substitutedInQty,
            )

            substitutedInQty > 0 -> substitutedInQty
            substitutedOutQty > 0 -> substitutedOutQty * -1
            else -> 0L
        }
}

fun CalculationRecord.toKey() = CalculationKey(this.cskuId, this.dcCode, this.date)
