package com.hellofresh.cif.api.calculation.csvexport

import com.hellofresh.cif.api.calculation.Calculation
import com.hellofresh.cif.api.calculation.DailyView
import com.hellofresh.cif.api.calculation.ProductionDay
import com.hellofresh.cif.api.calculation.Sku
import com.hellofresh.cif.api.calculation.WeeklyView
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.Context
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.lib.groupByFirst
import com.hellofresh.cif.models.purchaseorder.PoStatus
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.demand.models.EVERYPLATE_BRAND
import com.hellofresh.demand.models.HELLOFRESH_BRAND
import java.io.StringWriter
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import org.apache.commons.csv.CSVFormat
import org.apache.logging.log4j.kotlin.Logging

class CalculationsCsvConverter(
    private val dcConfigService: DcConfigService,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
    private val brandConsumptionMarkets: Set<String>,
) {

    fun convertDailyToCsv(dailyCalculations: List<DailyView>, purchaseOrders: List<PurchaseOrder>): String {
        val dailyPlannedPurchaseOrders =
            aggregatePlannedPurchaseOrdersByKey(
                purchaseOrders,
            ) { po, poSku, date, week -> DailyPurchaseOrderItemKey(po.dcCode, poSku.skuId, date) }

        return convertToCsv(dailyCalculations.map { toCalculationItem(it, dailyPlannedPurchaseOrders) })
    }

    fun convertWeeklyToCsv(weeklyCalculations: List<WeeklyView>, purchaseOrders: List<PurchaseOrder>): String {
        val weeklyPlannedPurchaseOrders =
            aggregatePlannedPurchaseOrdersByKey(
                purchaseOrders,
            ) { po, poSku, date, week -> WeeklyPurchaseOrderItemKey(po.dcCode, poSku.skuId, week) }
        return convertToCsv(weeklyCalculations.map { toCalculationItem(it, weeklyPlannedPurchaseOrders) })
    }

    fun convertToCsv(calculationItems: List<CalculationItem>): String {
        val market = calculationItems.flatMap { it.dcCodes }.distinct()
            .mapNotNull { dcConfigService.dcConfigurations[it] }
            .map {
                it.market
            }.firstOrNull()

        val aggCalculationItems = aggregateCalculations(calculationItems)
        val isBrandConsumption = isBrandConsumption(calculationItems)
        val isSafetyStockMarket = isSafetyStockMarket(calculationItems)
        return createCalculationCsvPrinter(
            isSafetyStock = isSafetyStockMarket,
            isBrandConsumption = isBrandConsumption,
            market = market,
        ).print(aggCalculationItems)
    }

    private fun isSafetyStockMarket(items: List<CalculationItem>) =
        items.flatMap { it.dcCodes }.distinct()
            .mapNotNull { dcConfigService.dcConfigurations[it] }
            .map {
                enableNetNeedsFeatureFlag(it)
            }
            .any { it }

    private fun enableNetNeedsFeatureFlag(it: DistributionCenterConfiguration) = statsigFeatureFlagClient.isEnabledFor(
        FeatureFlag.NetNeeds(setOf(ContextData(Context.DC, it.dcCode), ContextData(Context.MARKET, it.market))),
    )

    private fun isBrandConsumption(items: List<CalculationItem>) =
        items.flatMap { it.dcCodes }.distinct()
            .mapNotNull { dcConfigService.dcConfigurations[it]?.market }
            .any { brandConsumptionMarkets.contains(it) }

    private fun aggregateCalculations(items: List<CalculationItem>) = items
        .groupBy { Pair(it.sku.skuId, it.productionDay ?: it.week) }
        .map {
            it.value.reduce { accCalcItem, nextCalcItem ->
                val acc = accCalcItem.calculation
                val calculation = nextCalcItem.calculation
                accCalcItem.copy(
                    calculation = acc.copy(
                        incomingPos = acc.incomingPos.plus(calculation.incomingPos),
                        inbound = acc.inbound.plus(calculation.inbound),
                        pos = acc.pos + calculation.pos,
                        usableStock = acc.usableStock + calculation.usableStock,
                        unusableStock = acc.unusableStock + calculation.unusableStock,
                        consumption = acc.consumption + calculation.consumption,
                        closingStock = acc.closingStock + calculation.closingStock,
                        actualConsumption = acc.actualConsumption + calculation.actualConsumption,
                        dcCode = acc.dcCode + ' ' + calculation.dcCode,
                        safetyStock = acc.safetyStock?.plus(calculation.safetyStock ?: BigDecimal(0)) ?: calculation.safetyStock,
                        netNeeds = acc.netNeeds.plus(calculation.netNeeds),
                        substituted = acc.substituted?.plus(calculation.substituted ?: 0),
                        fumigatedDemand = acc.fumigatedDemand?.plus(calculation.fumigatedDemand ?: 0),
                        regularDemand = acc.regularDemand?.plus(calculation.regularDemand ?: 0),
                    ),
                    dcCodes = accCalcItem.dcCodes + calculation.dcCode,
                )
            }
        }

    private fun <K> aggregatePlannedPurchaseOrdersByKey(
        purchaseOrders: List<PurchaseOrder>,
        keyFunction: (
            purchaseOrder: PurchaseOrder,
            purchaseOrderSku: PurchaseOrderSku,
            date: LocalDate,
            week: String
        ) -> K
    ) =
        purchaseOrders
            .filter { it.poStatus == PoStatus.PLANNED }
            .flatMap { po ->
                dcConfigService.dcConfigurations[po.dcCode]?.let { dc ->
                    po.purchaseOrderSkus.mapNotNull { poSku ->
                        po.expectedDeliveryTimeslot?.expectedDeliveryDate?.let { date ->
                            keyFunction(po, poSku, date, DcWeek(date, dc.productionStart).value) to poSku.expectedQuantity?.getValue()
                        }
                    }
                } ?: emptyList()
            }
            .groupByFirst()
            .mapValues { (_, expectedQuantities) ->
                expectedQuantities.reduceOrNull { acc: BigDecimal?, qty ->
                    qty?.let { it + (acc ?: BigDecimal.ZERO) } ?: acc
                }
            }

    companion object : Logging {
        private val defaultCsvHeader = mutableListOf(
            "DC Names",
            "Sku Id",
            "Sku Code",
            "Sku Name",
            "Sku Categories",
            "DC Week",
            "Date",
            "Inbound quantity",
            "Incoming PO quantity",
            "Consumption",
            "Closing Stock",
            "Opening Stock",
            "Unusable Stock",
            "Actual Consumption",
            "Substituted",
        )

        fun getCsvHeaders(isSafetyStock: Boolean, isBrandConsumption: Boolean, market: String? = null) =
            defaultCsvHeader.toMutableList().apply {
                if (isSafetyStock) {
                    add("Safety Stock")
                }
                if (isBrandConsumption) {
                    add("HelloFresh")
                    add("EveryPlate")
                }
                if (market != null &&
                    market == ConfigurationLoader.getStringOrFail("demand.type.enabled.markets")
                ) {
                    add("Fumigated Demand")
                    add("Regular Demand")
                }
                add("Supply Quantity Recommendation")
                add("Preview POs")
            }

        private fun createCalculationCsvPrinter(
            isSafetyStock: Boolean,
            isBrandConsumption: Boolean,
            market: String?
        ) =
            CalculationCsvPrinter(
                getCsvHeaders(
                    isSafetyStock = isSafetyStock,
                    isBrandConsumption = isBrandConsumption,
                    market = market,
                ).toTypedArray(),
            ) { item ->
                itemMapping(
                    item = item,
                    isSafetyStock = isSafetyStock,
                    isBrandConsumption = isBrandConsumption,
                    market = market,
                )
            }

        private fun getConsumptionByBrand(item: CalculationItem): List<Long?> {
            val helloFreshQty = item.calculation.brandConsumptions.find { it.brand == HELLOFRESH_BRAND }?.qty
            val everyPlateQty = item.calculation.brandConsumptions.find { it.brand == EVERYPLATE_BRAND }?.qty
            return listOf(helloFreshQty, everyPlateQty)
        }

        private fun itemMapping(item: CalculationItem, isSafetyStock: Boolean, isBrandConsumption: Boolean, market: String?): List<*> =
            with(item.calculation) {
                var baseList = listOf(
                    dcCode,
                    item.sku.skuId, item.sku.skuCode, item.sku.skuName, item.sku.skuCategories,
                    item.week,
                    item.productionDay?.day ?: "",
                    inbound,
                    incomingPos,
                    consumption, closingStock, usableStock, unusableStock, actualConsumption, substituted,
                )

                if (isSafetyStock) {
                    baseList = baseList + listOf(safetyStock)
                }

                if (isBrandConsumption) {
                    baseList = baseList + getConsumptionByBrand(item)
                }

                if (market != null && market == ConfigurationLoader.getStringOrFail("demand.type.enabled.markets")) {
                    baseList = baseList + listOf(fumigatedDemand, regularDemand)
                }

                baseList = baseList + listOf(netNeeds, item.plannedPurchaseOrders)

                baseList
            }

        private fun toCalculationItem(item: DailyView, plannedPurchaseOrders: Map<DailyPurchaseOrderItemKey, BigDecimal?>) =
            CalculationItem(
                sku = item.sku,
                calculation = item.calculation,
                week = item.productionDay.week,
                productionDay = item.productionDay,
                dcCodes = setOf(item.calculation.dcCode),
                plannedPurchaseOrders = plannedPurchaseOrders[
                    DailyPurchaseOrderItemKey(
                        item.calculation.dcCode,
                        item.sku.skuId,
                        item.productionDay.day,
                    ),
                ],
            )

        private fun toCalculationItem(item: WeeklyView, plannedPurchaseOrders: Map<WeeklyPurchaseOrderItemKey, BigDecimal?>) =
            CalculationItem(
                sku = item.sku,
                calculation = item.calculation,
                week = item.week,
                productionDay = null,
                dcCodes = setOf(item.calculation.dcCode),
                plannedPurchaseOrders = plannedPurchaseOrders[
                    WeeklyPurchaseOrderItemKey(
                        item.calculation.dcCode,
                        item.sku.skuId,
                        item.week,
                    ),
                ],
            )

        @Suppress("SpreadOperator")
        private class CalculationCsvPrinter(
            val headers: Array<String>,
            val itemMapping: (CalculationItem) -> List<*>
        ) {

            fun print(items: List<CalculationItem>): String =
                StringWriter()
                    .use { writer ->
                        CSVFormat.DEFAULT
                            .builder().setHeader(*headers)
                            .build()
                            .print(writer)
                            .use { csvPrinter ->
                                items.onEach { item ->
                                    csvPrinter.printRecord(itemMapping(item))
                                }
                                csvPrinter.flush()
                            }
                        writer.buffer.toString()
                    }
        }

        data class CalculationItem(
            val sku: Sku,
            val calculation: Calculation,
            val week: String,
            val productionDay: ProductionDay?,
            val dcCodes: Set<String>,
            val plannedPurchaseOrders: BigDecimal?
        )

        private data class DailyPurchaseOrderItemKey(val dcCode: String, val skuId: UUID, val date: LocalDate)
        private data class WeeklyPurchaseOrderItemKey(val dcCode: String, val skuId: UUID, val week: String)
    }
}
