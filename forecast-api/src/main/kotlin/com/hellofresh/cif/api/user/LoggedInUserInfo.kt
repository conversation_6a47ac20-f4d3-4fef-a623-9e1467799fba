package com.hellofresh.cif.api.user

import com.hellofresh.cif.api.user.LoggedInUserInfo.Companion.logger
import io.ktor.server.auth.jwt.JWTPrincipal
import org.apache.logging.log4j.kotlin.Logging

typealias UserRoleClaim = Set<String>

data class LoggedInUserInfo(
    val userId: String,
    val userEmail: String,
    val userName: String?,
    val roleClaim: UserRoleClaim,
) {
    companion object : Logging
}

fun UserRoleClaim.isSuperManager(): Boolean =
    this.any { it.endsWith(".recommendation_manager") }

fun JWTPrincipal.getLoggedInUser(): LoggedInUserInfo {
    val isAzureToken = isAzureToken(this)
    logger.info("Resolving JWT token ${if (isAzureToken) "azure" else "google"}.")

    return if (isAzureToken) {
        getAzureUser(this)
    } else {
        getAuthServiceUser(this)
    }
}

fun JWTPrincipal.getRoleClaims(): Set<String> {
    val roles = this.payload.getClaim("roleclaim")?.asList(String::class.java) ?: emptyList()
    return roles.toSet()
}

fun getAzureUser(jwtPrincipal: JWTPrincipal): LoggedInUserInfo {
    val subject = jwtPrincipal.subject
    requireNotNull(subject) { "sub claim must be present in jwt" }
    val authorEmail = jwtPrincipal["email"]
    requireNotNull(authorEmail) { "email claim must be present in jwt" }
    val authorName = jwtPrincipal["name"]
    val roleClaims = jwtPrincipal.getRoleClaims()

    return LoggedInUserInfo(subject, authorEmail, authorName, roleClaims)
}

fun getAuthServiceUser(jwtPrincipal: JWTPrincipal): LoggedInUserInfo {
    val subject = jwtPrincipal.subject
    requireNotNull(subject) { "sub claim must be present in jwt" }
    val authorEmail = jwtPrincipal["email"]
    requireNotNull(authorEmail) { "email claim must be present in jwt" }
    val authorName = jwtPrincipal.getClaim("metadata", HashMap::class)?.get("name") as String?

    // returning emptySet because googleAuthService doesn't have the roleclaim
    return LoggedInUserInfo(subject, authorEmail, authorName, emptySet())
}

fun isAzureToken(jwt: JWTPrincipal): Boolean = jwt.getClaim("roleclaim", Any::class) != null
