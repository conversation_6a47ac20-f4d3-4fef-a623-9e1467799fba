package com.hellofresh.cif.api.note

import java.util.UUID

class NoteService(private val noteRepository: NoteRepository) {
    suspend fun createNote(createNoteRequest: CreateNoteRequest) =
        noteRepository.createNote(
            skuId = createNoteRequest.skuId,
            authorEmail = createNoteRequest.authorEmail,
            authorName = createNoteRequest.authorName,
            dcCodes = createNoteRequest.dcCodes,
            dcWeeks = createNoteRequest.dcWeeks,
            text = createNoteRequest.text,
        )

    suspend fun updateNote(updateNoteRequest: UpdateNoteRequest): Note {
        try {
            return noteRepository.updateNote(
                id = updateNoteRequest.noteId,
                authorEmail = updateNoteRequest.authorEmail,
                authorName = updateNoteRequest.authorName,
                dcWeeks = updateNoteRequest.dcWeeks,
                text = updateNoteRequest.text
            )
        } catch (exception: IllegalArgumentException) {
            throw NoteNotFoundException(exception)
        }
    }
    suspend fun deleteNote(id: UUID, authorName: String?, authorEmail: String) {
        try {
            noteRepository.deleteNote(
                id = id,
                authorName = authorName,
                authorEmail = authorEmail,
            )
        } catch (exception: IllegalArgumentException) {
            throw NoteNotFoundException(exception)
        }
    }

    suspend fun getNotes(findNotesRequest: FindNotesRequest): Map<UUID, List<Note>> =
        noteRepository.getNotes(findNotesRequest)
            .groupBy { it.skuId }
            .mapValues { (_, noteList) -> noteList.sortedBy { it.createdAt } }
}

class NoteNotFoundException(exception: Throwable) : Exception(exception)
