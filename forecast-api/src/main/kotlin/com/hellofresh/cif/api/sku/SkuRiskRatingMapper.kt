package com.hellofresh.cif.api.sku

import com.hellofresh.cif.api.calculation.generated.model.SkuRiskRatingEnum
import com.hellofresh.cif.api.schema.enums.SkuRiskRating

object SkuRiskRatingMapper {
    fun mapToSkuRiskRating(riskRatingEnum: SkuRiskRatingEnum?): SkuRiskRating =
        when (riskRatingEnum) {
            SkuRiskRatingEnum.LOW -> SkuRiskRating.LOW
            SkuRiskRatingEnum.MEDIUM, null -> SkuRiskRating.MEDIUM
            SkuRiskRatingEnum.MEDIUM_LOW -> SkuRiskRating.MEDIUM_LOW
            SkuRiskRatingEnum.HIGH -> SkuRiskRating.HIGH
            SkuRiskRatingEnum.CRITICAL -> SkuRiskRating.CRITICAL
        }
}
