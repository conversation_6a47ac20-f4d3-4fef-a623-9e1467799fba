package com.hellofresh.cif.api.note

import com.hellofresh.cif.api.calculation.AdditionalFilter
import com.hellofresh.cif.api.calculation.generated.model.CreateNoteResponse
import com.hellofresh.cif.api.calculation.generated.model.GetNotesResponse
import com.hellofresh.cif.api.calculation.generated.model.NoteResponse
import com.hellofresh.cif.api.calculation.generated.model.NotesResponse
import com.hellofresh.cif.api.calculation.generated.model.NotesResponseAtRiskInner
import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.getAllOrDefault
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getLoggedInUserInfo
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.delete
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.put
import io.ktor.server.routing.routing
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout
import org.jetbrains.annotations.VisibleForTesting

fun Routing.createNotes(
    noteService: NoteService,
    timeoutInMillis: Long,
) =
    authenticate {
        post("/notes") {
            withTimeout(timeoutInMillis) {
                kotlin.runCatching {
                    val createNoteRequest = toCreateNoteRequest(call)
                    val note = noteService.createNote(createNoteRequest)
                    note.toCreateNoteResponse()
                }.onFailure {
                    call.handleResponseError(it)
                }.onSuccess { notes ->
                    call.respond(HttpStatusCode.OK, notes)
                }
            }
        }
        put("/notes/{noteId}") {
            withTimeout(timeoutInMillis) {
                kotlin.runCatching {
                    val noteId = UUID.fromString(call.parameters.getOrThrow("noteId").trim())
                    val updateNoteRequest = toUpdateNoteRequest(noteId, call)
                    val note = noteService.updateNote(updateNoteRequest)
                    note.toCreateNoteResponse()
                }.onFailure {
                    call.handleResponseError(it)
                }.onSuccess { notes ->
                    call.respond(HttpStatusCode.OK, notes)
                }
            }
        }

        delete("/notes/{noteId}") {
            withTimeout(timeoutInMillis) {
                kotlin.runCatching {
                    val noteId = UUID.fromString(call.parameters.getOrThrow("noteId").trim())
                    val loggedInUserInfo = call.getLoggedInUserInfo()
                    noteService.deleteNote(noteId, loggedInUserInfo.userName, loggedInUserInfo.userEmail)
                }.onFailure {
                    call.handleResponseError(it)
                }.onSuccess { notes ->
                    call.respond(HttpStatusCode.OK, notes)
                }
            }
        }
    }

fun Routing.getNotes(
    getNoteService: NoteService,
    timeoutInMillis: Long,
) = authenticate {
    get("/notes") {
        withTimeout(timeoutInMillis) {
            kotlin.runCatching {
                val notesMap = getNoteService.getNotes(toGetNoteRequest(call))
                GetNotesResponse(
                    notesMap.map { (skuId, notes) ->
                        NotesResponse(
                            skuId = skuId,
                            atRisk = notes.flatMap { it.atRisk }.toAPIAtRisk(),
                            notes = notes.map { note ->
                                NoteResponse(
                                    id = note.id,
                                    authorEmail = note.authorEmail,
                                    dcCodes = note.dcCodes.toList(),
                                    weeks = note.weeks.map(DcWeek::value).toList(),
                                    text = note.text,
                                    createdAt = note.createdAt,
                                    authorName = note.authorName,
                                    isEdited = note.isEdited,
                                )
                            },
                        )
                    },
                )
            }.onFailure {
                call.handleResponseError(it)
            }.onSuccess { notes ->
                call.respond(HttpStatusCode.OK, notes)
            }
        }
    }
}

fun noteModule(
    noteService: NoteService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        createNotes(
            noteService,
            timeout.inWholeMilliseconds,
        ).also {
            configureSerialization(it)
        }
    }

    routing {
        getNotes(
            noteService,
            timeout.inWholeMilliseconds,
        ).also {
            configureSerialization(it)
        }
    }
}

private fun toGetNoteRequest(applicationCall: ApplicationCall) =
    FindNotesRequest(
        dcCodes = applicationCall.parameters.getAllOrThrow("dcCode").toSet(),
        dcWeeks = applicationCall.parameters.getAllOrThrow("weeks").map(::DcWeek).toSet(),
        skuIds = applicationCall.parameters.getAllOrDefault("skuId").map(UUID::fromString).toSet(),
        skuCategories = applicationCall.parameters.getAllOrDefault("skuCategory").toSet(),
        additionalFilters = applicationCall.parameters.getAllOrDefault("additionalFilters")
            .filter { it.isNotBlank() }
            .map { AdditionalFilter.valueOf(it.trim().uppercase()) }
            .toSet(),
        locationInBox = applicationCall.parameters.getAllOrDefault("locationInBox").toSet(),
    )

@VisibleForTesting
internal fun List<AtRisk>.toAPIAtRisk() =
    map { NotesResponseAtRiskInner(dcCode = it.dc, week = it.week.toString()) }
        .toSet().toList()

private fun Note.toCreateNoteResponse() = CreateNoteResponse(
    id = this.id,
    skuId = this.skuId,
    authorEmail = this.authorEmail,
    authorName = this.authorName,
    dcCodes = this.dcCodes.toList(),
    weeks = this.weeks.map(DcWeek::toString).toList(),
    text = this.text,
    createdAt = this.createdAt,
    isEdited = this.isEdited,
)

private suspend fun toCreateNoteRequest(applicationCall: ApplicationCall): CreateNoteRequest {
    val loggedInUserInfo = applicationCall.getLoggedInUserInfo()
    return applicationCall.receive<com.hellofresh.cif.api.calculation.generated.model.CreateNoteRequest>()
        .let {
            CreateNoteRequest(
                skuId = it.skuId,
                authorName = loggedInUserInfo.userName,
                authorEmail = loggedInUserInfo.userEmail,
                dcCodes = it.dcCodes.toSet(),
                dcWeeks = it.weeks.map(::DcWeek).toSet(),
                text = it.text,
            )
        }
}

private suspend fun toUpdateNoteRequest(id: UUID, applicationCall: ApplicationCall): UpdateNoteRequest {
    val loggedInUserInfo = applicationCall.getLoggedInUserInfo()
    return applicationCall.receive<com.hellofresh.cif.api.calculation.generated.model.UpdateNoteRequest>()
        .let {
            UpdateNoteRequest(
                noteId = id,
                authorName = loggedInUserInfo.userName,
                authorEmail = loggedInUserInfo.userEmail,
                dcWeeks = it.weeks.map(::DcWeek).toSet(),
                text = it.text,
            )
        }
}
