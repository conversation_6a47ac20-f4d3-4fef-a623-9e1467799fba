package com.hellofresh.cif.api.po

import com.hellofresh.cif.api.calculation.generated.model.Delivery
import com.hellofresh.cif.api.calculation.generated.model.PoStatus
import com.hellofresh.cif.api.calculation.generated.model.PurchaseOrdersDetailResponse
import com.hellofresh.cif.api.calculation.generated.model.PurchaseOrdersDetailResponsePosInner
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.purchaseorder.Asn
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.ASN_RECEIVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.DELIVERED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.DELIVERY_OPEN
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.OVERDUE
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.PLANNED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderStatus.SENT
import java.util.UUID

object PurchaseOrderResponseMapper {
    /**
     * Maps a PurchaseOrdersDetailResponse for the given skuId.
     * @param skuId the skuId for which deliveries and ASNs should be included.
     */
    fun mapToPoDetailResponse(
        poList: List<PurchaseOrder>,
        skuId: UUID,
        dcConfigService: DcConfigService
    ): PurchaseOrdersDetailResponse {
        val inner = poList.map { po ->
            val distributionCenter = requireNotNull(
                dcConfigService.dcConfigurations[po.dcCode]
            ) { "Dc Config Must exist when returning pos: ${po.dcCode}" }
            val poSkusForGivenSkuId = po.purchaseOrderSkus.filter { poSku -> poSku.skuId == skuId }
            val deliveriesTime = poSkusForGivenSkuId
                .flatMap { v ->
                    v.deliveries.map { t -> t.deliveryTime.toOffsetDateTime() }
                }
            val deliveries = poSkusForGivenSkuId.flatMap { v ->
                v.deliveries.map {
                    Delivery(
                        id = it.id,
                        state = Delivery.State.valueOf(it.state.name.uppercase()),
                        quantity = it.quantity.getValue().toInt(),
                        time = it.deliveryTime.toOffsetDateTime(),
                    )
                }
            }
            val (ordered, received) = poSkusForGivenSkuId.fold(Pair(0L, 0L)) { acc, poS ->
                Pair(
                    acc.first + (poS.expectedQuantity?.getValue()?.toLong() ?: 0),
                    acc.second + poS.deliveries.sumOf { v -> v.quantity.getValue().toLong() },
                )
            }
            val asnsForGivenSkuId = po.asns.filter { poSku -> poSku.skuId == skuId }
            val asnsDto = asnsForGivenSkuId.map { v -> mapToAsnDto(v) }
            PurchaseOrdersDetailResponsePosInner(
                status = mapToPoStatus(po.status(skuId)),
                deliveries = deliveries,
                deliveredTimes = deliveriesTime,
                inboundStartTime = po.expectedDeliveryTimeslot?.startTime
                    ?.toOffsetDateTime(),
                inboundEndTime = po.expectedDeliveryTimeslot?.endTime
                    ?.toOffsetDateTime(),
                week = po.expectedDeliveryTimeslot?.startTime?.let {
                    DcWeek(
                        it.toLocalDate(),
                        distributionCenter.productionStart
                    ).toString()
                },
                poRef = po.poReference,
                poId = po.poId,
                dcCode = po.dcCode,
                ordered = ordered.toInt(),
                received = received.toInt(),
                supplierId = po.supplier?.id,
                supplierName = po.supplier?.name,
                asns = asnsDto,
            )
        }
        return PurchaseOrdersDetailResponse(inner)
    }

    internal fun mapToPoStatus(status: PurchaseOrderStatus) = when (status) {
        PLANNED -> PoStatus.PLANNED
        SENT -> PoStatus.SENT
        APPROVED -> PoStatus.APPROVED
        ASN_RECEIVED -> PoStatus.ASN_RECEIVED
        DELIVERY_OPEN -> PoStatus.DELIVERY_OPEN
        DELIVERED -> PoStatus.DELIVERED
        OVERDUE -> PoStatus.OVERDUE
    }

    private fun mapToAsnDto(asn: Asn) = com.hellofresh.cif.api.calculation.generated.model.Asn(
        asn.id,
        asn.plannedDeliveryTime.toOffsetDateTime(),
        asn.shippedQuantity.getValue().toLong(),
    )
}
