package com.hellofresh.cif.api.demand

import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.ZoneOffset
import kotlinx.coroutines.future.await
import org.jooq.impl.DSL
import org.jooq.impl.DSL.using

class DemandRepositoryImpl(
    private val metricsDSLContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService,
) : DemandRepository {

    private val fetchDemandTimestamps = "fetch-latest-demand-timestamps"
    private val fetchDemandByWeeks = "fetch-demand-by-weeks"
    private val fetchDemandByWeeksWithTimeStamp = "fetch-demand-by-weeks-with-timestamp"

    override suspend fun getLatestDemandUpdates(
        dcCodes: List<DcCode>,
        weeks: List<DcWeek>
    ): Map<DcCode, List<DcDemandTimestampWeek>> {
        val dcConfigs = dcConfigService.dcConfigurations.values.toList().filter {
            dcCodes.contains(it.dcCode)
        }
        return metricsDSLContext.withTagName(fetchDemandTimestamps)
            .transactionResultAsync { dbTxConf ->
                dcConfigs
                    .groupBy { v -> v.productionStart }
                    .flatMap { (_, dcConfigs) ->
                        val dcCodesWithSameProductionStart = dcConfigs.map { v -> v.dcCode }
                        val firstDcConfig = dcConfigs.first()
                        val productionStartDay = firstDcConfig.productionStart
                        val zoneId = firstDcConfig.zoneId
                        // for each DC week from 4 weeks in the past until 9 weeks in the future
                        // same range as the FE uses in the DC week dropdown
                        @Suppress("MagicNumber")
                        weeks.map { dcWeek ->
                            val startDcWeekDate = dcWeek.getStartDateInDcWeek(productionStartDay, zoneId)
                            val endDcWeekDate = dcWeek.getLastDateInDcWeek(productionStartDay, zoneId)

                            dbTxConf.dsl().select(
                                Tables.DEMAND.DC_CODE,
                                DSL.greatest(
                                    DSL.max(Tables.DEMAND.CREATED_AT),
                                    DSL.max(Tables.DEMAND.UPDATED_AT),
                                ).`as`("demandTs"),
                            ).from(Tables.DEMAND)
                                .where(Tables.DEMAND.DATE.between(startDcWeekDate, endDcWeekDate))
                                .and(Tables.DEMAND.DC_CODE.`in`(dcCodesWithSameProductionStart))
                                .groupBy(Tables.DEMAND.DC_CODE)
                                .fetch()
                                .map { r ->
                                    DcDemandTimestampWeek(
                                        r[Tables.DEMAND.DC_CODE] as DcCode,
                                        dcWeek,
                                        (r["demandTs"] as LocalDateTime).atOffset(ZoneOffset.UTC),
                                    )
                                }
                        }
                    }
            }
            .await()
            .flatten()
            .groupBy { v -> v.dcCode }
    }

    override suspend fun getDemandByMarket(
        dcWeeks: List<DcWeek>,
        market: String,
        timestamp: LocalDateTime?
    ): MarketDemandByWeek {
        val dcCodesByMarket = dcConfigService.dcConfigurations.values
            .filter { it.market == market }
            .map { it.dcCode }
        return metricsDSLContext.withTagName(
            if (timestamp == null) fetchDemandByWeeks else fetchDemandByWeeksWithTimeStamp
        )
            .transactionResultAsync { txConfig ->
                val transactionTimestamp = using(txConfig)
                    .select(DSL.currentOffsetDateTime())
                    .fetchOne(0, LocalDateTime::class.java)!!

                val demand = txConfig.dsl().selectFrom(Tables.CALCULATION)
                    .where(Tables.CALCULATION.PRODUCTION_WEEK.`in`(dcWeeks.map { it.toString() }))
                    .and(Tables.CALCULATION.DEMANDED.notEqual(BigDecimal.ZERO))
                    .and(Tables.CALCULATION.DC_CODE.`in`(dcCodesByMarket))

                if (timestamp != null) {
                    demand.and(
                        Tables.CALCULATION.UPDATED_AT.greaterThan(timestamp)
                            .or(
                                Tables.CALCULATION.CREATED_AT.greaterThan(timestamp)
                                    .and(Tables.CALCULATION.UPDATED_AT.isNull()),
                            ),
                    )
                }

                val temp = demand.fetch().map {
                    MarketDemand(it.cskuId, it.dcCode, it.date, it.demanded.toLong())
                }
                transactionTimestamp to temp
            }.await()
            .let { (timestamp, temp) ->
                MarketDemandByWeek(
                    timestamp,
                    temp,
                )
            }
    }
}
