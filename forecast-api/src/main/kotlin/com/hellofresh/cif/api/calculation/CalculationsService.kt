package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.CaffeineCache.Cacheable
import com.hellofresh.cif.api.calculation.CalculationServiceMapper.toDailyCalculation
import com.hellofresh.cif.api.calculation.SortBy.SKU_CODE
import com.hellofresh.cif.api.calculation.SortBy.SKU_NAME
import com.hellofresh.cif.api.calculation.db.CalculationFiltersData
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.api.calculation.db.CalculationRepository
import com.hellofresh.cif.api.calculation.stockupdate.CalculationsPendingStockUpdateService
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.Context.MARKET
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.EnableWeeklySQROnStockOverview
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.inventory.CurrentStockUpdates
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import io.ktor.server.plugins.NotFoundException
import io.micrometer.core.instrument.MeterRegistry
import java.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging

private const val HASH_SIZE = 500
private const val FILTER_WEEK_CHUNK_SIZE = 4

@Suppress("LongParameterList")
class CalculationsService(
    meterRegistry: MeterRegistry,
    private val dcConfigService: DcConfigService,
    private val usableInventoryEvaluator: UsableInventoryEvaluator,
    private val calculationsPendingStockUpdateService: CalculationsPendingStockUpdateService,
    private val prodCalculationRepository: CalculationRepository,
    private val preProdCalculationRepository: CalculationRepository,
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient,
) {

    private val weeklyViewCache = CaffeineCache<CalculationRequest, CalculationsPage<WeeklyView>>(
        meterRegistry,
        Duration.ofMinutes(1),
        HASH_SIZE.toLong(),
    )

    private val filtersCache = CaffeineCache<CalculationFilterRequest, CalculationFiltersData>(
        meterRegistry,
        Duration.ofMinutes(1),
        HASH_SIZE.toLong(),
    )

    private val dailyViewCache = CaffeineCache<CalculationRequest, CalculationsPage<DailyView>>(
        meterRegistry,
        Duration.ofMinutes(1),
        HASH_SIZE.toLong(),
    )

    suspend fun getDailyCalculations(req: CalculationRequest): CalculationsPage<DailyView> =
        getCachedDailyCalculations(req)

    private suspend fun getCachedDailyCalculations(req: CalculationRequest): CalculationsPage<DailyView> =
        dailyViewCache.getOrPutCacheable(req) {
            withContext(Dispatchers.IO) {
                val stockUpdatesDeferred = async { calculationsPendingStockUpdateService.getCurrentStockUpdates(req) }

                val calculationRecords = selectRepository(req.consumptionDaysAhead).fetchPageableCalculations(req)

                val calculationsByKey = calculationRecords.calculationPage.associateBy { it.toKey() }

                val pendingCalculationsByKey =
                    evaluateStockUpdatePendingCalculations(req, calculationsByKey, stockUpdatesDeferred.await())

                val dailyCalculations = mergePending(calculationsByKey, pendingCalculationsByKey)

                Cacheable(
                    CalculationsPage(
                        dailyCalculations,
                        calculationRecords.totalPages,
                        calculationRecords.totalSkuAtRiskCount,
                        calculationRecords.totalSkuCount,
                    ),
                    dailyCalculations.none { it.calculation.status == CalculationStatus.PENDING },
                )
            }
        }

    private fun mergePending(
        calculationsByKey: Map<CalculationKey, CalculationRecord>,
        pendingCalculationsByKey: Map<CalculationKey, CalculationRecord>
    ) =
        calculationsByKey.map { (key, calculationRecord) ->
            pendingCalculationsByKey[key]?.let {
                toDailyCalculation(it, usableInventoryEvaluator, CalculationStatus.PENDING)
            } ?: toDailyCalculation(calculationRecord, usableInventoryEvaluator, CalculationStatus.PROCESSED)
        }

    private suspend fun evaluateStockUpdatePendingCalculations(
        calculationRequest: CalculationRequest,
        calculationsByKey: Map<CalculationKey, CalculationRecord>,
        stockUpdates: CurrentStockUpdates
    ) =
        calculationsPendingStockUpdateService.processPendingCalculations(
            calculationRequest,
            calculationsByKey,
            stockUpdates,
        ) {
            selectRepository(it.consumptionDaysAhead).fetchPageableCalculations(it).calculationPage
        }

    suspend fun getWeeklyCalculations(req: CalculationRequest): CalculationsPage<WeeklyView> = weeklyViewCache.getOrPut(
        req,
    ) {
        val daily = getCachedDailyCalculations(req)
        val weekly = toWeekly(daily.calculationPage, req)
        CalculationsPage(weekly, daily.totalPages, daily.totalSkuAtRiskCount, daily.totalSkuCount)
    }

    suspend fun getFilterCalculations(req: CalculationFilterRequest): CalculationFiltersData = filtersCache.getOrPut(
        req,
    ) {
        coroutineScope {
            val allResults = req.weeks
                .chunked(FILTER_WEEK_CHUNK_SIZE)
                .map { chunkedWeeks ->
                    async {
                        val chunkedRequest = req.copy(weeks = chunkedWeeks)
                        val result = selectRepository(chunkedRequest.consumptionDaysAhead)
                            .fetchCalculationFilters(chunkedRequest)
                        result
                    }
                }
                .awaitAll()

            // Merge allResults into a single CalculationFiltersData
            CalculationFiltersData(
                skuSet = allResults.flatMap { it.skuSet }.toSet(),
                ingredientCategories = allResults.flatMap { it.ingredientCategories }.toSet(),
                locationInBox = allResults.flatMap { it.locationInBox }.toSet(),
                suppliers = allResults.flatMap { it.suppliers }.toSet(),
                activeSuppliers = allResults.flatMap { it.activeSuppliers }.toSet()
            )
        }
    }

    private fun toWeekly(daily: List<DailyView>, req: CalculationRequest): List<WeeklyView> =
        daily.groupBy { Triple(it.sku.skuId, it.productionDay.week, it.calculation.dcCode) }
            .map {
                WeeklyView.fromDaily(
                    it.value.toSet(),
                    isMarketEnabledForWeeklySQRNetNeedCalculation(
                        getMarketFromDcCode(req.dcCodes),
                        statsigFeatureFlagClient,
                    ),
                )
            }
            .sortedWith(
                compareByDescending<WeeklyView> { it.calculation.skuAtRisk }
                    .thenBy { v ->
                        when (req.sortBy) {
                            SKU_CODE -> v.sku.skuCode
                            SKU_NAME -> v.sku.skuName
                        }
                    },
            )

    private fun selectRepository(consumptionDaysAhead: Int) = when (consumptionDaysAhead) {
        0 -> prodCalculationRepository
        1 -> preProdCalculationRepository
        else -> throw IllegalArgumentException("Calculating for $consumptionDaysAhead is not supported")
    }

    private fun isMarketEnabledForWeeklySQRNetNeedCalculation(
        market: String,
        statsigFeatureFlagClient: StatsigFeatureFlagClient
    ): Boolean =
        statsigFeatureFlagClient.isEnabledFor(
            EnableWeeklySQROnStockOverview(setOf(ContextData(MARKET, market))),
        )

    private fun getMarketFromDcCode(dcCodes: List<String>): String {
        require(dcCodes.isNotEmpty()) { "dcCodes could not be empty." }

        val market = dcConfigService.dcConfigurations[dcCodes.first()]?.market ?: run {
            val message = "Error: market value not found for the dcCode ${dcCodes.first()}"
            logger.warn(message)
            throw NotFoundException(message)
        }

        return market
    }

    companion object : Logging
}

data class CalculationsPage<T>(
    val calculationPage: List<T>,
    val totalPages: Int,
    val totalSkuAtRiskCount: Int,
    val totalSkuCount: Int,
)
