package com.hellofresh.cif.api.error

import com.fasterxml.jackson.databind.exc.InvalidFormatException
import com.hellofresh.cif.api.calculation.generated.model.ErrorResponse
import com.hellofresh.cif.api.ktor.RequestValidationException
import com.hellofresh.cif.api.note.NoteNotFoundException
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.ApplicationCall
import io.ktor.server.request.uri
import io.ktor.server.response.respond
import org.apache.logging.log4j.kotlin.logger

fun mapToErrorResponse(throwable: Throwable) = ErrorResponse(throwable.message)

suspend fun ApplicationCall.handleResponseError(throwable: Throwable) {
    val errorResponse = mapToErrorResponse(throwable)
    logger().warn("Failed to process request ${this.request.uri}", throwable)
    when (throwable) {
        is RequestValidationException -> respond(throwable.statusCode, errorResponse)
        is IllegalArgumentException -> respond(HttpStatusCode.BadRequest, errorResponse)
        is NoteNotFoundException -> respond(HttpStatusCode.NotFound, errorResponse)
        is InvalidFormatException -> respond(HttpStatusCode.BadRequest, errorResponse)
        else -> respond(HttpStatusCode.InternalServerError, errorResponse)
    }
}
