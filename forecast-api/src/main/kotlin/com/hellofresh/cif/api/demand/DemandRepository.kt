package com.hellofresh.cif.api.demand

import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.util.UUID

interface DemandRepository {
    suspend fun getLatestDemandUpdates(
        dcCodes: List<DcCode>,
        weeks: List<DcWeek>
    ): Map<DcCode, List<DcDemandTimestampWeek>>

    suspend fun getDemandByMarket(
        dcWeeks: List<DcWeek>,
        market: String,
        timestamp: LocalDateTime?
    ): MarketDemandByWeek
}

data class DcDemandTimestampWeek(
    val dcCode: DcCode,
    val dcWeek: DcWeek,
    val latestDemandTime: OffsetDateTime
)

data class MarketDemand(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate,
    val demandQty: Long
)

data class MarketDemandByWeek(
    val timeStamp: LocalDateTime,
    val marketDemand: List<MarketDemand>
)
