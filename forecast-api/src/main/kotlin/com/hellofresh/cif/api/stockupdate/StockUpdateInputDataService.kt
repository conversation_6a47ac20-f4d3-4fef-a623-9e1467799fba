package com.hellofresh.cif.api.stockupdate

import com.hellofresh.cif.api.stockupdate.model.StockUpdateInputDataDetails
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.demand.DemandRepositoryImpl
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.InventoryService.Companion.calculationInputDcDateRange
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderInbounds
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.safetystock.model.SafetyStockKey
import com.hellofresh.cif.safetystock.model.SafetyStockValue
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.cif.transferorder.db.TransferOrderRepository
import com.hellofresh.cif.transferorder.model.TransferOrder
import com.hellofresh.cif.transferorder.model.TransferOrderInbounds
import com.hellofresh.cif.transferorder.model.TransferOrderOutbounds
import com.hellofresh.demand.models.Demand
import com.hellofresh.demand.models.Demands
import com.hellofresh.inventory.models.InventorySnapshots
import com.hellofresh.sku.models.SkuSpecification
import com.hellofresh.sku.models.SupplierSkuDetail
import java.util.UUID
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.async
import kotlinx.coroutines.withContext

@Suppress("LongParameterList")
class StockUpdateInputDataService(
    private val skuInputDataRepository: SkuInputDataRepository,
    private val inventoryService: InventoryService,
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val transferOrderRepository: TransferOrderRepository,
    private val demandRepository: DemandRepositoryImpl,
    private val safetyStockRepository: SafetyStockRepository,
    private val preproductionCleardownDcs: Set<String>
) {

    suspend fun fetchInputData(
        dcCode: String,
        skuId: UUID,
        calculatorMode: CalculatorMode
    ): StockUpdateInputDataDetails =
        withContext(Dispatchers.IO) {
            val skuInputData = skuInputDataRepository.fetchSkuInputData(dcCode, skuId)
            val dcConfig = requireNotNull(skuInputData.dcConfig[dcCode]) { "Dc Configuration not found for $dcCode" }
            val skuSpec = requireNotNull(skuInputData.skuSpecification[skuId]) {
                "Sku Specification not found for $dcCode/$skuId"
            }
            val suppliersSku = skuInputData.supplierSkuDetails[skuId] ?: emptyList()

            val inventorySnapshots = inventoryService.fetchInventorySnapshots(setOf(dcConfig), skuId)

            val dateRange = calculationInputDcDateRange(dcConfig, inventorySnapshots)

            val purchaseOrdersDeferred = async { fetchPurchaseOrderForSku(dcCode, skuId, dateRange) }
            val transferOrderInboundsDeferred = async {
                transferOrderRepository.fetchInboundOrders(setOf(dcCode), dateRange)
            }
            val transferOrderOutboundsDeferred = async {
                transferOrderRepository.fetchOutboundOrders(setOf(dcCode), dateRange)
            }
            val demandsDeferred = async { demandRepository.findDemands(setOf(dcCode), dateRange, skuId) }
            val safetyStocksDeferred = async { fetchSafetyStocks(dcConfig, dateRange) }
            val demandConsumptionDetails = demandRepository.findDemandConsumptionDetails(
                setOf(dcCode),
                dateRange,
                skuId,
            )
            StockUpdateInputDataDetails(
                inputData = mapToInputData(
                    calculatorMode,
                    skuId,
                    skuSpec,
                    dcConfig,
                    inventorySnapshots,
                    purchaseOrders = purchaseOrdersDeferred.await(),
                    transferOrderInbounds = transferOrderInboundsDeferred.await(),
                    transferOrderOutbounds = transferOrderOutboundsDeferred.await(),
                    demandsDeferred.await(),
                    emptyMap(),
                    safetyStocksDeferred.await(),
                    suppliersSku,
                    preproductionCleardownDcs
                ),
                demandConsumptionDetails = demandConsumptionDetails,
            )
        }

    private suspend fun fetchPurchaseOrderForSku(dcCode: String, skuId: UUID, dateRange: DateRange): List<PurchaseOrder> {
        val purchaseOrders = purchaseOrderRepository.findPurchaseOrdersWithAsns(skuId, setOf(dcCode), setOf(dateRange))

        return purchaseOrders.map { po ->
            val poSkusForGivenSkuId = po.purchaseOrderSkus.filter { poSku -> poSku.skuId == skuId }

            val asnsForGivenSkuId = po.asns.filter { poSku -> poSku.skuId == skuId }

            po.copy(
                purchaseOrderSkus = poSkusForGivenSkuId,
                asns = asnsForGivenSkuId,
            )
        }
    }

    private suspend fun fetchSafetyStocks(
        dcConfig: DistributionCenterConfiguration,
        dcsDateRange: DateRange
    ): Map<SafetyStockKey, SafetyStockValue> =
        safetyStockRepository.fetchSafetyStock(
            DcWeek(dcsDateRange.fromDate, dcConfig.productionStart).value,
            dcConfig.dcCode,
        )
            .associate { it.toKey() to SafetyStockValue(it.value, it.strategy) }

    companion object {

        @Suppress("LongParameterList")
        internal fun mapToInputData(
            calculatorMode: CalculatorMode,
            skuId: UUID,
            skuSpecification: SkuSpecification,
            distributionCenterConfiguration: DistributionCenterConfiguration,
            inventorySnapshots: InventorySnapshots,
            purchaseOrders: List<PurchaseOrder>,
            transferOrderInbounds: List<TransferOrder>,
            transferOrderOutbounds: List<TransferOrder>,
            demands: List<Demand>,
            stockUpdates: Map<CalculationKey, SkuQuantity>,
            safetyStocks: Map<SafetyStockKey, SafetyStockValue>,
            suppliersSku: List<SupplierSkuDetail>,
            preproductionCleardownDcs: Set<String>
        ): InputData =
            InputData(
                mode = calculatorMode,
                skuDcCandidates =
                setOf(
                    SkuDcCandidate(skuId, skuSpecification, distributionCenterConfiguration),
                ),
                inventory = inventorySnapshots,
                demands = Demands(demands),
                purchaseOrderInbounds = PurchaseOrderInbounds(purchaseOrders),
                transferOrderInbounds = TransferOrderInbounds(transferOrderInbounds),
                transferOrderOutbounds = TransferOrderOutbounds(transferOrderOutbounds),
                stockUpdates = stockUpdates,
                supplierSku = mapOf(skuId to suppliersSku),
                safetyStocks = safetyStocks,
                preproductionCleardownDcs = preproductionCleardownDcs
            )
    }
}
