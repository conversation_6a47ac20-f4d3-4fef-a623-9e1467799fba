package com.hellofresh.cif.api.stockupdate

import com.hellofresh.cif.api.calculation.CalculationResponseMapper
import com.hellofresh.cif.api.calculation.generated.model.SkuStockUpdatesResponse
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateItem
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateRequest
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateV2Item
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateV2Response
import com.hellofresh.cif.api.stockupdate.StockUpdateMapper.buildStockUpdateDto
import com.hellofresh.cif.api.stockupdate.StockUpdateMapper.buildStockUpdates
import com.hellofresh.cif.api.stockupdate.StockUpdateMapper.buildStockUpdatesResponse
import com.hellofresh.cif.api.stockupdate.model.StockUpdateSimulationData
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateApiRepository
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateView
import com.hellofresh.cif.api.stockupdate.simulation.StockUpdateSimulationResponseMapper
import com.hellofresh.cif.api.user.LoggedInUserInfo
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.validateDates
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

// TODO CPS IT WILL BE REMOVED AS SOON AS WE REMOVE OLD FUNCTIONS
@SuppressWarnings("TooManyFunctions")
class StockUpdateApiService(
    private val stockUpdateService: StockUpdateService,
    private val stockUpdateCalculationService: StockUpdateCalculationService,
    private val stockUpdateApiRepository: StockUpdateApiRepository,
    private val dcConfigService: DcConfigService,
    private val skuInputDataRepository: SkuInputDataRepository,
) {

    suspend fun getStockUpdates(
        dcCode: String,
        skuId: UUID,
        calculatorMode: CalculatorMode
    ): SkuStockUpdatesResponse {
        val existingStockUpdates = getCurrentStockUpdate(dcCode, skuId)
        val calculations = stockUpdateCalculationService.runStockUpdatesComparison(
            dcCode,
            skuId,
            calculatorMode,
            buildStockUpdateForCalculations(existingStockUpdates),
        )
        return buildStockUpdatesResponse(calculations, existingStockUpdates)
    }

    suspend fun getAllStockUpdates(
        dcCode: String,
        week: String,
    ): List<StockUpdateView> = stockUpdateApiRepository.getAllStockUpdates(dcCode, week)

    suspend fun simulateStockUpdates(
        dcCode: String,
        skuId: UUID,
        calculatorMode: CalculatorMode,
        stockUpdatesQuantities: Map<LocalDate, BigDecimal>
    ): List<StockUpdateItem> {
        val existingStockUpdates = getCurrentStockUpdate(dcCode, skuId, stockUpdatesQuantities.keys)

        val stockUpdatesComparison = stockUpdateCalculationService.runStockUpdatesComparisonWithoutUom(
            dcCode = dcCode,
            skuId = skuId,
            calculatorMode = calculatorMode,
            stockUpdates = stockUpdatesQuantities
                .mapKeys { (date, _) -> CalculationKey(skuId, dcCode, date) },
        )

        return buildStockUpdates(stockUpdatesComparison, existingStockUpdates)
    }

    suspend fun upsertStockUpdate(
        stockUpdateRequest: StockUpdateRequest,
        skuId: UUID,
        dcCode: String,
        userInfo: LoggedInUserInfo
    ): List<StockUpdate> {
        val dcConfig =
            requireNotNull(dcConfigService.dcConfigurations[dcCode]) { "Dc Configuration not found for $dcCode" }
        val skuUOM = getSkuUom(skuId)
        val stockUpdates = buildStockUpdateDto(stockUpdateRequest, skuId, dcCode, skuUOM, userInfo, dcConfig)
        validateStockUpdateDateRange(dcCode, stockUpdates.map { it.date }.toSet())
        return stockUpdateApiRepository.upsertStockUpdate(stockUpdates)
    }

    @SuppressWarnings("LongParameterList")
    suspend fun upsertStockUpdateAndSimulate(
        stockUpdateRequest: StockUpdateRequest,
        skuId: UUID,
        dcCode: String,
        weeks: Set<String>,
        calculatorMode: CalculatorMode,
        userInfo: LoggedInUserInfo,
        statsigFeatureFlagClient: StatsigFeatureFlagClient,
        usableInventoryEvaluator: UsableInventoryEvaluator,
    ): StockUpdateV2Response {
        upsertStockUpdate(stockUpdateRequest, skuId, dcCode, userInfo)
        val currents = getCurrentStockUpdate(dcCode = dcCode, skuId = skuId)
        val stockUpdateSimulationData = StockUpdateSimulationData(
            dcCode = dcCode,
            skuId = skuId,
            weeks = weeks,
            calculatorMode = calculatorMode,
            stockUpdates = currents.mapNotNull { (localDate, stockUpdate) ->
                stockUpdate?.let { localDate to it.quantity.getValue() }
            }.toMap(),
        )
        val calculationResults = performStockUpdateSimulation(stockUpdateSimulationData)
        val stockUpdateSimulationResponse = StockUpdateSimulationResponseMapper(
            CalculationResponseMapper(statsigFeatureFlagClient = statsigFeatureFlagClient),
        )
            .mapToStockUpdateSimulationResponse(dcCode, calculationResults, usableInventoryEvaluator)

        return StockUpdateV2Response(
            stockUpdates = calculationResults.stockUpdates,
            calculations = stockUpdateSimulationResponse.calculations,
        )
    }

    suspend fun performStockUpdateSimulation(stockUpdateSimulationData: StockUpdateSimulationData): StockUpdateSimulationResult {
        val existingStockUpdates = getCurrentStockUpdate(
            stockUpdateSimulationData.dcCode,
            stockUpdateSimulationData.skuId,
            stockUpdateSimulationData.stockUpdates.keys,
        )

        val stockUpdateResults = stockUpdateCalculationService.runStockUpdatesWithoutUom(
            stockUpdateSimulationData.dcCode,
            stockUpdateSimulationData.skuId,
            stockUpdateSimulationData.weeks,
            stockUpdateSimulationData.calculatorMode,
            stockUpdateSimulationData.stockUpdateSimulations(),
        )

        return StockUpdateSimulationResult(
            stockUpdateResults = stockUpdateResults,
            stockUpdates = buildStockUpdates(existingStockUpdates, stockUpdateResults),
        )
    }

    private fun buildStockUpdateForCalculations(
        stockUpdates: Map<LocalDate, StockUpdate?>
    ): Map<CalculationKey, SkuQuantity> = stockUpdates.mapNotNull { (date, stockUpdate) ->
        stockUpdate?.let {
            CalculationKey(stockUpdate.skuId, stockUpdate.dcCode, date) to stockUpdate.quantity
        }
    }.toMap()

    suspend fun getCurrentStockUpdate(dcCode: String, skuId: UUID, dates: Set<LocalDate>? = null) =
        stockUpdateService.getCurrentStockUpdate(dcCode, skuId)
            .also { stockUpdatesMap ->
                dates?.also { stockUpdatesMap.keys.validateDates(dcCode, dates) }
            }

    suspend fun getCurrentStockUpdates(dcCodes: Set<String>) = stockUpdateService.getCurrentStockUpdates(dcCodes)

    suspend fun runStockUpdates(
        dcCode: String,
        skuId: UUID,
        weeks: Set<String>,
        stockUpdates: Map<CalculationKey, SkuQuantity>,
        calculatorMode: CalculatorMode
    ): StockUpdateResults =
        stockUpdateCalculationService.runStockUpdates(dcCode, skuId, weeks, stockUpdates, calculatorMode)

    suspend fun validateStockUpdateDateRange(dcCode: String, dates: Set<LocalDate>) {
        requireNotNull(
            stockUpdateService.getCurrentStockUpdateRange(dcCode),
        ) { "Couldn't get current stock update range for dc code $dcCode" }
            .also { stockUpdateRange ->
                stockUpdateRange.validateDates(dcCode, dates)
            }
    }

    suspend fun getSkuUom(skuId: UUID) =
        requireNotNull(
            skuInputDataRepository.fetchSkus(setOf(skuId))[skuId],
        ) { "Sku not found to Simulate stock updates $skuId" }
            .uom
}

data class StockUpdateSimulationResult(
    private val stockUpdateResults: StockUpdateResults,
    val stockUpdates: List<StockUpdateV2Item>
) {
    val calculations: List<DayCalculationResult>
        get() = stockUpdateResults.calculations

    val skuCandidates: Set<SkuDcCandidate>
        get() = stockUpdateResults.skuCandidates

    val demandConsumptionDetails: Map<com.hellofresh.demand.models.DemandKey, ConsumptionDetails>
        get() = stockUpdateResults.demandConsumptionDetails
}
