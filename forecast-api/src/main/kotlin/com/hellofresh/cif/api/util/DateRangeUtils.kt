package com.hellofresh.cif.api.util

import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.models.DateRange
import java.time.LocalDate

internal fun computeDateRanges(
    config: ConfigService,
    dcCodes: Set<String>,
    weeks: Set<DcWeek>?,
    fromDate: LocalDate?,
    toDate: LocalDate?
): List<DateRange> = fromDate?.let {
    listOf(
        DateRange(
            it, toDate ?: error("toDate can’t be null when fromDate is set")
        )
    )
} ?: dcCodes.flatMap { dcCode ->
    val dcConfig = config.getByCode(dcCode) ?: error("DC not found $dcCode")
    weeks?.map { getDateRange(it, dcConfig) } ?: emptyList()
}

internal fun getDateRange(
    it: Dc<PERSON>eek,
    dcConfig: DistributionCenterConfiguration
) = DateRange(
    it.getStartDateInDcWeek(dcConfig.productionStart, dcConfig.zoneId),
    it.getLastDateInDcWeek(dcConfig.productionStart, dcConfig.zoneId),
)
