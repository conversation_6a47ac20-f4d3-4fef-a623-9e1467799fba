package com.hellofresh.cif.api.inventory

import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.RequestValidationException
import com.hellofresh.cif.api.ktor.configureSerialization
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.server.application.Application
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.routing
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun Routing.inventory(
    skuDetailService: SkuInventoryDetailService,
    timeoutInMillis: Long
) = authenticate {
    get("/sku/{skuId}/detail") {
        kotlin.runCatching {
            val skuDetailRequest = call.parameters.toSkuDetailRequest()
            withTimeout(timeoutInMillis) {
                skuDetailService.fetchCurrentInventory(skuDetailRequest.skuId, skuDetailRequest.dcCodes)
            }
        }
            .onSuccess { result ->
                // FIXME: The openapi defines an object containing a list of SkuDetails but here an array is returned
                val response = result
                    .map(::toSkuDetailResponse)
                    .toList()
                call.respond(HttpStatusCode.OK, response)
            }
            .onFailure {
                val errorResponse = mapToErrorResponse(it)
                if (it is RequestValidationException) {
                    call.respond(HttpStatusCode.BadRequest, errorResponse)
                } else {
                    call.respond(HttpStatusCode.InternalServerError, errorResponse)
                }
            }
    }
}

fun inventoryRoutingModule(
    skuDetailService: SkuInventoryDetailService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        inventory(skuDetailService, timeout.inWholeMilliseconds).also {
            configureSerialization(it)
        }
    }
}
private fun Parameters.toSkuDetailRequest() = SkuDetailRequest(
    skuId = UUID.fromString(getOrThrow("skuId").trim()),
    dcCodes = parseDcCodes(),
)

private fun Parameters.parseDcCodes(): Set<String> {
    val dcCodes = getAllOrThrow("dcCodes")
        .map { it.trim().uppercase() }
        .filter { it.isNotBlank() }
        .toSet()

    if (dcCodes.isEmpty()) throw RequestValidationException("dcCode")

    return dcCodes
}
