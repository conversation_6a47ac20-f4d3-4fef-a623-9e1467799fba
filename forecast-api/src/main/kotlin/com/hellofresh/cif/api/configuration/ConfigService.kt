package com.hellofresh.cif.api.configuration

import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.ResolvedCleardown
import kotlinx.coroutines.runBlocking

class ConfigService(
    private val dcConfigService: DcConfigService,
    private val inventoryService: InventoryService,
) {
    fun fetchByMarket(market: String): Map<String, DcConfiguration> {
        val dcs = dcConfigService.dcConfigurations.values.asSequence().filter {
            it.market == market && it.enabled
        }
        val dcWithLastCleardown = runBlocking {
            inventoryService.getResolvedLatestCleardownTime(
                dcs.map { it.dcCode }.toSet(),
            )
        }
        return dcs.map { it.toDcConfiguration(dcWithLastCleardown) }.associateBy { it.dcCode }
    }

    private fun DistributionCenterConfiguration.toDcConfiguration(
        dcWithLastCleardownTime: Map<String, ResolvedCleardown>
    ): DcConfiguration = DcConfiguration(
        dcCode = this.dcCode,
        market = this.market,
        productionStartDay = this.productionStart,
        clearDownDay = this.cleardown,
        zoneId = this.zoneId,
        lastCleardownTime = dcWithLastCleardownTime[this.dcCode]?.cleardownTime?.toOffsetDateTime(),
        hasCleardown = this.hasCleardown,
        poCutoffTime = this.poCutoffTime,
    )

    fun getByCode(dcCode: String) = dcConfigService.dcConfigurations[dcCode]
}
