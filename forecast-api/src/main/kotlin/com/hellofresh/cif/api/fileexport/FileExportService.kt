package com.hellofresh.cif.api.fileexport

import com.hellofresh.cif.api.fileexport.repository.FileExportRequest
import com.hellofresh.cif.api.fileexport.repository.FileExportRequestRepository
import java.util.UUID

class FileExportService(
    private val csvExportRequestRepository: FileExportRequestRepository
) {
    suspend fun getFileExportRequest(requestId: UUID): FileExportRequest? =
        csvExportRequestRepository.fetchFileExportRequest(requestId)

    suspend fun saveFileExportRequest(jsonParameters: String): FileExportRequest =
        csvExportRequestRepository.saveFileExportRequest(jsonParameters)

    suspend fun updateFileExportRequest(fileExportRequest: FileExportRequest): FileExportRequest? =
        csvExportRequestRepository.updateFileExportRequest(fileExportRequest)
}
