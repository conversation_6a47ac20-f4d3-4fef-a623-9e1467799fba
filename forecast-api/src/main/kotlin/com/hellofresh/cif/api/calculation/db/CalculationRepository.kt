package com.hellofresh.cif.api.calculation.db

import com.hellofresh.cif.api.calculation.CalculationFilterRequest
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.CalculationsPage

const val CSKU_ID = "csku_id"
const val DC_CODE = "dc_code"
const val DATE = "date"
const val PRODUCTION_WEEK = "production_week"

interface CalculationRepository {
    suspend fun fetchPageableCalculations(calculationRequest: CalculationRequest): CalculationsPage<CalculationRecord>
    suspend fun fetchCalculationFilters(calculationFilterRequest: CalculationFilterRequest): CalculationFiltersData
}
