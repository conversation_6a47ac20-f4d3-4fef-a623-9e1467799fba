package com.hellofresh.cif.api.calculation.stockupdate

import com.hellofresh.cif.api.calculation.AllPages
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.InventoryRefreshType
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.api.calculation.db.toSkuUom
import com.hellofresh.cif.api.calculation.stockupdate.DayCalculationResultMapper.toCalculationRecord
import com.hellofresh.cif.api.calculation.toKey
import com.hellofresh.cif.api.stockupdate.StockUpdateApiService
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.Context
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.StockUpdate as StockUpdateFlag
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.inventory.CurrentStockUpdates
import com.hellofresh.cif.inventory.DcSku
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.lib.groupByFirst
import com.hellofresh.cif.lib.letIfNotEmpty
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.async
import kotlinx.coroutines.awaitAll
import kotlinx.coroutines.withContext
import org.apache.logging.log4j.kotlin.Logging

class CalculationsPendingStockUpdateService(
    private val dcConfigService: DcConfigService,
    private val stockUpdateApiService: StockUpdateApiService,
    private val featureFlagClient: StatsigFeatureFlagClient
) {

    /**
     * Returns Pending Calculations to use from stock updates synchronization
     */
    suspend fun processPendingCalculations(
        request: CalculationRequest,
        calculations: Map<CalculationKey, CalculationRecord>,
        currentStockUpdates: CurrentStockUpdates,
        calculationFunction: suspend (CalculationRequest) -> List<CalculationRecord>
    ): Map<CalculationKey, CalculationRecord> {
        if (currentStockUpdates.isEmpty()) {
            return emptyMap()
        }
        val resultsSkuCodeByDcSkuWeek =
            calculations.map { (k, v) -> DcSkuWeek(k.dcCode, k.cskuId, v.productionWeek) to v.code }.toMap()

        val stockUpdatesDcSkuWeek = filterStockUpdatesInResults(resultsSkuCodeByDcSkuWeek.keys, currentStockUpdates)

        val minStockUpdatesWeek = stockUpdatesDcSkuWeek.keys.minOfOrNull { it.week }

        return if (minStockUpdatesWeek != null &&
            resultsSkuCodeByDcSkuWeek.any { it.key.week >= minStockUpdatesWeek }
        ) {
            val missingCalculationRecords =
                fetchMissingCalculationStockUpdateWeek(
                    request,
                    stockUpdatesDcSkuWeek,
                    resultsSkuCodeByDcSkuWeek,
                    calculationFunction,
                )

            processStockUpdates(
                request,
                stockUpdatesDcSkuWeek,
                resultsSkuCodeByDcSkuWeek.keys,
                calculations,
                missingCalculationRecords,
            )
        } else {
            emptyMap()
        }
    }

    // This can be removed when upgrading kotlinx coroutines lib (needs ktor upgrade)
    @OptIn(ExperimentalCoroutinesApi::class)
    private suspend fun processStockUpdates(
        request: CalculationRequest,
        stockUpdatesDcSkuWeek: Map<DcSkuWeek, Map<CalculationKey, StockUpdate?>>,
        resultsSkuCodeByDcSkuWeek: Set<DcSkuWeek>,
        calculations: Map<CalculationKey, CalculationRecord>,
        missingCalculationsFromStockUpdateWeeks: Map<CalculationKey, CalculationRecord>
    ): Map<CalculationKey, CalculationRecord> {
        logger.info(
            "Checking Pending calculations for dc and weeks: ${resultsSkuCodeByDcSkuWeek.map { it.dcCode to it.week }.toSet()}",
        )

        val allCalculations = calculations + missingCalculationsFromStockUpdateWeeks
        val pendingCalculationDcSku =
            stockUpdatesDcSkuWeek
                .filterValues { stockUpdatesByCalcKey ->
                    stockUpdatesByCalcKey
                        .any { (calcKey, stockUpdate) ->
                            allCalculations[calcKey]?.let { calcRecord ->
                                stockUpdate?.quantity?.getValue() != calcRecord.stockUpdate &&
                                    stockUpdate?.quantity?.unitOfMeasure == calcRecord.uom.toSkuUom()
                            } == true
                        }
                }.map { (dcSkuWeek, stockUpdatesByCalcKey) ->
                    DcSku(
                        dcSkuWeek.dcCode,
                        dcSkuWeek.skuId,
                    ) to stockUpdatesByCalcKey
                }
                .groupByFirst()

        return if (pendingCalculationDcSku.isNotEmpty()) {
            logger.info(
                "Pending calculations found for: ${pendingCalculationDcSku.keys}," +
                    " with stock updates: ${stockUpdatesDcSkuWeek.keys}",
            )

            withContext(Dispatchers.Default.limitedParallelism(2)) {
                pendingCalculationDcSku
                    .map { (dcSku, stockUpdates) ->
                        val stockUpdatesValues = stockUpdates.flatMap { it.entries }
                            .mapNotNull { (date, stockUpdate) -> stockUpdate?.let { date to stockUpdate.quantity } }
                            .toMap()
                        async {
                            // Run calculation for not in sync dc and sku
                            stockUpdateApiService.runStockUpdates(
                                dcSku.dcCode,
                                dcSku.skuId,
                                resultsSkuCodeByDcSkuWeek.map { it.week }.toSet(),
                                stockUpdatesValues,
                                selectCalculatorModeRepository(request.consumptionDaysAhead),
                            ).calculations
                        }
                    }
            }.awaitAll().flatten()
                .mapNotNull { dailyCalculationResult ->
                    val key = CalculationKey(
                        dailyCalculationResult.cskuId,
                        dailyCalculationResult.dcCode,
                        dailyCalculationResult.date,
                    )
                    // Create pending results for existing returning keys only
                    calculations[key]
                        ?.let {
                            key to toCalculationRecord(dailyCalculationResult, it)
                        }
                }.toMap()
        } else {
            emptyMap()
        }
    }

    private suspend fun fetchMissingCalculationStockUpdateWeek(
        request: CalculationRequest,
        stockUpdatesDcSkuWeek: Map<DcSkuWeek, Map<CalculationKey, StockUpdate?>>,
        resultsSkuCodeByDcSkuWeek: Map<DcSkuWeek, String>,
        calculationFunction: suspend (CalculationRequest) -> List<CalculationRecord>
    ): Map<CalculationKey, CalculationRecord> {
        val missingCalculationsForStockUpdateWeekKeys = stockUpdatesDcSkuWeek.keys.filter {
            !resultsSkuCodeByDcSkuWeek.contains(
                it,
            )
        }

        return if (missingCalculationsForStockUpdateWeekKeys.isNotEmpty() &&
            stockUpdatesDcSkuWeek
                .flatMap { (_, v) -> v.mapNotNull { it.value } }
                .maxOf { it.createdAt } > LocalDateTime.now(ZoneOffset.UTC).minus(5.minutes.toJavaDuration())
        ) {
            logger.info(
                "Fetching missing to evaluate pending calculations: ${missingCalculationsForStockUpdateWeekKeys.map { it.dcCode to it.week }.toSet()}",
            )

            val skuCodesMap =
                resultsSkuCodeByDcSkuWeek.map { (dcSkuWeek, skuCode) -> dcSkuWeek.skuId to skuCode }.toMap()
            calculationFunction(
                CalculationRequest(
                    dcCodes = missingCalculationsForStockUpdateWeekKeys.map { it.dcCode }.distinct(),
                    weeks = missingCalculationsForStockUpdateWeekKeys.map { it.week }.distinct(),
                    skuCodes = missingCalculationsForStockUpdateWeekKeys.mapNotNull { skuCodesMap[it.skuId] }
                        .distinct(),
                    pageRequest = AllPages,
                    consumptionDaysAhead = request.consumptionDaysAhead,
                    inventoryRefreshType = request.inventoryRefreshType,
                ),
            ).filter {
                missingCalculationsForStockUpdateWeekKeys.contains(
                    DcSkuWeek(it.dcCode, it.cskuId, it.productionWeek),
                )
            }.associateBy { it.toKey() }
        } else {
            emptyMap()
        }
    }

    suspend fun getCurrentStockUpdates(request: CalculationRequest) =
        if (request.inventoryRefreshType == InventoryRefreshType.CLEARDOWN &&
            selectCalculatorModeRepository(request.consumptionDaysAhead) == CalculatorMode.PRODUCTION
        ) {
            request.dcCodes.mapNotNull { dcConfigService.dcConfigurations[it] }
                .filter {
                    featureFlagClient.isEnabledFor(StockUpdateFlag(setOf(ContextData(Context.MARKET, it.market))))
                }.map { it.dcCode }
                .letIfNotEmpty {
                    stockUpdateApiService.getCurrentStockUpdates(it.toSet())
                } ?: emptyMap()
        } else {
            emptyMap()
        }

    private fun filterStockUpdatesInResults(
        resultsDcSkuWeeks: Set<DcSkuWeek>,
        currentStockUpdates: CurrentStockUpdates
    ): Map<DcSkuWeek, Map<CalculationKey, StockUpdate?>> {
        val resultDcSku = resultsDcSkuWeeks.map { DcSku(it.dcCode, it.skuId) }.toSet()
        return currentStockUpdates
            .filter { (dcSku, _) -> resultDcSku.contains(dcSku) }
            .flatMap { (dcSku, stockUpdatesByDate) ->
                stockUpdatesByDate
                    .mapNotNull { (date, stockUpdate) ->
                        dcConfigService.dcConfigurations[dcSku.dcCode]
                            ?.let { dc ->
                                DcSkuWeek(
                                    dcSku.dcCode,
                                    dcSku.skuId,
                                    DcWeek.Companion(date, dc.productionStart).value,
                                ) to (CalculationKey(dcSku.skuId, dcSku.dcCode, date) to stockUpdate)
                            }
                    }
            }
            .groupByFirst().mapValues { (_, entries) -> entries.toMap() }
    }

    companion object : Logging {

        internal fun selectCalculatorModeRepository(consumptionDaysAhead: Int) = when (consumptionDaysAhead) {
            0 -> CalculatorMode.PRODUCTION
            1 -> CalculatorMode.PRE_PRODUCTION
            else -> throw IllegalArgumentException("Calculating for $consumptionDaysAhead is not supported")
        }

        private data class DcSkuWeek(
            val dcCode: String,
            val skuId: UUID,
            val week: String
        )
    }
}
