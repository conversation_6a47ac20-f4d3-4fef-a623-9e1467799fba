package com.hellofresh.cif.api.stockupdate

import com.hellofresh.cif.calculator.CalculatorClient
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.InputData
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.demand.models.ConsumptionDetails
import java.math.BigDecimal
import java.util.UUID

class StockUpdateCalculationService(
    private val calculatorClient: CalculatorClient,
    private val stockUpdateInputDataService: StockUpdateInputDataService,
) {

    suspend fun runStockUpdatesComparison(
        dcCode: String,
        skuId: UUID,
        calculatorMode: CalculatorMode,
        stockUpdates: Map<CalculationKey, SkuQuantity>,
        weeks: Set<String> = emptySet()
    ): StockUpdateComparison {
        val calculationInputDataDetails = stockUpdateInputDataService.fetchInputData(
            dcCode,
            skuId,
            calculatorMode
        )
        return runStockUpdatesComparison(calculationInputDataDetails.inputData, stockUpdates, weeks)
    }

    suspend fun runStockUpdatesComparisonWithoutUom(
        dcCode: String,
        skuId: UUID,
        calculatorMode: CalculatorMode,
        stockUpdates: Map<CalculationKey, BigDecimal>,
        weeks: Set<String> = emptySet()
    ): StockUpdateComparison {
        val calculationInputDataDetails = stockUpdateInputDataService.fetchInputData(
            dcCode,
            skuId,
            calculatorMode,
        )
        return runStockUpdatesComparison(
            calculationInputDataDetails.inputData,
            getStockUpdatesSkuQuantity(stockUpdates, calculationInputDataDetails.inputData),
            weeks,
        )
    }

    private fun runStockUpdatesComparison(
        inputData: InputData,
        stockUpdates: Map<CalculationKey, SkuQuantity>,
        weeks: Set<String> = emptySet()
    ): StockUpdateComparison {
        val beforeCalculations = runCalculations(inputData, weeks)

        val afterCalculations =
            if (stockUpdates.isNotEmpty()) {
                runCalculations(
                    inputData.copy(stockUpdates = stockUpdates),
                    weeks,
                )
            } else {
                beforeCalculations
            }

        return StockUpdateComparison(beforeCalculations, afterCalculations, stockUpdates)
    }

    suspend fun runStockUpdatesWithoutUom(
        dcCode: String,
        skuId: UUID,
        weeks: Set<String>,
        calculatorMode: CalculatorMode,
        stockUpdates: Map<CalculationKey, BigDecimal>
    ): StockUpdateResults {
        val calculationInputDataDetails = stockUpdateInputDataService.fetchInputData(
            dcCode,
            skuId,
            calculatorMode,
        )
        return runStockUpdates(
            calculationInputDataDetails.inputData,
            weeks,
            getStockUpdatesSkuQuantity(stockUpdates, calculationInputDataDetails.inputData),
            calculationInputDataDetails.demandConsumptionDetails,
        ).copy(
            demandConsumptionDetails = calculationInputDataDetails.demandConsumptionDetails,
        )
    }

    suspend fun runStockUpdates(
        dcCode: String,
        skuId: UUID,
        weeks: Set<String>,
        stockUpdates: Map<CalculationKey, SkuQuantity>,
        calculatorMode: CalculatorMode
    ): StockUpdateResults {
        val calculationInputDataDetails = stockUpdateInputDataService.fetchInputData(dcCode, skuId, calculatorMode)
        return runStockUpdates(
            calculationInputDataDetails.inputData,
            weeks,
            stockUpdates,
            calculationInputDataDetails.demandConsumptionDetails,
        )
    }

    private fun runStockUpdates(
        inputData: InputData,
        weeks: Set<String>,
        stockUpdates: Map<CalculationKey, SkuQuantity>,
        demandConsumptionDetails: Map<com.hellofresh.demand.models.DemandKey, ConsumptionDetails>
    ): StockUpdateResults =
        StockUpdateResults(
            runCalculations(
                inputData = inputData.copy(stockUpdates = stockUpdates),
                weeks = weeks,
            ),
            stockUpdates,
            skuCandidates = inputData.skuDcCandidates,
            demandConsumptionDetails = demandConsumptionDetails,
        )

    private fun runCalculations(
        inputData: InputData,
        weeks: Set<String>
    ): List<DayCalculationResult> =
        filterResults(calculatorClient.runDailyCalculations(inputData), weeks)

    private fun filterResults(dayCalculationsResults: List<DayCalculationResult>, weeks: Set<String>) =
        filter(dayCalculationsResults, weeks)

    companion object {

        fun getStockUpdatesSkuQuantity(stockUpdates: Map<CalculationKey, BigDecimal>, inputData: InputData) =
            stockUpdates.mapValues { (key, value) ->
                SkuQuantity.fromBigDecimal(
                    value,
                    getSkuUom(key.cskuId, inputData),
                )
            }

        fun getSkuUom(skuId: UUID, inputData: InputData) =
            requireNotNull(inputData.skuSpecs[skuId]?.uom) { "SkuUom not found to run stock updates $skuId" }

        internal fun filter(calculationExperimentResults: List<DayCalculationResult>, validWeeks: Set<String>) =
            if (validWeeks.isEmpty()) {
                calculationExperimentResults
            } else {
                calculationExperimentResults.filter {
                    validWeeks.contains(it.productionWeek)
                }
            }
    }
}

data class StockUpdateComparison(
    val defaultCalculation: List<DayCalculationResult>,
    val stockUpdateCalculations: List<DayCalculationResult>,
    val stockUpdates: Map<CalculationKey, SkuQuantity>
)

data class StockUpdateResults(
    val calculations: List<DayCalculationResult>,
    val stockUpdates: Map<CalculationKey, SkuQuantity>,
    val skuCandidates: Set<SkuDcCandidate>,
    val demandConsumptionDetails: Map<com.hellofresh.demand.models.DemandKey, ConsumptionDetails>,
)
