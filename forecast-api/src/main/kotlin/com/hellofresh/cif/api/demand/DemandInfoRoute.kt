package com.hellofresh.cif.api.demand

import com.hellofresh.cif.api.calculation.generated.model.Demand
import com.hellofresh.cif.api.calculation.generated.model.DemandPerWeeksResponse
import com.hellofresh.cif.api.calculation.generated.model.DemandRefreshResponseForDc
import com.hellofresh.cif.api.calculation.generated.model.DemandsRefreshResponse
import com.hellofresh.cif.api.calculation.generated.model.LastUpdatedDemandForWeek
import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import java.time.OffsetDateTime
import java.time.ZoneOffset
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun Routing.demand(
    demandService: DemandService,
    timeout: Duration
) = authenticate {
    route("/forecast") {
        get("") {
            kotlin.runCatching {
                val dcCodes = this.call.parameters.getAllOrThrow("dcCode")
                val weeks = this.call.parameters.getAllOrThrow("weeks").map { DcWeek(it) }
                dcCodes to weeks
            }.onFailure { exception ->
                call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
            }.onSuccess { (dcCodes, weeks) ->
                kotlin.runCatching {
                    withTimeout(timeout.inWholeMilliseconds) {
                        mapDemandInfoResponse(demandService.getLatestDemandInfo(dcCodes, weeks))
                    }
                }.onSuccess { result ->
                    call.respond(HttpStatusCode.OK, result)
                }.onFailure { call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(it)) }
            }
        }
    }
}

fun Routing.demandByMarket(
    demandService: DemandService,
    timeout: Duration
) = authenticate {
    route("/demand/{market}") {
        get("") {
            kotlin.runCatching {
                val market = call.parameters.getOrThrow("market")
                val weeks = call.parameters.getAllOrThrow("weeks").flatMap { it.split(",") }.map { DcWeek(it.trim()) }
                val timestampString = call.parameters["timestamp"]
                DemandByWeekRequestDto(
                    market,
                    weeks,
                    if (timestampString.isNullOrEmpty()) {
                        null
                    } else {
                        OffsetDateTime.parse(timestampString).atZoneSameInstant(ZoneOffset.UTC).toLocalDateTime()
                    },
                )
            }.onFailure { exception ->
                call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
            }.onSuccess { requestDto ->
                kotlin.runCatching {
                    withTimeout(timeout.inWholeMilliseconds) {
                        mapDemandPerWeekResponse(
                            demandService.getDemandByMarket(requestDto.weeks, requestDto.market, requestDto.timestamp)
                        )
                    }
                }.onSuccess { result ->
                    call.respond(HttpStatusCode.OK, result)
                }.onFailure { call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(it)) }
            }
        }
    }
}

fun mapDemandPerWeekResponse(ob: MarketDemandByWeek) =
    DemandPerWeeksResponse(
        ob.marketDemand.map { Demand(it.skuId, it.date, it.dcCode, it.demandQty) },
        ob.timeStamp.atOffset(ZoneOffset.UTC)
    )

fun mapDemandInfoResponse(ob: Map<DcCode, List<DcDemandTimestampWeek>>) =
    DemandsRefreshResponse(
        ob.entries.map {
            DemandRefreshResponseForDc(
                it.key,
                it.value.map { v -> LastUpdatedDemandForWeek(v.dcWeek.toString(), v.latestDemandTime) }
            )
        }
    )

fun demandInfoModule(
    demandService: DemandService,
    timeout: Duration
): Application.() -> Unit = {
    routing {
        demand(demandService, timeout).also {
            configureSerialization(it)
        }
    }
}

fun demandByMarket(
    demandService: DemandService,
    timeout: Duration
): Application.() -> Unit = {
    routing {
        demandByMarket(demandService, timeout).also {
            configureSerialization(it)
        }
    }
}
