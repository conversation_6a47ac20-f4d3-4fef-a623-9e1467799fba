package com.hellofresh.cif.api.calculation

import com.github.benmanes.caffeine.cache.Caffeine
import com.github.benmanes.caffeine.cache.Scheduler
import io.micrometer.core.instrument.Counter
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics
import java.time.Duration
import java.util.concurrent.TimeUnit.MINUTES

class CaffeineCache<in K, V>(
    meterRegistry: MeterRegistry,
    ttl: Duration,
    cacheSize: Long,
) {

    private val hitCounter = Counter.builder("filter_hit_counter")
        .description("Record the number of times the record filter was able to filter a record")
        .register(meterRegistry)

    private val missCounter = Counter.builder("filter_miss_counter")
        .description("Record the number of times the record filter was not able to filter a record")
        .register(meterRegistry)

    // TODO: Add alert on the high miss ratio
    // TODO: Alert on caffeine eviction

    private val cache = Caffeine.newBuilder()
        .scheduler(Scheduler.systemScheduler())
        .expireAfterWrite(ttl.toMinutes(), MINUTES)
        .maximumSize(cacheSize)
        .recordStats()
        .build<K, V>().also {
            CaffeineCacheMetrics.monitor(meterRegistry, it, "db-cache")
        }

    suspend fun getOrPut(key: K, eval: suspend () -> V): V = cache.getIfPresent(key)
        .also { hitCounter.increment() }
        ?: let {
            missCounter.increment()
            eval().also { cache.put(key, it) }
        }

    suspend fun getOrPutCacheable(key: K, eval: suspend () -> Cacheable<V>): V = cache.getIfPresent(key)
        .also { hitCounter.increment() }
        ?: let {
            eval().let {
                if (it.cacheable) {
                    missCounter.increment()
                    cache.put(key, it.value)
                }
                it.value
            }
        }

    data class Cacheable<V>(val value: V, val cacheable: Boolean)
}
