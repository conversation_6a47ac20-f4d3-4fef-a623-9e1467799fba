package com.hellofresh.cif.api.inventory

import com.hellofresh.cif.api.schema.enums.FileType
import com.hellofresh.cif.s3.S3FileService
import java.net.URL
import java.time.Instant
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter
import java.time.temporal.ChronoUnit
import java.util.UUID
import kotlin.time.Duration.Companion.minutes
import kotlin.time.toJavaDuration
import org.apache.logging.log4j.kotlin.Logging

class GenerateUploadUrlService(
    private val s3FileService: S3FileService,
    bucketSuffix: String
) {

    private val bucketName = "cif-stock-files-export$bucketSuffix"

    fun generateUploadUrl(
        market: String,
        fileName: String,
        metadata: Map<String, String>,
        fileType: String
    ): URL {
        val objectKey = buildKey(market, fileName, fileType)

        return s3FileService.getPreSignedPutUrl(
            bucketName,
            expirationDuration,
            objectKey,
            metadata,
        )
    }

    private fun buildKey(market: String, fileName: String, fileType: String): String {
        val now = Instant.now().truncatedTo(ChronoUnit.SECONDS)
        val prefix = getPrefixByFileType(fileType)
        return "$prefix/$market/${yearMonthDayFormatter.format(now)}/" +
            "${UUID.randomUUID()}/$fileName"
    }

    private fun getPrefixByFileType(fileType: String): String = when (fileType) {
        FileType.STOCK_UPDATE.literal -> "stock_updates"
        FileType.STOCK_INVENTORY.literal -> "third_pw_stock_inventory_uploads"
        else -> error("Unknown file type param: $fileType")
    }

    companion object : Logging {
        private val yearMonthDayFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
            .withZone(ZoneOffset.UTC)

        private val expirationDuration = 1.minutes.toJavaDuration()
    }
}
