package com.hellofresh.cif.api.po

import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.getAllOrDefault
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.api.po.PurchaseOrderResponseMapper.mapToPoDetailResponse
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.routing
import java.time.LocalDate
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

internal const val PURCHASE_ORDER_PATH = "/purchase-order"
fun Routing.purchaseOrder(poService: PurchaseOrderService, dcConfigService: DcConfigService, timeout: Duration) =
    authenticate {
        get(PURCHASE_ORDER_PATH) {
            kotlin.runCatching {
                PurchaseOrdersReq(
                    skuId = UUID.fromString(call.parameters.getOrThrow("skuId").trim()),
                    dcCodes = call.parameters.getAllOrThrow("dcCode").map { it.trim().uppercase() }.toSet(),
                    weeks = call.parameters.getAllOrDefault("weeks").map { DcWeek(it) }.toSet(),
                    fromDate = call.parameters["fromDate"]?.let { LocalDate.parse(it) },
                    toDate = call.parameters["toDate"]?.let { LocalDate.parse(it) },
                )
            }.onFailure { exception ->
                call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
            }.onSuccess {
                withTimeout(timeout) {
                    kotlin.runCatching {
                        mapToPoDetailResponse(poService.findPurchaseOrders(it), it.skuId, dcConfigService)
                    }
                }.onSuccess {
                    call.respond(HttpStatusCode.OK, it)
                }.onFailure {
                    call.handleResponseError(it)
                }
            }
        }
    }

data class PurchaseOrdersReq(
    val skuId: UUID,
    val dcCodes: Set<String>,
    val weeks: Set<DcWeek>?,
    val fromDate: LocalDate?,
    val toDate: LocalDate?
) {
    init {
        require(dcCodes.isNotEmpty()) {
            "at least one dc code is required"
        }
        if (weeks.isNullOrEmpty()) {
            require(fromDate != null && toDate != null) {
                "formDate and toDate are required if the weeks query param is not supplied"
            }
        }
        if (fromDate != null || toDate != null) {
            require(fromDate != null && toDate != null) {
                "One of formDate and toDate is null"
            }
            require(fromDate.isBefore(toDate) || fromDate == toDate) {
                "formDate must be before or equal with toDate"
            }
        }
    }
}

fun purchaseOrderModule(
    poService: PurchaseOrderService,
    dcConfigService: DcConfigService,
    timeout: Duration
): Application.() -> Unit = {
    routing {
        purchaseOrder(poService, dcConfigService, timeout).also {
            configureSerialization(it)
        }
    }
}
