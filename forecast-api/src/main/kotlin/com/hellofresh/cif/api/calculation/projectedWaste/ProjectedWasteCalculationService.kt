package com.hellofresh.cif.api.calculation.projectedWaste

import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_PROJECTED_WASTE
import com.hellofresh.cif.api.calculation.CaffeineCache
import com.hellofresh.cif.api.calculation.CalculationsPage
import com.hellofresh.cif.api.calculation.ProjectedWasteCalculationFilterRequest
import com.hellofresh.cif.api.calculation.ProjectedWasteInventory
import com.hellofresh.cif.api.calculation.ProjectedWasteView
import com.hellofresh.cif.api.calculation.db.projectedWaste.ProjectedWasteCalculationRecord
import com.hellofresh.cif.api.calculation.db.projectedWaste.ProjectedWasteCalculationsQueryStep
import com.hellofresh.cif.api.calculation.db.toSkuUom
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import io.micrometer.core.instrument.MeterRegistry
import java.time.DayOfWeek
import java.time.Duration
import java.time.LocalDate
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.time.temporal.ChronoUnit.WEEKS
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import org.jetbrains.annotations.VisibleForTesting

private const val MAX_TWELVE_WEEKS = 12L // 90 DAYS
private const val HASH_SIZE = 500

class ProjectedWasteCalculationService(
    meterRegistry: MeterRegistry,
    private val metricsDSLContext: MetricsDSLContext,
    private val dcConfigService: DcConfigService,
    statsigFeatureFlagClient: StatsigFeatureFlagClient,
) {
    private val projectedWasteViewCache =
        CaffeineCache<ProjectedWasteCalculationFilterRequest, CalculationsPage<ProjectedWasteView>>(
            meterRegistry,
            Duration.ofMinutes(1),
            HASH_SIZE.toLong(),
        )

    private val usableInventoryEvaluator = UsableInventoryEvaluator(statsigFeatureFlagClient)

    suspend fun prepareProjectedWasteDetails(request: ProjectedWasteCalculationFilterRequest):
        CalculationsPage<ProjectedWasteView> =
        withContext(Dispatchers.IO) {
            val dcConfigPair = getEarliestProductionStart(request.dcCodes)

            val dcWeekList = prepareDcWeeks(
                request,
                dcConfigPair.first,
                LocalDate.now(dcConfigPair.second),
            )

            val projectedWasteCalculationRequest = request.copy(
                weeks = dcWeekList,
            )
            projectedWasteViewCache.getOrPut(projectedWasteCalculationRequest) {
                fetchPageableProjectedWasteCalculations(projectedWasteCalculationRequest)
            }
        }

    @VisibleForTesting
    fun prepareDcWeeks(
        request: ProjectedWasteCalculationFilterRequest,
        earliestProductionStart: DayOfWeek,
        date: LocalDate,
    ): List<String> {
        val start = request.expiringInMin?.let {
            WEEKS.between(date, date.plusMonths(it.toLong()))
        } ?: 0L

        val end = request.expiringInMax?.let {
            WEEKS.between(date, date.plusMonths(it.toLong()))
        } ?: MAX_TWELVE_WEEKS

        val dcWeekList = (start..end).map {
            DcWeek(date.plusWeeks(it), earliestProductionStart).value
        }
        return dcWeekList
    }

    private fun getEarliestProductionStart(dcCodes: List<String>): Pair<DayOfWeek, ZoneId> {
        val dcConfigPair = dcConfigService.dcConfigurations
            .filter { (dcCode, _) -> dcCodes.contains(dcCode) }
            .mapNotNull { (dcCode, dcConfig) -> dcCode to dcConfig }
            .minByOrNull { it.second.productionStart }
        requireNotNull(dcConfigPair) { "dcCodes list is expected to be non-empty" }
        return Pair(dcConfigPair.second.productionStart, dcConfigPair.second.zoneId)
    }

    private suspend fun fetchPageableProjectedWasteCalculations(
        projectedWasteCalculationRequest: ProjectedWasteCalculationFilterRequest
    ): CalculationsPage<ProjectedWasteView> {
        val query = ProjectedWasteCalculationsQueryStep(
            metricsDSLContext,
            projectedWasteCalculationRequest.dcCodes,
            projectedWasteCalculationRequest.weeks,
            projectedWasteCalculationRequest.sortBy,
        )
            .withSkus(projectedWasteCalculationRequest.skuCodes)
            .withSkuCategories(projectedWasteCalculationRequest.skuCategories)
            .withAdditionalFilters(projectedWasteCalculationRequest.additionalFilters)
            .withAdditionalFilters(setOf(WITH_PROJECTED_WASTE))

        val calculationRecords = query.fetchPageableCalculations(projectedWasteCalculationRequest.pageRequest)

        return CalculationsPage(
            calculationRecords.calculationPage.map { toProjectedWasteCalculation(it) },
            calculationRecords.totalPages,
            calculationRecords.totalSkuAtRiskCount,
            calculationRecords.totalSkuCount,
        )
    }

    @VisibleForTesting
    fun toProjectedWasteCalculation(record: ProjectedWasteCalculationRecord) = with(record) {
        ProjectedWasteView(
            dcCode = dcCode,
            cskuId = cskuId,
            skuName = skuName,
            skuCode = skuCode,
            acceptableCodeLife = acceptableCodeLife,
            unusableInventory = this.unusableInventory
                ?.filter { it.locationType != null }
                ?.map {
                    val date = LocalDate.now(dcConfigService.dcConfigurations[dcCode]?.zoneId ?: UTC)
                    val usabilityDetails =
                        it.locationType?.let { locationType ->
                            usableInventoryEvaluator.isUsable(dcCode, date, locationType, it.expiryDate, acceptableCodeLife, skuCategory)
                        }
                    ProjectedWasteInventory(
                        inventory = it,
                        usable = usabilityDetails?.usable ?: true,
                        unusableReason = usabilityDetails?.unusableReason,
                    )
                } ?: emptyList(),
            uom = uom.toSkuUom()
        )
    }
}
