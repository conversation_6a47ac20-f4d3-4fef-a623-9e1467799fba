package com.hellofresh.cif.api.supplyQuantityRecommendation

import com.hellofresh.cif.api.calculation.generated.model.SQRStateEnum
import com.hellofresh.cif.api.calculation.generated.model.SkuRiskRatingEnum
import com.hellofresh.cif.api.calculation.generated.model.SqrSku
import com.hellofresh.cif.api.calculation.generated.model.SupplyQuantityRecommendationRequest
import com.hellofresh.cif.api.calculation.generated.model.SupplyQuantityRecommendationResponse
import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.getLoggedInUserInfo
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.api.schema.enums.SkuRiskRating
import com.hellofresh.cif.api.sku.SkuQuantityMapper
import com.hellofresh.cif.api.sku.SkuRiskRatingMapper
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SQRData
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SQRState.PENDING
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SQRState.PROCESSED
import com.hellofresh.cif.api.user.isSuperManager
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.sqr.SQRConfiguration
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.ApplicationCall
import io.ktor.server.auth.authenticate
import io.ktor.server.request.receive
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.post
import io.ktor.server.routing.routing
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun Routing.supplyQuantityRecommendation(
    configService: ConfigService,
    sqrConfigService: SQRConfigService,
    supplyQuantityRecommendationService: SupplyQuantityRecommendationService,
    timeoutInMillis: Long,
) =
    authenticate {
        post("/supply-quantity-recommendation/config") {
            handlePostSupplyQuantityRecommendation(
                call,
                configService,
                sqrConfigService,
                timeoutInMillis
            )
        }
        get("/supply-quantity-recommendation") {
            handleGetSupplyQuantityRecommendation(
                call,
                supplyQuantityRecommendationService,
                timeoutInMillis
            )
        }
    }

private suspend fun handlePostSupplyQuantityRecommendation(
    call: ApplicationCall,
    configService: ConfigService,
    sqrConfigService: SQRConfigService,
    timeoutInMillis: Long
) {
    val user = call.getLoggedInUserInfo()
    val isSuperManager = user.roleClaim.isSuperManager()

    runCatching {
        val supplyQuantityRecommendationRequest = call.receive<SupplyQuantityRecommendationRequest>()
        require(isSuperManager) { "User is not super manager" }

        val sqrRequest = supplyQuantityRecommendationRequest.toSQRRequest()
        require(sqrRequest.safetyMultiplier >= BigDecimal.ZERO) {
            "Safety multiplier should be a non negative number."
        }

        val dcConfiguration = configService.getByCode(sqrRequest.dcCode)
        val today = LocalDate.now(dcConfiguration?.zoneId ?: ZoneOffset.UTC)
        val productionStartDay = dcConfiguration?.productionStart
        require(productionStartDay != null) {
            "Production start day is missing."
        }

        val currentWeek = DcWeek(today, productionStartDay)
        require(currentWeek.toString() <= sqrRequest.week) {
            "Week ${sqrRequest.week} is in the past"
        }

        sqrRequest
    }.onFailure { exception ->
        var httpStatus = HttpStatusCode.BadRequest
        if (!isSuperManager) {
            httpStatus = HttpStatusCode.Forbidden
        }
        call.respond(httpStatus, mapToErrorResponse(exception))
    }.onSuccess { sqrRequest ->
        withTimeout(timeoutInMillis) {
            kotlin.runCatching {
                sqrConfigService.upsertSupplyQuantityRecommendation(sqrRequest)
            }
        }.onSuccess {
            call.respond(HttpStatusCode.Created, it)
        }.onFailure {
            call.handleResponseError(it)
        }
    }
}

private suspend fun handleGetSupplyQuantityRecommendation(
    call: ApplicationCall,
    supplyQuantityRecommendationService: SupplyQuantityRecommendationService,
    timeoutInMillis: Long
) {
    runCatching {
        val dcCode = call.parameters.getOrThrow("dcCode")
        val week = call.parameters.getOrThrow("week")
        dcCode to week
    }.onFailure { exception ->
        call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
    }.onSuccess { (dcCode, week) ->
        runCatching {
            withTimeout(timeoutInMillis) {
                mapSupplyQuantityRecommendationResponse(
                    supplyQuantityRecommendationService.getSupplyQuantityRecommendations(dcCode, week)
                )
            }
        }.onSuccess { result ->
            call.respond(HttpStatusCode.OK, result)
        }.onFailure {
            call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(it))
        }
    }
}

fun SupplyQuantityRecommendationRequest.toSQRRequest(): SQRRequest =
    SQRRequest(
        skuId = this.skuId,
        dcCode = this.dcCode,
        week = this.week,
        recommendationEnabled = this.recommendationEnabled,
        multiWeekEnabled = this.multiWeekEnabled ?: SQRConfiguration.DEFAULT_MULTI_WEEK_ENABLED,
        safetyMultiplier = this.safetyMultiplier,
        skuRiskRating = SkuRiskRatingMapper.mapToSkuRiskRating(this.skuRiskRating),
    )

private fun mapSupplyQuantityRecommendationResponse(
    supplyQuantityRecommendations: SQRData
): SupplyQuantityRecommendationResponse =
    SupplyQuantityRecommendationResponse(
        dcCode = supplyQuantityRecommendations.dcCode,
        week = supplyQuantityRecommendations.week,
        skus = supplyQuantityRecommendations.skus.map { sku ->
            SqrSku(
                skuId = sku.skuId,
                skuCode = sku.skuCode,
                skuName = sku.skuName,
                skuCategory = sku.skuCategory,
                inventoryRollover = sku.inventoryRollover,
                demand = sku.demand,
                supplyQuantityRecommendation = sku.supplyQuantityRecommendation,
                recommendationEnabled = sku.recommendationEnabled,
                multiWeekEnabled = sku.multiWeekEnabled,
                state = when (sku.state) {
                    PROCESSED -> SQRStateEnum.PROCESSED
                    PENDING -> SQRStateEnum.PENDING
                },
                updatedAt = sku.updatedAt,
                stockUpdates = sku.stockUpdates?.toLong(),
                safetyStock = sku.safetyStock,
                safetyStockRiskMultiplier = sku.safetyStockRiskMultiplier.toReadablePlainFormat(),
                uom = SkuQuantityMapper.mapSkuUOMToUomEnum(sku.uom),
                skuRiskRating = SkuRiskRatingEnum.valueOf(sku.skuRiskRating.name),
                bufferPercentage = sku.bufferPercentage?.toReadablePlainFormat()
            )
        },
    )

private fun BigDecimal.toReadablePlainFormat() =
    this.stripTrailingZeros().toPlainString().toBigDecimal()

fun supplyQuantityRecommendationModule(
    configService: ConfigService,
    sqrConfigService: SQRConfigService,
    supplyQuantityRecommendationService: SupplyQuantityRecommendationService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        supplyQuantityRecommendation(
            configService,
            sqrConfigService,
            supplyQuantityRecommendationService,
            timeout.inWholeMilliseconds,
        ).also {
            configureSerialization(it)
        }
    }
}

data class SQRRequest(
    val skuId: UUID,
    val dcCode: String,
    val week: String,
    val recommendationEnabled: Boolean,
    val multiWeekEnabled: Boolean,
    val safetyMultiplier: BigDecimal,
    val skuRiskRating: SkuRiskRating,
)
