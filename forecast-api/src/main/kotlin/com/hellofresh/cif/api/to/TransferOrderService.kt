package com.hellofresh.cif.api.to

import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.api.util.computeDateRanges
import com.hellofresh.cif.transferorder.db.TransferOrderRepository
import com.hellofresh.cif.transferorder.model.TransferOrder

class TransferOrderService(
    private val transferOrderRepository: TransferOrderRepository,
    private val config: ConfigService
) {
    suspend fun findTransferOrders(toReq: TransferOrdersReq): List<TransferOrder> {
        val dateRanges = computeDateRanges(config, toReq.dcCodes, toReq.weeks, toReq.fromDate, toReq.toDate)
        return transferOrderRepository.findTransferOrders(toReq.skuId, toReq.dcCodes, dateRanges)
    }
}
