package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.InventoryRefreshType.CLEARDOWN
import com.hellofresh.cif.api.calculation.generated.model.UnusableStockByType
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.distributionCenter.models.ProductionWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import java.math.BigDecimal
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.util.UUID

class UnusableStockDecorator(private val originalRequest: CalculationRequest, private val dcConfigService: DcConfigService) {

    private val needsDecorator = CLEARDOWN == originalRequest.inventoryRefreshType
    private val originalWeeks = originalRequest.weeks.toSet()

    suspend fun decorate(
        originalCalculationsPage: CalculationsPage<DailyView>,
        calculationFunction: suspend (CalculationRequest) -> CalculationsPage<DailyView>
    ) =
        if (needsDecorator) {
            val neededExtraWeeks =
                originalRequest.dcCodes
                    .mapNotNull { dcConfigService.dcConfigurations[it] }
                    .filter { it.hasCleardown && it.cleardown != it.productionStart }
                    .flatMap { dc ->
                        originalWeeks.map { week ->
                            ProductionWeek(week, dc.productionStart, dc.zoneId).minusWeeks(1).dcWeek.value
                        }
                    }.filter { !originalWeeks.contains(it) }

            if (originalCalculationsPage.calculationPage.isNotEmpty()) {
                val extraWeeksCalculationsPage =
                    if (neededExtraWeeks.isNotEmpty()) {
                        calculationFunction(
                            CalculationRequest(
                                dcCodes = originalRequest.dcCodes,
                                weeks = neededExtraWeeks,
                                pageRequest = AllPages,
                                consumptionDaysAhead = originalRequest.consumptionDaysAhead,
                                inventoryRefreshType = originalRequest.inventoryRefreshType,
                                skuCodes = if (originalRequest.pageRequest is AllPages) {
                                    emptyList()
                                } else {
                                    originalCalculationsPage.calculationPage.map { it.sku.skuCode }.distinct().toList()
                                },
                            ),
                        ).calculationPage
                    } else {
                        emptyList()
                    }

                accumulateUnusableStock(originalCalculationsPage, extraWeeksCalculationsPage)
            } else {
                originalCalculationsPage
            }
        } else {
            originalCalculationsPage
        }

    private fun accumulateUnusableStock(
        originalCalculationsPage: CalculationsPage<DailyView>,
        extraWeeksCalculationsPage: List<DailyView>
    ): CalculationsPage<DailyView> {
        val accumulatedUnusableDailyViews = (originalCalculationsPage.calculationPage + extraWeeksCalculationsPage)
            .mapNotNull { dcConfigService.dcConfigurations[it.calculation.dcCode]?.let { dc -> dc to it } }
            .groupBy(
                { (dc, calculation) ->
                    UnusableAccumulatorKey(
                        dcCode = dc.dcCode,
                        cleardownDate = dc.getCleardown(calculation.productionDay.day),
                        skuId = calculation.sku.skuId,
                    )
                },
            ) { (_, dailyView) -> dailyView }
            .flatMap { (_, dailyViews) ->
                var expiring = ZERO
                var unusableDetails: List<UnusableStockByType>? = null
                dailyViews.sortedBy { it.productionDay.day }.map { dailyView ->
                    expiring += dailyView.calculation.unusableStock
                    unusableDetails = unusableDetails
                        ?.let { it + (dailyView.calculation.unusableStockDetails ?: emptyList()) }
                        ?: dailyView.calculation.unusableStockDetails

                    dailyView.toKey() to UnusableAccumulator(
                        unusableStock = expiring,
                        unusableStockDetails = unusableDetails
                            ?.groupBy { it.type }
                            ?.map { (type, details) ->
                                UnusableStockByType(type, details.sumOf { it.qty })
                            },
                    )
                }
            }.toMap()

        return originalCalculationsPage.copy(
            calculationPage = originalCalculationsPage.calculationPage.map { dailyView ->
                accumulatedUnusableDailyViews[dailyView.toKey()]
                    ?.let { accumulatedUnusable ->
                        dailyView.copy(
                            calculation = dailyView.calculation.copy(
                                unusableStock = accumulatedUnusable.unusableStock,
                                unusableStockDetails = accumulatedUnusable.unusableStockDetails,
                            ),
                        )
                    } ?: dailyView
            },
        )
    }

    private fun DailyView.toKey() = CalculationKey(sku.skuId, calculation.dcCode, productionDay.day)

    private data class UnusableAccumulator(
        val unusableStock: BigDecimal,
        val unusableStockDetails: List<UnusableStockByType>?
    )
}

private data class UnusableAccumulatorKey(val dcCode: String, val cleardownDate: LocalDate, val skuId: UUID)
