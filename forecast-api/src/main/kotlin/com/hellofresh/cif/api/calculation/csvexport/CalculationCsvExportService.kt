package com.hellofresh.cif.api.calculation.csvexport

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonInclude.Include
import com.hellofresh.cif.api.calculation.AdditionalFilter
import com.hellofresh.cif.api.calculation.CalculationFiltersParams
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.InventoryRefreshType
import com.hellofresh.cif.api.calculation.SortBy
import com.hellofresh.cif.api.fileexport.FileExportService
import com.hellofresh.cif.api.fileexport.repository.FileExportRequest
import com.hellofresh.cif.lib.kafka.serde.objectMapper
import java.util.UUID
import java.util.concurrent.Executors
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.asCoroutineDispatcher
import kotlinx.coroutines.launch
import org.apache.logging.log4j.kotlin.Logging

class CalculationCsvExportService(
    private val fileExportService: FileExportService,
    private val calculationsCsvGenerator: CalculationCsvGenerator,
    csvParallelism: Int
) {

    private val dispatcher = Executors.newFixedThreadPool(csvParallelism).asCoroutineDispatcher()

    suspend fun createCsvExport(
        calculationViewType: CalculationViewType,
        calculationRequest: CalculationRequest
    ): FileExportRequest {
        val fileExportRequest = fileExportService.saveFileExportRequest(
            calculationRequest.toCsvExportParameters().toJsonString(),
        )

        launchCsvGeneration(calculationViewType, calculationRequest, fileExportRequest)

        return fileExportRequest
    }

    private fun launchCsvGeneration(
        calculationViewType: CalculationViewType,
        calculationRequest: CalculationRequest,
        fileExportRequest: FileExportRequest
    ) {
        CoroutineScope(dispatcher)
            .launch {
                runCatching {
                    calculationsCsvGenerator.generateCsvFile(
                        calculationViewType,
                        calculationRequest,
                        fileExportRequest,
                    )
                }.onFailure {
                    logger.error("Calculation Csv Generation Task Error for: $fileExportRequest")
                }.onSuccess {
                    logger.info("Calculation Csv Generation Task  Finished for: $fileExportRequest")
                }
            }
        logger.info("Calculation Csv Generation launched for: $fileExportRequest")
    }

    private fun CalculationRequest.toCsvExportParameters() =
        CsvExportParameters(
            dcCodes,
            weeks, skuCodes,
            skuCategories,
            additionalFilters, consumptionDaysAhead,
            inventoryRefreshType,
            locationInBox,
            supplierIds,
            activeSupplierNames,
            poDueInMin,
            poDueInMax,
            closingStockLessThanOrEqual,
            sortBy,
        )

    private fun Any.toJsonString() = objectMapper.writeValueAsString(this)

    companion object : Logging {

        fun CalculationRequest.toCsvExportParameters() =
            CsvExportParameters(
                dcCodes = dcCodes,
                weeks = weeks,
                skuCodes = skuCodes,
                skuCategories = skuCategories,
                additionalFilters = additionalFilters,
                consumptionDaysAhead = consumptionDaysAhead,
                inventoryRefreshType = inventoryRefreshType,
                locationInBox = locationInBox,
                supplierIds = supplierIds,
                activeSupplierNames = activeSupplierNames,
                poDueInMin = poDueInMin,
                poDueInMax = poDueInMax,
                closingStockLessThanOrEqual = closingStockLessThanOrEqual,
                sortBy = sortBy,
            )
    }
}

enum class CalculationViewType {
    DAILY,
    WEEKLY
}

@JsonIgnoreProperties(ignoreUnknown = true)
@JsonInclude(Include.NON_NULL)
data class CsvExportParameters(
    override val dcCodes: List<String>,
    override val weeks: List<String>,
    override val skuCodes: List<String>,
    override val skuCategories: List<String>,
    override val additionalFilters: Set<AdditionalFilter>,
    override val consumptionDaysAhead: Int,
    override val inventoryRefreshType: InventoryRefreshType,
    override val locationInBox: List<String>,
    override val supplierIds: List<UUID>,
    override val activeSupplierNames: Set<String>,
    override val poDueInMin: Int?,
    override val poDueInMax: Int?,
    override val closingStockLessThanOrEqual: Long?,
    val sortBy: SortBy
) : CalculationFiltersParams
