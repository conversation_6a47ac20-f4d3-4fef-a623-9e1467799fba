package com.hellofresh.cif.api.calculation.experiment

import com.hellofresh.cif.calculator.models.DayCalculationResult

object CalculationExperimentResultWeekFilter {

    fun filter(calculationExperimentResults: List<DayCalculationResult>, validWeeks: Set<String>) =
        if (validWeeks.isEmpty()) {
            calculationExperimentResults
        } else {
            calculationExperimentResults.filter {
                validWeeks.contains(it.productionWeek)
            }
        }
}
