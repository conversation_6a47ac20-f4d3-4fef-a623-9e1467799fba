package com.hellofresh.cif.api.stockupdate.repository

import com.hellofresh.cif.api.calculation.generated.model.Reason
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.models.SkuQuantity
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID

interface StockUpdateApiRepository {

    suspend fun getAllStockUpdates(dcCode: String, week: String): List<StockUpdateView>

    suspend fun upsertStockUpdate(stockUpdates: List<StockUpdateDto>): List<StockUpdate>
}

data class StockUpdateView(
    val dcCode: String,
    val date: LocalDate,
    val skuId: UUID,
    val skuCode: String,
    val skuName: String,
    val skuCategory: String,
    val versions: List<StockUpdateVersion>,
)

data class StockUpdateVersion(
    val version: Int,
    val quantity: SkuQuantity,
    val reason: String,
    val reasonDetail: String?,
    val deleted: Boolean,
    val authorName: String?,
    val authorEmail: String,
    val createdAt: OffsetDateTime
)

data class StockUpdateDto(
    val skuId: UUID,
    val dcCode: String,
    val date: LocalDate,
    val quantity: SkuQuantity,
    val reason: Reason,
    val week: String,
    val reasonDetail: String?,
    val authorName: String?,
    val authorEmail: String,
    val deleted: Boolean,
    val version: Int? = null,
)

data class StockUpdateKey(val dcCode: String, val skuId: UUID, val date: LocalDate, val version: Int)
