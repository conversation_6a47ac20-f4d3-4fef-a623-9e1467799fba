package com.hellofresh.cif.api.po

import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.api.util.computeDateRanges
import com.hellofresh.cif.api.util.getDateRange
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.purchaseorder.PoVariance
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository

class PurchaseOrderService(
    private val poRepo: PurchaseOrderRepository,
    private val configRepo: ConfigService
) {
    suspend fun findPurchaseOrders(poReq: PurchaseOrdersReq): List<PurchaseOrder> {
        val dateRanges = computeDateRanges(configRepo, poReq.dcCodes, poReq.weeks, poReq.fromDate, poReq.toDate)
        return poRepo.findPurchaseOrdersWithAsns(poReq.skuId, poReq.dcCodes, dateRanges.toSet())
    }

    suspend fun findPoVariance(dcCode: String, dcWeek: DcWeek): List<PoVariance> {
        val dateRanges = configRepo.getByCode(dcCode)?.let { dcConfig ->
            getDateRange(dcWeek, dcConfig)
        } ?: throw IllegalArgumentException("DC not found $dcCode")
        return poRepo.findPoVariance(setOf(dcCode), setOf(dateRanges))
    }
}
