package com.hellofresh.cif.api.docs

import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.response.respondText
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get

fun Routing.docs() = authenticate {
    get("/docs") {
        var openApiYaml = javaClass.getResource("/openapi.yaml")?.readText()
        val componentsYaml = javaClass.getResource("/components.yaml")?.readText()

        if (openApiYaml != null && componentsYaml != null) {
            openApiYaml = openApiYaml.replace("components.yaml", "")
            val combinedYaml = "$openApiYaml\n$componentsYaml"
            call.respondText(combinedYaml, ContentType.Any, HttpStatusCode.OK)
        } else {
            call.respond(HttpStatusCode.NotFound)
        }
    }
}
