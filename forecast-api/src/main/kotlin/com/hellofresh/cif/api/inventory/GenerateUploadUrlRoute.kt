package com.hellofresh.cif.api.inventory

import com.hellofresh.cif.api.calculation.generated.model.GenerateUploadUrlResponse
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.routing

fun Routing.generateUploadUrl(
    generateUploadUrlService: GenerateUploadUrlService,
) = authenticate {
    get("/generate-upload-url/{market}") {
        val market = call.parameters.getOrThrow("market")
        val fileName = call.parameters.getOrThrow("fileName")
        val authorName = call.parameters.getOrThrow("authorName")
        val authorEmail = call.parameters.getOrThrow("authorEmail")
        val metadata = mapOf(
            "author_name" to authorName,
            "author_email" to authorEmail,
            "market" to market,
        )
        val fileType = call.parameters.getOrThrow("fileType")
        val url = generateUploadUrlService.generateUploadUrl(market, fileName, metadata, fileType)
        call.respond(HttpStatusCode.OK, GenerateUploadUrlResponse(url.toString()))
    }
}

fun generateUploadUrlRoutingModule(
    generateUploadUrlService: GenerateUploadUrlService,
): Application.() -> Unit = {
    routing {
        generateUploadUrl(generateUploadUrlService).also {
            configureSerialization(it)
        }
    }
}
