package com.hellofresh.cif.api.calculation.db

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.cif.api.schema.enums.SubbedType
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.demand.models.ConsumptionDetails
import java.math.BigDecimal
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.util.UUID

@JsonIgnoreProperties(ignoreUnknown = true)
data class CalculationRecord(
    val dcCode: String,
    val cskuId: UUID,
    val code: String,
    val name: String,
    val category: String,
    val productionWeek: String,
    val date: LocalDate,
    val uom: Uom,
    val expired: BigDecimal,
    val openingStock: BigDecimal,
    val storageStock: BigDecimal?,
    val stagingStock: BigDecimal?,
    val stockUpdate: BigDecimal?,
    val demanded: BigDecimal,
    val present: BigDecimal,
    val closingStock: BigDecimal,
    val actualInbound: BigDecimal,
    val expectedInbound: BigDecimal,
    @JsonDeserialize(using = CommaSeparatedToSetJsonDeserializer::class)
    val actualInboundPo: Set<String>?,
    @JsonDeserialize(using = CommaSeparatedToSetJsonDeserializer::class)
    val expectedInboundPo: Set<String>?,
    val dailyNeeds: BigDecimal,
    val coolingType: String?,
    val packaging: String?,
    val actualConsumption: BigDecimal,
    val skuAtRisk: Boolean,
    @JsonProperty("safetystock")
    val safetyStock: BigDecimal?,
    @JsonProperty("safetystock_needs")
    val safetyStockNeeds: BigDecimal?,
    @JsonProperty("max_purchase_order_due_in")
    val poDueIn: Long?,
    val netNeeds: BigDecimal,
    @JsonProperty("subbed")
    val subbed: SubbedType? = null,
    @JsonProperty("substituted_in_qty")
    val substitutedInQty: Long? = null,
    @JsonProperty("substituted_out_qty")
    val substitutedOutQty: Long? = null,
    @JsonProperty("consumption_details")
    val consumptionDetails: ConsumptionDetails? = null,
    @JsonProperty("unusable_inventory")
    val unusableStockDetails: List<ForecastInventory>? = null,
    @JsonProperty("acceptable_code_life")
    val acceptableCodeLife: Int,
    @JsonProperty("strategy")
    val strategy: String,
    @JsonProperty("expected_inbound_transfer_orders")
    @JsonDeserialize(using = CommaSeparatedToSetJsonDeserializer::class)
    val expectedInboundTo: Set<String>? = emptySet(),
    @JsonProperty("expected_inbound_transfer_orders_quantity")
    val expectedInboundToQty: BigDecimal? = ZERO,
    @JsonProperty("actual_inbound_transfer_orders")
    @JsonDeserialize(using = CommaSeparatedToSetJsonDeserializer::class)
    val actualInboundTo: Set<String>? = emptySet(),
    @JsonProperty("actual_inbound_transfer_orders_quantity")
    val actualInboundToQty: BigDecimal? = ZERO,
    @JsonProperty("expected_outbound_transfer_orders")
    @JsonDeserialize(using = CommaSeparatedToSetJsonDeserializer::class)
    val expectedOutboundTo: Set<String>? = emptySet(),
    @JsonProperty("expected_outbound_transfer_orders_quantity")
    val expectedOutboundToQty: BigDecimal? = ZERO,
) {
    companion object
}

class CommaSeparatedToSetJsonDeserializer : JsonDeserializer<Set<String>>() {

    override fun deserialize(jsonParser: JsonParser, ctxt: DeserializationContext?): Set<String> =
        deserialize(jsonParser.valueAsString)

    companion object {
        fun deserialize(value: String?): Set<String> =
            if (value.isNullOrBlank()) {
                emptySet()
            } else {
                value.split(",").toSet()
            }
    }
}
