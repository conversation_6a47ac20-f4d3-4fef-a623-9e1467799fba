package com.hellofresh.cif.api.calculation

import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.cif.api.calculation.generated.model.UnusableStockByType
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.util.UUID

enum class InventoryRefreshType {
    CLEARDOWN,
    LIVE
}

data class ProductionDay(
    val week: String,
    val day: LocalDate,
)

data class Sku(
    val skuId: UUID,
    val skuCode: String,
    val skuName: String,
    val skuCategories: String,
)

data class Calculation(
    val uom: SkuUOM,
    val usableStock: BigDecimal,
    val unusableStock: BigDecimal,
    val incomingPos: BigDecimal,
    val inbound: BigDecimal,
    val pos: Set<String>,
    val consumption: BigDecimal,
    val dailyNeed: BigDecimal,
    val closingStock: BigDecimal,
    val actualConsumption: BigDecimal,
    val dcCode: DcCode,
    val skuAtRisk: Boolean,
    val safetyStock: BigDecimal?,
    val strategy: String,
    val safetyStockNeeds: BigDecimal?,
    val storageStock: BigDecimal?,
    val stagingStock: BigDecimal?,
    val poDueIn: Long?,
    val netNeeds: BigDecimal,
    val substituted: Long?,
    val brandConsumptions: List<BrandConsumption> = emptyList(),
    val prekitting: Long? = null,
    val unusableStockDetails: List<UnusableStockByType>? = null,
    val stockUpdate: BigDecimal? = null,
    val status: CalculationStatus = CalculationStatus.PROCESSED,
    val fumigatedDemand: Long? = null,
    val regularDemand: Long? = null,
    val expectedInboundTo: Set<String> = emptySet(),
    val expectedInboundToQty: BigDecimal = ZERO,
    val actualInboundTo: Set<String> = emptySet(),
    val actualInboundToQty: BigDecimal = ZERO,
    val expectedOutboundTo: Set<String> = emptySet(),
    val expectedOutboundToQty: BigDecimal = ZERO,
) {
    companion object
}

enum class CalculationStatus {
    PENDING,
    PROCESSED;

    fun merge(calculationStatus: CalculationStatus) =
        if (this == PENDING || calculationStatus == PENDING) {
            PENDING
        } else {
            PROCESSED
        }
}

data class BrandConsumption(
    val brand: String,
    val qty: Long
)

data class DailyView(
    val productionDay: ProductionDay,
    val sku: Sku,
    val calculation: Calculation
) {
    companion object
}

data class ProjectedWasteView(
    val dcCode: String,
    val cskuId: UUID,
    val skuCode: String,
    val skuName: String,
    val acceptableCodeLife: Int,
    val unusableInventory: List<ProjectedWasteInventory> = emptyList(),
    val uom: SkuUOM
)

data class ProjectedWasteInventory(
    val inventory: ForecastInventory,
    val usable: Boolean,
    val unusableReason: String?
)

data class WeeklyView(
    val week: String,
    val sku: Sku,
    val calculation: Calculation
) {

    companion object {
        fun fromDaily(
            dailyViews: Set<DailyView>,
            weeklySQREnabled: Boolean = false,
        ): WeeklyView {
            require(dailyViews.isNotEmpty()) { "daily views set cannot be empty" }

            val sortedViews = dailyViews.sortedBy { it.productionDay.day }
            val firstDailyView = sortedViews.first()
            val openingStock = firstDailyView.calculation.usableStock
            val closingStock = sortedViews.last().calculation.closingStock
            val safetyStockNeedsMonday = sortedViews.find {
                it.productionDay.day.dayOfWeek == MONDAY
            }?.calculation?.safetyStockNeeds

            return WeeklyView(
                week = firstDailyView.productionDay.week,
                sku = firstDailyView.sku,
                calculation = sortedViews
                    .map { it.calculation }
                    .reduce { acc, calculation ->
                        aggregateWeeklyCalculation(
                            openingStock,
                            closingStock,
                            acc,
                            calculation,
                            weeklySQREnabled,
                        )
                    }
                    .copy(
                        safetyStockNeeds = safetyStockNeedsMonday,
                        // don't aggregate the daily stock level for a weekly view.
                        // for a calculation list having a single element the reduce above will not execute
                        // and we have to reset storageStock here to avoid passing the value of that single calculation
                        storageStock = ONE.negate(),
                    ),
            )
        }

        @Suppress("LongParameterList")
        private fun aggregateWeeklyCalculation(
            openingStock: BigDecimal,
            closingStock: BigDecimal,
            acc: Calculation,
            calculation: Calculation,
            weeklySQREnabled: Boolean = false
        ) =
            Calculation(
                uom = acc.uom,
                incomingPos = acc.incomingPos + calculation.incomingPos,
                inbound = acc.inbound + calculation.inbound,
                pos = acc.pos + calculation.pos,
                usableStock = openingStock,
                unusableStock = acc.unusableStock + calculation.unusableStock,
                consumption = acc.consumption + calculation.consumption,
                dailyNeed = acc.dailyNeed + calculation.dailyNeed,
                closingStock = closingStock,
                actualConsumption = acc.actualConsumption + calculation.actualConsumption,
                dcCode = calculation.dcCode,
                skuAtRisk = acc.skuAtRisk || calculation.skuAtRisk,
                safetyStock = calculation.safetyStock,
                strategy = calculation.strategy,
                safetyStockNeeds = null,
                storageStock = ZERO,
                stagingStock = ZERO,
                poDueIn = null,
                netNeeds = if (weeklySQREnabled) {
                    SkuQuantity.max(
                        SkuQuantity.ZERO,
                        SkuQuantity.fromBigDecimal(
                            ((acc.consumption + calculation.consumption) + (acc.safetyStock ?: ZERO)) - acc.usableStock,
                        ),
                    ).getValue()
                } else {
                    acc.netNeeds + calculation.netNeeds
                },
                substituted = acc.substituted?.plus(calculation.substituted ?: 0) ?: calculation.substituted,
                brandConsumptions = getBrandConsumption(acc.brandConsumptions, calculation.brandConsumptions),
                prekitting = acc.prekitting?.plus(calculation.prekitting ?: 0L) ?: calculation.prekitting,
                stockUpdate = acc.stockUpdate?.plus(calculation.stockUpdate ?: ZERO) ?: calculation.stockUpdate,
                status = acc.status.merge(calculation.status),
                fumigatedDemand = acc.fumigatedDemand?.plus(calculation.fumigatedDemand ?: 0L)
                    ?: calculation.fumigatedDemand,
                regularDemand = acc.regularDemand?.plus(calculation.regularDemand ?: 0L) ?: calculation.regularDemand,
                expectedInboundTo = acc.expectedInboundTo + calculation.expectedInboundTo,
                expectedInboundToQty = acc.expectedInboundToQty.plus(calculation.expectedInboundToQty),
                actualInboundTo = acc.actualInboundTo + calculation.actualInboundTo,
                actualInboundToQty = acc.actualInboundToQty.plus(calculation.actualInboundToQty),
                expectedOutboundTo = acc.expectedOutboundTo + calculation.expectedOutboundTo,
                expectedOutboundToQty = acc.expectedOutboundToQty.plus(calculation.expectedOutboundToQty),
            )

        private fun getBrandConsumption(
            accBrandConsumptions: List<BrandConsumption>,
            brandConsumptions: List<BrandConsumption>
        ): List<BrandConsumption> =
            (accBrandConsumptions + brandConsumptions)
                .groupBy { it.brand }
                .map { (brand, consumptions) ->
                    BrandConsumption(
                        brand = brand,
                        qty = consumptions.sumOf { it.qty },
                    )
                }
    }
}
