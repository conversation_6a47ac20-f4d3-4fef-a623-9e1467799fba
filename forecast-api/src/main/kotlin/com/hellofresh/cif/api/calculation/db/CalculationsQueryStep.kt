package com.hellofresh.cif.api.calculation.db

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.calculation.AllPages
import com.hellofresh.cif.api.calculation.CalculationsPage
import com.hellofresh.cif.api.calculation.InventoryRefreshType
import com.hellofresh.cif.api.calculation.InventoryRefreshType.CLEARDOWN
import com.hellofresh.cif.api.calculation.InventoryRefreshType.LIVE
import com.hellofresh.cif.api.calculation.Page
import com.hellofresh.cif.api.calculation.PageRequest
import com.hellofresh.cif.api.calculation.SortBy
import com.hellofresh.cif.api.calculation.SortBy.SKU_CODE
import com.hellofresh.cif.api.calculation.SortBy.SKU_NAME
import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.api.schema.Tables.DEMAND
import com.hellofresh.cif.api.schema.Tables.LIVE_INVENTORY_CALCULATION
import com.hellofresh.cif.api.schema.Tables.LIVE_INVENTORY_PRE_PRODUCTION_CALCULATION
import com.hellofresh.cif.api.schema.Tables.PRE_PRODUCTION_CALCULATION
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.concurrent.CompletionStage
import kotlin.math.ceil
import kotlinx.coroutines.future.await
import org.jetbrains.annotations.VisibleForTesting
import org.jooq.Condition
import org.jooq.JSON
import org.jooq.JSONArrayAggOrderByStep
import org.jooq.OrderField
import org.jooq.Record
import org.jooq.Result
import org.jooq.Table
import org.jooq.impl.DSL
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.select
import org.jooq.impl.SQLDataType

@Suppress("TooManyFunctions")
class CalculationsQueryStep(
    private val dsl: MetricsDSLContext,
    dcCodes: List<String>,
    weeks: List<String>,
    calculationInventoryRefreshMode: CalculationInventoryRefreshMode,
    sortBy: SortBy
) : CalculationConditionsStep {

    override val conditionsStep = CalculationsConditions(dcCodes, weeks, calculationInventoryRefreshMode)

    private val fetchCalculation = "fetch-calculations"
    private val fetchAllCalculation = "fetch-all-calculations"
    private val fetchCount = "fetch-count"

    private val calculationsTable = resolveCalculationTable(calculationInventoryRefreshMode)

    private val cskuIdColumn = calculationsTable.field(CSKU_ID, SQLDataType.UUID)!!
    private val dateColumn = calculationsTable.field(DATE, SQLDataType.LOCALDATE)!!
    private val dcCodeColumn = calculationsTable.field(DC_CODE, SQLDataType.VARCHAR)!!

    private val isSkuAtRiskCondition = skuAtRiskCondition(calculationsTable)
    private val isSkuAtRiskAggregateField = DSL.boolOr(isSkuAtRiskCondition)
    private val isSkuAtRiskColumn = DSL.jsonEntry("sku_at_risk", isSkuAtRiskCondition)

    private val groupByColumn = listOf(
        SKU_SPECIFICATION_VIEW.ID,
        when (sortBy) {
            SKU_NAME -> SKU_SPECIFICATION_VIEW.NAME
            SKU_CODE -> SKU_SPECIFICATION_VIEW.CODE
        },
    )

    private val sortFields: MutableList<OrderField<*>> = mutableListOf(
        isSkuAtRiskAggregateField.desc(),
        when (sortBy) {
            SKU_NAME -> SKU_SPECIFICATION_VIEW.NAME.sortDefault()
            SKU_CODE -> SKU_SPECIFICATION_VIEW.CODE.sortDefault()
        },
    )

    private val statement = select(groupByColumn + skuCalculationsField())
        .from(calculationsTable)
        .join(SKU_SPECIFICATION_VIEW).on(SKU_SPECIFICATION_VIEW.ID.eq(cskuIdColumn))
        .leftJoin(DEMAND).on(
            DEMAND.SKU_ID.eq(cskuIdColumn)
                .and(
                    DEMAND.DATE.eq(
                        if (calculationInventoryRefreshMode.isProdCalculation) {
                            dateColumn
                        } else {
                            dateColumn.plus(1)
                        },
                    ),
                ).and(
                    DEMAND.DC_CODE.eq(dcCodeColumn),
                ),
        )

    private fun skuCalculationsField(): JSONArrayAggOrderByStep<JSON> {
        val fields = calculationsTable.fields()
        val skuSpecFields = setOf(
            SKU_SPECIFICATION_VIEW.NAME,
            SKU_SPECIFICATION_VIEW.CODE,
            SKU_SPECIFICATION_VIEW.CATEGORY,
            SKU_SPECIFICATION_VIEW.PACKAGING,
            SKU_SPECIFICATION_VIEW.COOLING_TYPE,
            SKU_SPECIFICATION_VIEW.ACCEPTABLE_CODE_LIFE,
        )

        val demandFields = setOf(
            DEMAND.SUBBED,
            DEMAND.SUBSTITUTED_IN_QTY,
            DEMAND.SUBSTITUTED_OUT_QTY,
            DEMAND.CONSUMPTION_DETAILS,
        )
        return DSL.jsonArrayAgg(
            DSL.jsonObject(
                fields
                    .map { DSL.key(it.name).value(it) } +
                    skuSpecFields.map { DSL.key(it.name).value(it) } +
                    demandFields.map { DSL.key(it.name).value(it) } +
                    isSkuAtRiskColumn,
            ),
        )
    }

    suspend fun fetchPageableCalculations(page: PageRequest): CalculationsPage<CalculationRecord> =
        when (page) {
            AllPages -> fetchAll() to fetchPageMetadata()
            is Page -> fetch(page) to fetchPageMetadata(page)
        }.let { (calculationPageStage, pageMetadataStage) ->
            val pageMetadata = pageMetadataStage.await()
            CalculationsPage(
                calculationPage = calculationPageStage.await(),
                totalPages = pageMetadata.totalPages,
                totalSkuAtRiskCount = pageMetadata.skusCount.totalSkusAtRisk,
                totalSkuCount = pageMetadata.skusCount.totalSkus,
            )
        }

    private fun fetch(page: Page): CompletionStage<List<CalculationRecord>> =
        dsl.withTagName(fetchCalculation)
            .fetchAsync(
                statement
                    .where(conditionsStep.conditions)
                    .groupBy(groupByColumn)
                    .orderBy(sortFields)
                    .offset(page.offset)
                    .limit(page.skuCount),
            ).thenApply(::deserializeJson)

    private fun fetchAll() =
        dsl.withTagName(fetchAllCalculation)
            .fetchAsync(
                statement
                    .where(conditionsStep.conditions)
                    .groupBy(groupByColumn)
                    .orderBy(groupByColumn),
            ).thenApply(::deserializeJson)

    private fun deserializeJson(results: Result<Record>) = results.flatMap { r ->
        val calculationsArrayJsonStr = r[skuCalculationsField()].toString()
        deserializeCalculation(calculationsArrayJsonStr).asList()
    }

    private fun fetchPageMetadata() = fetchSkusCount().thenApply { PageMetadata(it, 1) }

    private fun fetchPageMetadata(page: Page) =
        fetchSkusCount().thenApply { PageMetadata(it, ceil(it.totalSkus.toDouble() / page.skuCount).toInt()) }

    private fun fetchSkusCount() =
        run {
            val nestedSkuAtRiskSelect = select(SKU_SPECIFICATION_VIEW.CODE, isSkuAtRiskAggregateField)
                .from(calculationsTable)
                .join(SKU_SPECIFICATION_VIEW).on(SKU_SPECIFICATION_VIEW.ID.eq(cskuIdColumn))
                .where(conditionsStep.conditions)
                .groupBy(SKU_SPECIFICATION_VIEW.CODE)
            val skuAtRiskColumn = nestedSkuAtRiskSelect.field(isSkuAtRiskAggregateField.name, Boolean::class.java)
            dsl.withTagName(fetchCount)
                .select(skuAtRiskColumn, count())
                .from(nestedSkuAtRiskSelect)
                .groupBy(skuAtRiskColumn)
                .fetchAsync()
                .thenApply { r ->
                    val skuCountByRisk: Map<Boolean, Int> = r.associate { Pair(it.value1(), it.value2()) }
                    SkusCount(skuCountByRisk[true] ?: 0, skuCountByRisk.values.sum())
                }
        }

    private data class PageMetadata(val skusCount: SkusCount, val totalPages: Int)

    private data class SkusCount(val totalSkusAtRisk: Int, val totalSkus: Int)

    data class CalculationInventoryRefreshMode(
        val isProdCalculation: Boolean,
        val inventoryRefreshType: InventoryRefreshType
    )

    companion object {

        private val objectMapper: ObjectMapper = jacksonObjectMapper()
            .findAndRegisterModules()
            .setPropertyNamingStrategy(SnakeCaseStrategy())

        @VisibleForTesting
        fun deserializeCalculation(calculationsArrayJsonStr: String): Array<CalculationRecord> =
            objectMapper.readValue(calculationsArrayJsonStr, Array<CalculationRecord>::class.java)

        fun resolveCalculationTable(calculationInventoryRefreshMode: CalculationInventoryRefreshMode) =
            if (calculationInventoryRefreshMode.isProdCalculation) {
                when (calculationInventoryRefreshMode.inventoryRefreshType) {
                    CLEARDOWN -> CALCULATION
                    LIVE -> LIVE_INVENTORY_CALCULATION
                }
            } else {
                when (calculationInventoryRefreshMode.inventoryRefreshType) {
                    CLEARDOWN -> PRE_PRODUCTION_CALCULATION
                    LIVE -> LIVE_INVENTORY_PRE_PRODUCTION_CALCULATION
                }
            }

        fun skuAtRiskCondition(calculationsTable: Table<*>): Condition {
            val dateColumn = calculationsTable.field(CALCULATION.DATE)!!
            val actualInboundField = calculationsTable.field(CALCULATION.ACTUAL_INBOUND)!!
            val expectedInboundField = calculationsTable.field(CALCULATION.EXPECTED_INBOUND)!!
            val dailyNeedsField = calculationsTable.field(CALCULATION.DAILY_NEEDS)!!

            return dailyNeedsField.gt(BigDecimal.ZERO).or(
                dateColumn.lessThan(LocalDate.now(ZoneOffset.UTC))
                    .and(actualInboundField.lt(expectedInboundField)),
            )
        }
    }
}
