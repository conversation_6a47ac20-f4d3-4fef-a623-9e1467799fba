package com.hellofresh.cif.api.inventory

import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryRepository
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.inventory.models.InventorySnapshot
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import com.hellofresh.sku.models.SkuSpecification
import java.time.LocalDate
import java.util.UUID

class SkuInventoryDetailService(
    private val dcConfigService: DcConfigService,
    private val inventoryRepository: InventoryRepository,
    private val skuInputDataRepository: SkuInputDataRepository,
    statsigFeatureFlagClient: StatsigFeatureFlagClient,
) {

    private val usableInventoryEvaluator = UsableInventoryEvaluator(statsigFeatureFlagClient)

    suspend fun fetchCurrentInventory(skuId: UUID, dcCodes: Set<String>): List<SkuDetail> =
        dcCodes.mapNotNull { dcConfigService.dcConfigurations[it] }
            .groupBy { LocalDate.now(it.zoneId) }
            .flatMap { (todayDc, dcConfigurations) ->
                val dcs = dcConfigurations.map { it.dcCode }.toSet()
                val inventorySnapshots = inventoryRepository.fetchBy(
                    dcs,
                    DateRange(todayDc, todayDc),
                    skuId,
                )
                val skuSpecificationMap = skuInputDataRepository.fetchSkus(
                    inventorySnapshots.flatMap { it.skus }.map { it.skuId }.toSet(),
                )
                inventorySnapshots.flatMap { inventory ->
                    inventory.skus.flatMap { skuInventory ->
                        skuSpecificationMap[skuInventory.skuId]?.let { skuSpec ->
                            toSkuDetails(
                                inventory,
                                todayDc,
                                skuSpec,
                            )
                        } ?: emptyList()
                    }
                }
            }

    private fun toSkuDetails(inventorySnapshot: InventorySnapshot, todayDc: LocalDate, skuSpecification: SkuSpecification) =
        inventorySnapshot.skus.flatMap { skuInventory ->
            skuInventory.inventory.map { inventory ->
                val usabilityDetails = usableInventoryEvaluator.isUsable(
                    inventorySnapshot.dcCode,
                    todayDc,
                    inventory,
                    skuSpecification.acceptableCodeLife,
                    skuSpecification.category,
                )
                SkuDetail(
                    inventorySnapshot.dcCode,
                    inventory.expiryDate,
                    usabilityDetails.usable,
                    inventory.qty.getValue().toLong(),
                    usabilityDetails.unusableReason,
                )
            }.sortedBy { it.expiryDate }
        }
}
