package com.hellofresh.cif.api.shortShelfLife.model

import com.hellofresh.cif.api.calculation.generated.model.UomEnum
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

data class ShortShelfLife(
    val skuId: UUID,
    val skuName: String,
    val skuCode: String,
    val skuCategory: String,
    val dcCode: String,
    val dcWeek: String,
    val date: LocalDate,
    val openingStock: BigDecimal,
    val unusableStock: BigDecimal,
    val consumption: BigDecimal,
    val stockUpdates: BigDecimal?,
    val sqrQuantity: BigDecimal,
    val bufferAdditional: BigDecimal,
    val bufferPercentage: BigDecimal,
    val uom: UomEnum,
    val touchlessOrderingEnabled: Boolean,

) {
    companion object
}
