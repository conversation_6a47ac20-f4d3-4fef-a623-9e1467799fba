package com.hellofresh.cif.api.stockupdate.simulation

import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.cif.api.calculation.Calculation
import com.hellofresh.cif.api.calculation.CalculationResponseMapper
import com.hellofresh.cif.api.calculation.CalculationServiceMapper
import com.hellofresh.cif.api.calculation.CalculationStatus
import com.hellofresh.cif.api.calculation.DailyView
import com.hellofresh.cif.api.calculation.ProductionDay
import com.hellofresh.cif.api.calculation.Sku
import com.hellofresh.cif.api.calculation.generated.model.DailyCalculationResponse
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateSimulationResponse
import com.hellofresh.cif.api.schema.enums.SubbedType
import com.hellofresh.cif.api.stockupdate.StockUpdateSimulationResult
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.demand.models.DemandKey
import com.hellofresh.demand.models.Substitutions
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import java.util.UUID

class StockUpdateSimulationResponseMapper(val calculationResponseMapper: CalculationResponseMapper) {
    fun mapToStockUpdateSimulationResponse(
        dcCode: String,
        stockUpdateSimulationResult: StockUpdateSimulationResult,
        usableInventoryEvaluator: UsableInventoryEvaluator,
    ): StockUpdateSimulationResponse {
        val calculations = mapToDailyCalculationResponse(stockUpdateSimulationResult, dcCode, usableInventoryEvaluator)
        return StockUpdateSimulationResponse(
            stockUpdates = stockUpdateSimulationResult.stockUpdates,
            calculations = calculations,
        )
    }

    @Suppress("LongMethod")
    private fun mapToDailyCalculationResponse(
        stockUpdateSimulationResult: StockUpdateSimulationResult,
        dcCode: String,
        usableInventoryEvaluator: UsableInventoryEvaluator
    ): List<DailyCalculationResponse> {
        val skuSpecificationMap: Map<UUID, SkuDcCandidate> = stockUpdateSimulationResult.skuCandidates.associateBy {
            it.skuId
        }
        return stockUpdateSimulationResult.calculations.map { dayCalculationResult ->
            with(dayCalculationResult) {
                val consumptionDetail = stockUpdateSimulationResult.demandConsumptionDetails[
                    DemandKey(
                        skuId = cskuId,
                        dcCode = dcCode,
                        date = date,
                    ),
                ]
                calculationResponseMapper.toDailyCalculationResponse(
                    DailyView(
                        productionDay = ProductionDay(productionWeek, date),
                        sku = getSku(skuSpecificationMap),
                        calculation = Calculation(
                            uom = uom,
                            usableStock = openingStock.getValue(),
                            unusableStock = unusable.getValue(),
                            incomingPos = expectedInbound.getValue(),
                            inbound = actualInbound.getValue(),
                            pos = getPOs(dayCalculationResult).toSet(),
                            consumption = demanded.getValue(),
                            dailyNeed = dailyNeeds.getValue(),
                            closingStock = closingStock.getValue(),
                            actualConsumption = actualConsumption.getValue(),
                            dcCode = dcCode,
                            skuAtRisk = false,
                            safetyStock = safetyStock?.getValue(),
                            strategy = strategy,
                            safetyStockNeeds = safetyStockNeeds?.getValue(),
                            storageStock = storageStock.getValue(),
                            stagingStock = stagingStock.getValue(),
                            poDueIn = maxPurchaseOrderDueIn
                                ?.toLong(),
                            netNeeds = netNeeds.getValue(),
                            substituted = CalculationServiceMapper.calculateSubstituted(
                                getSubbedType(consumptionDetail?.substitutions),
                                substitutedInQty = consumptionDetail?.substitutions?.`in`?.sumOf { it.qty },
                                substitutedOutQty = consumptionDetail?.substitutions?.out?.sumOf { it.qty },
                            ),
                            brandConsumptions = CalculationServiceMapper.getBrandConsumption(
                                consumptionDetail
                            ),
                            prekitting = CalculationServiceMapper.getPrekitting(
                                consumptionDetail
                            ),
                            unusableStockDetails = prepareUnusableStockDetails(
                                dcCode,
                                skuSpecificationMap,
                                usableInventoryEvaluator,
                            ),
                            stockUpdate = stockUpdate?.getValue(),
                            status = CalculationStatus.PENDING,
                            expectedInboundTo = expectedInboundTransferOrders ?: emptySet(),
                            expectedInboundToQty = expectedInboundTransferOrdersQuantity.getValue(),
                            actualInboundTo = actualInboundTransferOrders ?: emptySet(),
                            actualInboundToQty = actualInboundTransferOrdersQuantity.getValue(),
                            expectedOutboundTo = expectedOutboundTransferOrders ?: emptySet(),
                            expectedOutboundToQty = expectedOutboundTransferOrdersQuantity.getValue(),
                        ),
                    ),
                )
            }
        }
    }

    private fun getSubbedType(substitutions: Substitutions?): String =
        when {
            substitutions == null -> SubbedType.NONE.name
            substitutions.`in`.isNotEmpty() && substitutions.out.isNotEmpty() -> SubbedType.SUB_IN_AND_OUT.name
            substitutions.`in`.isNotEmpty() -> SubbedType.SUB_IN.name
            substitutions.out.isNotEmpty() -> SubbedType.SUB_OUT.name
            else -> SubbedType.NONE.name
        }

    private fun DayCalculationResult.getSku(skuSpecificationMap: Map<UUID, SkuDcCandidate>) =
        Sku(
            skuId = cskuId,
            skuName = skuSpecificationMap[cskuId]?.skuSpecification?.name ?: "",
            skuCode = skuSpecificationMap[cskuId]?.skuSpecification?.skuCode ?: "",
            skuCategories = skuSpecificationMap[cskuId]?.skuSpecification?.category ?: "",
        )

    private fun DayCalculationResult.prepareUnusableStockDetails(
        dcCode: String,
        skuSpecificationMap: Map<UUID, SkuDcCandidate>,
        usableInventoryEvaluator: UsableInventoryEvaluator
    ) = CalculationServiceMapper.calculateUnusableStockDetails(
        dcCode = dcCode,
        date = date,
        acceptableCodeLife = skuSpecificationMap[cskuId]?.skuSpecification?.acceptableCodeLife
            ?: 0,
        category = skuSpecificationMap[cskuId]?.skuSpecification?.category ?: "",
        unusableStockDetails = unusableInventory?.map { unusableInventory ->
            ForecastInventory(
                qty = unusableInventory.qty.getValue(),
                expiryDate = unusableInventory.expiryDate,
                locationType = unusableInventory.locationType,
            )
        },
        usableInventoryEvaluator = usableInventoryEvaluator,
    )

    private fun getPOs(dayCalculationResult: DayCalculationResult) =
        (dayCalculationResult.expectedInboundPurchaseOrders ?: emptyList()) +
            (
                dayCalculationResult.actualInboundPurchaseOrders
                    ?: emptyList()
                )
}
