package com.hellofresh.cif.api.calculation.db

import com.hellofresh.cif.api.calculation.AdditionalFilter
import com.hellofresh.cif.api.calculation.db.CalculationsQueryStep.CalculationInventoryRefreshMode
import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.api.schema.Tables.PO_CALCULATIONS_VIEW
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.api.schema.Tables.SUPPLIER_DETAILS_VIEW
import java.util.UUID
import org.jooq.Field
import org.jooq.Record
import org.jooq.Table
import org.jooq.TableField
import org.jooq.impl.DSL
import org.jooq.impl.DSL.select
import org.jooq.impl.DSL.selectDistinct

class CalculationsConditions(
    dcCodes: List<String>,
    weeks: List<String>,
    calculationInventoryRefreshMode: CalculationInventoryRefreshMode,
) {

    private val calculationsTable: Table<*> = CalculationsQueryStep.resolveCalculationTable(
        calculationInventoryRefreshMode,
    )

    private val dcCodeColumn = CALCULATION.DC_CODE.inTable(calculationsTable)
    private val productionWeekColumn = CALCULATION.PRODUCTION_WEEK.inTable(calculationsTable)
    private val dateColumn = CALCULATION.DATE.inTable(calculationsTable)
    private val cskuIdColumn = CALCULATION.CSKU_ID.inTable(calculationsTable)

    private val additionalFilterConditions = CalculationsAdditionalFilterConditions(
        cskuIdColumn,
        calculationInventoryRefreshMode,
    ) { skuId, dcCode, week ->
        skuId.eq(cskuIdColumn)
            .and(dcCode.eq(dcCodeColumn))
            .and(week.eq(productionWeekColumn))
    }

    val conditions = mutableListOf(
        dcCodeColumn.`in`(dcCodes),
        productionWeekColumn.`in`(weeks),
    )

    fun withSkus(codes: List<String>): CalculationsConditions = this.apply {
        if (codes.isNotEmpty()) conditions.add(SKU_SPECIFICATION_VIEW.CODE.`in`(codes))
    }

    fun withSkuCategories(categories: List<String>): CalculationsConditions = this.apply {
        if (categories.isNotEmpty()) {
            conditions.add(
                SKU_SPECIFICATION_VIEW.CATEGORY.`in`(categories),
            )
        }
    }

    fun withLocationInBox(locationInBox: List<String>): CalculationsConditions = this.apply {
        if (locationInBox.isNotEmpty()) {
            conditions.add(
                SKU_SPECIFICATION_VIEW.PACKAGING.`in`(locationInBox),
            )
        }
    }

    fun withPoDueInGreaterThan(value: Int?): CalculationsConditions = this.apply {
        if (value != null) {
            conditions.add(
                cskuIdColumn.`in`(
                    selectDistinct(cskuIdColumn)
                        .from(calculationsTable)
                        .where(
                            conditions +
                                CALCULATION.MAX_PURCHASE_ORDER_DUE_IN.inTable(calculationsTable).greaterOrEqual(value),
                        ),
                ),
            )
        }
    }

    fun withPoDueInLessThan(value: Int?): CalculationsConditions = this.apply {
        if (value != null) {
            conditions.add(
                cskuIdColumn.`in`(
                    selectDistinct(cskuIdColumn)
                        .from(calculationsTable)
                        .where(
                            conditions +
                                CALCULATION.MAX_PURCHASE_ORDER_DUE_IN.inTable(calculationsTable).lessOrEqual(value),
                        ),
                ),
            )
        }
    }

    fun withClosingStockLessThanOrEqual(value: Long?): CalculationsConditions = this.apply {
        if (value != null) {
            conditions.add(
                cskuIdColumn.`in`(
                    selectDistinct(cskuIdColumn)
                        .from(calculationsTable)
                        .where(
                            conditions +
                                CALCULATION.CLOSING_STOCK.inTable(calculationsTable).lessOrEqual(value.toBigDecimal()),
                        ),
                ),
            )
        }
    }

    fun withSupplierIds(supplierIds: List<UUID>): CalculationsConditions = this.apply {
        if (supplierIds.isNotEmpty()) {
            val poSkuId = cskuIdColumn.`as`("po_sku_id")
            val poDcCode = PO_CALCULATIONS_VIEW.DC_CODE.`as`("po_dc_code")
            val poProdWeek = productionWeekColumn.`as`("po_prod_week")
            conditions.add(
                cskuIdColumn.`in`(
                    select(cskuIdColumn)
                        .from(
                            selectDistinct(
                                poSkuId,
                                poDcCode,
                                poProdWeek,
                            ).from(calculationsTable)
                                .join(PO_CALCULATIONS_VIEW).on(
                                    PO_CALCULATIONS_VIEW.SKU_ID.eq(cskuIdColumn)
                                        .and(PO_CALCULATIONS_VIEW.DC_CODE.eq(dcCodeColumn))
                                        .and(PO_CALCULATIONS_VIEW.DATE.eq(dateColumn)),
                                )
                                .where(
                                    DSL.arrayOverlap(
                                        PO_CALCULATIONS_VIEW.SUPPLIER_IDS,
                                        DSL.cast(
                                            DSL.array(supplierIds.toTypedArray()),
                                            PO_CALCULATIONS_VIEW.SUPPLIER_IDS.dataType,
                                        ),
                                    ),
                                ),
                        ).where(
                            poSkuId.eq(cskuIdColumn)
                                .and(poDcCode.eq(dcCodeColumn))
                                .and(poProdWeek.eq(productionWeekColumn)),
                        ),
                ),
            )
        }
    }

    fun withActiveSupplierNames(activeSupplierNames: Set<String>): CalculationsConditions = this.apply {
        if (activeSupplierNames.isNotEmpty()) {
            conditions.add(
                cskuIdColumn.`in`(
                    selectDistinct(cskuIdColumn)
                        .from(calculationsTable)
                        .join(SUPPLIER_DETAILS_VIEW).on(
                            SUPPLIER_DETAILS_VIEW.SKU_ID.eq(cskuIdColumn)
                        )
                        .where(
                            conditions +
                                SUPPLIER_DETAILS_VIEW.SUPPLIER_NAME.`in`(activeSupplierNames)
                        ),
                ),
            )
        }
    }

    fun withAdditionalFilters(additionalFilters: Set<AdditionalFilter>) = this.apply {
        conditions.addAll(
            additionalFilterConditions.withAdditionalFilters(
                additionalFilters,
                calculationsTable,
            ),
        )
    }
}

internal fun <A : Record, B> TableField<A, B>.inTable(table: Table<*>): Field<B> = table.field(name, type)!!
