package com.hellofresh.cif.api.calculation.csvexport

import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import kotlin.text.get

class PurchaseOrderCSVService(
    private val purchaseOrderRepository: PurchaseOrderRepository,
    private val dcConfigService: DcConfigService,
) {

    suspend fun fetchPurchaseOrders(calculationRequest: CalculationRequest): List<PurchaseOrder> =
        calculationRequest.dcCodes
            .mapNotNull { dcConfigService.dcConfigurations[it] }
            .groupBy { it.productionStart to it.zoneId }
            .flatMap { (key, dcs) ->
                val (productionStart, zoneId) = key
                calculationRequest.weeks
                    .flatMap { week ->
                        val dcWeek = DcWeek(week)
                        purchaseOrderRepository.findPurchaseOrders(
                            dcs.map { it.dcCode }.toSet(),
                            DateRange(
                                dcWeek.getStartDateInDcWeek(productionStart, zoneId),
                                dcWeek.getLastDateInDcWeek(productionStart, zoneId),
                            ),
                        )
                    }
            }
}
