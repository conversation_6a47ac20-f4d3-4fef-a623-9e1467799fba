package com.hellofresh.cif.api.calculation.db.projectedWaste

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.cif.api.schema.enums.Uom
import java.util.UUID

@JsonIgnoreProperties(ignoreUnknown = true)
data class ProjectedWasteCalculationRecord(
    val dcCode: String,
    val cskuId: UUID,
    @JsonProperty("name")
    val skuName: String,
    @JsonProperty("code")
    val skuCode: String,
    @JsonProperty("acceptable_code_life")
    val acceptableCodeLife: Int,
    @JsonProperty("category")
    val skuCategory: String,
    @JsonProperty("unusable_inventory")
    val unusableInventory: List<ForecastInventory>? = null,
    val uom: Uom
)
