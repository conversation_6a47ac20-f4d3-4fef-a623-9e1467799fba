package com.hellofresh.cif.api.ktor

import com.auth0.jwk.JwkProvider
import com.auth0.jwk.JwkProviderBuilder
import com.auth0.jwt.JWT
import com.auth0.jwt.algorithms.Algorithm
import com.auth0.jwt.interfaces.DecodedJWT
import com.auth0.jwt.interfaces.JWTVerifier
import com.hellofresh.cif.api.docs.docs
import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.auth.authentication
import io.ktor.server.auth.jwt.JWTPrincipal
import io.ktor.server.auth.jwt.jwt
import io.ktor.server.plugins.contentnegotiation.ContentNegotiation
import io.ktor.server.routing.Route
import io.ktor.server.routing.routing
import java.net.URI
import java.security.interfaces.RSAPublicKey

const val DEFAULT_LEEWAY_SECONDS: Long = 10

enum class AcceptedAlgorithm(val value: String) {
    RS256("RS256"),
    HS256("HS256"),
    NONE("none")
}

fun docsRoutingModule(): Application.() -> Unit = { routing { docs() } }

fun configureSerialization(app: Route) {
    app.install(ContentNegotiation) {
        customJackson()
    }
}

fun Application.configureJwtAuth(jwtCredentials: JwtCredentials, enabled: Boolean) {
    val jwksUrl = URI(jwtCredentials.jwksURI).toURL()

    val jwkProvider = JwkProviderBuilder(jwksUrl)
        .build()

    authentication {
        jwt {
            skipWhen { !enabled }
            realm = jwtCredentials.realm

            verifier { token ->
                createJwtVerifier(jwkProvider, jwtCredentials, token.toString())
            }

            validate { credential ->
                JWTPrincipal(credential.payload)
            }
        }
    }
}

fun getJwtAlgorithmType(jwt: DecodedJWT): String = jwt.algorithm

fun createJwtVerifier(jwkProvider: JwkProvider, jwtCredentials: JwtCredentials, token: String): JWTVerifier {
    val jwt = JWT.decode(token.split("Bearer ")[1])

    return when (getJwtAlgorithmType(jwt)) {
        AcceptedAlgorithm.RS256.value -> {
            val jwk = jwkProvider[jwt.keyId]
            val algorithm = Algorithm.RSA256(jwk.publicKey as RSAPublicKey, null)
            JWT.require(algorithm)
                .withIssuer(jwtCredentials.issuer)
                .withAudience(jwtCredentials.clientId)
                .acceptLeeway(DEFAULT_LEEWAY_SECONDS)
                .build()
        }
        AcceptedAlgorithm.HS256.value -> {
            JWT.require(Algorithm.HMAC256(jwtCredentials.secret))
                .withClaimPresence("sub")
                .withClaimPresence("email")
                .build()
        }
        AcceptedAlgorithm.NONE.value -> {
            JWT.require(Algorithm.none())
                .withIssuer(jwtCredentials.issuer)
                .withAudience(jwtCredentials.clientId)
                .acceptLeeway(DEFAULT_LEEWAY_SECONDS)
                .build()
        }
        else -> { throw IllegalArgumentException("Unsupported jwt algorithm ${getJwtAlgorithmType(jwt)}") }
    }
}
