package com.hellofresh.cif.api.fileupload

import com.hellofresh.cif.api.calculation.generated.model.FileUploadResponse
import com.hellofresh.cif.api.fileupload.repository.FileUploadRepository

class FileUploadService(private val fileUploadRepository: FileUploadRepository) {
    suspend fun getFileUploads(market: String, fileType: String): List<FileUploadResponse> =
        fileUploadRepository.fetchFileUploads(market, fileType)
}
