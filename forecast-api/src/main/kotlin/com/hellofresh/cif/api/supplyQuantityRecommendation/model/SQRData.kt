package com.hellofresh.cif.api.supplyQuantityRecommendation.model

import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SQRState.PROCESSED
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.safetystock.model.SkuRiskRating
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.UUID

data class SQRData(
    val dcCode: String,
    val week: String,
    val skus: List<SkuDetail>
)

data class SkuDetail(
    val skuId: UUID,
    val skuCode: String,
    val skuName: String,
    val skuCategory: String,
    val uom: SkuUOM,
    val inventoryRollover: BigDecimal,
    val stockUpdates: BigDecimal? = null,
    val safetyStock: BigDecimal?,
    val demand: BigDecimal,
    val safetyStockRiskMultiplier: BigDecimal,
    val skuRiskRating: SkuRiskRating,
    val bufferPercentage: BigDecimal?,
    val supplyQuantityRecommendation: BigDecimal,
    val recommendationEnabled: Boolean,
    val multiWeekEnabled: Boolean,
    val updatedAt: OffsetDateTime,
    val state: SQRState = PROCESSED
) {
    companion object
}

enum class SQRState {
    PENDING,
    PROCESSED
}
