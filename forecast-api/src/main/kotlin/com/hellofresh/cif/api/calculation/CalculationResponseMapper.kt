package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.generated.model.ActualInboundResponse
import com.hellofresh.cif.api.calculation.generated.model.CalculationResponse
import com.hellofresh.cif.api.calculation.generated.model.CalculationStatus as CalculationStatusApi
import com.hellofresh.cif.api.calculation.generated.model.ConsumptionByBrand
import com.hellofresh.cif.api.calculation.generated.model.ConsumptionDetail
import com.hellofresh.cif.api.calculation.generated.model.DailyCalculationResponse
import com.hellofresh.cif.api.calculation.generated.model.ExpectedInboundResponse
import com.hellofresh.cif.api.calculation.generated.model.UnusableStockByType
import com.hellofresh.cif.api.sku.SkuQuantityMapper
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.featureflags.Context.DC
import com.hellofresh.cif.featureflags.ContextData
import com.hellofresh.cif.featureflags.FeatureFlag.AutomatedDcLiveRules
import com.hellofresh.cif.featureflags.StatsigFeatureFlagClient
import java.math.BigDecimal
import org.apache.logging.log4j.kotlin.Logging

private const val MIN_PURCHASE_ORDER_DUE_IN = -7
private const val MAX_PURCHASE_ORDER_DUE_IN = 10

class CalculationResponseMapper(
    private val statsigFeatureFlagClient: StatsigFeatureFlagClient
) {
    fun toDailyCalculationResponse(dailyView: DailyView): DailyCalculationResponse {
        val hideStagingStorage = automatedDCLiveRulesFlag(dailyView.calculation.dcCode)
        return DailyCalculationResponse(
            skuId = dailyView.sku.skuId,
            skuCode = dailyView.sku.skuCode,
            unusableStock = dailyView.calculation.unusableStock.stripTrailingZerosAndConvert(),
            usableStock = dailyView.calculation.usableStock.stripTrailingZerosAndConvert(),
            incomingPos = ExpectedInboundResponse(dailyView.calculation.incomingPos.stripTrailingZerosAndConvert()),
            inbound = ActualInboundResponse(dailyView.calculation.inbound.stripTrailingZerosAndConvert()),
            consumption = dailyView.calculation.consumption.stripTrailingZerosAndConvert(),
            dailyNeed = dailyView.calculation.dailyNeed.stripTrailingZerosAndConvert(),
            closingStock = dailyView.calculation.closingStock.stripTrailingZerosAndConvert(),
            skuName = dailyView.sku.skuName,
            skuCategories = dailyView.sku.skuCategories,
            date = dailyView.productionDay.day,
            pos = dailyView.calculation.pos.toList(),
            actualConsumption = dailyView.calculation.actualConsumption.stripTrailingZerosAndConvert(),
            dcCode = dailyView.calculation.dcCode,
            atRisk = dailyView.calculation.skuAtRisk,
            week = dailyView.productionDay.week,
            safetyStock = dailyView.calculation.safetyStock?.stripTrailingZerosAndConvert(),
            strategy = dailyView.calculation.strategy,
            safetyStockNeeds = dailyView.calculation.safetyStockNeeds?.stripTrailingZerosAndConvert(),
            storageStock = if (!hideStagingStorage) {
                dailyView.calculation.storageStock?.stripTrailingZerosAndConvert()
            } else {
                null
            },
            stagingStock = if (!hideStagingStorage) {
                dailyView.calculation.stagingStock?.stripTrailingZerosAndConvert()
            } else {
                null
            },
            poDueIn = dailyView.calculation.poDueIn
                ?.takeIf { it in MIN_PURCHASE_ORDER_DUE_IN..MAX_PURCHASE_ORDER_DUE_IN },
            netNeeds = dailyView.calculation.netNeeds.stripTrailingZerosAndConvert(),
            consumptionDetails = getConsumptionDetails(dailyView.calculation),
            unusableStockDetails = dailyView.calculation.unusableStockDetails
                ?.map { unusableStockByType ->
                    UnusableStockByType(
                        type = unusableStockByType.type,
                        qty = unusableStockByType.qty,
                    )
                },
            stockUpdate = dailyView.calculation.stockUpdate,
            uom = SkuQuantityMapper.mapSkuUOMToUomEnum(dailyView.calculation.uom),
            status = mapStatus(dailyView.calculation.status),
            expectedInboundTransferOrders = dailyView.calculation.expectedInboundTo.toList(),
            expectedInboundTransferOrdersQuantity = dailyView.calculation.expectedInboundToQty,
            actualInboundTransferOrders = dailyView.calculation.actualInboundTo.toList(),
            actualInboundTransferOrdersQuantity = dailyView.calculation.actualInboundToQty,
            expectedOutboundTransferOrders = dailyView.calculation.expectedOutboundTo.toList(),
            expectedOutboundTransferOrdersQuantity = dailyView.calculation.expectedOutboundToQty,
        )
    }

    fun toWeeklyCalculationResponse(weeklyView: WeeklyView) =
        CalculationResponse(
            skuId = weeklyView.sku.skuId,
            skuCode = weeklyView.sku.skuCode,
            unusableStock = weeklyView.calculation.unusableStock.stripTrailingZerosAndConvert(),
            usableStock = weeklyView.calculation.usableStock.stripTrailingZerosAndConvert(),
            incomingPos = ExpectedInboundResponse(weeklyView.calculation.incomingPos.stripTrailingZerosAndConvert()),
            inbound = ActualInboundResponse(weeklyView.calculation.inbound.stripTrailingZerosAndConvert()),
            consumption = weeklyView.calculation.consumption.stripTrailingZerosAndConvert(),
            dailyNeed = weeklyView.calculation.dailyNeed.stripTrailingZerosAndConvert(),
            closingStock = weeklyView.calculation.closingStock.stripTrailingZerosAndConvert(),
            skuName = weeklyView.sku.skuName,
            skuCategories = weeklyView.sku.skuCategories,
            week = weeklyView.week,
            pos = weeklyView.calculation.pos.toList(),
            actualConsumption = weeklyView.calculation.actualConsumption.stripTrailingZerosAndConvert(),
            dcCode = weeklyView.calculation.dcCode,
            atRisk = weeklyView.calculation.skuAtRisk,
            safetyStock = weeklyView.calculation.safetyStock?.stripTrailingZerosAndConvert(),
            safetyStockNeeds = weeklyView.calculation.safetyStockNeeds?.stripTrailingZerosAndConvert(),
            netNeeds = weeklyView.calculation.netNeeds.stripTrailingZerosAndConvert(),
            consumptionDetails = getConsumptionDetails(weeklyView.calculation),
            unusableStockDetails = weeklyView.calculation.unusableStockDetails
                ?.map { unusableStockByType ->
                    UnusableStockByType(
                        type = unusableStockByType.type,
                        qty = unusableStockByType.qty,
                    )
                },
            stockUpdate = weeklyView.calculation.stockUpdate,
            uom = SkuQuantityMapper.mapSkuUOMToUomEnum(weeklyView.calculation.uom),
            status = mapStatus(weeklyView.calculation.status),
            expectedInboundTransferOrders = weeklyView.calculation.expectedInboundTo.toList(),
            expectedInboundTransferOrdersQuantity = weeklyView.calculation.expectedInboundToQty,
            actualInboundTransferOrders = weeklyView.calculation.actualInboundTo.toList(),
            actualInboundTransferOrdersQuantity = weeklyView.calculation.actualInboundToQty,
            expectedOutboundTransferOrders = weeklyView.calculation.expectedOutboundTo.toList(),
            expectedOutboundTransferOrdersQuantity = weeklyView.calculation.expectedOutboundToQty,
        )

    private fun getConsumptionDetails(calculation: Calculation): ConsumptionDetail =
        ConsumptionDetail(
            consumption = calculation.brandConsumptions.map { brandConsumption ->
                ConsumptionByBrand(
                    brand = brandConsumption.brand,
                    qty = brandConsumption.qty,
                )
            },
            prekitting = calculation.prekitting,
            substituted = calculation.substituted,
        )

    private fun automatedDCLiveRulesFlag(dcCode: DcCode) = statsigFeatureFlagClient.isEnabledFor(
        AutomatedDcLiveRules(setOf(ContextData(DC, dcCode))),
    )

    private fun BigDecimal.stripTrailingZerosAndConvert() =
        this.stripTrailingZeros().toPlainString().toBigDecimal()

    private fun mapStatus(calculationStatus: CalculationStatus) =
        when (calculationStatus) {
            CalculationStatus.PENDING -> CalculationStatusApi.PENDING
            CalculationStatus.PROCESSED -> CalculationStatusApi.PROCESSED
        }

    companion object : Logging
}
