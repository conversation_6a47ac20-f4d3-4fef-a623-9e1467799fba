package com.hellofresh.cif.api.demand

import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.time.LocalDateTime

class DemandService(private val demandRepository: DemandRepository) {
    suspend fun getLatestDemandInfo(dcCodes: List<DcCode>, weeks: List<DcWeek>) =
        demandRepository.getLatestDemandUpdates(dcCodes, weeks)

    suspend fun getDemandByMarket(dcWeeks: List<DcWeek>, market: String, timestamp: LocalDateTime?) =
        demandRepository.getDemandByMarket(dcWeeks, market, timestamp)
}
