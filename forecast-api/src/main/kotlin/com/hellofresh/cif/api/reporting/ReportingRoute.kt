package com.hellofresh.cif.api.reporting

import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.route
import io.ktor.server.routing.routing
import java.time.LocalDate
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

private const val DC_CODE = "dcCode"
private const val WEEK = "week"
fun Routing.stockReporting(
    stockReportingService: StockReportingService,
    timeoutInMillis: Long
) = authenticate {
    route("/dc/{dcCode}") {
        get("/stockVariance") {
            withTimeout(timeoutInMillis) {
                kotlin.runCatching {
                    val dcCode = call.parameters.getOrThrow(DC_CODE).trim().uppercase()
                    val cleardownDate = LocalDate.parse(call.parameters.getOrThrow("cleardownDate").trim().uppercase())
                    kotlin.runCatching {
                        val variances = stockReportingService
                            .getStockVarianceReport(dcCode, cleardownDate)
                        mapStockVarianceResponse(dcCode, variances)
                    }.onSuccess { v ->
                        call.respond(HttpStatusCode.OK, v)
                    }.onFailure {
                        call.handleResponseError(it)
                    }
                }
            }
        }
        get("/liveVariance") {
            withTimeout(timeoutInMillis) {
                kotlin.runCatching {
                    val dcCode = call.parameters.getOrThrow(DC_CODE).trim().uppercase()
                    val dcWeek = DcWeek(call.parameters.getOrThrow(WEEK).trim().uppercase())
                    kotlin.runCatching {
                        val liveStockReport = stockReportingService
                            .getLiveStockVarianceReport(dcCode, dcWeek)
                        mapLiveStockVarianceResponse(liveStockReport)
                    }.onSuccess { v ->
                        call.respond(HttpStatusCode.OK, v)
                    }.onFailure {
                        call.handleResponseError(it)
                    }
                }
            }
        }
        get("/inboundVariance") {
            withTimeout(timeoutInMillis) {
                kotlin.runCatching {
                    val dcCode = call.parameters.getOrThrow(DC_CODE).trim().uppercase()
                    val dcWeek = DcWeek(call.parameters.getOrThrow(WEEK).trim().uppercase())
                    kotlin.runCatching {
                        val inboundVariances = stockReportingService
                            .getStockInboundVarianceReport(dcCode, dcWeek)
                        mapInboundVarianceResponse(inboundVariances)
                    }.onFailure { exception ->
                        call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
                    }
                }.onFailure { exception ->
                    call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
                }.onSuccess { v ->
                    call.respond(HttpStatusCode.OK, v)
                }
            }
        }
    }
}

fun stockReportingRoutingModule(
    stockReportingService: StockReportingService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        stockReporting(stockReportingService, timeout.inWholeMilliseconds).also {
            configureSerialization(it)
        }
    }
}
