package com.hellofresh.cif.api.shortShelfLife.repository

import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLife
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeConfig
import com.hellofresh.cif.api.shortShelfLife.model.SkuSpecification
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateDto
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.inventory.StockUpdate
import java.time.LocalDate
import java.util.UUID

interface ShortShelfLifeRepository {
    suspend fun getShortShelfLife(dcCode: DcCode, dates: List<LocalDate>): List<ShortShelfLife>
    suspend fun upsertShortShelfLifeConfig(
        configs: List<ShortShelfLifeConfig>,
        stockUpdates: List<StockUpdateDto>,
    ): List<StockUpdate>

    suspend fun getSkuSpecificationById(skuId: UUID): SkuSpecification
}
