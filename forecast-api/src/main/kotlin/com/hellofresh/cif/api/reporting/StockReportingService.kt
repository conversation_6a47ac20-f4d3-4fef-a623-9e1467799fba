package com.hellofresh.cif.api.reporting

import com.hellofresh.cif.api.configuration.Sku
import com.hellofresh.cif.api.po.PurchaseOrderService
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationViewRecord
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.Asn
import com.hellofresh.cif.models.purchaseorder.PoSkuVariance
import com.hellofresh.cif.models.purchaseorder.StockInboundVarianceSku
import com.hellofresh.inventory.models.variance.DailyInventoryVarianceData
import java.math.BigDecimal
import java.math.RoundingMode.HALF_EVEN
import java.math.RoundingMode.HALF_UP
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.UUID

private const val PERCENTAGE = 100.0
private val HUNDRED = BigDecimal("100")

@Suppress("MagicNumber")
class StockReportingService(
    private val stockReportingRepository: StockReportingRepository,
    private val purchaseOrderService: PurchaseOrderService,
) {
    suspend fun getStockVarianceReport(dcCode: String, cleardownDate: LocalDate): List<StockVariance> =
        stockReportingRepository.stockVarianceFromCleardown(dcCode, cleardownDate).map { variance ->
            val previousWeekCalculations = variance.previousWeek.map { it.calculationRecord }
            val openingStockPreviousCleardown = previousWeekCalculations.first().openingStock
            val openingStockLatestCleardown = variance.latestCleardown.calculationRecord.openingStock
            val sumActualInbound = previousWeekCalculations.sumOf { c -> c.actualInbound }
            val sumDemanded = previousWeekCalculations.sumOf { c -> c.demanded }
            val sumUnusable = previousWeekCalculations.sumOf { c -> c.expired }
            val openingStockBeginningOfWeek = variance.previousWeek.first().calculationRecord.openingStock

            StockVariance(
                sku = mapSku(variance.latestCleardown.sku),
                cleardownDay = variance.latestCleardown.calculationRecord.date.dayOfWeek,
                currentCleardownWeek = DcWeek(variance.latestCleardown.calculationRecord.productionWeek),
                previousCleardownWeek = DcWeek(previousWeekCalculations.first().productionWeek),
                openingStockLatestCleardown = openingStockLatestCleardown.toLong(),
                openingStockPreviousCleardown = openingStockPreviousCleardown.toLong(),
                openingStockBeginningOfWeek = openingStockBeginningOfWeek.toLong(),
                weekActualInbound = sumActualInbound.toLong(),
                weekConsumption = sumDemanded.toLong(),
                weekUnusable = sumUnusable.toLong(),
                skuCategory = variance.latestCleardown.sku.category,
            )
        }

    suspend fun getLiveStockVarianceReport(dcCode: String, dcWeek: DcWeek) =
        stockReportingRepository.liveStockVariance(dcCode, dcWeek.value)

    suspend fun getStockInboundVarianceReport(dcCode: String, dcWeek: DcWeek): List<StockInboundVarianceSku> {
        val poVariance =
            purchaseOrderService.findPoVariance(dcCode, dcWeek)
        return poVariance.flatMap { purchaseOrder ->
            purchaseOrder.poSkuVariances.map { purchaseOrderSku ->
                val asnForGivenSku = purchaseOrder.asns.find { it.skuId == purchaseOrderSku.skuId }
                StockInboundVarianceSku(
                    poRef = purchaseOrder.poReference,
                    asnId = asnForGivenSku?.id,
                    supplierId = purchaseOrder.supplier?.id,
                    supplierName = purchaseOrder.supplier?.name,
                    skuId = purchaseOrderSku.skuId,
                    skuName = purchaseOrderSku.skuName,
                    skuCode = purchaseOrderSku.skuCode,
                    skuCategory = purchaseOrderSku.skuCategory,
                    poVsInbound = poVsInboundVariance(purchaseOrderSku)?.let { SkuQuantity.fromLong(it) },
                    asnVsInbound = asnVsInboundVariance(purchaseOrderSku, asnForGivenSku)?.let { quantity ->
                        purchaseOrderSku.expectedQuantity?.unitOfMeasure?.let { uom ->
                            SkuQuantity.fromLong(quantity, uom)
                        }
                    },
                )
            }
        }
    }

    private fun poVsInboundVariance(purchaseOrderSku: PoSkuVariance) =
        purchaseOrderSku.expectedQuantity?.let {
            val expectedQuantity = it.getValue()
            val deliveredQuantity = purchaseOrderSku.deliveredQuantity?.getValue()
                ?: BigDecimal.ZERO.stripTrailingZeros().toPlainString().toBigDecimal()

            val variance = expectedQuantity.subtract(
                deliveredQuantity,
            ).stripTrailingZeros().toPlainString().toBigDecimal()
            val percentageVariance = if (expectedQuantity != BigDecimal.ZERO.stripTrailingZeros().toPlainString().toBigDecimal()) {
                variance.divide(expectedQuantity, 2, HALF_EVEN) * BigDecimal(PERCENTAGE)
            } else {
                if (deliveredQuantity == BigDecimal.ZERO.stripTrailingZeros().toPlainString().toBigDecimal()) {
                    BigDecimal.ZERO.stripTrailingZeros().toPlainString().toBigDecimal()
                } else {
                    HUNDRED
                }
            }
            toMaxHundredPercent(percentageVariance.abs()).toLong()
        }

    private fun asnVsInboundVariance(
        purchaseOrderSku: PoSkuVariance,
        asnForGivenSku: Asn?
    ) =
        asnForGivenSku?.shippedQuantity?.let {
            val shippedQuantity = it.getValue()
            val deliveredQuantity = purchaseOrderSku.deliveredQuantity?.getValue()
                ?: BigDecimal.ZERO.setScale(5, HALF_UP)

            val variance = shippedQuantity.subtract(
                deliveredQuantity,
            ).stripTrailingZeros().toPlainString().toBigDecimal()
            val percentageVariance = if (shippedQuantity != BigDecimal.ZERO.stripTrailingZeros().toPlainString().toBigDecimal()) {
                variance.divide(shippedQuantity, 2, HALF_EVEN) * BigDecimal(PERCENTAGE).stripTrailingZeros().toPlainString().toBigDecimal()
            } else {
                if (
                    shippedQuantity == BigDecimal.ZERO.stripTrailingZeros().toPlainString().toBigDecimal() &&
                    deliveredQuantity == BigDecimal.ZERO.stripTrailingZeros().toPlainString().toBigDecimal()
                ) {
                    BigDecimal.ZERO.stripTrailingZeros().toPlainString().toBigDecimal()
                } else {
                    HUNDRED
                }
            }
            toMaxHundredPercent(percentageVariance.abs()).toLong()
        }

    private fun mapSku(skuSpec: SkuSpecificationViewRecord) = Sku(skuSpec.code, skuSpec.id, skuSpec.name)
}

private fun toMaxHundredPercent(variancePercent: BigDecimal) =
    if (variancePercent > HUNDRED) HUNDRED else variancePercent

data class StockLiveVarianceSku(
    val skuId: UUID,
    val skuCode: String,
    val skuName: String,
    val skuCategories: String,
    val cleardownVariance: Long,
    val liveVariance: Long,
    val dailyInventoryVarianceData: List<DailyInventoryVarianceData>
)

data class StockVariance(
    val sku: Sku,
    val skuCategory: String,
    val cleardownDay: DayOfWeek,
    val currentCleardownWeek: DcWeek,
    val previousCleardownWeek: DcWeek,
    val openingStockLatestCleardown: Long,
    val openingStockPreviousCleardown: Long,
    val openingStockBeginningOfWeek: Long,
    val weekActualInbound: Long,
    val weekConsumption: Long,
    val weekUnusable: Long,
) {
    val calculatedConsumption = openingStockBeginningOfWeek + weekActualInbound - weekUnusable - openingStockLatestCleardown
    val missing = calculatedConsumption - weekConsumption
    val unallocatedStock = weekConsumption - calculatedConsumption
}
