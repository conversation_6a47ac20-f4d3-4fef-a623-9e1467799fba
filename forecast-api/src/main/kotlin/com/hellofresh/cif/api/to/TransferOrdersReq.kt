package com.hellofresh.cif.api.to

import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.time.LocalDate
import java.util.UUID

data class TransferOrdersReq(
    val skuId: UUID,
    val dcCodes: Set<String>,
    val weeks: Set<DcWeek>?,
    val fromDate: LocalDate?,
    val toDate: LocalDate?
) {
    init {
        require(dcCodes.isNotEmpty()) {
            "at least one dc code is required"
        }

        if (weeks.isNullOrEmpty()) {
            require(fromDate != null && toDate != null) {
                "fromDate and toDate are required if the weeks query param is not supplied"
            }
        }

        if (fromDate != null || toDate != null) {
            require(fromDate != null && toDate != null) {
                "One of fromDate and toDate is null"
            }
            require(fromDate.isBefore(toDate) || fromDate == toDate) {
                "fromDate must be before or equal with toDate"
            }
        }
    }
}
