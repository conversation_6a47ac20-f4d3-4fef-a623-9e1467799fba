package com.hellofresh.cif.api.calculation.experiment

import com.hellofresh.cif.api.calculation.generated.model.DailyCalculationExperimentResponse
import com.hellofresh.cif.api.sku.SkuQuantityMapper
import com.hellofresh.cif.api.stockupdate.StockUpdateResults
import com.hellofresh.cif.calculator.models.CalculationKey

object CalculationExperimentResponseMapper {
    fun mapToCalculationExperimentResponse(
        stockUpdateResults: StockUpdateResults
    ) = stockUpdateResults.calculations.map { dayCalculationResult ->
        DailyCalculationExperimentResponse(
            date = dayCalculationResult.date,
            incoming = dayCalculationResult.expectedInbound.getValue().toInt(),
            inbound = dayCalculationResult.actualInbound.getValue().toInt(),
            unusable = dayCalculationResult.unusable.getValue().toInt(),
            closing = dayCalculationResult.closingStock.getValue().toInt(),
            opening = dayCalculationResult.openingStock.getValue().toInt(),
            consumption = dayCalculationResult.demanded.getValue().toInt(),
            actualConsumption = dayCalculationResult.actualConsumption.getValue().toInt(),
            uom = SkuQuantityMapper.mapSkuUOMToUomEnum(dayCalculationResult.demanded.unitOfMeasure),
            experiment = stockUpdateResults
                .stockUpdates[
                CalculationKey(
                    dayCalculationResult.cskuId,
                    dayCalculationResult.dcCode,
                    dayCalculationResult.date,
                ),
            ]
                ?.getValue()?.toInt(),
        )
    }
}
