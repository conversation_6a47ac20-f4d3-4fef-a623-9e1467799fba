package com.hellofresh.cif.api.cleardown

import com.hellofresh.cif.api.error.handleResponseError
import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getLoggedInUserInfo
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.api.user.LoggedInUserInfo
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.plugins.NotFoundException
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.post
import io.ktor.server.routing.routing
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun Routing.cleardownTrigger(
    cleardownTriggerRepository: CleardownTriggerRepository,
    timeout: Duration
) = authenticate {
    post("/inventory/cleardownTrigger") {
        kotlin.runCatching {
            val dcCodes = this.call.parameters.getAllOrThrow("dcCode")
            val snapshotId = this.call.parameters["snapshotId"]
            val snapshotUUID = if (!snapshotId.isNullOrEmpty()) UUID.fromString(snapshotId) else null
            val loggedInUserInfo = call.getLoggedInUserInfo()
            CleardownTriggerRequest(dcCodes, snapshotUUID, loggedInUserInfo)
        }.onFailure { exception ->
            call.respond(HttpStatusCode.BadRequest, call.handleResponseError(exception))
        }.onSuccess { request ->
            kotlin.runCatching {
                withTimeout(timeout.inWholeMilliseconds) {
                    cleardownTriggerRepository.saveCleardownTrigger(
                        request.dcCodes,
                        request.loggedInUserInfo.userEmail,
                        request.loggedInUserInfo.userName,
                        request.snapshotId
                    )
                }
            }.onSuccess { result ->
                call.respond(HttpStatusCode.OK, result)
            }.onFailure { exception ->
                if (exception is NotFoundException) {
                    call.respond(HttpStatusCode.NotFound, mapToErrorResponse(exception))
                } else {
                    call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(exception))
                }
            }
        }
    }
}

fun cleardownTriggerModule(
    cleardownTriggerRepository: CleardownTriggerRepository,
    timeout: Duration
): Application.() -> Unit = {
    routing {
        cleardownTrigger(cleardownTriggerRepository, timeout).also {
            configureSerialization(it)
        }
    }
}

internal data class CleardownTriggerRequest(
    val dcCodes: List<String>,
    val snapshotId: UUID?,
    val loggedInUserInfo: LoggedInUserInfo
)
