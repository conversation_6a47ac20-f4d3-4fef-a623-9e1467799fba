package com.hellofresh.cif.api.supplier

import com.hellofresh.cif.api.calculation.generated.model.SupplierLeadTimeResponse
import com.hellofresh.cif.api.error.mapToErrorResponse
import com.hellofresh.cif.api.getAllOrThrow
import com.hellofresh.cif.api.getOrThrow
import com.hellofresh.cif.api.ktor.configureSerialization
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.skuinput.repo.SupplierRepository
import com.hellofresh.sku.models.SupplierSkuDetail
import io.ktor.http.HttpStatusCode
import io.ktor.server.application.Application
import io.ktor.server.application.call
import io.ktor.server.auth.authenticate
import io.ktor.server.plugins.NotFoundException
import io.ktor.server.response.respond
import io.ktor.server.routing.Routing
import io.ktor.server.routing.get
import io.ktor.server.routing.routing
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import kotlin.time.Duration
import kotlinx.coroutines.withTimeout

fun Routing.supplier(
    supplierRepository: SupplierRepository,
    dcConfigService: DcConfigService,
    timeoutInMillis: Long
) = authenticate {
    get("/suppliers/{dcCode}/{skuId}") {
        runCatching {
            val skuId = UUID.fromString(call.parameters.getOrThrow("skuId").trim())
            val dcCodes = call.parameters.getAllOrThrow("dcCode").toSet()
            require(dcCodes.count() == 1) {
                "Multiple DCs are not supported"
            }
            val today = LocalDate.now(dcConfigService.dcConfigurations[dcCodes.first()]?.zoneId ?: ZoneOffset.UTC)
            skuId to today
        }.onFailure { exception ->
            if (exception is NotFoundException) {
                call.respond(HttpStatusCode.NotFound, mapToErrorResponse(exception))
            } else {
                call.respond(HttpStatusCode.BadRequest, mapToErrorResponse(exception))
            }
        }.onSuccess { (skuId, today) ->
            runCatching {
                withTimeout(timeoutInMillis) {
                    supplierRepository.fetchSuppliersBySkuId(skuId)[skuId] ?: emptyList()
                }.map { toSupplierLeadTimeResponse(today, it) }.filter {
                    it.leadTime != null
                }
            }
                .onSuccess { result ->
                    val status = if (result.isEmpty()) HttpStatusCode.NotFound else HttpStatusCode.OK
                    call.respond(status, result)
                }
                .onFailure { call.respond(HttpStatusCode.InternalServerError, mapToErrorResponse(it)) }
        }
    }
}

fun toSupplierLeadTimeResponse(today: LocalDate, supplierSkuDetail: SupplierSkuDetail) =
    SupplierLeadTimeResponse(
        supplierId = supplierSkuDetail.supplierId,
        supplierName = supplierSkuDetail.supplierName,
        leadTime = supplierSkuDetail.getMaxLeadTimeByDate(today)?.toLong(),
    )
fun supplierRoutingModule(
    supplierRepository: SupplierRepository,
    dcConfigService: DcConfigService,
    timeout: Duration,
): Application.() -> Unit = {
    routing {
        supplier(supplierRepository, dcConfigService, timeout.inWholeMilliseconds).also {
            configureSerialization(it)
        }
    }
}
