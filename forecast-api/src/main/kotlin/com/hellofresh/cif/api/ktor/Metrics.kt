package com.hellofresh.cif.api.ktor

import io.ktor.server.application.Application
import io.ktor.server.application.install
import io.ktor.server.metrics.micrometer.MicrometerMetrics
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.distribution.DistributionStatisticConfig

@Suppress("MagicNumber")
fun Application.installMetrics(meterRegistry: MeterRegistry) {
    install(MicrometerMetrics) {
        registry = meterRegistry
        distributionStatisticConfig = DistributionStatisticConfig.Builder()
            .percentilesHistogram(true)
            .percentiles(0.50, 0.90, 0.95, 0.99)
            .build()
    }
}
