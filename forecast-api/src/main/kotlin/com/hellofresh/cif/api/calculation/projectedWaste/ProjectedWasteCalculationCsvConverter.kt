package com.hellofresh.cif.api.calculation.projectedWaste

import com.hellofresh.cif.api.calculation.generated.model.ProjectedWastesResponse
import io.ktor.http.ContentType
import io.ktor.http.content.OutgoingContent
import io.ktor.http.content.TextContent
import io.ktor.serialization.ContentConverter
import io.ktor.util.reflect.TypeInfo
import io.ktor.utils.io.ByteReadChannel
import java.io.StringWriter
import java.nio.charset.Charset
import java.time.LocalDate
import org.apache.commons.csv.CSVFormat

class ProjectedWasteCalculationCsvConverter : ContentConverter {

    override suspend fun deserialize(charset: Charset, typeInfo: TypeInfo, content: ByteReadChannel): Any? {
        throw IllegalArgumentException("illegal call")
    }

    override suspend fun serialize(contentType: ContentType, charset: io.ktor.utils.io.charsets.Charset, typeInfo: TypeInfo, value: Any?): OutgoingContent {
        val projectedWasteCalculations = require(value is ProjectedWastesResponse) {
            "Type ${value?.javaClass} not supported"
        }.let {
            toProjectedWasteCalculationItems(value)
        }
        val content = createProjectedWasteCalculationCsvPrinter().print(projectedWasteCalculations)
        return TextContent(content, contentType)
    }

    private fun toProjectedWasteCalculationItems(
        projectedWastesResponse: ProjectedWastesResponse
    ): List<ProjectedWasteCalculationItem> =
        projectedWastesResponse.projectedWasteResponses?.map { projectedWasteResponse ->
            ProjectedWasteCalculationItem(
                dcCode = projectedWasteResponse.dcCode,
                skuCode = projectedWasteResponse.sku.skuCode ?: "",
                skuName = projectedWasteResponse.sku.skuName ?: "",
                quantity = projectedWasteResponse.quantity ?: 0,
                date = projectedWasteResponse.date,
                isUsable = if (projectedWasteResponse.isUsable == true) "YES" else "NO",
                unusableStockReason = projectedWasteResponse.unusableStockReason,
            )
        } ?: emptyList()

    companion object {
        private val defaultCsvHeader = listOf(
            "Distribution Center",
            "SKU Code",
            "Sku Name",
            "Quantity",
            "Use by Date",
            "Usable Today",
            "Reason Unusable",
        )

        fun getCsvHeaders() = defaultCsvHeader

        private fun createProjectedWasteCalculationCsvPrinter() =
            ProjectedWasteCalculationCsvPrinter(defaultCsvHeader) { item ->
                listOf(
                    item.dcCode,
                    item.skuCode,
                    item.skuName,
                    item.quantity,
                    item.date,
                    item.isUsable,
                    item.unusableStockReason,
                )
            }
    }
}

@Suppress("SpreadOperator")
private class ProjectedWasteCalculationCsvPrinter(
    private val headers: List<String>,
    private val itemMapping: (ProjectedWasteCalculationItem) -> List<*>
) {

    fun print(items: List<ProjectedWasteCalculationItem>): String =
        StringWriter().use { writer ->
            CSVFormat.DEFAULT
                .builder().setHeader(*headers.toTypedArray())
                .build()
                .print(writer)
                .use { csvPrinter ->
                    items.forEach { item ->
                        csvPrinter.printRecord(itemMapping(item))
                    }
                    csvPrinter.flush()
                }
            writer.buffer.toString()
        }
}

data class ProjectedWasteCalculationItem(
    val dcCode: String,
    val skuCode: String?,
    val skuName: String?,
    val quantity: Long?,
    val date: LocalDate?,
    val isUsable: String,
    val unusableStockReason: String?
)
