package com.hellofresh.cif.api.calculation.db

import com.hellofresh.cif.api.calculation.AdditionalFilter
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_CONSUMPTION_ONLY
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_INBOUND_SHORTAGE
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_INVENTORY_SHORTAGE
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_NET_NEEDS_ONLY
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_NO_CONSUMPTION_ONLY
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_PROJECTED_WASTE
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_SKU_SUBBED_IN_ONLY
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_SKU_SUBBED_OUT_ONLY
import com.hellofresh.cif.api.calculation.db.CalculationsQueryStep.CalculationInventoryRefreshMode
import com.hellofresh.cif.api.schema.Tables.CALCULATION
import com.hellofresh.cif.api.schema.Tables.CALCULATION_SUBSTITUTION_VIEW
import com.hellofresh.cif.api.schema.Tables.PRE_PROD_CALCULATION_SUBSTITUTION_VIEW
import com.hellofresh.cif.api.schema.enums.SubbedType.SUB_IN
import com.hellofresh.cif.api.schema.enums.SubbedType.SUB_IN_AND_OUT
import com.hellofresh.cif.api.schema.enums.SubbedType.SUB_OUT
import java.math.BigDecimal
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import org.jooq.Condition
import org.jooq.Field
import org.jooq.SelectJoinStep
import org.jooq.Table
import org.jooq.impl.DSL
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.select

private const val WEEK_WITH_NO_CONSUMPTION_ONLY = 7

class CalculationsAdditionalFilterConditions(
    val field: Field<UUID>,
    calculationInventoryRefreshMode: CalculationInventoryRefreshMode,
    val condition: (Field<UUID>, Field<String>, Field<String>) -> Condition
) {

    private val substitutionView =
        if (calculationInventoryRefreshMode.isProdCalculation) {
            CALCULATION_SUBSTITUTION_VIEW
        } else {
            PRE_PROD_CALCULATION_SUBSTITUTION_VIEW
        }

    private val dcCodeSubstitutionViewField = substitutionView.field(CALCULATION_SUBSTITUTION_VIEW.DC_CODE)!!
    private val weekSubstitutionViewField = substitutionView.field(CALCULATION_SUBSTITUTION_VIEW.PRODUCTION_WEEK)!!
    private val skuIdSubstitutionViewField = substitutionView.field(CALCULATION_SUBSTITUTION_VIEW.CSKU_ID)!!
    private val subbedSubstitutionViewField = substitutionView.field(CALCULATION_SUBSTITUTION_VIEW.DEMAND_SUBBED)!!

    fun withAdditionalFilters(additionalFilters: Set<AdditionalFilter>, calculationsTable: Table<*>): List<Condition> {
        if (additionalFilters.isEmpty()) {
            return emptyList()
        }

        val subQueryTable = calculationsTable.`as`("addition_filter_query")

        val dailyNeedsField = CALCULATION.DAILY_NEEDS.inTable(subQueryTable)
        val actualInboundField = CALCULATION.ACTUAL_INBOUND.inTable(subQueryTable)
        val expectedInboundField = CALCULATION.EXPECTED_INBOUND.inTable(subQueryTable)
        val dateField = CALCULATION.DATE.inTable(subQueryTable)
        val demandedField = CALCULATION.DEMANDED.inTable(subQueryTable)
        val expiredField = CALCULATION.EXPIRED.inTable(subQueryTable)
        val openStockField = CALCULATION.OPENING_STOCK.inTable(subQueryTable)
        val netNeedsField = CALCULATION.NET_NEEDS.inTable(subQueryTable)

        val conditionsForSubquery = additionalFilters.map { additionalFilter ->
            when (additionalFilter) {
                WITH_INVENTORY_SHORTAGE -> DSL.boolOr(dailyNeedsField.gt(BigDecimal.ZERO)).eq(true)
                WITH_CONSUMPTION_ONLY -> DSL.boolOr(demandedField.gt(BigDecimal.ZERO)).eq(true)
                WITH_PROJECTED_WASTE -> DSL.boolOr(expiredField.gt(BigDecimal.ZERO)).eq(true)
                WITH_INBOUND_SHORTAGE -> DSL.boolOr(
                    actualInboundField.lt(expectedInboundField)
                        .and(dateField.lessThan(LocalDate.now(ZoneOffset.UTC))),
                ).eq(true)

                WITH_NO_CONSUMPTION_ONLY -> count(
                    demandedField.equal(BigDecimal.ZERO).and(
                        openStockField.greaterThan(BigDecimal.ZERO),
                    ),
                ).greaterOrEqual(DSL.`val`(WEEK_WITH_NO_CONSUMPTION_ONLY))

                WITH_SKU_SUBBED_IN_ONLY -> DSL.boolOr(
                    subbedSubstitutionViewField.eq(SUB_IN).or(subbedSubstitutionViewField.eq(SUB_IN_AND_OUT)),
                ).eq(true)

                WITH_SKU_SUBBED_OUT_ONLY -> DSL.boolOr(
                    subbedSubstitutionViewField.eq(SUB_OUT).or(subbedSubstitutionViewField.eq(SUB_IN_AND_OUT)),
                ).eq(true)

                WITH_NET_NEEDS_ONLY -> DSL.boolOr(
                    netNeedsField.isNotNull.and(netNeedsField.gt(BigDecimal.ZERO))
                ).eq(true)
            }
        }

        val subQuery = select(CALCULATION.CSKU_ID.inTable(subQueryTable))
            .from(subQueryTable)
            .apply {
                filterSubstitutes(additionalFilters, subQueryTable)
            }
            .where(
                condition(
                    CALCULATION.CSKU_ID.inTable(subQueryTable),
                    CALCULATION.DC_CODE.inTable(subQueryTable),
                    CALCULATION.PRODUCTION_WEEK.inTable(subQueryTable),
                ),
            )
            .groupBy(CALCULATION.CSKU_ID.inTable(subQueryTable))
            .having(conditionsForSubquery)

        return listOf(field.`in`(subQuery))
    }

    private fun SelectJoinStep<*>.filterSubstitutes(filters: Collection<AdditionalFilter>, subQueryTable: Table<*>) {
        if (filters.contains(WITH_SKU_SUBBED_IN_ONLY) || filters.contains(WITH_SKU_SUBBED_OUT_ONLY)) {
            leftJoin(substitutionView)
                .on(
                    CALCULATION.CSKU_ID.inTable(subQueryTable).eq(skuIdSubstitutionViewField),
                    CALCULATION.DC_CODE.inTable(subQueryTable).eq(dcCodeSubstitutionViewField),
                    CALCULATION.PRODUCTION_WEEK.inTable(
                        subQueryTable,
                    ).eq(weekSubstitutionViewField),
                )
        }
    }
}
