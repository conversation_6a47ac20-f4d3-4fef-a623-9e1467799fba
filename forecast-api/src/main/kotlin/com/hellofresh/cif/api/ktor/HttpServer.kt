package com.hellofresh.cif.api.ktor

import com.hellofresh.cif.checks.CheckResult
import com.hellofresh.cif.checks.HealthChecks
import com.hellofresh.cif.checks.StartUpChecks
import com.hellofresh.cif.shutdown.shutdownNeeded
import io.ktor.server.application.Application
import io.ktor.server.application.ServerReady
import io.ktor.server.application.install
import io.ktor.server.engine.EngineConnectorBuilder
import io.ktor.server.engine.embeddedServer
import io.ktor.server.netty.Netty
import io.ktor.server.plugins.callid.CallId
import io.ktor.server.plugins.callid.callIdMdc
import io.ktor.server.plugins.callid.generate
import io.ktor.server.plugins.calllogging.CallLogging
import io.ktor.server.plugins.compression.Compression
import io.ktor.server.plugins.compression.gzip
import io.micrometer.core.instrument.MeterRegistry
import java.util.concurrent.atomic.AtomicBoolean
import org.apache.logging.log4j.kotlin.Logging

private const val MAX_BYTES = 32 * 1024 // 32KB, FE sends more than 15 KB request.
private const val MAX_INITIAL_LENGTH = MAX_BYTES
private const val MAX_HEADER_SIZE = MAX_BYTES
private const val MAX_CHUNK_SIZE = MAX_BYTES
private const val CALL_ID_LENGTH = 12

// a custom dictionary that skips the +/=- characters for better grepping
private const val CALL_ID_DICTIONARY = "abcdefghijklmnopqrstuvwxyz0123456789"

object HttpServer : Logging {

    private val isUp = AtomicBoolean(false)

    fun start(
        port: Int,
        meterRegistry: MeterRegistry,
        routes: List<Application.() -> Unit>,
        serverReadyListeners: List<ServerReadyListener> = emptyList()
    ) =
        embeddedServer(
            factory = Netty,
            configure = {
                connectors.add(
                    EngineConnectorBuilder().apply {
                        this.port = port
                    },
                )
                maxHeaderSize = MAX_HEADER_SIZE
                maxInitialLineLength = MAX_INITIAL_LENGTH
                maxChunkSize = MAX_CHUNK_SIZE
            },
            module = {
                install(Compression) {
                    gzip()
                }
                install(CallId) {
                    generate(length = CALL_ID_LENGTH, dictionary = CALL_ID_DICTIONARY)
                }
                install(CallLogging) {
                    callIdMdc("request-id")
                }
                installMetrics(meterRegistry)
                routes.forEach { it() }
            },
        ).also {
            shutdownNeeded {
                AutoCloseable {
                    it.stop(
                        gracePeriodMillis = 15_000,
                        timeoutMillis = 15_000,
                    )
                    isUp.set(false)
                }
            }
            it.monitor.subscribe(ServerReady) {
                isUp.set(true)
                serverReadyListeners.forEach {
                    it.notifyServerReady()
                }
                logger.info("Main HTTP Server ready")
            }

            StartUpChecks.add(::check)
            HealthChecks.add(::check)
        }.start(false)

    private fun check() = CheckResult("HttpServer", isUp.get())
}

fun interface ServerReadyListener {
    fun notifyServerReady()
}
