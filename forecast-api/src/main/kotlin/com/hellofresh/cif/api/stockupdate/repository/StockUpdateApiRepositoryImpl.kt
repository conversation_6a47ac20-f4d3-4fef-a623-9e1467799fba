package com.hellofresh.cif.api.stockupdate.repository

import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.StockUpdate.STOCK_UPDATE
import com.hellofresh.cif.api.schema.tables.records.StockUpdateRecord
import com.hellofresh.cif.api.sku.SkuQuantityMapper.mapToDbUom
import com.hellofresh.cif.api.sku.SkuQuantityMapper.mapToSkuUom
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.inventory.lib.schema.Tables
import com.hellofresh.cif.models.SkuQuantity
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlinx.coroutines.future.await
import org.jooq.InsertResultStep
import org.jooq.Record
import org.jooq.Record15

const val DEFAULT_VERSION = 1

class StockUpdateApiRepositoryImpl(
    private val dcConfigService: DcConfigService,
    private val readDslContext: MetricsDSLContext,
    private val readWriteDslContext: MetricsDSLContext,
) : StockUpdateApiRepository {

    override suspend fun getAllStockUpdates(dcCode: String, week: String): List<StockUpdateView> =
        readDslContext.withTagName(getAllStockUpdatesTag)
            .select(
                STOCK_UPDATE.DC_CODE,
                STOCK_UPDATE.DATE,
                STOCK_UPDATE.SKU_ID,
                STOCK_UPDATE.VERSION,
                STOCK_UPDATE.QUANTITY,
                STOCK_UPDATE.UOM,
                STOCK_UPDATE.REASON,
                STOCK_UPDATE.REASON_DETAIL,
                STOCK_UPDATE.DELETED,
                STOCK_UPDATE.AUTHOR_NAME,
                STOCK_UPDATE.AUTHOR_EMAIL,
                STOCK_UPDATE.CREATED_AT,
                SKU_SPECIFICATION_VIEW.CODE,
                SKU_SPECIFICATION_VIEW.NAME,
                SKU_SPECIFICATION_VIEW.CATEGORY,
            )
            .from(STOCK_UPDATE)
            .join(SKU_SPECIFICATION_VIEW)
            .on(SKU_SPECIFICATION_VIEW.ID.eq(STOCK_UPDATE.SKU_ID))
            .where(
                STOCK_UPDATE.DC_CODE.eq(dcCode),
                STOCK_UPDATE.WEEK.eq(week),
            )
            .fetchAsync()
            .thenApply { records ->
                records.groupBy {
                    StockUpdateViewKey(
                        it[STOCK_UPDATE.DC_CODE],
                        it[STOCK_UPDATE.DATE],
                        it[STOCK_UPDATE.SKU_ID],
                    )
                }
                    .map { (key, versions) ->
                        toStockUpdateView(key, versions)
                    }
            }.await()

    private fun toStockUpdateView(
        key: StockUpdateViewKey,
        versions:
        List<Record15<String, LocalDate, UUID, Int, BigDecimal, Uom, String, String, Boolean, String, String, LocalDateTime, String, String, String>>
    ) =
        StockUpdateView(
            dcCode = key.dcCode,
            date = key.date,
            skuId = key.skuId,
            skuCode = versions.first().get(SKU_SPECIFICATION_VIEW.CODE),
            skuName = versions.first().get(SKU_SPECIFICATION_VIEW.NAME),
            skuCategory = versions.first().get(SKU_SPECIFICATION_VIEW.CATEGORY),
            versions = versions.map { versionRecord ->
                StockUpdateVersion(
                    version = versionRecord[STOCK_UPDATE.VERSION],

                    quantity = SkuQuantity.fromBigDecimal(
                        versionRecord[STOCK_UPDATE.QUANTITY],
                        mapToSkuUom(versionRecord[STOCK_UPDATE.UOM]),
                    ),
                    reason = versionRecord[STOCK_UPDATE.REASON],
                    reasonDetail = versionRecord[STOCK_UPDATE.REASON_DETAIL],
                    deleted = versionRecord[STOCK_UPDATE.DELETED],
                    authorName = versionRecord[STOCK_UPDATE.AUTHOR_NAME],
                    authorEmail = versionRecord[STOCK_UPDATE.AUTHOR_EMAIL],
                    createdAt = versionRecord[STOCK_UPDATE.CREATED_AT]
                        .atZone(dcConfigService.dcConfigurations[key.dcCode]?.zoneId ?: ZoneOffset.UTC)
                        .toOffsetDateTime(),
                )
            },
        )

    override suspend fun upsertStockUpdate(stockUpdates: List<StockUpdateDto>): List<StockUpdate> =
        insertStockUpdateQuery(readWriteDslContext, stockUpdates)
            .fetchAsync()
            .thenApply { results ->
                results.map { toStockUpdate(it) }
            }
            .await()

    companion object {

        private const val createStockUpdateTag = "create-stock-update"
        private const val editStockUpdateTag = "edit-stock-update"
        private const val getAllStockUpdatesTag = "get-all-stock-update"

        fun upsertStockUpdate(dslContext: MetricsDSLContext, stockUpdates: List<StockUpdateDto>): List<StockUpdate> =
            insertStockUpdateQuery(dslContext, stockUpdates)
                .fetch()
                .map { toStockUpdate(it) }

        private fun insertStockUpdateQuery(dslContext: MetricsDSLContext, stockUpdates: List<StockUpdateDto>): InsertResultStep<StockUpdateRecord> {
            val stockUpdateRecords = mapToStockUpdateRecord(stockUpdates)

            return dslContext.withTagName(
                if (stockUpdates.any { it.version == null }) createStockUpdateTag else editStockUpdateTag,
            ).insertInto(STOCK_UPDATE)
                .set(stockUpdateRecords)
                .returning()
        }

        private fun mapToStockUpdateRecord(stockUpdates: List<StockUpdateDto>): List<StockUpdateRecord> =
            stockUpdates.map { stockUpdate ->
                StockUpdateRecord().apply {
                    skuId = stockUpdate.skuId
                    version = stockUpdate.version?.plus(1) ?: DEFAULT_VERSION
                    dcCode = stockUpdate.dcCode
                    week = stockUpdate.week
                    date = stockUpdate.date
                    quantity = stockUpdate.quantity.getValue()
                    uom = mapToDbUom(stockUpdate.quantity.unitOfMeasure)
                    reason = stockUpdate.reason.value
                    reasonDetail = stockUpdate.reasonDetail
                    authorName = stockUpdate.authorName
                    authorEmail = stockUpdate.authorEmail
                    deleted = stockUpdate.deleted
                }
            }

        fun toStockUpdate(record: Record) =
            StockUpdate(
                skuId = record[STOCK_UPDATE.SKU_ID],
                dcCode = record[STOCK_UPDATE.DC_CODE],
                date = record[STOCK_UPDATE.DATE],
                week = record[STOCK_UPDATE.WEEK],
                quantity = SkuQuantity.fromBigDecimal(
                    record[STOCK_UPDATE.QUANTITY],
                    mapToSkuUom(record.get(STOCK_UPDATE.UOM)),
                ),
                reason = record[STOCK_UPDATE.REASON],
                reasonDetail = record[STOCK_UPDATE.REASON_DETAIL],
                authorName = record[STOCK_UPDATE.AUTHOR_NAME],
                authorEmail = record[STOCK_UPDATE.AUTHOR_EMAIL],
                version = record[STOCK_UPDATE.VERSION],
                createdAt = record[STOCK_UPDATE.CREATED_AT],
                deleted = record[Tables.STOCK_UPDATE.DELETED],
            )
    }

    private data class StockUpdateViewKey(
        val dcCode: String,
        val date: LocalDate,
        val skuId: UUID,
    )
}
