package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.InventoryRefreshType.CLEARDOWN
import com.hellofresh.cif.api.calculation.SortBy.SKU_NAME
import com.hellofresh.cif.api.getOrDefault
import io.ktor.http.Parameters
import java.util.UUID

data class CalculationRequest(
    override val dcCodes: List<String>,
    override val weeks: List<String>,
    val pageRequest: PageRequest,
    override val skuCodes: List<String> = emptyList(),
    override val skuCategories: List<String> = emptyList(),
    override val additionalFilters: Set<AdditionalFilter> = emptySet(),
    override val consumptionDaysAhead: Int,
    override val inventoryRefreshType: InventoryRefreshType = CLEARDOWN,
    val sortBy: SortBy = SKU_NAME,
    override val locationInBox: List<String> = emptyList(),
    override val supplierIds: List<UUID> = emptyList(),
    override val activeSupplierNames: Set<String> = emptySet(),
    override val poDueInMin: Int? = null,
    override val poDueInMax: Int? = null,
    override val closingStockLessThanOrEqual: Long? = null,
) : CalculationFiltersParams {

    companion object {

        fun from(parameters: Parameters) = with(CalculationFilterRequest.from(parameters)) {
            CalculationRequest(
                dcCodes = dcCodes,
                weeks = weeks,
                pageRequest = Page(
                    page = parameters.getOrDefault("page", "1").toInt(),
                    skuCount = parameters.getOrDefault("skuCount", "50").toInt(),
                ),
                skuCodes = skuCodes,
                skuCategories = skuCategories,
                additionalFilters = additionalFilters,
                consumptionDaysAhead = consumptionDaysAhead,
                inventoryRefreshType = inventoryRefreshType,
                sortBy = SortBy(parameters.getOrDefault("sortBy", "skuName")),
                locationInBox = locationInBox,
                supplierIds = supplierIds,
                activeSupplierNames = activeSupplierNames,
                poDueInMax = poDueInMax,
                poDueInMin = poDueInMin,
                closingStockLessThanOrEqual = closingStockLessThanOrEqual,
            )
        }
    }
}

enum class SortBy(val paramValue: String) {
    SKU_NAME("skuName"), SKU_CODE("skuCode");

    companion object {
        operator fun invoke(s: String?) = when (s?.trim()?.uppercase()) {
            "", null, "SKUNAME" -> SKU_NAME // default
            "SKUCODE" -> SKU_CODE
            else -> error("Unknown sorting order: $s")
        }
    }
}

sealed interface PageRequest {
    val number: Int
}

data class Page(val page: Int, val skuCount: Int) : PageRequest {
    override val number = page
    val offset = (page - 1) * skuCount
}

object AllPages : PageRequest {
    override val number = 1
}
