package com.hellofresh.cif.api.stockupdate.model

import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

data class StockUpdateSimulationData(
    val dcCode: String,
    val skuId: UUID,
    val calculatorMode: CalculatorMode,
    val weeks: Set<String>,
    val stockUpdates: Map<LocalDate, BigDecimal>
) {
    fun stockUpdateSimulations() = stockUpdates.mapKeys { CalculationKey(skuId, dcCode, it.key) }
}
