package com.hellofresh.cif.api.stockupdate

import com.hellofresh.cif.api.calculation.generated.model.Reason
import com.hellofresh.cif.api.calculation.generated.model.SkuStockUpdatesResponse
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateItem
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateRequest
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateV2Item
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateVersionItem
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateWithVersions
import com.hellofresh.cif.api.calculation.generated.model.StockUpdatesVersionsResponse
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateDto
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateView
import com.hellofresh.cif.api.user.LoggedInUserInfo
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.util.UUID

object StockUpdateMapper {

    fun buildStockUpdatesResponse(calculations: StockUpdateComparison, stockUpdates: Map<LocalDate, StockUpdate?>) =
        SkuStockUpdatesResponse(
            stockUpdates = buildStockUpdates(calculations, stockUpdates),
            reason = Reason.entries.find {
                it.value == stockUpdates.values.firstNotNullOfOrNull { stockUpdate -> stockUpdate?.reason }
            },
            reasonDetails = stockUpdates.values.firstNotNullOfOrNull { stockUpdate -> stockUpdate?.reasonDetail },
        )

    fun buildStockUpdates(
        calculations: StockUpdateComparison,
        stockUpdates: Map<LocalDate, StockUpdate?>
    ): List<StockUpdateItem> {
        val closingStockByDate = calculations.defaultCalculation.associate { it.date to it.closingStock }
        val stockUpdateClosingStockByDate = calculations.stockUpdateCalculations.associate { it.date to it.closingStock }
        val calculatedStockUpdates = calculations.stockUpdates.map { it.key.date to it.value }.associateBy(
            { it.first },
        ) { it.second }
        return stockUpdates.map { (date, stockUpdate) ->
            StockUpdateItem(
                date = date,
                previousClosingStock = closingStockByDate[date]?.getValue()?.toLong() ?: 0,
                stockUpdateQuantity = calculatedStockUpdates[date]?.getValue()?.toLong() ?: stockUpdate?.quantity?.getValue()?.toLong(),
                updatedClosingStock = stockUpdateClosingStockByDate[date]?.getValue()?.toLong() ?: 0,
                version = stockUpdate?.version,
                deleted = false,
            )
        }
    }

    fun buildStockUpdates(
        existingStockUpdates: Map<LocalDate, StockUpdate?>,
        stockUpdateResults: StockUpdateResults,
    ): List<StockUpdateV2Item> {
        val calculatedStockUpdates = stockUpdateResults.stockUpdates.entries.associate { it.key.date to it.value }
        return existingStockUpdates.map { (date, stockUpdate) ->
            StockUpdateV2Item(
                date = date,
                quantity = calculatedStockUpdates[date]?.getValue() ?: stockUpdate?.quantity?.getValue(),
                version = stockUpdate?.version,
            )
        }
    }

    @SuppressWarnings("LongParameterList")
    fun buildStockUpdateDto(
        stockUpdateRequest: StockUpdateRequest,
        skuId: UUID,
        dcCode: String,
        skuUOM: SkuUOM,
        userInfo: LoggedInUserInfo,
        dcConfig: DistributionCenterConfiguration,
    ): List<StockUpdateDto> =
        stockUpdateRequest.stockUpdates.map { stockUpdate ->
            StockUpdateDto(
                skuId = skuId,
                dcCode = dcCode,
                date = stockUpdate.date,
                quantity = SkuQuantity.fromBigDecimal(stockUpdate.stockUpdateQuantity.toBigDecimal(), skuUOM),
                reason = stockUpdateRequest.reason,
                week = DcWeek(
                    stockUpdate.date,
                    dcConfig.productionStart,
                ).toString(),
                reasonDetail = stockUpdateRequest.reasonDetails,
                authorName = userInfo.userName,
                authorEmail = userInfo.userEmail,
                version = stockUpdate.version,
                deleted = stockUpdate.stockUpdateQuantity.toBigDecimal() == ZERO,
            )
        }

    fun mapToAllStockUpdatesResponse(
        stockUpdates: List<StockUpdateView>
    ) =
        StockUpdatesVersionsResponse(
            stockUpdates = stockUpdates
                .map {
                    StockUpdateWithVersions(
                        it.dcCode,
                        it.date,
                        it.skuId,
                        it.skuCode,
                        it.skuName,
                        it.skuCategory,
                        it.versions.map { v ->
                            StockUpdateVersionItem(
                                v.version,
                                v.quantity.getValue().toLong(),
                                v.reason,
                                v.deleted,
                                v.authorName ?: v.authorEmail,
                                v.createdAt,
                                v.reasonDetail,
                            )
                        },
                    )
                },
        )
}
