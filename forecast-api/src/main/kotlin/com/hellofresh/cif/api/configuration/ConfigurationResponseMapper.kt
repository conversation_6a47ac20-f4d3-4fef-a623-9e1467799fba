package com.hellofresh.cif.api.configuration

import com.hellofresh.cif.api.calculation.generated.model.CountryDcConfigurationResponse
import com.hellofresh.cif.api.calculation.generated.model.DayOfWeekResponse

fun toConfigurationResponse(dcConfiguration: DcConfiguration) = CountryDcConfigurationResponse(
    market = dcConfiguration.market,
    minWeek = dcConfiguration.minWeek,
    currentWeek = dcConfiguration.currentWeek,
    startDate = dcConfiguration.startDate,
    productionStartDay = DayOfWeekResponse.valueOf(dcConfiguration.productionStartDay.name),
    clearDownDay = DayOfWeekResponse.valueOf(dcConfiguration.clearDownDay.name),
    lastCleardown = dcConfiguration.lastCleardown,
    lastCleardownTime = dcConfiguration.lastCleardownTime,
)
