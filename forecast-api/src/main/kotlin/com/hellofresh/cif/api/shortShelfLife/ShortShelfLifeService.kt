@file:Suppress("TooManyFunctions")

package com.hellofresh.cif.api.shortShelfLife

import com.hellofresh.cif.api.calculation.generated.model.ShortShelfLifeResponse
import com.hellofresh.cif.api.calculation.generated.model.ShortShelfLifeSimulationResponse
import com.hellofresh.cif.api.calculation.generated.model.ShortShelfLifeUpdateRequest
import com.hellofresh.cif.api.calculation.generated.model.ShortShelfLifeUpdateRequestSqrsInner
import com.hellofresh.cif.api.calculation.generated.model.Sqr
import com.hellofresh.cif.api.calculation.generated.model.SslSqr
import com.hellofresh.cif.api.calculation.generated.model.SslSqrDataInner
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateRequest
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateRequestStockUpdatesInner
import com.hellofresh.cif.api.calculation.generated.model.UomEnum.UNIT
import com.hellofresh.cif.api.shortShelfLife.ShortShelLifeUpdateType.ALL
import com.hellofresh.cif.api.shortShelfLife.ShortShelLifeUpdateType.BUFFER
import com.hellofresh.cif.api.shortShelfLife.ShortShelLifeUpdateType.STOCK_UPDATES
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLife
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeConfig
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeConfigKey
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeConfigWithStockUpdate
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeSimulation
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeSimulationDetail
import com.hellofresh.cif.api.shortShelfLife.model.ShortShelfLifeSimulationWeek
import com.hellofresh.cif.api.shortShelfLife.repository.ShortShelfLifeRepositoryImpl
import com.hellofresh.cif.api.sku.SkuQuantityMapper
import com.hellofresh.cif.api.stockupdate.StockUpdateApiService
import com.hellofresh.cif.api.stockupdate.StockUpdateCalculationService
import com.hellofresh.cif.api.stockupdate.StockUpdateMapper
import com.hellofresh.cif.api.user.LoggedInUserInfo
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.DcCode
import com.hellofresh.cif.calculator.models.SkuDcCandidate
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeCalc
import com.hellofresh.cif.sqr.shortshelflife.SQRShortShelfLifeConfigurations
import com.hellofresh.cif.sqr.shortshelflife.repository.SQRShortShelfLifeConfRepository
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import java.util.stream.Collectors
import org.apache.logging.log4j.kotlin.Logging
import org.jetbrains.annotations.VisibleForTesting

private enum class ShortShelLifeUpdateType {
    ALL,
    BUFFER,
    STOCK_UPDATES,
}

class ShortShelfLifeService(
    private val shortShelfLifeRepositoryImpl: ShortShelfLifeRepositoryImpl,
    private val dcConfigService: DcConfigService,
    private val stockUpdateApiService: StockUpdateApiService,
    private val stockUpdateCalculationService: StockUpdateCalculationService,
    private val sqrShortShelfLifeConfRepository: SQRShortShelfLifeConfRepository,
    private val stockUpdateService: StockUpdateService,
) {
    /**
     * Function helps to prepare the SSL records for the given inputs,
     * Loads the calculation sqr data , override with the sqr configuration data
     * finally override with the simulated results and return the response.
     */
    suspend fun getShortShelfLife(
        dcCode: String,
        weeks: Set<DcWeek>
    ): ShortShelfLifeResponse {
        val dcConfig = dcConfigService.dcConfigurations[dcCode]
            ?: throw IllegalArgumentException("DC config not found for $dcCode")

        val dates = weeks.flatMap {
            generateDatesBetween(
                it.getStartDateInDcWeek(dcConfig.productionStart, dcConfig.zoneId),
                it.getLastDateInDcWeek(dcConfig.productionStart, dcConfig.zoneId),
            )
        }
        val shortShelfLifeSqrs = shortShelfLifeRepositoryImpl.getShortShelfLife(dcCode, dates)
            .map { item ->
                val week = DcWeek(item.date, dcConfig.productionStart)
                item.copy(dcWeek = week.toString())
            }

        val sourceShortShelfLifeConfigs: SQRShortShelfLifeConfigurations =
            sqrShortShelfLifeConfRepository.fetchSQRShortShelfLifeConfigurationFromWeek(
                weeks.minOf { it.value },
                dcConfig,
            )
        val stockUpdates: List<StockUpdate> = stockUpdateService.getStockUpdates(
            dcCode,
            weeks.map { it.value }.toSet(),
            shortShelfLifeSqrs.map { it.skuId }.toSet(),
        )

        val sourceShortShelfLifeMap =
            prepareShortShelfLifeMap(shortShelfLifeSqrs, sourceShortShelfLifeConfigs, stockUpdates)

        val (matchingShortShelfLife, nonMatchingShortShelfLife) = getShortShelfLifeConfigsMatching(
            sourceShortShelfLifeMap,
            shortShelfLifeSqrs,
        )

        val matchedSkus = prepareMatchedShortShelfLife(matchingShortShelfLife, sourceShortShelfLifeMap)
        val nonMatchedSkus = if (nonMatchingShortShelfLife.isNotEmpty()) {
            simulateShortShelfLife(nonMatchingShortShelfLife, sourceShortShelfLifeMap).skus
        } else {
            emptyList()
        }
        val allSkus = matchedSkus + nonMatchedSkus

        return allSkus.takeIf { it.isNotEmpty() }
            ?.let {
                ShortShelfLifeResponse(
                    skus = it,
                    totalNoOfSkus = it.size.toBigDecimal(),
                )
            } ?: ShortShelfLifeResponse(
            skus = emptyList(),
            totalNoOfSkus = BigDecimal.ZERO,
        )
    }

    suspend fun updateShortShelfLife(
        dcCode: DcCode,
        skuId: UUID,
        shortShelfLifeUpdateRequest: ShortShelfLifeUpdateRequest,
        user: LoggedInUserInfo
    ): SslSqr {
        require(shortShelfLifeUpdateRequest.sqrs.isNotEmpty()) {
            "No Short Shelf Life Config data provided."
        }

        val dcConfig = dcConfigService.dcConfigurations[dcCode]
            ?: throw IllegalArgumentException("DC config not found for $dcCode")

        val (sslConfigList, stockUpdateList) = getSSLConfigList(shortShelfLifeUpdateRequest, skuId, dcCode)

        // Insert stock updates
        val stockUpdatesList = shortShelfLifeUpdateRequest.stockUpdate?.let {
            val stockUpdateRequest = StockUpdateRequest(
                reason = shortShelfLifeUpdateRequest.stockUpdate.reason,
                reasonDetails = shortShelfLifeUpdateRequest.stockUpdate.reasonDetails,
                stockUpdates = stockUpdateList.filterNotNull(),
            )

            val skuUom = stockUpdateApiService.getSkuUom(skuId)
            val stockUpdatesDtoList = StockUpdateMapper
                .buildStockUpdateDto(
                    stockUpdateRequest = stockUpdateRequest,
                    skuId = skuId,
                    dcCode = dcCode,
                    skuUOM = skuUom,
                    userInfo = user,
                    dcConfig = dcConfig,
                )

            stockUpdateApiService.validateStockUpdateDateRange(
                dcCode,
                stockUpdatesDtoList.map { it.date }.toSet(),
            )

            stockUpdatesDtoList
        } ?: emptyList()

        val sslWeekConfig = sslConfigList + getMissingWeekDatesConfig(sslConfigList, dcConfig, skuId)

        // update short shelf life config and stock updates
        val persistedStockUpdates = shortShelfLifeRepositoryImpl.upsertShortShelfLifeConfig(
            sslWeekConfig.filterNotNull(),
            stockUpdatesList,
        )

        val simulationSQRList = prepareShortShelfLifeSQRList(
            shortShelfLifeUpdateRequest.sqrs,
            stockUpdateList.filterNotNull(),
            dcConfig,
            shortShelfLifeUpdateRequest.touchlessOrdering,
        )
        val simulationDataList = prepareSimulationData(dcCode, skuId, simulationSQRList, persistedStockUpdates)

        // perform simulation
        return simulateShortShelfLife(
            dcCode,
            skuId,
            simulationDataList,
        )
            .shortShelfLifeSimulationResponse.sslSqr
    }

    @Suppress("UnusedPrivateMember")
    private suspend fun getMissingWeekDatesConfig(
        sslConfigList: List<ShortShelfLifeConfig?>,
        dcConfig: DistributionCenterConfiguration,
        skuId: UUID,
    ): List<ShortShelfLifeConfig> {
        val incomingDates = sslConfigList.mapNotNull { it?.date }
        val firstDate = incomingDates.firstOrNull() ?: return emptyList()
        val weekDatesRange = dcConfig.getProductionDateRange(firstDate)
        val existingDates = incomingDates.toSet()

        val missingConfigs =
            sqrShortShelfLifeConfRepository.fetchSQRShortShelfLifeConfigurationFromDateRange(
                skuId,
                dcConfig.dcCode,
                weekDatesRange,
            ).filterNot { it.date in existingDates }

        return if (missingConfigs.isEmpty()) {
            emptyList()
        } else {
            val touchlessOrderingFlag = sslConfigList.firstOrNull()?.touchlessOrderingEnabled ?: false

            missingConfigs.map {
                ShortShelfLifeConfig(
                    skuId = it.skuId,
                    dcCode = it.dcCode,
                    date = it.date,
                    bufferAdditional = it.bufferAdditional,
                    bufferPercentage = it.bufferPercentage,
                    touchlessOrderingFlag
                )
            }
        }
    }

    private fun getSSLConfigList(
        body: ShortShelfLifeUpdateRequest,
        skuId: UUID,
        dcCode: DcCode
    ) = body.sqrs
        .map { item ->
            val touchlessOrdering = body.touchlessOrdering
            when (determineUpdateType(item, touchlessOrdering)) {
                ALL -> Pair(
                    insertBuffer(skuId, dcCode, item, touchlessOrdering ?: false),
                    insertStockUpdate(item),
                )

                BUFFER -> Pair(insertBuffer(skuId, dcCode, item, touchlessOrdering ?: false), null)
                STOCK_UPDATES -> Pair(null, insertStockUpdate(item))
            }
        }
        .unzip()

    private suspend fun getStockUpdatesForCalculation(dcCode: String, skuId: UUID): Map<LocalDate, StockUpdate?> =
        stockUpdateService.getCurrentStockUpdate(dcCode, skuId)

    suspend fun simulateShortShelfLife(
        dcCode: String,
        skuId: UUID,
        sslSimulationInputs: List<ShortShelfLifeSimulationDetail>
    ): ShortShelfLifeSimulationResult {
        val sslSimulationInputsByWeek = sslSimulationInputs.associateBy { it.week }
        val calculationKeyToSimulationInputs = sslSimulationInputs.flatMap { input ->
            input.sqrs.map { sqr ->
                CalculationKey(skuId, dcCode, sqr.date) to sqr
            }
        }.toMap()

        val stockUpdatesForCalculation = getStockUpdatesForCalculation(
            dcCode,
            skuId,
        ).mapKeys { (date, _) ->
            CalculationKey(skuId, dcCode, date)
        }

        val calculationWithStockUpdateResults = stockUpdateCalculationService.runStockUpdatesWithoutUom(
            dcCode = dcCode,
            skuId = skuId,
            weeks = sslSimulationInputsByWeek.keys,
            calculatorMode = CalculatorMode.PRODUCTION,
            stockUpdates = stockUpdatesForCalculation.mapValues { (key, stockUpdate) ->
                calculationKeyToSimulationInputs[key]?.stockUpdates ?: stockUpdate?.quantity?.getValue()
            }.mapNotNull { (key, stockUpdateValue) ->
                stockUpdateValue?.let { key to it }
            }.toMap(),
        )

        val calculationsByWeek = calculationWithStockUpdateResults.calculations.groupBy { it.productionWeek }

        val sslSqrData: List<SslSqrDataInner> = prepareShortShelfLifeData(
            calculationsByWeek,
            skuId,
            dcCode,
            stockUpdatesForCalculation,
            sslSimulationInputsByWeek,
        )

        val skuSpecificationMap: Map<UUID, SkuDcCandidate> =
            calculationWithStockUpdateResults.skuCandidates.associateBy {
                it.skuId
            }

        return ShortShelfLifeSimulationResult(
            shortShelfLifeSimulationResponse = ShortShelfLifeSimulationResponse(
                sslSqr = SslSqr(
                    dcCode = dcCode,
                    skuId = skuId,
                    skuCode = skuSpecificationMap[skuId]?.skuSpecification?.skuCode ?: "",
                    skuName = skuSpecificationMap[skuId]?.skuSpecification?.name ?: "",
                    skuCategory = skuSpecificationMap[skuId]?.skuSpecification?.category ?: "",
                    data = sslSqrData,
                ),
            ),
            skuDcCandidateMap = skuSpecificationMap,
        )
    }

    private fun prepareMatchedShortShelfLife(
        matchingShortShelfLife: List<ShortShelfLife>,
        sourceShortShelfLifeMap: Map<ShortShelfLifeConfigKey, ShortShelfLifeConfigWithStockUpdate>
    ) = matchingShortShelfLife
        .groupBy { it.skuId }
        .map { (skuId, skuData) ->
            val sku = skuData.first()
            SslSqr(
                skuId = skuId,
                skuName = sku.skuName,
                skuCode = sku.skuCode,
                skuCategory = sku.skuCategory,
                dcCode = sku.dcCode,
                data = skuData
                    .groupBy { it.dcWeek }
                    .map { (dcWeek, itemsByWeek) ->
                        val sourceTouchlessOrdering = itemsByWeek
                            .all { item ->
                                sourceShortShelfLifeMap[ShortShelfLifeConfigKey(sku.dcCode, skuId, item.date)]
                                    ?.touchlessOrderingEnabled ?: false
                            }
                        SslSqrDataInner(
                            week = dcWeek,
                            touchlessOrdering = sourceTouchlessOrdering,
                            sqrs = itemsByWeek.map { item ->
                                val shortShelfLifeConfigWithStockUpdate = sourceShortShelfLifeMap[
                                    ShortShelfLifeConfigKey(
                                        dcCode = sku.dcCode,
                                        skuId = skuId,
                                        date = item.date,
                                    ),
                                ]
                                Sqr(
                                    date = item.date,
                                    openingStock = item.openingStock,
                                    unusableStock = item.unusableStock,
                                    consumption = item.consumption,
                                    bufferPercentage = shortShelfLifeConfigWithStockUpdate?.bufferPercentage
                                        ?: BigDecimal.ZERO,
                                    bufferAdditional = shortShelfLifeConfigWithStockUpdate?.bufferAdditional
                                        ?: BigDecimal.ZERO,
                                    sqrQuantity = item.sqrQuantity,
                                    uom = item.uom,
                                    stockUpdates = shortShelfLifeConfigWithStockUpdate?.stockUpdates?.getValue(),
                                    stockUpdatesVersion = shortShelfLifeConfigWithStockUpdate?.version,
                                )
                            },
                        )
                    },
            )
        }

    /**
     * Function helps to simulate the non-sync sqr records.
     * Prepares the simulation input details and run the simulation.
     */
    private suspend fun simulateShortShelfLife(
        nonMatchingShortShelfLife: List<ShortShelfLife>,
        sourceShortShelfLifeMap: Map<ShortShelfLifeConfigKey, ShortShelfLifeConfigWithStockUpdate>
    ): ShortShelfLifeResponse {
        val sslByDcCodeToSkuId = nonMatchingShortShelfLife.groupBy { it.dcCode to it.skuId }
        val sslSimulations = sslByDcCodeToSkuId.map { (skuKey, shortShelfLives) ->
            val groupedByWeek = shortShelfLives.groupBy { it.dcWeek }
            val simulationDetails = groupedByWeek.map { (week, shelfLives) ->
                val touchlessOrderingEnabled = sourceShortShelfLifeMap
                    .filter { (key, value) ->
                        key.skuId == skuKey.second && key.dcCode == skuKey.first && value.week == week
                    }
                    .all { it.value.touchlessOrderingEnabled }

                val sqrs = shelfLives.map { ssl ->
                    val shortShelfLifeConfigWithStockUpdate = sourceShortShelfLifeMap[
                        ShortShelfLifeConfigKey(
                            dcCode = ssl.dcCode,
                            skuId = ssl.skuId,
                            date = ssl.date,
                        ),
                    ]
                    ShortShelfLifeSimulation(
                        date = ssl.date,
                        stockUpdates = shortShelfLifeConfigWithStockUpdate?.stockUpdates?.getValue(),
                        bufferPercentage = shortShelfLifeConfigWithStockUpdate?.bufferPercentage,
                        bufferAdditional = shortShelfLifeConfigWithStockUpdate?.bufferAdditional,
                        uom = ssl.uom,
                    )
                }
                ShortShelfLifeSimulationDetail(
                    week = week,
                    touchlessOrderingEnabled = touchlessOrderingEnabled,
                    sqrs = sqrs,
                )
            }
            simulateShortShelfLife(skuKey.first, skuKey.second, simulationDetails)
        }

        val mergedResults = mergeDbResultsWithSimulatedResults(
            sslSimulations,
            nonMatchingShortShelfLife,
            sourceShortShelfLifeMap,
        )

        return ShortShelfLifeResponse(
            skus = mergedResults,
            totalNoOfSkus = mergedResults.size.toBigDecimal(),
        )
    }

    /**
     * Function helps to merge the results from calculator simulation into
     * sqr calculation database records.
     */
    private fun mergeDbResultsWithSimulatedResults(
        simulatedResults: List<ShortShelfLifeSimulationResult>,
        nonMatchingShortShelfLife: List<ShortShelfLife>,
        sourceShortShelfLifeMap: Map<ShortShelfLifeConfigKey, ShortShelfLifeConfigWithStockUpdate>
    ): List<SslSqr> {
        val simulatedResultsByCalculationKey: Map<CalculationKey, Sqr> = getSimulatedResultsByCalculationKey(
            simulatedResults,
        )
        val map: Map<UUID, SkuDcCandidate> = getSkuCandidateMap(simulatedResults)
        val sslByDcCodeToSkuId = nonMatchingShortShelfLife.groupBy { it.dcCode to it.skuId }
        val mergedResults = sslByDcCodeToSkuId.map { (skuKey, shortShelfLives) ->
            val groupedByWeek = shortShelfLives.groupBy { it.dcWeek }

            val sslSqrDataList = groupedByWeek.map { (week, shelfLives) ->
                val isDemandExistForTheEntireWeek = shelfLives.any { it.consumption != BigDecimal.ZERO }

                val touchlessOrderingEnabled = sourceShortShelfLifeMap
                    .filter { (key, value) ->
                        key.skuId == skuKey.second && key.dcCode == skuKey.first && value.week == week
                    }
                    .all { it.value.touchlessOrderingEnabled }

                val sqrs: List<Sqr> = shelfLives.map { ssl ->
                    val key = CalculationKey(ssl.skuId, ssl.dcCode, ssl.date)
                    val shortShelfLifeConfigKey = ShortShelfLifeConfigKey(ssl.dcCode, ssl.skuId, ssl.date)

                    simulatedResultsByCalculationKey[key] ?: run {
                        val bufferPercentage = ssl.bufferPercentage
                        val bufferAdditional = ssl.bufferAdditional

                        val sqrQuantity = if (isDemandExistForTheEntireWeek) {
                            calculateSQRQuantity(ssl, bufferPercentage, bufferAdditional)
                        } else {
                            BigDecimal.ZERO
                        }

                        Sqr(
                            date = ssl.date,
                            openingStock = ssl.openingStock,
                            unusableStock = ssl.unusableStock,
                            consumption = ssl.consumption,
                            stockUpdates = ssl.stockUpdates,
                            stockUpdatesVersion = sourceShortShelfLifeMap[shortShelfLifeConfigKey]?.version,
                            bufferPercentage = bufferPercentage,
                            bufferAdditional = bufferAdditional,
                            sqrQuantity = sqrQuantity,
                            uom = ssl.uom,
                        )
                    }
                }

                SslSqrDataInner(week = week, sqrs = sqrs, touchlessOrdering = touchlessOrderingEnabled)
            }

            skuKey to sslSqrDataList
        }.groupBy { it.first } // Group by (dcCode, skuId)
            .map { (skuKey, dataList) ->
                SslSqr(
                    dcCode = skuKey.first,
                    skuId = skuKey.second,
                    skuCode = map[skuKey.second]?.skuSpecification?.skuCode ?: "",
                    skuName = map[skuKey.second]?.skuSpecification?.name ?: "",
                    skuCategory = map[skuKey.second]?.skuSpecification?.category ?: "",
                    data = dataList.flatMap { it.second },
                )
            }
        return mergedResults
    }

    private fun calculateSQRQuantity(
        ssl: ShortShelfLife,
        bufferPercentage: BigDecimal,
        bufferAdditional: BigDecimal
    ) = SQRShortShelfLifeCalc.applyFormula(
        SkuQuantity.fromBigDecimal(ssl.openingStock, SkuQuantityMapper.mapUomEnumToSkuUOM(ssl.uom)),
        SkuQuantity.fromBigDecimal(ssl.consumption, SkuQuantityMapper.mapUomEnumToSkuUOM(ssl.uom)),
        SkuQuantity.fromBigDecimal(ssl.stockUpdates ?: BigDecimal.ZERO, SkuQuantityMapper.mapUomEnumToSkuUOM(ssl.uom)),
        bufferPercentage,
        bufferAdditional,
    ).getValue()

    @VisibleForTesting
    fun getShortShelfLifeConfigsMatching(
        sourceShortShelfLife: Map<ShortShelfLifeConfigKey, ShortShelfLifeConfigWithStockUpdate>,
        calculatedShortShelfLife: List<ShortShelfLife>
    ): ShortShelfLifeDetails {
        val sslByDcSkuIds = calculatedShortShelfLife.groupBy { it.dcCode to it.skuId }
            .mapValues { (_, ssl) ->
                ssl.all { shortShelfLife ->
                    val key = ShortShelfLifeConfigKey(shortShelfLife.dcCode, shortShelfLife.skuId, shortShelfLife.date)
                    sourceShortShelfLife[key]?.let { sourceConfig ->
                        sourceConfig.bufferAdditional.compareTo(shortShelfLife.bufferAdditional) == 0 &&
                            sourceConfig.bufferPercentage.compareTo(shortShelfLife.bufferPercentage) == 0 &&
                            (sourceConfig.stockUpdates ?: SkuQuantity.ZERO)
                                .compareTo(SkuQuantity.fromBigDecimal(shortShelfLife.stockUpdates ?: BigDecimal.ZERO)) == 0
                    } ?: false
                }
            }

        val (matching, nonMatching) = calculatedShortShelfLife.partition { shortShelfLife ->
            sslByDcSkuIds[shortShelfLife.dcCode to shortShelfLife.skuId] ?: true
        }

        return ShortShelfLifeDetails(
            matchingList = matching,
            nonMatchingList = nonMatching,
        )
    }

    @VisibleForTesting
    fun prepareShortShelfLifeMap(
        shortShelfLives: List<ShortShelfLife>,
        shortShelfLifeConfigs: SQRShortShelfLifeConfigurations,
        stockUpdates: List<StockUpdate>
    ): Map<ShortShelfLifeConfigKey, ShortShelfLifeConfigWithStockUpdate> {
        val stockUpdateMap = stockUpdates.associateBy { ShortShelfLifeConfigKey(it.dcCode, it.skuId, it.date) }
        return shortShelfLives.associate { shortShelfLife ->
            val sqrShortShelfLifeConf = shortShelfLifeConfigs.getConfiguration(
                shortShelfLife.dcCode,
                shortShelfLife.skuId,
                shortShelfLife.date,
            )
            val shortShelfLifeConfigKey = ShortShelfLifeConfigKey(
                dcCode = shortShelfLife.dcCode,
                skuId = shortShelfLife.skuId,
                date = shortShelfLife.date,
            )
            val isDcCodeEmpty = sqrShortShelfLifeConf.dcCode == ""
            shortShelfLifeConfigKey to ShortShelfLifeConfigWithStockUpdate(
                week = shortShelfLife.dcWeek,
                bufferAdditional = if (isDcCodeEmpty) shortShelfLife.bufferAdditional else sqrShortShelfLifeConf.bufferAdditional,
                bufferPercentage = if (isDcCodeEmpty) shortShelfLife.bufferPercentage else sqrShortShelfLifeConf.bufferPercentage,
                touchlessOrderingEnabled = if (isDcCodeEmpty) shortShelfLife.touchlessOrderingEnabled else sqrShortShelfLifeConf.touchlessOrderingEnabled,
                stockUpdates = stockUpdateMap[shortShelfLifeConfigKey]?.quantity,
                version = stockUpdateMap[shortShelfLifeConfigKey]?.version,
            )
        }
    }

    private fun getSkuCandidateMap(simulatedResults: List<ShortShelfLifeSimulationResult>) =
        simulatedResults.flatMap { simulatedResult ->
            simulatedResult.skuDcCandidateMap.map { it.key to it.value }
        }.toMap()

    private fun getSimulatedResultsByCalculationKey(simulatedResults: List<ShortShelfLifeSimulationResult>) =
        simulatedResults.map { simulatedResult ->
            simulatedResult.shortShelfLifeSimulationResponse.sslSqr.data
                .flatMap { dataInner ->
                    dataInner.sqrs.map { sqr ->
                        CalculationKey(
                            simulatedResult.shortShelfLifeSimulationResponse.sslSqr.skuId,
                            simulatedResult.shortShelfLifeSimulationResponse.sslSqr.dcCode, sqr.date,
                        ) to sqr
                    }
                }.toMap()
        }.flatMap { it.entries }
            .associate { it.toPair() }

    private fun insertBuffer(
        skuId: UUID,
        dcCode: DcCode,
        item: ShortShelfLifeUpdateRequestSqrsInner,
        touchlessOrdering: Boolean
    ): ShortShelfLifeConfig =
        ShortShelfLifeConfig(
            skuId = skuId,
            dcCode = dcCode,
            date = item.date,
            bufferAdditional = item.bufferAdditional ?: BigDecimal.ZERO,
            bufferPercentage = item.bufferPercentage ?: BigDecimal.ZERO,
            touchlessOrderingEnabled = touchlessOrdering,
        )

    private fun insertStockUpdate(item: ShortShelfLifeUpdateRequestSqrsInner): StockUpdateRequestStockUpdatesInner =
        StockUpdateRequestStockUpdatesInner(
            date = item.date,
            stockUpdateQuantity = item.stockUpdates?.toLong() ?: 0,
            version = item.stockUpdatesVersion ?: 0,
        )

    private fun determineUpdateType(
        item: ShortShelfLifeUpdateRequestSqrsInner,
        touchlessOrdering: Boolean?
    ): ShortShelLifeUpdateType =
        when {
            item.bufferAdditional != null || item.bufferPercentage != null || touchlessOrdering != null -> {
                if (item.stockUpdates != null) ALL else BUFFER
            }

            item.stockUpdates != null -> STOCK_UPDATES
            else -> ALL
        }

    private fun generateDatesBetween(startDate: LocalDate, endDate: LocalDate): List<LocalDate> =
        if (startDate <= endDate) {
            startDate.datesUntil(endDate.plusDays(1))
                .collect(Collectors.toList())
        } else {
            emptyList()
        }

    @Suppress("LongParameterList")
    private fun prepareShortShelfLifeData(
        calculationsByWeek: Map<String, List<DayCalculationResult>>,
        skuId: UUID,
        dcCode: String,
        stockUpdateByKey: Map<CalculationKey, StockUpdate?>,
        sslSimulationInputsByWeek: Map<String, ShortShelfLifeSimulationDetail>,
    ): List<SslSqrDataInner> =
        calculationsByWeek.mapNotNull { (week, calculations) ->
            val shortShelfLifeSimulationByKey = sslSimulationInputsByWeek[week]?.sqrs?.associateBy {
                CalculationKey(skuId, dcCode, it.date)
            } ?: emptyMap()
            val isDemandExistForTheEntireWeek = calculations.any { it.demanded.getValue() != BigDecimal.ZERO }

            val sqrs = calculations.map { calculation ->
                val calcKey = CalculationKey(skuId, dcCode, calculation.date)
                val shortShelfLifeSimulation = shortShelfLifeSimulationByKey[calcKey]

                val bufferPercentage = shortShelfLifeSimulation?.bufferPercentage ?: BigDecimal.ZERO
                val bufferAdditional = shortShelfLifeSimulation?.bufferAdditional ?: BigDecimal.ZERO
                val sqrQuantity = if (isDemandExistForTheEntireWeek) {
                    SQRShortShelfLifeCalc.applyFormula(
                        calculation.openingStock,
                        calculation.demanded,
                        calculation.stockUpdate,
                        bufferPercentage,
                        bufferAdditional
                    ).getValue()
                } else {
                    BigDecimal.ZERO
                }

                Sqr(
                    date = calculation.date,
                    openingStock = calculation.openingStock.getValue(),
                    unusableStock = calculation.unusable.getValue(),
                    consumption = calculation.demanded.getValue(),
                    stockUpdates = calculation.stockUpdate?.getValue(),
                    stockUpdatesVersion = stockUpdateByKey[calcKey]?.version,
                    bufferPercentage = bufferPercentage,
                    bufferAdditional = bufferAdditional,
                    sqrQuantity = sqrQuantity,
                    uom = SkuQuantityMapper.mapSkuUOMToUomEnum(calculation.uom),
                )
            }

            val touchlessOrdering = sslSimulationInputsByWeek[week]?.touchlessOrderingEnabled ?: false

            SslSqrDataInner(
                week = week,
                sqrs = sqrs,
                touchlessOrdering = touchlessOrdering,
            )
        }

    private fun prepareShortShelfLifeSQRList(
        sqrs: List<ShortShelfLifeUpdateRequestSqrsInner>,
        stockUpdates: List<StockUpdateRequestStockUpdatesInner>,
        dcConfig: DistributionCenterConfiguration,
        touchlessOrderingEnabled: Boolean? = false,
    ): List<ShortShelfLifeSimulationWeek> =
        sqrs.map { item ->
            ShortShelfLifeSimulationWeek(
                date = item.date,
                week = DcWeek(item.date, dcConfig.productionStart),
                stockUpdates = stockUpdates.find { it.date == item.date }?.stockUpdateQuantity?.toBigDecimal(),
                bufferPercentage = item.bufferPercentage,
                bufferAdditional = item.bufferAdditional,
                uom = UNIT,
                touchlessOrderingEnabled = touchlessOrderingEnabled ?: false,
            )
        }

    private suspend fun prepareSimulationData(
        dcCode: String,
        skuId: UUID,
        simulationSQRList: List<ShortShelfLifeSimulationWeek>,
        stockUpdates: List<StockUpdate>,
    ): List<ShortShelfLifeSimulationDetail> {
        val sku = shortShelfLifeRepositoryImpl.getSkuSpecificationById(skuId)
        val stockUpdateByKey: Map<StockUpdateKey, StockUpdate> = stockUpdates.associateBy {
            StockUpdateKey(
                it.dcCode,
                it.skuId,
                it.date,
            )
        }
        return simulationSQRList
            .groupBy { it.week }
            .map { (dcWeek, itemsByWeek) ->
                val touchlessOrderingEnabled = itemsByWeek.all { it.touchlessOrderingEnabled }
                ShortShelfLifeSimulationDetail(
                    week = dcWeek.toString(),
                    touchlessOrderingEnabled = touchlessOrderingEnabled,
                    sqrs = itemsByWeek.map { item ->
                        ShortShelfLifeSimulation(
                            date = item.date,
                            stockUpdates = item.stockUpdates,
                            stockUpdatesVersion = stockUpdateByKey[StockUpdateKey(dcCode, skuId, item.date)]?.version,
                            bufferPercentage = item.bufferPercentage,
                            bufferAdditional = item.bufferAdditional,
                            uom = sku.uom,
                        )
                    },
                )
            }
    }

    data class ShortShelfLifeDetails(
        val matchingList: List<ShortShelfLife>,
        val nonMatchingList: List<ShortShelfLife>,
    )

    data class StockUpdateKey(
        val dcCode: String,
        val skuId: UUID,
        val date: LocalDate,
    )

    data class ShortShelfLifeSimulationResult(
        val shortShelfLifeSimulationResponse: ShortShelfLifeSimulationResponse,
        val skuDcCandidateMap: Map<UUID, SkuDcCandidate>,
    )

    companion object : Logging
}
