package com.hellofresh.cif.api.shortShelfLife.model

import com.hellofresh.cif.api.calculation.generated.model.UomEnum
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID

data class ShortShelfLifeSimulationDetail(
    val week: String,
    val touchlessOrderingEnabled: Boolean? = false,
    val sqrs: List<ShortShelfLifeSimulation>,
)

data class ShortShelfLifeSimulation(
    val date: LocalDate,
    val stockUpdates: BigDecimal? = null,
    val stockUpdatesVersion: Int? = null,
    val bufferPercentage: BigDecimal? = null,
    val bufferAdditional: BigDecimal? = null,
    val uom: UomEnum? = null,
)

data class ShortShelfLifeSimulationWeek(
    val date: LocalDate,
    val week: DcWeek? = null,
    val stockUpdates: BigDecimal? = null,
    val bufferPercentage: BigDecimal? = null,
    val bufferAdditional: BigDecimal? = null,
    val uom: UomEnum? = null,
    val touchlessOrderingEnabled: Boolean = false,
)

data class SkuSpecification(
    val skuId: UUID,
    val name: String,
    val skuCode: String,
    val category: String,
    val uom: UomEnum
)
