db.connections=Number of connections to the database
db.master.connections=Number of connections to the master database
db.timeout=Duration after which DB query timeout
inventory.db.host=DB Host containing forecast
inventory.readonly.db.username=Readonly DB Username
inventory.readonly.db.password=Readonly DB Password
inventory.db.master.host=DB Host containing forecast master
inventory.db.username=DB Username
inventory.db.password=DB Password
calculation.csv.parallelism=Number of concurrent csv generation allowed by service
auth.service.jwt.secret.key=JWT Secret Key
csv.brands.consumption.markets=Markets that should include consumption by brand in csv export

demand.type.enabled.markets= The market where the demand type will be shown in exported files
