---
environment: '@tier@'
tribe: '@tribe@'
squad: '@squad@'
# Deployment experiment, see build.gradle.kts
tag: 'latest'
fullnameOverride: '@applicationId@'
slack: '@slackAlertChannel@-@tier@'

vaultNamespace: services/@projectName@

serviceAccountAnnotations:
    eks.amazonaws.com/role-arn: "arn:aws:iam::************:role/csku-inventory-forecast-@tier@-role"

deployments:
  app:
    replicaCount: 2
    minAvailable: 1
    deploymentStrategy:
      type: RollingUpdate
    containerPorts:
      http: 8080
      http-actuator: 8081
    repository: '@dockerRepository@'
    pullPolicy: IfNotPresent
    resources:
      requests:
        memory: '1Gi'
        cpu: '500m'
      limits:
        memory: '1Gi'
        cpu: '1'
    nodeSelector: { }
    tolerations: [ ]
    affinity: { }
    podAnnotations:
      sidecar.istio.io/inject: 'true'
    env:
      HF_INVENTORY_DB_HOST: 'vault:@tier@/key-value/data/inventory#DB_HOST'
      HF_INVENTORY_READONLY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#READONLY_DB_PASSWORD'
      HF_INVENTORY_READONLY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#READONLY_DB_USERNAME'
      HF_INVENTORY_DB_MASTER_HOST: 'vault:@tier@/key-value/data/inventory#DB_MASTER_HOST'
      HF_INVENTORY_DB_PASSWORD: 'vault:@tier@/key-value/data/inventory#DB_PASSWORD'
      HF_INVENTORY_DB_USERNAME: 'vault:@tier@/key-value/data/inventory#DB_USERNAME'
      HF_AUTH_SERVICE_JWT_SECRET_KEY: 'vault:@tier@/key-value/data/misc#AUTH_SERVICE_JWT_SECRET_KEY'
      HF_STATSIG_SDK_KEY: 'vault:@tier@/key-value/data/misc#STATSIG_SDK_KEY'
      HF_AZURE_ISSUER: 'vault:@tier@/key-value/data/misc#AZURE_ISSUER'
      HF_AZURE_CLIENT_ID: 'vault:@tier@/key-value/data/misc#AZURE_CLIENT_ID'
      HF_AZURE_JWKS_URI: 'vault:@tier@/key-value/data/misc#AZURE_JWKS_URI'
    hpa:
      enabled: false
    spotInstance:
      preferred: true
    startupProbe:
      httpGet:
        path: /startup
        port: 8081
      initialDelaySeconds: 20
      timeoutSeconds: 3
      failureThreshold: 15
      periodSeconds: 10
    livenessProbe:
      httpGet:
        path: /health
        port: 8081
      initialDelaySeconds: 60
      periodSeconds: 15
      timeoutSeconds: 3
      successThreshold: 1
      failureThreshold: 3
    terminationGracePeriodSeconds: 60

services:
  app:
    enablePrometheus: true
    metricPortName: 'http-actuator'
    metricPath: '/prometheus'
    enabled: true
    type: ClusterIP
    ports:
      http: 80
      http-actuator: 8081

istio:
  app:
    enabled: true
    gateway:
      enabled: true
      redirect: true # route HTTPS traffic
      hosts:
        - 'cif-api.@<EMAIL>'
    virtualService:
      enabled: true
    destinationRule:
      enabled: true
      outlierDetection: # See https://istio.io/docs/reference/config/networking/v1alpha3/destination-rule/#OutlierDetection
        consecutive5xxErrors: 100         # Number of errors before a host is ejected from the connection pool.
        interval: 10s                     # Time interval between ejection sweep analysis.
        baseEjectionTime: 30s             # Minimum ejection duration.
        maxEjectionPercent: 20            # Maximum % of hosts in the load balancing pool that can be ejected

configMap:
  HF_TIER: '@tier@'
  SLACK_ALERT_CHANNEL: '@slackAlertChannel@-@tier@'
  SLACK_WEBHOOK: 'vault:common/key-value/data/misc#SLACK_URL'
