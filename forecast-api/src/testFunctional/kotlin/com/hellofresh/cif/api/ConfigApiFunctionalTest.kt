package com.hellofresh.cif.api

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.KotlinFeature
import com.fasterxml.jackson.module.kotlin.KotlinModule
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.calculation.generated.model.CountryDcConfigurationResponse
import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.api.configuration.configRoutingModule
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.Tables.DC_CONFIG
import com.hellofresh.cif.api.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryActivityRepositoryImpl
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.LiveInventoryRepositoryImpl
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.concurrent.Executors
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach

private const val TEN_WEEKS = 10L

@Suppress("SpreadOperator")
class ConfigApiFunctionalTest {
    private val datasource = getMigratedDataSource()
    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"

    private val dbConfig = DefaultConfiguration().apply {
        setSQLDialect(POSTGRES)
        setDataSource(datasource)
        setExecutor(Executors.newSingleThreadExecutor())
    }
    private val dcConfigRepository = DcRepositoryImpl(dbConfig.dsl().withMetrics(SimpleMeterRegistry()))
    private val dcConfigService = DcConfigService(SimpleMeterRegistry(), dcConfigRepository)
    private val dsl = dbConfig.dsl()
    private val inventoryRepository = InventoryRepositoryImpl(dsl.withMetrics(SimpleMeterRegistry()))
    private val liveInventoryRepository = LiveInventoryRepositoryImpl(dsl.withMetrics(SimpleMeterRegistry()))
    private val inventoryActivityRepository = InventoryActivityRepositoryImpl(dsl.withMetrics(SimpleMeterRegistry()))
    private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
    private val inventoryService =
        InventoryService(
            inventoryRepository,
            liveInventoryRepository,
            inventoryActivityRepository,
            statsigFeatureFlagClient,
        )
    private val configService = ConfigService(dcConfigService, inventoryService)

    private val objectMapper = ObjectMapper()
        .registerModule(KotlinModule.Builder().configure(KotlinFeature.NullIsSameAsDefault, true).build())
        .findAndRegisterModules()
    private val timeOutInMillis = Duration.parse("PT1S")
    private val defaultDcConfigRecord: DcConfigRecord = DcConfigRecord().apply {
        dcCode = "VE"
        market = "DACH"
        productionStart = "SUNDAY"
        cleardown = "FRIDAY"
        zoneId = "Europe/Berlin"
        enabled = true
        hasCleardown = true
        recordTimestamp_ = LocalDateTime.now()
        createdAt = LocalDateTime.now()
        updatedAt = LocalDateTime.now()
        scheduledCleardownTime = LocalTime.now()
        poCutoffTime = LocalTime.now()
        brands = emptyArray()
    }
    private val dcConfigRecords = listOf(
        defaultDcConfigRecord,
        defaultDcConfigRecord.copy().apply {
            dcCode = "CH"
            market = "DACH"
        },
        defaultDcConfigRecord.copy().apply {
            dcCode = "EK"
            market = "DACH"
        },
        defaultDcConfigRecord.copy().apply {
            dcCode = "DH"
            market = "BENELUXFR"
        },
        defaultDcConfigRecord.copy().apply {
            dcCode = "GR"
            market = "GB"
        },
    )

    @BeforeTest
    fun setUp() {
        dsl.deleteFrom(DC_CONFIG).execute()
        dsl.batchInsert(
            *dcConfigRecords.toTypedArray(),
        )
            .execute()
        dcConfigService.fetchOnDemand()
    }

    @AfterEach
    fun cleanup() {
        dsl.deleteFrom(DC_CONFIG).execute()
        dcConfigService.fetchOnDemand()
    }

    @Test
    fun `config endpoint returns records specific to country`() {
        val countryCode = "CH"
        val distributionCenterConfiguration = DistributionCenterConfiguration(
            defaultDcConfigRecord.dcCode,
            DayOfWeek.valueOf(defaultDcConfigRecord.productionStart),
            DayOfWeek.valueOf(defaultDcConfigRecord.cleardown),
            defaultDcConfigRecord.market,
            ZoneId.of(defaultDcConfigRecord.zoneId),
            true,
            wmsType = WmsSystem.WMS_SYSTEM_FCMS,
            poCutoffTime = LocalTime.now(),
            brands = emptyList(),
        )
        val today = LocalDate.now(UTC)
        val expectedCurrentWeek = DcWeek(
            today,
            DayOfWeek.valueOf(defaultDcConfigRecord.productionStart),
        )
        val expectedMinWeek = DcWeek(
            today.minusWeeks(TEN_WEEKS),
            DayOfWeek.valueOf(defaultDcConfigRecord.productionStart),
        )
        val expectedLastCleardownDate = distributionCenterConfiguration.getLatestCleardown()
        val dachConfig = dcConfigRecords.filter { it.market == "DACH" }
        val marketSized = dachConfig.size
        val dcCodes = dachConfig.map { it.dcCode }.toSet()
        runBlocking {
            getUrl("/config/$countryCode").apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val resultMap = objectMapper.readValue<Map<String, CountryDcConfigurationResponse>>(
                    body,
                )
                assertEquals(marketSized, resultMap.size)
                val dcConfigResponseVe = resultMap[defaultDcConfigRecord.dcCode]
                assertEquals(dcCodes, resultMap.keys)
                assertEquals(expectedLastCleardownDate, dcConfigResponseVe?.lastCleardown)
                assertEquals(
                    expectedLastCleardownDate,
                    dcConfigResponseVe?.lastCleardownTime?.atZoneSameInstant(
                        distributionCenterConfiguration.zoneId,
                    )?.toLocalDate(),
                )
                assertEquals(expectedMinWeek.toString(), dcConfigResponseVe?.minWeek)
                assertEquals(expectedCurrentWeek.toString(), dcConfigResponseVe?.currentWeek)
            }
        }
    }

    @Test
    fun `config endpoint returns HTTP 404 on records not found`() {
        runBlocking {
            getUrl("/config/NR").apply {
                assertEquals(HttpStatusCode.NotFound, status)
                assertEquals("""{"reason":"Country NR not found"}""", bodyAsText())
            }
        }
    }

    private fun getUrl(url: String): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            this.application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                configRoutingModule(configService, timeOutInMillis)()
            }
            response = client.get(url) {
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }
}
