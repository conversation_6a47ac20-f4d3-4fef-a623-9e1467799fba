package com.hellofresh.cif.api.cleardown

import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.demand.lib.schema.tables.records.DcConfigRecord
import io.ktor.server.plugins.NotFoundException
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

class CleardownTriggerRepositoryTest : FunctionalTest() {

    private val testAuthorEmail = "<EMAIL>"
    private val testAuthorName = "test-author"

    @Test
    fun `should be able to save inventory cleardown trigger`() {
        // when
        val snapshotId = UUID.randomUUID()
        val newDcConfigRecord = DcConfigRecord(
            "VE", "DACH", "MONDAY", "FRIDAY", "Europe/Berlin",
            true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null, LocalTime.now(),
            emptyArray()
        )
        dsl.batchInsert(newDcConfigRecord).execute()
        dcConfigService.fetchOnDemand()
        runBlocking {
            cleardownTriggerRepository.saveCleardownTrigger(
                listOf("VE"),
                testAuthorEmail,
                testAuthorName,
                snapshotId,
            )
        }

        // then
        val inventoryCleardownTriggers = dsl.selectFrom(
            Tables.INVENTORY_CLEARDOWN_TRIGGER,
        ).toList()
        assertEquals(1, inventoryCleardownTriggers.size)
        inventoryCleardownTriggers.first().apply {
            assertEquals("VE", dcCode)
            assertEquals(testAuthorEmail, authorEmail)
            assertEquals(testAuthorName, authorName)
            assertEquals(snapshotId, inventorySnapshotId)
        }
    }

    @Test
    fun `should throw an exception saving inventory cleardown trigger using invalid dc code`() {
        val exception = assertThrows<NotFoundException> {
            runBlocking {
                cleardownTriggerRepository.saveCleardownTrigger(
                    listOf("Invalid-dcCode"),
                    testAuthorEmail,
                    testAuthorName,
                    UUID.randomUUID(),
                )
            }
        }
        assertTrue(exception.message!!.contains("DcCode Invalid-dcCode not found"))
    }
}
