package com.hellofresh.cif.api.inventory

import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.enums.FileType
import com.hellofresh.cif.s3.S3FileService
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.net.URI
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class GenerateUploadUrlApiTest {

    private val jwtSecret = "testSecret"
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"
    val s3FileService = mockk<S3FileService>()

    @ParameterizedTest(name = "{index} => {2}")
    @MethodSource("provideInputsForGeneratingUrl")
    fun `should create new s3 pre signed url and return it in api response`(fileType: String, folderName: String) {
        val market = "DACH"
        val fileName = "testFile.csv"
        coEvery {
            s3FileService.getPreSignedPutUrl(
                any(), any(),
                match {
                    it.contains("/$market/") && it.contains("/$fileName")
                },
                mapOf("author_name" to authorName, "author_email" to authorEmail, "market" to market),
            )
        } returns URI("http://testPresigned/$folderName").toURL()
        runBlocking {
            val response = getPreSignedUrl(
                "/generate-upload-url/$market?fileName=$fileName&authorName=$authorName&authorEmail=$authorEmail&fileType=$fileType",
            )
            assertEquals(response.status, HttpStatusCode.OK)
            assertTrue(response.bodyAsText().contains("http://testPresigned"))
            assertTrue(response.bodyAsText().contains(folderName))
        }
    }

    private fun getPreSignedUrl(url: String): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            this.application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                generateUploadUrlRoutingModule(GenerateUploadUrlService(s3FileService, "staging"))()
            }
            response = client.get(url) {
                addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }

    companion object {
        @JvmStatic
        fun provideInputsForGeneratingUrl(): Stream<Arguments> = Stream.of(
            Arguments.of(
                FileType.STOCK_INVENTORY.literal,
                "third_pw_stock_inventory_uploads",
                "should process and generate url for the STOCK_INVENTORY"
            ),
            Arguments.of(
                FileType.STOCK_UPDATE.literal,
                "stock_update",
                "should process and generate url for the STOCK_UPDATE"
            ),
        )
    }
}
