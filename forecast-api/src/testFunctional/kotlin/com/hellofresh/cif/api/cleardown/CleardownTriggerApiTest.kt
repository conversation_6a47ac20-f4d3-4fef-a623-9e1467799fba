package com.hellofresh.cif.api.cleardown

import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.demand.lib.schema.tables.records.DcConfigRecord
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType.Application
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import java.time.LocalDateTime
import java.time.LocalTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class CleardownTriggerApiTest : FunctionalTest() {
    @ParameterizedTest
    @MethodSource("snapshotIdProvider")
    fun `should create a cleardown trigger for the given dc`(snapshotId: String?) {
        val authorEmailParam = UUID.randomUUID().toString()
        val authorNameParam = UUID.randomUUID().toString()
        val dcCodeParam = "VE"
        val newDcConfigRecord = DcConfigRecord(
            dcCodeParam, "DACH", "MONDAY", "FRIDAY", "Europe/Berlin",
            true, LocalDateTime.now(), LocalDateTime.now(), LocalDateTime.now(), true, null, null, LocalTime.now(),
            emptyArray()
        )
        dsl.batchInsert(newDcConfigRecord).execute()
        dcConfigService.fetchOnDemand()
        val url = if (snapshotId == null) {
            "/inventory/cleardownTrigger?dcCode=$dcCodeParam"
        } else {
            "/inventory/cleardownTrigger?dcCode=$dcCodeParam&snapshotId=$snapshotId"
        }

        runBlocking {
            post(
                url,
                authorEmailParam,
                authorNameParam,
            ) {
                assertEquals(HttpStatusCode.OK, this.status)
            }
        }

        val inventoryCleardownTriggers = dsl.selectFrom(
            Tables.INVENTORY_CLEARDOWN_TRIGGER,
        ).toList()
        assertEquals(1, inventoryCleardownTriggers.size)
        inventoryCleardownTriggers.first().apply {
            assertEquals(dcCodeParam, dcCode)
            assertEquals(authorEmailParam, authorEmail)
            assertEquals(authorNameParam, authorName)
            if (snapshotId.isNullOrEmpty()) {
                assertEquals(null, inventorySnapshotId)
            } else {
                assertEquals(snapshotId, inventorySnapshotId.toString())
            }
        }
    }

    @Test
    fun `should return 404 not found when the given dc does not exist`() {
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        runBlocking {
            post("/inventory/cleardownTrigger?dcCode=InvalidDc", authorEmail, authorName) {
                assertEquals(HttpStatusCode.NotFound, this.status)
            }
        }
    }

    private suspend fun post(
        postUrl: String,
        authorEmail: String,
        authorName: String,
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", "https://test.com"), true)
                cleardownTriggerModule(cleardownTriggerRepository, timeOutInMillis)()
            }
            response = client.post(postUrl) {
                setBody("")
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        response.block()
    }

    companion object {
        val timeOutInMillis: kotlin.time.Duration = kotlin.time.Duration.parse("PT1S")

        @JvmStatic
        fun snapshotIdProvider() = Stream.of(
            Arguments.of(UUID.randomUUID().toString()),
            Arguments.of(""),
            Arguments.of(null),
        )
    }
}
