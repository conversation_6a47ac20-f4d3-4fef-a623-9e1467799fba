package com.hellofresh.cif.api.calculation

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_CONSUMPTION_ONLY
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_INBOUND_SHORTAGE
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_INVENTORY_SHORTAGE
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_NO_CONSUMPTION_ONLY
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_PROJECTED_WASTE
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_SKU_SUBBED_IN_ONLY
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_SKU_SUBBED_OUT_ONLY
import com.hellofresh.cif.api.calculation.SortBy.SKU_CODE
import com.hellofresh.cif.api.calculation.SortBy.SKU_NAME
import com.hellofresh.cif.api.calculation.generated.model.ActualInboundResponse
import com.hellofresh.cif.api.calculation.generated.model.CalculationStatus
import com.hellofresh.cif.api.calculation.generated.model.ConsumptionDetail
import com.hellofresh.cif.api.calculation.generated.model.DailyCalculationResponse
import com.hellofresh.cif.api.calculation.generated.model.DailyCalculationsResponse
import com.hellofresh.cif.api.calculation.generated.model.ExpectedInboundResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.enums.SubbedType
import com.hellofresh.cif.api.schema.enums.SubbedType.NONE
import com.hellofresh.cif.api.schema.enums.SubbedType.SUB_IN
import com.hellofresh.cif.api.schema.enums.SubbedType.SUB_IN_AND_OUT
import com.hellofresh.cif.api.schema.enums.SubbedType.SUB_OUT
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.DemandRecord
import com.hellofresh.cif.api.sku.SkuQuantityMapper
import com.hellofresh.cif.api.toSkuSpecification
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.demand.models.Prekitting
import com.hellofresh.demand.models.Prekittings
import com.hellofresh.demand.models.RecipeBreakdown
import com.hellofresh.demand.models.Substitution
import com.hellofresh.demand.models.Substitutions
import io.ktor.client.request.get
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.math.BigDecimal.TWO
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.time.LocalDateTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

private const val HELLO_FRESH = "HelloFresh"
private const val EVERY_PLATE = "EveryPlate"

class CalculationsApiAdditionalFiltersTest : CalculationFunctionalTest() {
    private val objectMapper = jacksonObjectMapper().findAndRegisterModules()

    @Test
    fun `should return bad request if an additional filter name is invalid`() {
        val invalidFilterName = "invalid filter name"
        val params = Parameters.build {
            set("dcCode", FIXTURE_DC)
            set("weeks", "2022-W01")
            append("additionalFilters", WITH_CONSUMPTION_ONLY.name.lowercase())
            append("additionalFilters", invalidFilterName)
            set("sortBy", "skuCode")
        }.formUrlEncode()
        runBlocking {
            testApplication {
                application {
                    configureJwtAuth(JwtCredentials("", "", "", "", "https://test.com"), false)
                    calculationRoutingModule(
                        CalculationsService(
                            SimpleMeterRegistry(),
                            dcConfigService,
                            usableInventoryEvaluator,
                            calculationsPendingStockUpdateService,
                            prodCalculationsRepo,
                            preProdCalculationsRepo,
                            statsigFeatureFlagClient,
                        ),
                        calculationResponseMapper,
                        Duration.parse("PT1S"),
                    )()
                }
                client.get("/calculation/dailyView?$params") {
                    this.addAuthHeader(UUID.randomUUID().toString(), UUID.randomUUID().toString(), jwtSecret)
                }.apply {
                    assertEquals(HttpStatusCode.BadRequest, status)
                    assertEquals(
                        """{"reason":"No enum constant com.hellofresh.cif.api.calculation.AdditionalFilter.${invalidFilterName.uppercase()}"}""",
                        bodyAsText(),
                    )
                }
            }
        }
    }

    @Test
    fun `should return all calculations for the given week where at least one date has shortage`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.parse("2022-01-01")
        val day2 = day1.plusDays(1)
        val givenCulinarySku = firstCulinarySku()
        val calculation = emptyCalculationRecord(givenWeek, day1, givenCulinarySku)
        val calculationWithShortage = withInventoryShortages(day2, givenCulinarySku)
        dsl.batchInsert(listOf(calculation, calculationWithShortage)).execute()
        insertSkuRecords(givenCulinarySku.toSkuSpecificationRecord())

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 10),
            additionalFilters = setOf(WITH_INVENTORY_SHORTAGE),
            consumptionDaysAhead = 0,
            sortBy = SKU_NAME,
        )

        // when & then
        val response = getDailyCalculationsOkResponse(req)
        val emptyDailyCalculation = emptyCalculations(day1, givenCulinarySku)
        val withShortage = emptyDailyCalculation.copy(dailyNeed = BigDecimal(1), date = day2, atRisk = true)
        val expectedResponse = DailyCalculationsResponse(
            totalPages = 1,
            page = 1,
            calculations = listOf(
                emptyDailyCalculation,
                withShortage,
            ),
            totalSkusAtRiskCount = 1,
            totalSkusCount = 1,

        )
        assertEquals(expectedResponse, response)
    }

    @Test
    fun `should return all calculations for skus which are SUBBED_IN, SUBBED_OUT, SUBBED_IN_AND_OUT`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.now()
        val subInSku = firstCulinarySku()
        val subOutSku = secondCulinarySku()
        val subInOutSku = thirdCulinarySku()

        val calculationIn = emptyCalculationRecord(givenWeek, day1, subInSku)
        val calculationOut = emptyCalculationRecord(givenWeek, day1, subOutSku)
        val calculationInOut = emptyCalculationRecord(givenWeek, day1, subInOutSku)

        val calculations = listOf(calculationIn, calculationOut, calculationInOut)
        dsl.batchInsert(calculations).execute()
        insertSkuRecords(
            subInSku.toSkuSpecificationRecord(),
            subOutSku.toSkuSpecificationRecord(),
            subInOutSku.toSkuSpecificationRecord()
        )

        val demand = calculations.map {
            createDemand(
                day1,
                skuIdParam = it.cskuId,
                dcCodeParam = it.dcCode,
                subbed = when (it.cskuId) {
                    subInSku.id -> SUB_IN
                    subOutSku.id -> SUB_OUT
                    subInOutSku.id -> SUB_IN_AND_OUT
                    else -> error("Unreachable state")
                },
            )
        }
        dsl.batchInsert(demand).execute()
        dsl.execute("REFRESH MATERIALIZED VIEW calculation_substitution_view")
        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 10),
            consumptionDaysAhead = 0,
            sortBy = SKU_NAME,
        )
        var response = getDailyCalculationsOkResponse(req.copy(additionalFilters = setOf(WITH_SKU_SUBBED_IN_ONLY)))
        assertEquals(2, response.totalSkusCount)
        assertEquals(setOf(subInSku.id, subInOutSku.id), response.calculations.map { it.skuId }.toSet())

        response = getDailyCalculationsOkResponse(req.copy(additionalFilters = setOf(WITH_SKU_SUBBED_OUT_ONLY)))
        assertEquals(2, response.totalSkusCount)
        assertEquals(setOf(subOutSku.id, subInOutSku.id), response.calculations.map { it.skuId }.toSet())

        response = getDailyCalculationsOkResponse(
            req
                .copy(additionalFilters = setOf(WITH_SKU_SUBBED_OUT_ONLY, WITH_SKU_SUBBED_IN_ONLY)),
        )
        assertEquals(1, response.totalSkusCount)
        assertEquals(setOf(subInOutSku.id), response.calculations.map { it.skuId }.toSet())
    }

    @Test
    fun `should return calculations with consumption brand details`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.now()
        val subInSku = firstCulinarySku()
        val subOutSku = secondCulinarySku()
        val subInOutSku = thirdCulinarySku()

        val calculationIn = emptyCalculationRecord(givenWeek, day1, subInSku)
        val calculationOut = emptyCalculationRecord(givenWeek, day1, subOutSku)
        val calculationInOut = emptyCalculationRecord(givenWeek, day1, subInOutSku)

        val calculations = listOf(calculationIn, calculationOut, calculationInOut)
        dsl.batchInsert(calculations).execute()
        insertSkuRecords(
            subInSku.toSkuSpecificationRecord(),
            subOutSku.toSkuSpecificationRecord(),
            subInOutSku.toSkuSpecificationRecord()
        )
        val consumptionDetails = JSONB.valueOf(
            objectMapper.writeValueAsString(
                ConsumptionDetails(
                    recipeBreakdowns = listOf(
                        RecipeBreakdown(
                            brand = "HelloFresh",
                            recipeIndex = "1",
                            qty = 100L,
                        ),
                        RecipeBreakdown(
                            brand = "HelloFresh",
                            recipeIndex = "2",
                            qty = 100L,
                        ),
                        RecipeBreakdown(
                            brand = "EveryPlate",
                            recipeIndex = "3",
                            qty = 100L,
                        ),
                        RecipeBreakdown(
                            brand = "EveryPlate",
                            recipeIndex = "4",
                            qty = 100L,
                        ),
                    ),
                    prekitting = Prekittings(
                        `in` = listOf(
                            Prekitting(
                                qty = 100,
                            ),
                        ),
                        out = listOf(
                            Prekitting(
                                qty = 100,
                            ),
                        ),
                    ),
                    substitutions = Substitutions(
                        `in` = listOf(
                            Substitution(
                                brand = "HelloFresh",
                                recipeIndex = "1",
                                qty = 100L,
                            ),
                        ),
                        out = listOf(
                            Substitution(
                                brand = "HelloFresh",
                                recipeIndex = "1",
                                qty = 100L,
                            ),
                        ),
                    ),
                    crossDockings = null,
                ),
            ),
        )
        val demand = calculations.map {
            createDemand(
                day1,
                skuIdParam = it.cskuId,
                dcCodeParam = it.dcCode,
                subbed = when (it.cskuId) {
                    subInSku.id -> SUB_IN
                    subOutSku.id -> SUB_OUT
                    subInOutSku.id -> SUB_IN_AND_OUT
                    else -> error("Unreachable state")
                },
                consumptionDetails = consumptionDetails,
                subInQty = 100L,
            )
        }
        dsl.batchInsert(demand).execute()
        dsl.execute("REFRESH MATERIALIZED VIEW calculation_substitution_view")
        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 10),
            consumptionDaysAhead = 0,
            sortBy = SKU_NAME,
        )
        var response = getDailyCalculationsOkResponse(req.copy(additionalFilters = setOf(WITH_SKU_SUBBED_IN_ONLY)))
        assertEquals(2, response.totalSkusCount)
        assertEquals(setOf(subInSku.id, subInOutSku.id), response.calculations.map { it.skuId }.toSet())
        assertBrand(response, subInSku)
        assertPrekitting(response, subInSku)
        assertSubstituted(response, subInSku)
        assertBrand(response, subInOutSku)
        assertPrekitting(response, subInOutSku)
        assertSubstituted(response, subInOutSku)

        response = getDailyCalculationsOkResponse(req.copy(additionalFilters = setOf(WITH_SKU_SUBBED_OUT_ONLY)))
        assertEquals(2, response.totalSkusCount)
        assertEquals(setOf(subOutSku.id, subInOutSku.id), response.calculations.map { it.skuId }.toSet())
        assertBrand(response, subOutSku)
        assertPrekitting(response, subOutSku)
        assertSubstituted(response, subOutSku)
        assertBrand(response, subInOutSku)
        assertPrekitting(response, subInOutSku)
        assertSubstituted(response, subInOutSku)

        response = getDailyCalculationsOkResponse(
            req
                .copy(additionalFilters = setOf(WITH_SKU_SUBBED_OUT_ONLY, WITH_SKU_SUBBED_IN_ONLY)),
        )
        assertEquals(1, response.totalSkusCount)
        assertEquals(setOf(subInOutSku.id), response.calculations.map { it.skuId }.toSet())
        assertBrand(response, subInOutSku)
        assertPrekitting(response, subInOutSku)
        assertSubstituted(response, subInOutSku)
    }

    @Test
    fun `should return all calculations for the given week where at least one date has inbound shortage`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.parse("2022-01-01")
        val day2 = day1.plusDays(1)
        val givenCulinarySku = firstCulinarySku()
        val calculation = emptyCalculationRecord(givenWeek, day1, givenCulinarySku)
        val calculationWithInboundShortage = withInboundShortages(day2, givenCulinarySku)

        dsl.batchInsert(
            listOf(
                calculation,
                calculationWithInboundShortage,
            ),
        ).execute()
        insertSkuRecords(givenCulinarySku.toSkuSpecificationRecord())

        val params = Parameters.build {
            set("dcCode", FIXTURE_DC)
            set("weeks", givenWeek)
            set("additionalFilters", WITH_INBOUND_SHORTAGE.filterName())
            set("sortBy", "skuCode")
        }
        refreshView()
        // when & then
        runBlocking {
            getDailyCalculations(CalculationRequest.from(params)).apply {
                assertEquals(HttpStatusCode.OK, status)
                val emptyDailyCalculation = emptyCalculations(day1, givenCulinarySku)
                val withInboundShortage = emptyDailyCalculation
                    .copy(
                        date = day2,
                        incomingPos = ExpectedInboundResponse(calculationWithInboundShortage.expectedInbound),
                        inbound = ActualInboundResponse(BigDecimal(1)),
                        atRisk = true,
                    )
                val expectedResponse = DailyCalculationsResponse(
                    totalPages = 1,
                    page = 1,
                    calculations = listOf(
                        emptyDailyCalculation,
                        withInboundShortage,
                    ),
                    totalSkusAtRiskCount = 1,
                    totalSkusCount = 1,
                )
                val response = objectMapper.readValue(bodyAsText(), DailyCalculationsResponse::class.java)
                assertEquals(expectedResponse, response)
            }
        }
    }

    @Test
    fun `should return all calculations for the given week where at least one date has consumption`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.parse("2022-01-01")
        val day2 = day1.plusDays(1)
        val givenCulinarySku = firstCulinarySku()
        val calculation = emptyCalculationRecord(givenWeek, day1, givenCulinarySku)
        val calculationWithConsumption = withConsumptionOnly(day2, givenCulinarySku)
        dsl.batchInsert(listOf(calculation, calculationWithConsumption)).execute()
        insertSkuRecords(givenCulinarySku.toSkuSpecificationRecord())

        val params = Parameters.build {
            set("dcCode", FIXTURE_DC)
            set("weeks", givenWeek)
            set("additionalFilters", WITH_CONSUMPTION_ONLY.filterName())
            set("sortBy", "skuCode")
        }

        // when & then
        runBlocking {
            getDailyCalculations(CalculationRequest.from(params)).apply {
                assertEquals(HttpStatusCode.OK, status)
                val emptyDailyCalculation = emptyCalculations(day1, givenCulinarySku)
                val withConsumption = emptyDailyCalculation
                    .copy(
                        date = day2,
                        consumption = BigDecimal(1),
                    )
                val expectedResponse = DailyCalculationsResponse(
                    totalPages = 1,
                    page = 1,
                    calculations = listOf(
                        emptyDailyCalculation,
                        withConsumption,
                    ),
                    totalSkusAtRiskCount = 0,
                    totalSkusCount = 1,
                )
                val response = objectMapper.readValue(
                    bodyAsText(),
                    DailyCalculationsResponse::class.java,
                )
                assertEquals(expectedResponse, response)
            }
        }
    }

    @Test
    fun `should return all calculations ONLY for the given week where at least date has shortage`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.parse("2022-01-01")

        val givenAnotherWeek = "2022-W02"
        val nextWeekDate = day1.plusDays(8)

        val givenCulinarySku = firstCulinarySku()
        val calculationFromAnotherWeek = emptyCalculationRecord(givenAnotherWeek, nextWeekDate, givenCulinarySku)
        val calculationWithShortage = withInventoryShortages(day1, givenCulinarySku)
        dsl.batchInsert(listOf(calculationWithShortage, calculationFromAnotherWeek)).execute()

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 10),
            additionalFilters = setOf(WITH_INVENTORY_SHORTAGE),
            consumptionDaysAhead = 0,
            sortBy = SKU_CODE,
        )

        // when & then
        val response = getDailyCalculationsOkResponse(req)

        val dailyCalculationFromAnotherWeek =
            emptyCalculations(nextWeekDate, givenCulinarySku)
        assertFalse(response.calculations.contains(dailyCalculationFromAnotherWeek))
    }

    @Test
    fun `should return all calculations ONLY for the given week where at least date has inbound shortage`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.parse("2022-01-01")

        val givenAnotherWeek = "2022-W02"
        val nextWeekDate = day1.plusDays(8)

        val givenCulinarySku = firstCulinarySku()
        val calculationFromAnotherWeek = emptyCalculationRecord(givenAnotherWeek, nextWeekDate, givenCulinarySku)
        val calculationWithInboundShortage = withInboundShortages(day1, givenCulinarySku)
        dsl.batchInsert(listOf(calculationWithInboundShortage, calculationFromAnotherWeek)).execute()

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 10),
            additionalFilters = setOf(WITH_INBOUND_SHORTAGE),
            consumptionDaysAhead = 0,
            sortBy = SKU_CODE,
        )

        // when & then
        val response = getDailyCalculationsOkResponse(req)

        val dailyCalculationFromAnotherWeek = emptyCalculations(nextWeekDate, givenCulinarySku)
        assertFalse(response.calculations.contains(dailyCalculationFromAnotherWeek))
    }

    @Test
    fun `should return all calculations ONLY for the given week where at least date has consumption`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.parse("2022-01-01")

        val givenNextWeek = "2022-W02"
        val nextWeekDate = day1.plusDays(8)

        val givenCulinarySku = firstCulinarySku()
        val calculationFromAnotherWeek = emptyCalculationRecord(givenNextWeek, nextWeekDate, givenCulinarySku)
        val calculationWithConsumption = withConsumptionOnly(day1.plusDays(1), givenCulinarySku)
        dsl.batchInsert(listOf(calculationWithConsumption, calculationFromAnotherWeek)).execute()

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 10),
            additionalFilters = setOf(WITH_CONSUMPTION_ONLY),
            consumptionDaysAhead = 0,
            sortBy = SKU_CODE,
        )

        // when & then
        val response = getDailyCalculationsOkResponse(req)
        val dailyCalculationFromAnotherWeek =
            emptyCalculations(nextWeekDate, givenCulinarySku)
        assertFalse(response.calculations.contains(dailyCalculationFromAnotherWeek))
    }

    @Test
    fun `should return only the calculations for the week with at least one day with unusable inventory if the additional filter is WITH_PROJECTED_WASTE`() {
        val sku = firstCulinarySku()
        val givenWeek = "2022-W01"
        val givenWeek2 = "2022-W02"

        val date = LocalDate.parse("2022-01-01")

        val unusableStockDay1 = TWO
        val calculationWithExpired = withExpiry(emptyCalculationRecord(givenWeek, date, sku), unusableStockDay1)
        val calculationWithValid = emptyCalculationRecord(givenWeek2, date.plusWeeks(1), sku)
        dsl.batchInsert(calculationWithExpired, calculationWithValid).execute()
        insertSkuRecords(toSkuSpecification(calculationWithValid))

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek, givenWeek2),
            pageRequest = Page(1, 10),
            additionalFilters = setOf(WITH_PROJECTED_WASTE),
            consumptionDaysAhead = 0,
            sortBy = SKU_CODE,
        )

        val response = getDailyCalculationsOkResponse(req)

        assertEquals(1, response.calculations.size)
        assertEquals(unusableStockDay1, response.calculations.first().unusableStock)
    }

    @Test
    fun `should return no calculations for the week with different consumption values`() {
        val sku1 = firstCulinarySku()
        val sku2 = secondCulinarySku()
        val givenWeek = "2022-W01"

        val date = LocalDate.parse("2022-01-01")

        val calculationWithNoConsumptionOnly = withNoConsumptionOnly(givenWeek, date, sku1)
        val nonMatchingRecord = emptyCalculationRecord(givenWeek, date.plusWeeks(1), sku2).apply { demanded = ONE }
        dsl.batchInsert(listOf(calculationWithNoConsumptionOnly, nonMatchingRecord)).execute()
        insertSkuRecords(
            toSkuSpecification(calculationWithNoConsumptionOnly),
            toSkuSpecification(nonMatchingRecord),
        )

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(page = 1, skuCount = 10),
            additionalFilters = setOf(WITH_NO_CONSUMPTION_ONLY),
            consumptionDaysAhead = 0,
            sortBy = SKU_CODE,
        )

        val response = getDailyCalculationsOkResponse(req)

        assertTrue(response.calculations.isEmpty())
    }

    @Test
    fun `should return only the calculations for the week with no consumption`() {
        val givenWeek = "2022-W02"
        val date = LocalDate.of(2022, 1, 2)
        val culinarySku = randomCulinarySku()

        val calculationRecords = listOf(
            withNoConsumptionOnly(givenWeek, date, culinarySku),
            withNoConsumptionOnly(givenWeek, date.plusDays(1), culinarySku),
            withNoConsumptionOnly(givenWeek, date.plusDays(2), culinarySku),
            withNoConsumptionOnly(givenWeek, date.plusDays(3), culinarySku),
            withNoConsumptionOnly(givenWeek, date.plusDays(4), culinarySku),
            withNoConsumptionOnly(givenWeek, date.plusDays(5), culinarySku),
            withNoConsumptionOnly(givenWeek, date.plusDays(6), culinarySku),
        )

        dsl.batchInsert(calculationRecords).execute()
        insertSkuRecords(culinarySku.toSkuSpecificationRecord())

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(page = 1, skuCount = 10),
            additionalFilters = setOf(WITH_NO_CONSUMPTION_ONLY),
            consumptionDaysAhead = 0,
            sortBy = SKU_CODE,
        )

        val response = getDailyCalculationsOkResponse(req)

        assertEquals(7, response.calculations.size)
    }

    @Test
    fun `should return no calculations if no day with unusable inventory is found`() {
        val givenWeek = "2022-W01"
        val sku = firstCulinarySku()
        val calculationsNoWaste = (1..3).map {
            emptyCalculationRecord(givenWeek, LocalDate.now().plusDays(it.toLong()), sku)
        }
        dsl.batchInsert(calculationsNoWaste).execute()

        val req = CalculationRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(givenWeek),
            pageRequest = Page(1, 10),
            additionalFilters = setOf(WITH_PROJECTED_WASTE),
            consumptionDaysAhead = 0,
            sortBy = SKU_CODE,
        )

        val response = getDailyCalculationsOkResponse(req)
        assertEquals(0, response.calculations.size)
    }

    @Test
    fun `Should return all the calculations When additional filters is empty`() {
        // given
        val records = listOf(
            emptyCalculationRecord("2022-W01", LocalDate.parse("2022-01-01"), firstCulinarySku()),
            emptyCalculationRecord("2022-W01", LocalDate.parse("2022-01-02"), secondCulinarySku()),
            emptyCalculationRecord("2022-W01", LocalDate.parse("2022-01-03"), thirdCulinarySku()),
        )
        dsl.batchInsert(records).execute()
        insertSkuRecords(
            firstCulinarySku().toSkuSpecificationRecord(),
            secondCulinarySku().toSkuSpecificationRecord(),
            thirdCulinarySku().toSkuSpecificationRecord(),
        )

        val params = Parameters.build {
            set("dcCode", FIXTURE_DC)
            set("weeks", "2022-W01")
            set("sortBy", "skuCode")
        }

        // when & then
        runBlocking {
            getDailyCalculations(CalculationRequest.from(params)).apply {
                assertEquals(HttpStatusCode.OK, status)
                val result = ObjectMapper().readValue(bodyAsText(), object : TypeReference<Map<String, Any>>() {})

                @Suppress("UNCHECKED_CAST")
                val calculations = result["calculations"] as List<Map<String, String>>
                assertEquals(
                    listOf(
                        "2022-01-01",
                        "2022-01-02",
                        "2022-01-03",
                    ),
                    calculations.map { calc -> calc["date"].toString() }.toList(),
                )
            }
        }
    }

    @Test
    fun `Should return all daily calculations When ALL additional filters is present`() {
        // given
        val givenCulinarySku = firstCulinarySku()
        val dcWeek = "2022-W01"
        val date = DcWeek(dcWeek).getStartDateInDcWeek(productionStart, zoneId)

        val poRef = "A_1"

        val record = emptyCalculationRecord(dcWeek, date, givenCulinarySku)
            .apply {
                expectedInboundPo = poRef
            }
        val additionalFilters = listOf(
            WITH_INBOUND_SHORTAGE.filterName(),
            WITH_INVENTORY_SHORTAGE.filterName(),
            WITH_CONSUMPTION_ONLY.filterName(),
            WITH_PROJECTED_WASTE.filterName(),
        )
        withInventoryShortages(record)
        withInboundShortages(record)
        withConsumptionOnly(record)
        withExpiry(record, ONE)

        dsl.batchInsert(record).execute()
        insertSkuRecords(givenCulinarySku.toSkuSpecificationRecord())

        val params = Parameters.build {
            set("dcCode", FIXTURE_DC)
            set("weeks", dcWeek)
            additionalFilters.forEach {
                append("additionalFilters", it)
            }
            set("sortBy", "skuCode")
        }

        // when & then
        runBlocking {
            getDailyCalculations(CalculationRequest.from(params)).apply {
                assertEquals(HttpStatusCode.OK, status)

                val objectMapper = ObjectMapper().findAndRegisterModules()
                val expectedResponse = """
                    {
                        "calculations":[
                            {
                                "skuId":"${givenCulinarySku.id}",
                                "skuCode":"${givenCulinarySku.code}",
                                "unusableStock":1,
                                "usableStock":0,
                                "incomingPos":{
                                    "amount":10
                                },
                                "inbound":{
                                    "amount":1
                                },
                                "netNeeds":null,
                                "poDueIn":null,
                                "netNeeds": 0,
                                "pos":["$poRef"],
                                "consumption":1,
                                "actualConsumption":0,
                                "dailyNeed":1,
                                "closingStock":0,
                                "skuName":"${givenCulinarySku.name}",
                                "skuCategories":"${givenCulinarySku.getCategory()}",
                                "dcCode": "$FIXTURE_DC",
                                "atRisk":true,
                                "week":"$dcWeek",
                                "date":"$date",
                                "safetyStock": null,
                                "strategy": "$DEFAULT_TEST_STRATEGY",
                                "safetyStockNeeds": null,
                                "storageStock": null,
                                "stagingStock": null,
                                "subIn": null,
                                "subOut": null,
                                "consumptionDetails": {"consumption":[],"prekitting":null,"substituted":null},
                                "unusableStockDetails": null,
                                "uom": "UNIT",
                                "status": "PROCESSED",
                                "expectedInboundTransferOrders": [],
                                "expectedInboundTransferOrdersQuantity": 0,
                                "actualInboundTransferOrders": [],
                                "actualInboundTransferOrdersQuantity": 0,
                                "expectedOutboundTransferOrders": [],
                                "expectedOutboundTransferOrdersQuantity": 0
                            }
                        ],
                        "totalPages":1,
                        "page":1,
                        "totalSkusAtRiskCount": 1,
                        "totalSkusCount": 1
                    }
                """.let {
                    objectMapper.readValue<DailyCalculationsResponse>(it)
                }
                val actualResponse = objectMapper.readValue<DailyCalculationsResponse>(bodyAsText())
                assertEquals(expectedResponse, actualResponse)
            }
        }
    }

    @ParameterizedTest
    @MethodSource("substitutionValues")
    fun `should return calculation with substituted values`(
        subIn: Long,
        subOut: Long,
        expectedSubQty: Long?,
        day: LocalDate,
        subbed: SubbedType
    ) {
        // given
        val givenWeek = "2022-W01"
        val givenCulinarySku = firstCulinarySku()
        val calculationWithInboundShortage = withInboundShortages(day, givenCulinarySku)
        dsl.batchInsert(
            listOf(
                calculationWithInboundShortage,
            ),
        ).execute()
        insertSkuRecords(givenCulinarySku.toSkuSpecificationRecord())
        createDemand(day, givenCulinarySku, subbed, subIn, subOut)
        refreshView()
        val params = Parameters.build {
            set("dcCode", FIXTURE_DC)
            set("weeks", givenWeek)
            set("additionalFilters", WITH_INBOUND_SHORTAGE.filterName())
            set("sortBy", "skuCode")
        }

        // when & then
        runBlocking {
            getDailyCalculations(CalculationRequest.from(params)).apply {
                assertEquals(HttpStatusCode.OK, status)
                val emptyDailyCalculation = emptyCalculations(day, givenCulinarySku, expectedSubQty)
                val withInboundShortage = emptyDailyCalculation
                    .copy(
                        date = day,
                        incomingPos = ExpectedInboundResponse(calculationWithInboundShortage.expectedInbound),
                        inbound = ActualInboundResponse(BigDecimal(1)),
                        atRisk = true,
                    )
                val expectedResponse = DailyCalculationsResponse(
                    totalPages = 1,
                    page = 1,
                    calculations = listOf(
                        withInboundShortage,
                    ),
                    totalSkusAtRiskCount = 1,
                    totalSkusCount = 1,
                )
                val response = objectMapper.readValue(bodyAsText(), DailyCalculationsResponse::class.java)
                assertEquals(expectedResponse, response)
            }
        }
    }

    private fun assertSubstituted(
        response: DailyCalculationsResponse,
        subOutSku: CulinarySkuFixture
    ) {
        assertEquals(100L, response.calculations.first { it.skuId == subOutSku.id }.consumptionDetails?.substituted)
    }

    private fun assertPrekitting(
        response: DailyCalculationsResponse,
        subOutSku: CulinarySkuFixture
    ) {
        assertEquals(100L, response.calculations.first { it.skuId == subOutSku.id }.consumptionDetails?.prekitting)
    }

    private fun assertBrand(
        response: DailyCalculationsResponse,
        subInSku: CulinarySkuFixture
    ) {
        assertEquals(
            200L,
            response.calculations.first {
                it.skuId == subInSku.id
            }.consumptionDetails?.consumption?.first { it.brand == HELLO_FRESH }?.qty,
        )
        assertEquals(
            200L,
            response.calculations.first {
                it.skuId == subInSku.id
            }.consumptionDetails?.consumption?.first { it.brand == EVERY_PLATE }?.qty,
        )
    }

    private fun createDemand(
        day: LocalDate,
        culinarySkuFixture: CulinarySkuFixture,
        subbed: SubbedType,
        substitutedInQty: Long,
        substitutedOutQty: Long
    ) {
        val demandRecord = DemandRecord(
            culinarySkuFixture.id,
            "VE",
            day,
            LocalDateTime.now(),
            LocalDateTime.now(),
            LocalDateTime.now(),
            subbed,
            BigDecimal(0),
            substitutedInQty,
            substitutedOutQty,
            null,
            com.hellofresh.cif.api.schema.enums.Uom.UOM_UNIT,
        )
        dsl.executeInsert(demandRecord)
    }

    private fun emptyCalculations(
        date: LocalDate,
        givenCulinarySku: CulinarySkuFixture,
        substituted: Long? = null
    ) = DailyCalculationResponse(
        date = date,
        skuId = givenCulinarySku.id,
        skuCode = givenCulinarySku.code,
        skuCategories = givenCulinarySku.getCategory(),
        skuName = givenCulinarySku.name,
        usableStock = BigDecimal(0),
        unusableStock = BigDecimal(0),
        incomingPos = ExpectedInboundResponse(BigDecimal(0)),
        inbound = ActualInboundResponse(BigDecimal(0)),
        consumption = BigDecimal(0),
        dailyNeed = BigDecimal(0),
        closingStock = BigDecimal(0),
        pos = emptyList(),
        actualConsumption = BigDecimal(0),
        dcCode = FIXTURE_DC,
        atRisk = false,
        week = "2022-W01",
        storageStock = null,
        strategy = DEFAULT_TEST_STRATEGY,
        stagingStock = null,
        consumptionDetails = ConsumptionDetail(
            consumption = emptyList(),
            prekitting = null,
            substituted = substituted,
        ),
        netNeeds = BigDecimal(0),
        uom = SkuQuantityMapper.mapSkuUOMToUomEnum(SkuUOM.UOM_UNIT),
        status = CalculationStatus.PROCESSED,
        expectedInboundTransferOrders = emptyList(),
        expectedInboundTransferOrdersQuantity = ZERO,
        actualInboundTransferOrders = emptyList(),
        actualInboundTransferOrdersQuantity = ZERO,
        expectedOutboundTransferOrders = emptyList(),
        expectedOutboundTransferOrdersQuantity = ZERO,
    )

    private fun withInventoryShortages(date: LocalDate, culinarySku: CulinarySkuFixture): CalculationRecord {
        val record = emptyCalculationRecord("2022-W01", date, culinarySku)
        return withInventoryShortages(record)
    }

    private fun withInventoryShortages(record: CalculationRecord): CalculationRecord {
        record.dailyNeeds = ONE
        return record
    }

    private fun withInboundShortages(date: LocalDate, culinarySku: CulinarySkuFixture): CalculationRecord {
        val record = emptyCalculationRecord("2022-W01", date, culinarySku)
        return withInboundShortages(record)
    }

    private fun withInboundShortages(record: CalculationRecord): CalculationRecord {
        record.actualInbound = ONE
        record.expectedInbound = TEN
        return record
    }

    private fun withConsumptionOnly(date: LocalDate, culinarySku: CulinarySkuFixture): CalculationRecord {
        val record = emptyCalculationRecord("2022-W01", date, culinarySku)
        return withConsumptionOnly(record)
    }

    private fun withConsumptionOnly(record: CalculationRecord): CalculationRecord {
        record.demanded = ONE
        return record
    }

    private fun withNoConsumptionOnly(
        week: String,
        date: LocalDate,
        culinarySku: CulinarySkuFixture,
    ) =
        emptyCalculationRecord(week, date, culinarySku).apply {
            demanded = ZERO
            openingStock = ONE
            strategy = DEFAULT_TEST_STRATEGY
        }

    private fun withExpiry(record: CalculationRecord, expiredQty: BigDecimal) = record.apply {
        expired = expiredQty
    }

    private fun refreshView() {
        dsl.query("refresh materialized view calculation_substitution_view").execute()
    }

    companion object {
        @JvmStatic
        fun substitutionValues(): Stream<Arguments> = Stream.of(
            Arguments.of(10L, 0L, 10L, LocalDate.now().minusDays(1), SUB_IN),
            Arguments.of(0L, 10L, -10L, LocalDate.now().minusDays(1), SUB_OUT),
            Arguments.of(20L, 40L, 20L, LocalDate.now().minusDays(1), SUB_IN_AND_OUT),
            Arguments.of(20L, 20L, null, LocalDate.now().minusDays(1), NONE),
        )
    }
}
