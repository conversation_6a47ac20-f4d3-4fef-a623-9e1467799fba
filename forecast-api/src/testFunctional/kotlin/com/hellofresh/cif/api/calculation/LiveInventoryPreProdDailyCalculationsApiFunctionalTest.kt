package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.CalculationServiceMapper.toDailyCalculation
import com.hellofresh.cif.api.calculation.InventoryRefreshType.LIVE
import com.hellofresh.cif.api.calculation.SortBy.SKU_CODE
import com.hellofresh.cif.api.calculation.db.CalculationRecord
import com.hellofresh.cif.api.calculation.db.CommaSeparatedToSetJsonDeserializer
import com.hellofresh.cif.api.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.api.schema.tables.records.LiveInventoryPreProductionCalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import java.math.BigDecimal
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

class LiveInventoryPreProdDailyCalculationsApiFunctionalTest : CalculationFunctionalTest() {

    @Test fun `Should return live inventory pre production calculations if consumptionDaysAhead is 1 and live inventory refresh type`() {
        val dc = "VE"
        val week = "2022-W08"
        val count = 12

        val testData = insertTestData(count, dc, week)
        val liveInventoryPreProdDBRecords = testData.keys.sortedBy { it.date }
        runBlocking {
            getDailyCalculationsOkResponse(
                CalculationRequest(
                    listOf(dc),
                    listOf(week),
                    Page(page = 1, skuCount = count),
                    consumptionDaysAhead = 1,
                    inventoryRefreshType = LIVE,
                    sortBy = SKU_CODE,
                ),
            ).also {
                val calculationResults = it.calculations
                assertEquals(liveInventoryPreProdDBRecords.size, it.calculations.size)
                liveInventoryPreProdDBRecords.indices.forEach {
                    val record = mapToCommonCalculation(
                        liveInventoryPreProdDBRecords[it],
                        testData[liveInventoryPreProdDBRecords[it]]!!,
                    )
                    val dailyCalculation = toDailyCalculation(record, usableInventoryEvaluator)
                    val resource = calculationResponseMapper.toDailyCalculationResponse(dailyCalculation)
                    assertEquals(resource, calculationResults.find { v -> v.skuId == record.cskuId }!!)
                }
            }
        }
    }

    private fun insertTestData(
        n: Int,
        dc: String,
        week: String
    ): Map<LiveInventoryPreProductionCalculationRecord, SkuSpecificationRecord> {
        val records = (0 until n).map {
            LiveInventoryPreProductionCalculationRecord().apply {
                dcCode = dc
                productionWeek = week
                cskuId = UUID.randomUUID()
                date = LocalDate.now().plusDays(it.toLong())
                expired = randomQty()
                openingStock = randomQty()
                storageStock = randomQty()
                stagingStock = randomQty()
                openingStock = randomQty()
                demanded = randomQty()
                actualConsumption = randomQty()
                present = randomQty()
                closingStock = randomQty()
                actualInbound = randomQty()
                expectedInbound = randomQty()
                actualInboundPo = UUID.randomUUID().toString()
                expectedInboundPo = UUID.randomUUID().toString()
                dailyNeeds = randomQty()
                safetystock = randomQty()
                safetystockNeeds = randomQty()
                netNeeds = randomQty()
                uom = UOM_UNIT
            }
        }.associateWith {
            SkuSpecificationRecord()
                .apply {
                    id = it.cskuId
                    this.code = it.cskuId.toString()
                    name = it.cskuId.toString()
                    this.category = it.cskuId.toString().take(3)
                    coolingType = ""
                    packaging = ""
                    acceptableCodeLife = 0
                    market = ""
                }
        }

        dsl.batchInsert(records.map { it.key }).execute()
        insertSkuRecords(records.map { it.value })
        return records
    }

    private fun mapToCommonCalculation(
        record: LiveInventoryPreProductionCalculationRecord,
        skuSpecificationRecord: SkuSpecificationRecord
    ) = CalculationRecord(
        record.dcCode,
        record.cskuId,
        code = skuSpecificationRecord.code,
        name = skuSpecificationRecord.name,
        category = skuSpecificationRecord.category,
        record.productionWeek,
        record.date,
        uom = record.uom,
        record.expired,
        record.openingStock,
        record.storageStock,
        record.stagingStock,
        stockUpdate = null,
        record.demanded,
        record.present,
        record.closingStock,
        record.actualInbound,
        record.expectedInbound,
        CommaSeparatedToSetJsonDeserializer.deserialize(record.actualInboundPo),
        CommaSeparatedToSetJsonDeserializer.deserialize(record.expectedInboundPo),
        record.dailyNeeds,
        coolingType = skuSpecificationRecord.coolingType,
        packaging = skuSpecificationRecord.packaging,
        actualConsumption = record.actualConsumption,
        skuAtRisk = record.dailyNeeds > BigDecimal.ZERO || (record.date < LocalDate.now() && record.expectedInbound > record.actualInbound),
        safetyStock = record.safetystock,
        strategy = DEFAULT_TEST_STRATEGY,
        safetyStockNeeds = record.safetystockNeeds,
        poDueIn = record.maxPurchaseOrderDueIn?.toLong(),
        netNeeds = record.netNeeds,
        acceptableCodeLife = skuSpecificationRecord.acceptableCodeLife,
    )
}
