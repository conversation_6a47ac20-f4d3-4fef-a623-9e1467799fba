package com.hellofresh.cif.api.inventory

import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.inventory.lib.schema.tables.records.InventorySnapshotRecord
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryValue
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_WASTE
import com.hellofresh.inventory.models.SHORT_SHELF_LIFE_UNUSABLE_REASON
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.Test
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.junit.jupiter.api.Assertions

class InventoryRepositoryTest : FunctionalTest() {

    @Test
    fun `should properly map usable, unusable and expired stocks`() {
        // given
        val dcCode = "VE"
        val skuId = UUID.randomUUID()
        val date = LocalDate.now()

        val usable = Inventory(
            SkuQuantity.fromBigDecimal(BigDecimal(10L)),
            date.plusDays(10),
            Location("", LOCATION_TYPE_STAGING, null),
        )
        val unusable = Inventory(
            SkuQuantity.fromBigDecimal(BigDecimal(20L)),
            date.plusDays(10),
            Location("", LOCATION_TYPE_WASTE, null),
        )
        val expired = Inventory(
            SkuQuantity.fromBigDecimal(BigDecimal(30L)),
            date.plusDays(1),
            Location("", LOCATION_TYPE_STAGING, null),
        )
        val inventories = listOf(usable, unusable, expired)
        insertFixture(dcCode, skuId, date, InventoryValue(inventories))

        // when
        val result = runBlocking { skuInventoryDetailService.fetchCurrentInventory(skuId, setOf(dcCode)) }

        // then
        val expectedUsable = SkuDetail(
            dcCode = dcCode,
            expiryDate = usable.expiryDate,
            isUsable = true,
            quantity = usable.qty.getValue().toLong(),
        )
        val expectedUnusable = SkuDetail(
            dcCode = dcCode,
            expiryDate = unusable.expiryDate,
            isUsable = false,
            quantity = unusable.qty.getValue().toLong(),
            unusableStockReason = "Waste",
        )
        val expectedExpired = SkuDetail(
            dcCode = dcCode,
            expiryDate = expired.expiryDate,
            isUsable = false,
            quantity = expired.qty.getValue().toLong(),
            unusableStockReason = SHORT_SHELF_LIFE_UNUSABLE_REASON,
        )
        val expected = listOf(expectedUsable, expectedUnusable, expectedExpired)
        Assertions.assertIterableEquals(expected.sortedBy { it.quantity }, result.sortedBy { it.quantity })
    }

    private fun insertFixture(
        dcCode: String,
        skuId: UUID,
        date: LocalDate?,
        inventoryInfo: InventoryValue
    ) {
        insertSkus(setOf(skuId))

        val inventoryRecord = InventorySnapshotRecord().apply {
            this.snapshotId = UUID.randomUUID()
            value = JSONB.jsonb(objectMapper.writeValueAsString(inventoryInfo))
            this.dcCode = dcCode
            this.skuId = skuId
            this.date = date
            this.createdAt = LocalDateTime.now()
            this.updatedAt = LocalDateTime.now()
            this.snapshotTime = OffsetDateTime.now(ZoneOffset.UTC)
        }
        val dcConfigRecord = DcConfigRecord().apply {
            this.dcCode = dcCode
            market = "DACH"
            productionStart = "MONDAY"
            cleardown = "FRIDAY"
            zoneId = ZoneOffset.UTC.id
            enabled = true
            hasCleardown = true
            recordTimestamp_ = LocalDateTime.now()
            createdAt = LocalDateTime.now()
            updatedAt = LocalDateTime.now()
        }
        dsl.batchInsert(inventoryRecord, dcConfigRecord).execute()
    }

    companion object {
        private val objectMapper = ObjectMapper()
            .findAndRegisterModules()
    }
}
