package com.hellofresh.cif.api.calculation.db

import com.hellofresh.cif.api.calculation.AllPages
import com.hellofresh.cif.api.calculation.CalculationFunctionalTest
import com.hellofresh.cif.api.calculation.CalculationRequest
import com.hellofresh.cif.api.calculation.fixtures.Default
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.PurchaseOrderRecord
import com.hellofresh.cif.api.schema.tables.records.PurchaseOrderSkuRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.api.schema.tables.records.SupplierRecord
import com.hellofresh.cif.distributionCenter.models.DcWeek
import java.math.BigDecimal
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

class CalculationRepositoryForSuppliersTest : CalculationFunctionalTest() {
    @Test
    fun `should return calculations having POs with suppliers using dc timezone`() {
        // given
        val dcCode = "DC"
        val zoneId = ZoneId.of("Australia/Adelaide")
        createDcConfig(dcCode) {
            productionStart = FRIDAY.name
            this.zoneId = zoneId.id
        }
        val supplierId = UUID.randomUUID()
        val skuId = UUID.randomUUID()
        val dcWeek = DcWeek(LocalDate.now(), FRIDAY)
        val poNumber = "2022111"
        val poRef = "2022111_01"
        val startDateInDcWeek = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId)
        val startTime = startDateInDcWeek
            .minusDays(1)
            .atTime(22, 0).atZone(UTC)

        val calculationRecords = (0..6).map {
            Default.calculationRecord {
                this.cskuId = skuId
                this.dcCode = dcCode
                this.date = dcWeek.getStartDateInDcWeek(FRIDAY, zoneId).plusDays(it.toLong())
                productionWeek = dcWeek.value
                expectedInboundPo = ""
                actualInboundPo = poNumber
            }
        }

        val poRecord = PurchaseOrderRecord(
            poRef, UUID.randomUUID(), poNumber, dcCode,
            startTime.toOffsetDateTime(), startTime.plusHours(2).toOffsetDateTime(), supplierId,
            OffsetDateTime.now(UTC), OffsetDateTime.now(UTC), null, null
        )
        val poSkuRecord = PurchaseOrderSkuRecord(skuId, BigDecimal(99), poRecord.poNumber, Uom.UOM_UNIT)
        val skuSpecRecord = SkuSpecificationRecord().apply {
            id = skuId
            name = "Name1"
            code = "PRO-00-00000-1"
            category = "PHF"
            acceptableCodeLife = 1
            coolingType = ""
            packaging = ""
            market = "dach"
            uom = Uom.UOM_LBS
        }
        val supplierRecord = SupplierRecord(supplierId, null, "Sup Name")

        val po = newPurchaseOrder(
            UUID.randomUUID(),
            poNumber,
            supplierId,
            "S1",
            skuId,
            dcCode,
            calculationRecords.first().date,
        )

        insertSkuRecords(skuSpecRecord)
        @Suppress("SpreadOperator")
        dsl.batchInsert(
            poRecord,
            poSkuRecord,
            supplierRecord,
            *calculationRecords.toTypedArray(),
        )
            .execute()
        dsl.query("refresh materialized view concurrently po_calculations_view").execute()

        // when
        val pageableCalculations = runBlocking {
            prodCalculationsRepo.fetchPageableCalculations(
                CalculationRequest(
                    dcCodes = listOf(dcCode),
                    weeks = listOf(dcWeek.value),
                    supplierIds = listOf(supplierId),
                    pageRequest = AllPages,
                    consumptionDaysAhead = 0,
                ),
            )
        }

        // then
        assertEquals(calculationRecords.size, pageableCalculations.calculationPage.size)
        val calcOnPoDeliveryTime = pageableCalculations.calculationPage.first {
            it.date == po.expectedDeliveryTimeslot?.startTime?.toLocalDate()
        }
        assertEquals(1, calcOnPoDeliveryTime.actualInboundPo?.size)
        assertEquals(po.number, calcOnPoDeliveryTime.actualInboundPo?.first())
    }
}
