package com.hellofresh.cif.api.fileupload

import com.hellofresh.cif.api.schema.enums.FileType
import com.hellofresh.cif.api.schema.enums.FileUploadStatus
import com.hellofresh.cif.api.schema.tables.records.FileUploadsRecord
import java.time.LocalDateTime
import java.time.ZoneOffset
import java.util.UUID

const val DACH_MARKET = "DACH"

fun getFileUploadStockInventoryData(marketParam: String) = getFileUploadData(marketParam, FileType.STOCK_INVENTORY)

fun getFileUploadStockUpdateData(marketParam: String) = getFileUploadData(marketParam, FileType.STOCK_UPDATE)

private fun getFileUploadData(marketParam: String, type: FileType) = FileUploadsRecord().apply {
    id = UUID.randomUUID()
    fileName = "test_file"
    market = marketParam
    dcs = arrayOf("VE")
    authorName = "Test"
    authorEmail = "<EMAIL>"
    createdAt = LocalDateTime.now(ZoneOffset.UTC)
    status = FileUploadStatus.IMPORTED
    message = ""
    fileType = type
}
