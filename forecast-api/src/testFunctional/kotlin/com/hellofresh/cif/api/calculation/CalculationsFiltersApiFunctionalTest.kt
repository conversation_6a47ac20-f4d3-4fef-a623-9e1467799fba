package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus
import com.hellofresh.cif.models.purchaseorder.PoStatus.APPROVED
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.Supplier
import com.hellofresh.cif.models.purchaseorder.TimeRange
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import java.time.ZonedDateTime
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking

@Suppress("LargeClass", "MagicNumber")
internal class CalculationsFiltersApiFunctionalTest : CalculationFunctionalTest() {
    private val dcCodeVE = "VE"
    private val dcCodesVE = listOf(dcCodeVE)
    private val dcWeek = "2022-W08"

    @Test fun `returns 400 Bad request when week is not given`() {
        runBlocking {
            getCalculationFilters(CalculationFilterRequest(dcCodesVE, listOf(), consumptionDaysAhead = 0)).apply {
                assertEquals(HttpStatusCode.BadRequest, status)
                assertEquals("""{"reason":"Required Parameter: weeks is not specified"}""", bodyAsText())
            }
        }
    }

    @Test
    fun `unauthorized HTTP response when no jwt token is given for daily calculations`() {
        runBlocking {
            getCalculationFilters(
                CalculationFilterRequest(dcCodesVE, listOf(), consumptionDaysAhead = 0),
                jwtEnabled = false,
            ).apply {
                assertEquals(HttpStatusCode.Unauthorized, status)
            }
        }
    }

    @Test
    fun `unauthorized HTTP response when no jwt token is given for weekly calculations`() {
        runBlocking {
            getCalculationFilters(
                CalculationFilterRequest(dcCodesVE, listOf(), consumptionDaysAhead = 0),
                jwtEnabled = false,
            ).apply {
                assertEquals(HttpStatusCode.Unauthorized, status)
            }
        }
    }

    @Test fun `calculation filters on skuCode and skuCategory`() {
        val code = "some code"
        val category = "PTN"
        // We want to have more than 1 page and have some offset in the last page
        val count = 2
        insertTestData(count, dcCodeVE, dcWeek, code = code, category = category)

        val filterData = getCalculationFiltersOkResponse(
            CalculationFilterRequest(
                dcCodes = dcCodesVE,
                weeks = listOf(dcWeek),
                skuCodes = listOf(code),
                consumptionDaysAhead = 0,
            ),
        )

        assertEquals(1, filterData.skuSet.size)
        assertEquals(code, filterData.skuSet.first().skuCode)

        val filterDataWithCategory = getCalculationFiltersOkResponse(
            CalculationFilterRequest(
                dcCodes = dcCodesVE,
                weeks = listOf(dcWeek),
                skuCategories = listOf(category),
                consumptionDaysAhead = 0,
            ),
        )

        assertEquals(1, filterDataWithCategory.skuSet.size)
        assertEquals(code, filterDataWithCategory.skuSet.first().skuCode)
        assertEquals(category, filterDataWithCategory.ingredientCategories.first())
    }

    @Test fun `calculation filters using location-in-box filters`() {
        val locationInBox = "Assembly-Cool Pouch"
        val dailyCalculationsCount = 1
        val expectedSku = Sku(UUID.randomUUID().toString(), UUID.randomUUID())
        val sku2 = Sku(UUID.randomUUID().toString(), UUID.randomUUID())
        val sku3 = Sku(UUID.randomUUID().toString(), UUID.randomUUID())
        val skuToLocationInBox = mapOf(
            expectedSku to locationInBox,
            sku2 to "Meal Kit",
            sku3 to "Pack Chilled",
        )
        skuToLocationInBox.forEach {
            insertSkuCalculations(it.key, dailyCalculationsCount, dcCodeVE, dcWeek, it.value)
        }

        val skuFetchedUsingLocationInBox =
            getCalculationFiltersOkResponse(
                CalculationFilterRequest(
                    dcCodesVE,
                    listOf(dcWeek),
                    listOf(),
                    consumptionDaysAhead = 0,
                    locationInBox = listOf(locationInBox),
                ),
            )

        assertEquals(1, skuFetchedUsingLocationInBox.skuSet.size)
        val actualSku = skuFetchedUsingLocationInBox.skuSet.first()
        assertEquals(expectedSku.skuId, actualSku.skuId)
        assertEquals(expectedSku.skuCode, actualSku.skuCode)
        assertEquals("sku name ${expectedSku.skuId}", actualSku.skuName)
        assertEquals(1, skuFetchedUsingLocationInBox.locationInBox.size)
        assertEquals(locationInBox, skuFetchedUsingLocationInBox.locationInBox.first())
    }

    @Test
    fun `calculation filters for selected suppliers`() {
        val sku = Sku("PRO-1111", UUID.randomUUID())
        insertSkuCalculations(sku, 1, FIXTURE_DC, dcWeek)
        val supplierId = UUID.randomUUID()
        val po = PurchaseOrder(
            "po",
            "po-ref",
            UUID.randomUUID(),
            FIXTURE_DC,
            TimeRange(ZonedDateTime.now(), ZonedDateTime.now().plusMinutes(1)),
            Supplier(supplierId, ""),
            listOf(
                PurchaseOrderSku(
                    sku.skuId,
                    SkuQuantity.fromLong(99),
                    deliveries = listOf(
                        DeliveryInfo("ID1", ZonedDateTime.now(), DeliveryInfoStatus.CLOSED, SkuQuantity.fromLong(99))
                    ),
                ),
            ),
            poStatus = APPROVED
        )
        insertPurchaseOrderAndSupplier(po)
        val req = CalculationFilterRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(dcWeek),
            consumptionDaysAhead = 0,
            supplierIds = listOf(supplierId),
        )

        // when
        val response = getCalculationFiltersOkResponse(req)

        // then
        assertEquals(1, response.skuSet.size)
        assertEquals(1, response.suppliers.size)
        assertEquals(supplierId, response.suppliers.first().id)

        val responseSupplierIdUnknown = getCalculationFiltersOkResponse(req.copy(supplierIds = listOf(UUID(0, 0))))
        // then
        assertEquals(0, responseSupplierIdUnknown.skuSet.size)
    }

    @Test
    fun `calculation filters return list of active suppliers`() {
        val sku = Sku("PRO-1112", UUID.randomUUID())
        insertSkuCalculations(sku, 1, FIXTURE_DC, dcWeek)
        val supplierId = UUID.randomUUID()

        val po = PurchaseOrder(
            "po",
            "po-ref",
            UUID.randomUUID(),
            FIXTURE_DC,
            TimeRange(ZonedDateTime.now(), ZonedDateTime.now().plusMinutes(1)),
            Supplier(supplierId, "Test Supplier"),
            listOf(
                PurchaseOrderSku(
                    sku.skuId,
                    SkuQuantity.fromLong(99),
                    deliveries = listOf(
                        DeliveryInfo("ID1", ZonedDateTime.now(), DeliveryInfoStatus.CLOSED, SkuQuantity.fromLong(99))
                    ),
                ),
            ),
            poStatus = APPROVED
        )
        insertPurchaseOrderAndSupplier(po)
        val req = CalculationFilterRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(dcWeek),
            consumptionDaysAhead = 0,
            supplierIds = emptyList(),
        )

        // when filtering without sending any active supplier parameter
        val response = getCalculationFiltersOkResponse(req)

        // then
        assertEquals(1, response.skuSet.size)
        assertEquals(2, response.activeSuppliers.size)
        assertEquals(supplierId, response.suppliers.first().id)
    }

    @Test
    fun `calculation filters using activeSuppliersName query parameter`() {
        val sku = Sku("PRO-1113", UUID.randomUUID())
        insertSkuCalculations(sku, 1, FIXTURE_DC, dcWeek)
        val supplierId = UUID.randomUUID()

        val po = PurchaseOrder(
            "po",
            "po-ref",
            UUID.randomUUID(),
            FIXTURE_DC,
            TimeRange(ZonedDateTime.now(), ZonedDateTime.now().plusMinutes(1)),
            Supplier(supplierId, "Test Supplier"),
            listOf(
                PurchaseOrderSku(
                    sku.skuId,
                    SkuQuantity.fromLong(99),
                    deliveries = listOf(
                        DeliveryInfo("ID1", ZonedDateTime.now(), DeliveryInfoStatus.CLOSED, SkuQuantity.fromLong(99))
                    ),
                ),
            ),
            poStatus = APPROVED
        )
        insertPurchaseOrderAndSupplier(po)

        val req = CalculationFilterRequest(
            dcCodes = listOf(FIXTURE_DC),
            weeks = listOf(dcWeek),
            consumptionDaysAhead = 0,
            supplierIds = emptyList(),
            activeSupplierNames = setOf("Test Supplier"),
        )

        // when filtering with a supplier with records and will get list of active suppliers
        val response = getCalculationFiltersOkResponse(req)

        // then
        assertEquals(1, response.skuSet.size)
        assertEquals(2, response.activeSuppliers.size)

        // When filtering with a supplier without any records
        val req2 = req.copy(activeSupplierNames = setOf("Test Supplier 3"))
        // when
        val response2 = getCalculationFiltersOkResponse(req2)

        // then
        assertEquals(0, response2.skuSet.size)
    }
}
