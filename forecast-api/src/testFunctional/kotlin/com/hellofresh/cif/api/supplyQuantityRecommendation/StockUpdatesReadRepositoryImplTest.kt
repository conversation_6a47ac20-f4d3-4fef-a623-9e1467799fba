package com.hellofresh.cif.api.supplyQuantityRecommendation

import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.models.DateRange
import java.math.BigDecimal
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class StockUpdatesReadRepositoryImplTest : FunctionalTest() {
    private val testDcCode = "VE"
    private val testWeek = "2024-W21"
    private val testDate = DcWeek(testWeek).getStartDateInDcWeek(MONDAY, ZoneOffset.UTC)
    private val testSkuId = UUID.randomUUID()

    @Test
    fun `fetchStockUpdates should return empty list when no records found`() {
        val stockUpdates = runBlocking {
            stockUpdatesReadRepository.fetchStockUpdates(testDcCode, DateRange.oneDay(LocalDate.now()))
        }
        assertTrue { stockUpdates.isEmpty() }
    }

    @Test
    fun `fetchStockUpdates should return matching latest version records by dc code and date range`() {
        createStockUpdateRecord(testDcCode, testDate, testWeek, BigDecimal(100), testSkuId, 1)
        createStockUpdateRecord(testDcCode, testDate, testWeek, BigDecimal(1000), testSkuId, 2)
        val createStockUpdateRecord =
            createStockUpdateRecord(testDcCode, testDate, testWeek, BigDecimal(2000), testSkuId, 3)

        val stockUpdates = runBlocking {
            stockUpdatesReadRepository.fetchStockUpdates(testDcCode, DateRange(testDate, testDate))
        }
        assertEquals(1, stockUpdates.size)
        assertEquals(stockUpdates[0].skuId, testSkuId)
        assertEquals(stockUpdates[0].quantity.getValue(), createStockUpdateRecord.quantity)
    }

    @Test
    fun `fetchStockUpdates should return matching records by dc code and date range`() {
        val stockUpdateRecord1 = createStockUpdateRecord(testDcCode, testDate, testWeek, BigDecimal(100), testSkuId, 1)
        val stockUpdateRecord2 =
            createStockUpdateRecord(testDcCode, testDate.plusDays(1), testWeek, BigDecimal(1000), testSkuId, 1)
        createStockUpdateRecord(testDcCode, testDate.plusDays(2), testWeek, BigDecimal(2000), testSkuId, 1)

        val stockUpdates = runBlocking {
            stockUpdatesReadRepository.fetchStockUpdates(testDcCode, DateRange(testDate, testDate.plusDays(1)))
        }
        assertEquals(2, stockUpdates.size)
        assertTrue { stockUpdates.all { it.skuId == testSkuId } }
        assertEquals(
            stockUpdateRecord1.quantity,
            stockUpdates.first { it.date == stockUpdateRecord1.date }.quantity.getValue()
        )
        assertEquals(
            stockUpdateRecord2.quantity,
            stockUpdates.first { it.date == stockUpdateRecord2.date }.quantity.getValue()
        )
    }

    @Test
    fun `fetchStockUpdates should return empty list if the dc code matches and not the week`() {
        createStockUpdateRecord(testDcCode, testDate, testWeek, BigDecimal(100), testSkuId, 1)
        val stockUpdates = runBlocking {
            stockUpdatesReadRepository.fetchStockUpdates(testDcCode, DateRange.oneDay(testDate.minusDays(1)))
        }
        assertTrue { stockUpdates.isEmpty() }
    }

    @Test
    fun `fetchStockUpdates should return empty list if the week matches and not the dc code`() {
        createStockUpdateRecord(testDcCode, testDate, testWeek, BigDecimal(100), testSkuId, 1)

        val stockUpdates = runBlocking {
            stockUpdatesReadRepository.fetchStockUpdates("non-existing-dc-code", DateRange.oneDay(testDate))
        }
        assertTrue { stockUpdates.isEmpty() }
    }
}
