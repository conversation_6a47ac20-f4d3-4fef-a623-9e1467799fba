package com.hellofresh.cif.api.supplyQuantityRecommendation

import com.auth0.jwt.JWT
import com.auth0.jwt.JWTCreator
import com.auth0.jwt.algorithms.Algorithm
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.fixtures.default
import com.hellofresh.cif.api.calculation.generated.model.SkuRiskRatingEnum
import com.hellofresh.cif.api.calculation.generated.model.SupplyQuantityRecommendationRequest
import com.hellofresh.cif.api.calculation.generated.model.SupplyQuantityRecommendationResponse
import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.api.configuration.DcConfiguration
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.Tables.SAFETY_STOCK_CONF
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.api.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION
import com.hellofresh.cif.api.schema.Tables.SUPPLY_QUANTITY_RECOMMENDATION_CONF
import com.hellofresh.cif.api.schema.tables.records.SafetyStocksRecord
import com.hellofresh.cif.api.supplyQuantityRecommendation.model.SkuDetail
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SupplyQuantityRecommendationRepositoryImpl
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.safetystock.Configuration
import com.hellofresh.cif.safetystock.SafetyStockConfiguration.Companion.DEFAULT_RISK_MULTIPLIER
import com.hellofresh.cif.safetystock.SafetyStockConfiguration.Companion.DEFAULT_SKU_RISK_RATING
import com.hellofresh.cif.safetystock.repository.SafetyStockConfigurationRepository
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.sqr.repository.SupplyQuantityRecommendationConfigRepository
import io.ktor.client.request.HttpRequestBuilder
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType.Application
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.auth.AuthScheme
import io.ktor.http.auth.HttpAuthHeader
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.withContext
import org.jooq.JSONB
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.ValueSource

class SupplyQuantityRecommendationApiTest : FunctionalTest() {
    private val testDcCode = "VE"
    private val testWeek = "2024-W21"
    private val testMarket = "DE"
    private val testSkuId = UUID.randomUUID()
    private val testAuthorEmail = UUID.randomUUID().toString()
    private val testAuthorName = UUID.randomUUID().toString()
    private val sqrConfigService = SQRConfigService(
        sqrConfigRepositoryImpl,
    )
    private val dcConfigServiceMock = mockk<DcConfigService>(relaxed = true)
    private val configService = mockk<ConfigService>(relaxed = true)
    private val supplyQuantityRecommendationRepository =
        SupplyQuantityRecommendationRepositoryImpl(dsl, dcConfigServiceMock)
    private val supplyQuantityRecommendationConfigRepository = SupplyQuantityRecommendationConfigRepository(dsl)
    private val supplyQuantityRecommendationService = SupplyQuantityRecommendationService(
        supplyQuantityRecommendationRepository,
        stockUpdatesReadRepository,
        supplyQuantityRecommendationConfigRepository,
        SafetyStockRepository(dsl),
        SafetyStockConfigurationRepository(dsl),
        dcConfigServiceMock,
    )
    private val jwksURI = "https://test.com/"

    private val jwtCredentials = JwtCredentials(jwtSecret, "test", "test", "testClient", jwksURI)
    private val today = LocalDate.now()
    private val productionStartClearDownDay = LocalDate.now().dayOfWeek
    private val currentWeek = DcWeek(today, productionStartClearDownDay)

    @BeforeEach
    internal fun setUp() {
        coEvery {
            dcConfigServiceMock.dcConfigurations[testDcCode]
        } returns DistributionCenterConfiguration.default().copy(zoneId = ZoneId.of("UTC"))
        coEvery { configService.fetchByMarket(any()) } returns mapOf(
            testDcCode to
                DcConfiguration(
                    currentWeek.toString(),
                    testMarket,
                    productionStartClearDownDay,
                    productionStartClearDownDay,
                    ZoneId.of("UTC"),
                    null,
                    false,
                    poCutoffTime = LocalTime.now(),
                ),
        )
    }

    @ParameterizedTest
    @ValueSource(ints = [0, 10])
    fun `should create a new supply quantity recommendation configuration including when safetyMultiplier is zero`(
        safetyMultiplier: Int
    ) {
        val sqrRequest = SupplyQuantityRecommendationRequest(
            dcCode = "VE",
            week = currentWeek.toString(),
            skuId = UUID.randomUUID(),
            recommendationEnabled = true,
            multiWeekEnabled = true,
            safetyMultiplier = BigDecimal(safetyMultiplier),
            skuRiskRating = SkuRiskRatingEnum.HIGH,
        )

        runBlocking {
            post(
                "/supply-quantity-recommendation/config",
                testAuthorEmail,
                testAuthorName,
                testSuperManagerRole,
                sqrRequest.toRequestBody(),
            ) {
                assertEquals(HttpStatusCode.Created, this.status)
                val sqrConfigRecord = withContext(Dispatchers.IO) {
                    dsl.selectFrom(SUPPLY_QUANTITY_RECOMMENDATION_CONF)
                        .where(SUPPLY_QUANTITY_RECOMMENDATION_CONF.SKU_ID.eq(sqrRequest.skuId))
                        .fetch()
                }.first()
                assertEquals(sqrRequest.dcCode, sqrConfigRecord.dcCode)
                assertEquals(sqrRequest.week, sqrConfigRecord.week)
                assertTrue(sqrConfigRecord.recommendationEnabled)
                assertTrue(sqrConfigRecord.multiWeekEnabled)

                val safetyStockConfigRecord = withContext(Dispatchers.IO) {
                    dsl.selectFrom(SAFETY_STOCK_CONF)
                        .where(SAFETY_STOCK_CONF.SKU_ID.eq(sqrRequest.skuId))
                        .fetch()
                }.first()
                assertEquals(sqrRequest.dcCode, safetyStockConfigRecord.dcCode)
                assertEquals(sqrRequest.week, safetyStockConfigRecord.week)
                assertEquals(sqrRequest.safetyMultiplier, safetyStockConfigRecord.riskMultiplier)
                assertEquals(sqrRequest.skuRiskRating?.value, safetyStockConfigRecord.skuRiskRating.toString())
            }
        }

        val updatedSqrRequest = SupplyQuantityRecommendationRequest(
            dcCode = "VE",
            week = currentWeek.toString(),
            skuId = sqrRequest.skuId,
            recommendationEnabled = true,
            multiWeekEnabled = true,
            safetyMultiplier = BigDecimal(safetyMultiplier),
            skuRiskRating = SkuRiskRatingEnum.CRITICAL,
        )

        runBlocking {
            post(
                "/supply-quantity-recommendation/config",
                testAuthorEmail,
                testAuthorName,
                testSuperManagerRole,
                updatedSqrRequest.toRequestBody(),
            ) {
                assertEquals(HttpStatusCode.Created, this.status)
                val safetyStockConfigRecord = withContext(Dispatchers.IO) {
                    dsl.selectFrom(SAFETY_STOCK_CONF)
                        .where(SAFETY_STOCK_CONF.SKU_ID.eq(sqrRequest.skuId))
                        .fetch()
                }.first()
                assertEquals(updatedSqrRequest.safetyMultiplier, safetyStockConfigRecord.riskMultiplier)
                assertEquals(updatedSqrRequest.skuRiskRating?.value, safetyStockConfigRecord.skuRiskRating.toString())
            }
        }
    }

    @Test
    fun `should return bad request if the given week is in the past`() {
        val pastWeek = "2024-W20"
        val sqrRequest = SupplyQuantityRecommendationRequest(
            dcCode = "VE",
            week = pastWeek,
            skuId = UUID.randomUUID(),
            recommendationEnabled = true,
            safetyMultiplier = BigDecimal.ONE,
        )

        runBlocking {
            post(
                "/supply-quantity-recommendation/config",
                testAuthorEmail,
                testAuthorName,
                testSuperManagerRole,
                sqrRequest.toRequestBody(),
            ) {
                assertEquals(HttpStatusCode.BadRequest, this.status)
                val responseText = this.bodyAsText()
                assertTrue(responseText.contains("Week $pastWeek is in the past"))
            }
        }
    }

    @Test
    fun `should return bad request if the given safety multiplier is negative number`() {
        val sqrRequest = SupplyQuantityRecommendationRequest(
            dcCode = "VE",
            week = "2024-W20",
            skuId = UUID.randomUUID(),
            recommendationEnabled = true,
            safetyMultiplier = BigDecimal(-1),
        )
        runBlocking {
            post(
                "/supply-quantity-recommendation/config",
                testAuthorEmail,
                testAuthorName,
                testSuperManagerRole,
                sqrRequest.toRequestBody(),
            ) {
                assertEquals(HttpStatusCode.BadRequest, this.status)
            }
        }
    }

    @Test
    fun `should return forbidden error if the user is not a super manager`() {
        val sqrRequest = SupplyQuantityRecommendationRequest(
            dcCode = "VE",
            week = "2024-W20",
            skuId = UUID.randomUUID(),
            recommendationEnabled = true,
            safetyMultiplier = BigDecimal(-1),
        )
        runBlocking {
            post(
                "/supply-quantity-recommendation/config",
                testAuthorEmail,
                testAuthorName,
                testOnlyManager,
                sqrRequest.toRequestBody(),
            ) {
                assertEquals(HttpStatusCode.Forbidden, this.status)
            }
        }
    }

    @Test
    fun `should get list of supply quantity recommendations`() {
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        val dcCode = "VE"
        val week = testWeek
        insertSkus(setOf(testSkuId))
        val skuDetail = SkuDetail.default(testSkuId)
        createStockUpdateRecord(
            testDcCode,
            DcWeek(testWeek).getStartDateInDcWeek(MONDAY, UTC),
            testWeek,
            BigDecimal(100),
            testSkuId,
            1,
        )
        val sqr = createSQRRecord(skuIdParam = testSkuId, weekParam = testWeek)
        val existingSQRConfigRecord = createSQRConfigRecord(
            weekParam = testWeek,
            skuIdParam = testSkuId,
        )
        val buffer = BigDecimal.TEN.pow(2)
        val safetyStockRecord = SafetyStocksRecord().apply {
            this.dcCode = sqr.dcCode
            this.skuId = sqr.skuId
            this.week = sqr.week
            this.safetyStock = sqr.safetyStock.toLong()
            this.configuration = JSONB.jsonb(objectMapper.writeValueAsString(Configuration(DEFAULT_RISK_MULTIPLIER, DEFAULT_SKU_RISK_RATING, buffer)))
        }
        dsl.batchInsert(sqr, existingSQRConfigRecord, safetyStockRecord).execute()

        runBlocking {
            get("/supply-quantity-recommendation", authorEmail, authorName, dcCode, week) {
                assertEquals(HttpStatusCode.OK, this.status)
                val sqrResponse = objectMapper.readValue(
                    bodyAsText(),
                    SupplyQuantityRecommendationResponse::class.java,
                )
                val sku = withContext(Dispatchers.IO) {
                    dsl.selectFrom(SKU_SPECIFICATION_VIEW)
                        .where(SKU_SPECIFICATION_VIEW.ID.eq(testSkuId))
                        .fetch()
                }.first()
                val sqrRecord = withContext(Dispatchers.IO) {
                    dsl.selectFrom(SUPPLY_QUANTITY_RECOMMENDATION)
                        .where(SUPPLY_QUANTITY_RECOMMENDATION.SKU_ID.eq(testSkuId))
                        .fetch()
                }.first()
                assertEquals(dcCode, sqrResponse.dcCode)
                assertEquals(week, sqrResponse.week)
                sqrResponse.skus.first().apply {
                    assertEquals(sku.id, skuId)
                    assertEquals(sku.code, skuCode)
                    assertEquals(sqrRecord.uom.name, uom?.value)
                    assertEquals(sqrRecord.inventoryRollover, inventoryRollover)
                    assertEquals(sqrRecord.demand.toLong(), demand.toLong())
                    assertEquals(sqrRecord.safetyStock, safetyStock)
                    assertEquals(sqrRecord.sqr, supplyQuantityRecommendation)
                    assertEquals(skuDetail.state.toString(), state.toString())
                    assertEquals(existingSQRConfigRecord.recommendationEnabled, recommendationEnabled)
                    assertEquals(existingSQRConfigRecord.multiWeekEnabled, multiWeekEnabled)
                    assertEquals(SkuRiskRatingEnum.MEDIUM, skuRiskRating)
                    assertEquals(buffer, bufferPercentage)
                }
            }
        }
    }

    @Suppress("LongParameterList")
    private suspend fun get(
        getUrl: String,
        authorEmail: String,
        authorName: String,
        dcCode: String,
        week: String,
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(jwtCredentials, true)
                supplyQuantityRecommendationModule(
                    configService,
                    sqrConfigService,
                    supplyQuantityRecommendationService,
                    timeOutInMillis,
                )()
            }
            response = client.get("$getUrl?dcCode=$dcCode&week=$week") {
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, arrayListOf(""))
            }
        }
        response.block()
    }

    private suspend fun post(
        postUrl: String,
        authorEmail: String,
        authorName: String,
        roleClaim: ArrayList<String>,
        requestBody: String,
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(jwtCredentials, true)
                supplyQuantityRecommendationModule(
                    configService,
                    sqrConfigService,
                    supplyQuantityRecommendationService,
                    timeOutInMillis,
                )()
            }
            response = client.post(postUrl) {
                setBody(requestBody)
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, roleClaim)
            }
        }
        response.block()
    }

    private fun HttpRequestBuilder.addAuthHeader(authorEmail: String, authorName: String, roleClaim: ArrayList<String>) {
        val token = buildJwtTokenWithNoAlgorithm {
            this.withClaim("roleclaim", roleClaim)
            this.withClaim("email", authorEmail)
            this.withClaim("name", authorName)
        }

        return this.header(
            HttpHeaders.Authorization,
            HttpAuthHeader.Single(AuthScheme.Bearer, token),
        )
    }

    private fun buildJwtTokenWithNoAlgorithm(jwtBuilderConf: JWTCreator.Builder.() -> Unit): String =
        JWT.create()
            .withClaim("sub", UUID.randomUUID().toString())
            .withKeyId("keyId")
            .withIssuer(jwtCredentials.issuer)
            .withAudience(jwtCredentials.clientId)
            .also(jwtBuilderConf)
            .sign(Algorithm.none())

    private fun <T> T.toRequestBody() = objectMapper.writeValueAsString(this)

    companion object {
        val timeOutInMillis: Duration = Duration.parse("PT1S")
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()

        @Suppress("unused")
        @JvmStatic
        fun safetyMultiplier() =
            Stream.of(
                Arguments.of(BigDecimal(0.5)),
                Arguments.of(null),
            )
    }
}
