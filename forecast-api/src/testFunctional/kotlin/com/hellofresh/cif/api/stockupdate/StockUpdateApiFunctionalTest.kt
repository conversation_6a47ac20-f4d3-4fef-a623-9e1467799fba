package com.hellofresh.cif.api.stockupdate

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.CalculationStatus
import com.hellofresh.cif.api.calculation.generated.model.CurrentSkuStockUpdatesResponse
import com.hellofresh.cif.api.calculation.generated.model.DayStockUpdateInner
import com.hellofresh.cif.api.calculation.generated.model.Reason
import com.hellofresh.cif.api.calculation.generated.model.StockSimulationRequest
import com.hellofresh.cif.api.calculation.generated.model.StockSimulationResponse
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateItem
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateRequest
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateRequestStockUpdatesInner
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateSimulationResponse
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateV2Response
import com.hellofresh.cif.api.calculation.generated.model.StockUpdateVersionItem
import com.hellofresh.cif.api.calculation.generated.model.StockUpdatesVersionsResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.api.stockupdate.model.StockUpdateSimulationData
import com.hellofresh.cif.calculator.models.CalculationKey
import com.hellofresh.cif.calculator.models.CalculatorMode
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.calculator.models.default
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.inventory.StockUpdate
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.inventory.lib.schema.tables.records.StockUpdateRecord
import com.hellofresh.cif.models.DateRange
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.ContentType.Application
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.ParametersBuilder
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.mockk.coEvery
import io.mockk.mockk
import java.math.BigDecimal
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

class StockUpdateApiFunctionalTest : FunctionalTest() {

    private val stockUpdateCalculationService = mockk<StockUpdateCalculationService>(relaxed = true)
    private val stockUpdateService = mockk<StockUpdateService>(relaxed = true)
    private val dcCode = "VE"
    private val today = LocalDate.now()
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"

    @Test
    fun `should create a new StockUpdate matching request with version incremented`() {
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        val skuId = UUID.randomUUID()
        val date = LocalDate.now()
        createDcConfig("VE") { productionStart = FRIDAY.name }
        insertSkus(setOf(skuId))
        val stockUpdateRequest = StockUpdateRequest(
            reason = Reason.WMS_ISSUE,
            reasonDetails = "reasonDetails",
            stockUpdates = listOf(
                StockUpdateRequestStockUpdatesInner(
                    date = date,
                    stockUpdateQuantity = 100,
                    version = 1,
                ),
            ),
        )
        coEvery {
            stockUpdateService.getCurrentStockUpdateRange("VE")
        } returns DateRange.oneDay(date)

        runBlocking {
            post("/stock-updates/VE/$skuId", authorEmail, authorName, stockUpdateRequest.toRequestBody()) {
                assertEquals(HttpStatusCode.Created, this.status)
                assertCreatedInDb(stockUpdateRequest, skuId, authorEmail, authorName)
            }
        }
    }

    @ParameterizedTest
    @CsvSource("PRODUCTION", "PRE_PRODUCTION")
    fun `should be able to get experimental calculations version 2 for the given input`(
        calculatorMode: CalculatorMode
    ) {
        val skuId = UUID.randomUUID().toString()
        val weeks = setOf("2024-W49")
        val dayCalculation = createDayCalculationResult(UUID.fromString(skuId))
        val expectedStockUpdate = BigDecimal(1000)
        val queryParams = Parameters.build {
            setOf("2024-W49").forEach { append("weeks", it) }
            appendInventoryRefreshParamValue(calculatorMode)
        }.formUrlEncode()
        val postUrl = "/stock-updates/v2/simulation/$dcCode/$skuId?$queryParams"
        val nextDay = today.plusDays(1)

        val stockUpdateSimulationData = StockUpdateSimulationData(
            dcCode,
            UUID.fromString(skuId),
            calculatorMode,
            weeks,
            mapOf(today to expectedStockUpdate),
        )
        val stockUpdate = StockUpdate(
            stockUpdateSimulationData.skuId, stockUpdateSimulationData.dcCode, today, weeks.first(),
            SkuQuantity.fromLong(10), "", null, null, "", 1, LocalDateTime.now(UTC), false,
        )
        coEvery {
            stockUpdateService.getCurrentStockUpdate(stockUpdateSimulationData.dcCode, stockUpdateSimulationData.skuId)
        } returns mapOf(
            today to stockUpdate, nextDay to null,
        )

        coEvery {
            stockUpdateCalculationService.runStockUpdatesWithoutUom(
                stockUpdateSimulationData.dcCode,
                stockUpdateSimulationData.skuId,
                stockUpdateSimulationData.weeks,
                stockUpdateSimulationData.calculatorMode,
                stockUpdateSimulationData.stockUpdateSimulations(),
            )
        } returns StockUpdateResults(
            listOf(dayCalculation),
            stockUpdateSimulationData.stockUpdateSimulations().mapValues { (_, value) -> SkuQuantity.fromBigDecimal(value, UOM_UNIT) },
            emptySet(),
            emptyMap(),
        )

        runBlocking {
            postV2(
                postUrl,
                today,
                expectedStockUpdate.toLong(),
            )
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val stockUpdateSimulationResponses = objectMapper.readValue<StockUpdateSimulationResponse>(
                        bodyAsText(),
                    )
                    assertEquals(1, stockUpdateSimulationResponses.calculations.size)
                    val actualCalculation = stockUpdateSimulationResponses.calculations.first()
                    assertEquals(dayCalculation.date, actualCalculation.date)
                    assertEquals(dayCalculation.openingStock.getValue(), actualCalculation.usableStock)
                    assertEquals(dayCalculation.actualInbound.getValue(), actualCalculation.inbound.amount)
                    assertEquals(dayCalculation.unusable.getValue(), actualCalculation.unusableStock)
                    assertEquals(dayCalculation.closingStock.getValue(), actualCalculation.closingStock)
                    assertEquals(dayCalculation.demanded.getValue(), actualCalculation.consumption)
                    assertEquals(dayCalculation.actualConsumption.getValue(), actualCalculation.actualConsumption)
                    assertEquals(dayCalculation.stockUpdate?.getValue(), actualCalculation.stockUpdate)

                    assertEquals(2, stockUpdateSimulationResponses.stockUpdates.size)
                    assertEquals(
                        expectedStockUpdate,
                        stockUpdateSimulationResponses.stockUpdates.first { it.date == today }.quantity,
                    )
                    assertEquals(
                        stockUpdate.version,
                        stockUpdateSimulationResponses.stockUpdates.first { it.date == today }.version,
                    )

                    assertNull(stockUpdateSimulationResponses.stockUpdates.first { it.date == nextDay }.quantity)
                    assertNull(stockUpdateSimulationResponses.stockUpdates.first { it.date == nextDay }.version)
                }
        }
    }

    @Test
    fun `should throw error if out of range stockUpdate is posted`() {
        /**
         * Date range is from today to past last clear-down day
         */
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        val skuId = UUID.randomUUID()
        val date = LocalDate.now()
        val dcCode = "VE"

        coEvery {
            stockUpdateService.getCurrentStockUpdate(dcCode, skuId)
        } returns mapOf(date to null)

        createDcConfig(dcCode) { productionStart = FRIDAY.name }

        insertSkus(setOf(skuId))

        val stockUpdateRequest = StockUpdateRequest(
            reason = Reason.WMS_ISSUE,
            reasonDetails = "reasonDetails",
            stockUpdates = listOf(
                StockUpdateRequestStockUpdatesInner(
                    date = date.plusDays(7),
                    stockUpdateQuantity = 200,
                    version = 0,
                ),
            ),
        )
        coEvery {
            stockUpdateService.getCurrentStockUpdateRange(dcCode)
        } returns DateRange.oneDay(date)

        val postUrl = "/stock-updates/v2/VE/$skuId?weeks=2024-W50"
        runBlocking {
            post(postUrl, authorEmail, authorName, stockUpdateRequest.toRequestBody()) {
                assertEquals(HttpStatusCode.BadRequest, this.status)
                assertTrue { this.bodyAsText().contains("Simulation with stock updates out of valid range") }
            }
        }
    }

    @ParameterizedTest
    @CsvSource("PRODUCTION", "PRE_PRODUCTION")
    fun `should create stockUpdate and return simulated response`(calculatorMode: CalculatorMode) {
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        val skuId = UUID.randomUUID()
        val date = LocalDate.now()
        val dcCode = "VE"
        val quantity = 150.toBigDecimal()
        val dayCalculation = createDayCalculationResult(skuId)

        coEvery {
            stockUpdateService.getCurrentStockUpdate(dcCode, skuId)
        } returns mapOf(date to StockUpdate(skuId, dcCode, date, "", SkuQuantity.fromLong(150), "", "", "", "", 1, LocalDateTime.now(UTC), deleted = false))

        createDcConfig(dcCode) { productionStart = FRIDAY.name }
        insertSkus(setOf(skuId))

        val stockUpdateRequest = StockUpdateRequest(
            reason = Reason.WMS_ISSUE,
            reasonDetails = "reasonDetails",
            stockUpdates = listOf(
                StockUpdateRequestStockUpdatesInner(
                    date = date,
                    stockUpdateQuantity = quantity.toLong(),
                    version = 0,
                ),
            ),
        )
        coEvery {
            stockUpdateService.getCurrentStockUpdateRange(dcCode)
        } returns DateRange.oneDay(date)

        coEvery {
            val key = CalculationKey(skuId, dcCode, date)
            stockUpdateCalculationService.runStockUpdatesWithoutUom(dcCode, skuId, setOf("2024-W50"), calculatorMode, mapOf(key to quantity))
        } returns StockUpdateResults(
            calculations = listOf(dayCalculation.copy(stockUpdate = SkuQuantity.fromLong(150))),
            stockUpdates = emptyMap(),
            emptySet(),
            emptyMap(),
        )

        val queryParams = Parameters.build {
            setOf("2024-W50").forEach { append("weeks", it) }
            appendInventoryRefreshParamValue(calculatorMode)
        }.formUrlEncode()

        runBlocking {
            post(
                "/stock-updates/v2/VE/$skuId?$queryParams",
                authorEmail,
                authorName,
                stockUpdateRequest.toRequestBody(),
            ) {
                val response = objectMapper.readValue<StockUpdateV2Response>(this.bodyAsText())

                assertEquals(HttpStatusCode.Created, this.status)
                assertEquals(1, response.stockUpdates.size)
                assertEquals(1, response.calculations.size)

                response.stockUpdates.first().let { first ->
                    assertEquals(date, first.date)
                    assertEquals(quantity, first.quantity)
                    assertEquals(1, first.version)
                }

                response.calculations.first().let { first ->
                    assertEquals(CalculationStatus.PENDING, first.status)
                    assertEquals(quantity, first.stockUpdate ?: 0)
                }
            }
        }
    }

    @Test
    fun `should get current stock updates`() {
        val skuId = UUID.randomUUID()
        val date1 = LocalDate.now()
        val date2 = date1.plusDays(1)
        val dcCode = "VE"

        val stockUpdate =
            StockUpdate(
                skuId, dcCode, date1, "WEEK",
                SkuQuantity.fromLong(
                    10,
                ),
                "", null, null, "", 1, LocalDateTime.now(UTC), false,
            )

        coEvery {
            stockUpdateService.getCurrentStockUpdate(dcCode, skuId)
        } returns mapOf(date1 to stockUpdate, date2 to null)

        createDcConfig(dcCode) { productionStart = FRIDAY.name }

        runBlocking {
            get("/stock-updates/v2/current/VE/$skuId") {
                assertEquals(HttpStatusCode.OK, this.status)
                val response = objectMapper.readValue<CurrentSkuStockUpdatesResponse>(this.bodyAsText())
                assertEquals(2, response.stockUpdates.size)
                assertEquals(stockUpdate.version, response.stockUpdates.first { it.date == date1 }.version)
                assertEquals(stockUpdate.quantity.getValue(), response.stockUpdates.first { it.date == date1 }.quantity)

                assertNull(response.stockUpdates.first { it.date == date2 }.version)
                assertNull(response.stockUpdates.first { it.date == date2 }.quantity)
            }
        }
    }

    @Test
    fun `should return bad request when provided reason isn't in the enumerated values`() {
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        val skuId = UUID.randomUUID()

        val requestBodyWithIncorrectReason = """
            {
                "reason": "incorrectReason",
                "reasonDetails": "reasonDetails",
                "stockUpdates": [
                    {
                        "date": [2024, 4, 23],
                        "updateQuantity": 100,
                        "week": "2021-01",
                        "version": 1,
                        "deleted": false
                    }
                ]
            }
        """

        runBlocking {
            post("/stock-updates/VE/$skuId", authorEmail, authorName, requestBodyWithIncorrectReason) {
                assertEquals(HttpStatusCode.BadRequest, this.status)
                assertTrue { this.bodyAsText().contains("not one of the values accepted for Enum class") }
            }
        }
    }

    @ParameterizedTest
    @CsvSource("PRODUCTION", "PRE_PRODUCTION")
    fun `stockUpdates are simulated using values from request`(calculatorMode: CalculatorMode) {
        val skuId = UUID.randomUUID()
        val yesterday = LocalDate.now()
        val stockUpdateValue = BigDecimal(100)
        val dcCode = "DC"
        coEvery {
            stockUpdateService.getCurrentStockUpdate(dcCode, skuId)
        } returns mapOf(yesterday to null)

        val yesterdayCalculationResult = DayCalculationResult.default()
            .copy(
                date = yesterday,
                cskuId = skuId,
                dcCode = dcCode,
                closingStock = SkuQuantity.fromLong(100),
            )
        val yesterdayCalculationResultWithStockUpdate = yesterdayCalculationResult.copy(
            closingStock = yesterdayCalculationResult.closingStock + SkuQuantity.fromLong(50),
        )

        coEvery {
            stockUpdateCalculationService.runStockUpdatesComparisonWithoutUom(
                dcCode, skuId, calculatorMode,
                mapOf(CalculationKey(skuId, dcCode, yesterday) to stockUpdateValue), emptySet(),
            )
        } returns StockUpdateComparison(
            defaultCalculation = listOf(yesterdayCalculationResult),
            stockUpdateCalculations = listOf(yesterdayCalculationResultWithStockUpdate),
            stockUpdates = mapOf(CalculationKey(skuId, dcCode, yesterday) to SkuQuantity.fromBigDecimal(stockUpdateValue, UOM_UNIT)),
        )

        val queryParams = Parameters.build {
            appendInventoryRefreshParamValue(calculatorMode)
        }.formUrlEncode()

        runBlocking {
            post(
                "/stock-updates/simulation/$dcCode/$skuId?$queryParams",
                UUID.randomUUID().toString(),
                UUID.randomUUID().toString(),
                StockSimulationRequest(
                    stockUpdates = listOf(
                        DayStockUpdateInner(
                            date = yesterday,
                            stockUpdateQuantity = stockUpdateValue.toLong(),
                        ),
                    ),
                ).toRequestBody(),
            ) {
                assertEquals(HttpStatusCode.OK, this.status)
                val stockUpdates = objectMapper.readValue<StockSimulationResponse>(this.bodyAsText()).stockUpdates
                assertEquals(1, stockUpdates.size)
                assertEquals(
                    StockUpdateItem(
                        yesterday,
                        yesterdayCalculationResult.closingStock.getValue().toLong(),
                        yesterdayCalculationResultWithStockUpdate.closingStock.getValue().toLong(),
                        false,
                        stockUpdateValue.toLong(),
                    ),
                    stockUpdates.first { it.date == yesterday },
                )
            }
        }
    }

    @Test
    fun `all stockUpdates versions are requested`() {
        val dc = createDcConfig()
        val dcCode = dc.dcCode
        val date = LocalDate.now().plusWeeks(1)
        val week = DcWeek(date, MONDAY).value
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        insertSkus(setOf(skuId1, skuId2))
        val stockUpdateRecord1 = createStockUpdateRecord(dcCode, date, week, BigDecimal(100), skuId1, 1)
        val stockUpdateRecord2 = createStockUpdateRecord(dcCode, date, week, BigDecimal(100), skuId1, 2)
        val stockUpdateRecord3 = createStockUpdateRecord(dcCode, date, week, BigDecimal(100), skuId2, 1)

        runBlocking {
            get("/stock-updates/$dcCode/$week") {
                assertEquals(HttpStatusCode.OK, this.status)
                val stockUpdates = objectMapper.readValue<StockUpdatesVersionsResponse>(this.bodyAsText()).stockUpdates

                assertEquals(2, stockUpdates.size)

                with(stockUpdates.first { it.skuId == skuId1 }) {
                    assertEquals(dcCode, this.dc)
                    assertEquals(date, this.date)
                    assertEquals(2, versions.size)

                    assertStockUpdateVersion(
                        stockUpdateRecord1,
                        versions.first { it.version == stockUpdateRecord1.version },
                    )
                    assertStockUpdateVersion(
                        stockUpdateRecord2,
                        versions.first { it.version == stockUpdateRecord2.version },
                    )
                }

                with(stockUpdates.first { it.skuId == skuId2 }) {
                    assertEquals(dcCode, this.dc)
                    assertEquals(date, this.date)
                    assertEquals(1, versions.size)
                    assertStockUpdateVersion(
                        stockUpdateRecord3,
                        versions.first { it.version == stockUpdateRecord3.version },
                    )
                }
            }
        }
    }

    private fun assertStockUpdateVersion(stockUpdateRecord: StockUpdateRecord, stockUpdateVersionItem: StockUpdateVersionItem) {
        assertEquals(stockUpdateRecord.quantity, stockUpdateVersionItem.quantity.toBigDecimal())
        assertEquals(stockUpdateRecord.reason, stockUpdateVersionItem.reason)
        assertEquals(stockUpdateRecord.reasonDetail, stockUpdateVersionItem.reasonDetails)
        assertEquals(stockUpdateRecord.deleted, stockUpdateVersionItem.deleted)
        assertEquals(stockUpdateRecord.authorName, stockUpdateVersionItem.username)
        assertNotNull(stockUpdateVersionItem.updateTimeStamp)
    }

    private fun assertCreatedInDb(stockUpdateRequest: StockUpdateRequest, skuId: UUID, authorEmail: String, authorName: String) {
        dsl.selectFrom(Tables.STOCK_UPDATE).where(Tables.STOCK_UPDATE.SKU_ID.eq(skuId))
            .fetch().first()
            .also {
                assertEquals(stockUpdateRequest.reason.value, it.get(Tables.STOCK_UPDATE.REASON))
                assertEquals(skuId, it.get(Tables.STOCK_UPDATE.SKU_ID))
                assertEquals(authorEmail, it.get(Tables.STOCK_UPDATE.AUTHOR_EMAIL))
                assertEquals(authorName, it.get(Tables.STOCK_UPDATE.AUTHOR_NAME))
                assertEquals(
                    stockUpdateRequest.stockUpdates[0].date,
                    it.get(Tables.STOCK_UPDATE.DATE),
                )
                assertEquals(
                    stockUpdateRequest.stockUpdates[0].stockUpdateQuantity.toBigDecimal(),
                    it.get(Tables.STOCK_UPDATE.QUANTITY),
                )
                assertEquals(
                    stockUpdateRequest.stockUpdates[0].version.plus(1),
                    it.get(
                        Tables.STOCK_UPDATE.VERSION,
                    ),
                )
            }
    }

    private suspend fun get(
        url: String,
        authorEmail: String = "author@email",
        authorName: String = "authorName",
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", "https://test.com"), true)
                stockUpdateModule(
                    StockUpdateApiService(
                        stockUpdateService,
                        stockUpdateCalculationService,
                        stockUpdateApiRepository,
                        dcConfigService,
                        skuInputDataRepository,
                    ),
                    statsigFeatureFlagClient,
                    usableInventoryEvaluator,
                    timeOutInMillis,
                )()
            }
            response = client.get(url) {
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        response.block()
    }

    private suspend fun post(
        postUrl: String,
        authorEmail: String,
        authorName: String,
        requestBody: String,
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", "https://test.com"), true)
                stockUpdateModule(
                    StockUpdateApiService(
                        stockUpdateService,
                        stockUpdateCalculationService,
                        stockUpdateApiRepository,
                        dcConfigService,
                        skuInputDataRepository,
                    ),
                    statsigFeatureFlagClient,
                    usableInventoryEvaluator,
                    timeOutInMillis,
                )()
            }
            response = client.post(postUrl) {
                setBody(requestBody)
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        response.block()
    }

    private fun postV2(
        postUrl: String,
        date: LocalDate,
        stockUpdate: Long
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                stockUpdateModule(
                    StockUpdateApiService(
                        stockUpdateService,
                        stockUpdateCalculationService,
                        stockUpdateApiRepository,
                        dcConfigService,
                        skuInputDataRepository,
                    ),
                    statsigFeatureFlagClient,
                    usableInventoryEvaluator,
                    timeOutInMillis,
                )()
            }
            response = client.post(postUrl) {
                setBody(prepareRequestBodyV2(date, stockUpdate))
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
                addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }

    private fun <T> T.toRequestBody() = objectMapper.writeValueAsString(this)

    private fun prepareRequestBodyV2(date: LocalDate, stockUpdate: Long) = """
        [
           {
              "date":"$date",
              "stockUpdate":$stockUpdate
           }
        ]
    """.trimIndent()

    private fun ParametersBuilder.appendInventoryRefreshParamValue(calculatorMode: CalculatorMode) =
        append(
            "consumptionDaysAhead",
            when (calculatorMode) {
                CalculatorMode.PRODUCTION -> 0
                CalculatorMode.PRE_PRODUCTION -> 1
                else -> error("Invalid mode: $calculatorMode")
            }.toString(),
        )

    companion object {
        val timeOutInMillis: Duration = Duration.parse("PT1S")
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
