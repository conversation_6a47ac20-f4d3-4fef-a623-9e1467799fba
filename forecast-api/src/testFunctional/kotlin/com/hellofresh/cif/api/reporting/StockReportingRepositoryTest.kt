package com.hellofresh.cif.api.reporting

import com.hellofresh.cif.api.calculation.fixtures.Default
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.demand.lib.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.demand.lib.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.inventory.models.InventoryVarianceFixtures
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.ZERO
import java.time.LocalDate
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking

@Suppress("SpreadOperator")
class StockReportingRepositoryTest : CommonStockReportingTest() {
    @Test
    fun `should return calculations if a stock variance is present`() {
        // given
        val givenCalculations =
            persistCalculationRecordsAndSku(
                closingStockDayBeforeCleardown = BigDecimal(999),
                openingStockCleardown = ONE,
                market = "VE"
            )

        // when
        val variances = runBlocking { stockReportingRepository.stockVarianceFromCleardown(dc, currentCleardownDate) }

        // then
        assertEquals(1, variances.size)
        val variance = variances.first()
        assertEquals(7, variance.previousWeek.size)
        assertEquals(givenCalculations.latestCleardown, variance.latestCleardown)
        assertEquals(givenCalculations.previousWeek, variance.previousWeek)
    }

    @Test
    fun `should not include in the variance report calculations after the end of the current DC week`() {
        // given
        persistCalculationRecordsAndSku(
            closingStockDayBeforeCleardown = BigDecimal(999),
            openingStockCleardown = ZERO,
            numDays = 14,
            market = "DE"
        )

        // when
        val variances = runBlocking { stockReportingRepository.stockVarianceFromCleardown(dc, currentCleardownDate) }

        // then
        assertEquals(1, variances.size)
        assertEquals(7, variances.first().previousWeek.size)
    }

    @Test
    fun `should not include calculations for the same sku in other DCs`() {
        // given
        val skuId = UUID.randomUUID()
        persistCalculationRecordsAndSku(
            skuId = skuId,
            openingStockCleardown = BigDecimal(999),
            closingStockDayBeforeCleardown = ZERO,
            dcCode = dc,
            market = "VE"
        )
        persistCalculationRecords(
            skuId = skuId,
            openingStockCleardown = BigDecimal(999),
            closingStockDayBeforeCleardown = ZERO,
            dcCode = "XX",
            market = "DE"
        )

        // when
        val variances = runBlocking { stockReportingRepository.stockVarianceFromCleardown(dc, currentCleardownDate) }

        // then
        assertEquals(1, variances.size)
        assertEquals(7, variances.first().previousWeek.size)
    }

    @Test
    fun `should return variances for multiple skus`() {
        // given
        val skuCount = 10
        val givenCalculations = (0 until skuCount).map {
            persistCalculationRecordsAndSku(
                closingStockDayBeforeCleardown = BigDecimal(999),
                openingStockCleardown = ZERO,
                numDays = 7,
                market = "DE" + UUID.randomUUID().toString()
            )
        }

        // when
        val calculations = runBlocking { stockReportingRepository.stockVarianceFromCleardown(dc, currentCleardownDate) }

        // then
        assertEquals(skuCount, calculations.size)
        givenCalculations.forEach {
            val varianceForSku = calculations.find { v -> it.latestCleardown.sku.id == v.latestCleardown.sku.id }
            assertEquals(it.latestCleardown, varianceForSku?.latestCleardown)
            assertEquals(it.previousWeek, varianceForSku?.previousWeek)
        }
    }

    @Test
    fun `should return an empty stock variance report if closing stock cleardown is equal to opening stock on day after cleardown`() {
        // given
        persistCalculationRecordsAndSku(
            closingStockDayBeforeCleardown = BigDecimal(999),
            openingStockCleardown = BigDecimal(999),
            market = "DE",
        )

        // when
        val variances = runBlocking { stockReportingRepository.stockVarianceFromCleardown(dc, currentCleardownDate) }

        // then
        assertTrue(variances.isEmpty())
    }

    @Test
    fun `should exclude skus that don't have past week calculations`() {
        // given
        val calculationRecordCleardown = Default.calculationRecord {
            this.cskuId = UUID.randomUUID()
            this.dcCode = dc
            productionWeek = "2022-W11"
            this.openingStock = BigDecimal(999)
            date = currentCleardownDate
        }
        dsl.batchInsert(calculationRecordCleardown).execute()

        // when
        val variances = runBlocking { stockReportingRepository.stockVarianceFromCleardown(dc, currentCleardownDate) }

        // then
        assertTrue(variances.isEmpty())
    }

    @Test
    fun `should not return calculations if a cleardown date does not match present data`() {
        // given
        persistCalculationRecordsAndSku(
            closingStockDayBeforeCleardown = BigDecimal(999),
            openingStockCleardown = ONE,
            market = "DE"
        )

        // when
        val variances =
            runBlocking { stockReportingRepository.stockVarianceFromCleardown(dc, LocalDate.now().plusWeeks(10)) }
        val variances2 =
            runBlocking { stockReportingRepository.stockVarianceFromCleardown(dc, LocalDate.now().minusWeeks(10)) }

        // then
        assertTrue(variances.isEmpty())
        assertTrue(variances2.isEmpty())
    }

    @Test
    fun `should return inventory variance records successfully`() {
        val inventoryVariance = InventoryVarianceFixtures.default
        val skuSpecificationRecord = SkuSpecificationRecord().apply {
            id = inventoryVariance.skuId
            name = "Sku"
            code = "SPI-0000"
            category = "SPI"
            acceptableCodeLife = 0
            coolingType = ""
            packaging = ""
            market = "dach"
            uom = UOM_UNIT
        }
        dsl.executeInsert(skuSpecificationRecord)
        refreshSkuView()
        persistInventoryVarianceFixtures(inventoryVariance)
        runBlocking {
            val stockLiveVarianceSkuList = stockReportingRepository.liveStockVariance(
                inventoryVariance.dcCode,
                inventoryVariance.dcWeek,
            )
            assertEquals(1, stockLiveVarianceSkuList.size)
            stockLiveVarianceSkuList.first().apply {
                assertEquals(inventoryVariance.skuId, skuId)
                assertEquals(skuSpecificationRecord.code, skuCode)
                assertEquals(skuSpecificationRecord.name, skuName)
                assertEquals(skuSpecificationRecord.category, skuCategories)
                assertEquals(inventoryVariance.cleardownVariance, inventoryVariance.cleardownVariance)
                assertEquals(inventoryVariance.liveVariance, inventoryVariance.liveVariance)
            }

            inventoryVariance.dailyInventoryVarianceData.forEach { dailyInventoryVarianceData ->
                val actual = stockLiveVarianceSkuList.first().dailyInventoryVarianceData.first {
                    it.date == dailyInventoryVarianceData.date
                }
                dailyInventoryVarianceData.apply {
                    assertEquals(date, actual.date)
                    assertEquals(inventoryQty, actual.inventoryQty)
                    assertEquals(cleardownClosingStock, actual.cleardownClosingStock)
                    assertEquals(liveClosingStock, actual.liveClosingStock)
                }
            }
        }
    }
}
