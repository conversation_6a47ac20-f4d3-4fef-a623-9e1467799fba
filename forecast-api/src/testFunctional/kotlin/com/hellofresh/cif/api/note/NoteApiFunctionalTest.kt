package com.hellofresh.cif.api.note

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.CreateNoteResponse
import com.hellofresh.cif.api.calculation.generated.model.GetNotesResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.Tables.NOTE
import io.ktor.client.request.delete
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.post
import io.ktor.client.request.put
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType.Application
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Test

class NoteApiFunctionalTest : FunctionalTest() {
    private val jwksURI = "https://test.com"

    @Test
    fun `should create a new note matching given request`() {
        val createDtoNoteRequest = createDtoNoteRequest()
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()

        runBlocking {
            post("/notes", createDtoNoteRequest, authorEmail, authorName) {
                assertEquals(HttpStatusCode.OK, this.status)

                val createNoteResponse = objectMapper.readValue(this.bodyAsText(), CreateNoteResponse::class.java)

                assertNotNull(createNoteResponse.id)
                assertEquals(createDtoNoteRequest.skuId, createNoteResponse.skuId)
                assertEquals(authorEmail, createNoteResponse.authorEmail)
                assertEquals(authorName, createNoteResponse.authorName)
                assertEquals(createDtoNoteRequest.dcCodes, createNoteResponse.dcCodes)
                assertEquals(createDtoNoteRequest.weeks, createNoteResponse.weeks)
                assertEquals(createDtoNoteRequest.text, createNoteResponse.text)
                assertNotNull(createNoteResponse.createdAt)

                assertDb(createNoteResponse)
            }
        }
    }

    @Test
    fun `should update an existing note`() {
        val createDtoNoteRequest = createDtoNoteRequest()
        val updateNoteRequest = getUpdateNoteRequest()
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        var id: UUID? = null

        runBlocking {
            post("/notes", createDtoNoteRequest, authorEmail, authorName) {
                id = objectMapper.readValue(this.bodyAsText(), CreateNoteResponse::class.java).id
            }
            put("/notes/$id", updateNoteRequest.copy(text = "updated note text"), authorEmail, authorName) {
                assertEquals(HttpStatusCode.OK, this.status)

                val updateNoteResponse = objectMapper.readValue(this.bodyAsText(), CreateNoteResponse::class.java)

                assertNotNull(updateNoteResponse.id)
                assertEquals(createDtoNoteRequest.skuId, updateNoteResponse.skuId)
                assertEquals(authorEmail, updateNoteResponse.authorEmail)
                assertEquals(authorName, updateNoteResponse.authorName)
                assertEquals(createDtoNoteRequest.dcCodes, updateNoteResponse.dcCodes)
                assertEquals(createDtoNoteRequest.weeks, updateNoteResponse.weeks)
                assertEquals("updated note text", updateNoteResponse.text)
                assertNotNull(updateNoteResponse.createdAt)
                assertDb(updateNoteResponse, 1)
            }
        }
    }

    @Test
    fun `should delete an existing note`() {
        val createDtoNoteRequest = createDtoNoteRequest()
        val authorEmail = UUID.randomUUID().toString()
        val authorName = UUID.randomUUID().toString()
        var id: UUID? = null

        runBlocking {
            post("/notes", createDtoNoteRequest, authorEmail, authorName) {
                id = objectMapper.readValue(this.bodyAsText(), CreateNoteResponse::class.java).id
            }
            delete("/notes/$id", authorEmail, authorName) {
            }
            get("/notes?dcCode=${createDtoNoteRequest.dcCodes[0]}&weeks=2023-W50", authorEmail, authorName) {
                assertEquals(HttpStatusCode.OK, this.status)
                val getNoteResponse = objectMapper.readValue(this.bodyAsText(), GetNotesResponse::class.java)
                assertTrue(getNoteResponse.notesBySkuId.isEmpty())
            }
        }
    }

    private fun assertDb(createNoteResponse: CreateNoteResponse, version: Int = 0) {
        dsl.selectFrom(NOTE).where(NOTE.ID.eq(createNoteResponse.id).and(NOTE.VERSION.eq(version)))
            .fetch().first()
            .also {
                assertEquals(createNoteResponse.id, it.get(NOTE.ID))
                assertEquals(createNoteResponse.skuId, it.get(NOTE.SKU_ID))
                assertEquals(createNoteResponse.authorEmail, it.get(NOTE.AUTHOR_EMAIL))
                assertEquals(createNoteResponse.authorName, it.get(NOTE.AUTHOR_NAME))
                assertEquals(createNoteResponse.text, it.get(NOTE.NOTE_TEXT))
                assertEquals(createNoteResponse.dcCodes?.toSet(), it.get(NOTE.DC_CODES, Set::class.java))
                assertEquals(createNoteResponse.weeks.toSet(), it.get(NOTE.PRODUCTION_WEEKS, Set::class.java))
            }
    }

    private fun createDtoNoteRequest() =
        com.hellofresh.cif.api.calculation.generated.model.CreateNoteRequest(
            skuId = UUID.randomUUID(),
            dcCodes = listOf(UUID.randomUUID().toString()),
            weeks = listOf("2023-W50"),
            text = UUID.randomUUID().toString(),
        )

    private fun getUpdateNoteRequest() =
        com.hellofresh.cif.api.calculation.generated.model.UpdateNoteRequest(
            weeks = listOf("2023-W50"),
            text = UUID.randomUUID().toString(),
        )

    private suspend fun post(
        postUrl: String,
        createNoteRequest: com.hellofresh.cif.api.calculation.generated.model.CreateNoteRequest,
        authorEmail: String,
        authorName: String,
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", jwksURI), true)
                noteModule(NoteService(noteRepository), timeOutInMillis)()
            }
            response = client.post(postUrl) {
                setBody(prepareRequestBody(createNoteRequest))
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        response.block()
    }

    private suspend fun put(
        putUrl: String,
        updateNoteRequest: com.hellofresh.cif.api.calculation.generated.model.UpdateNoteRequest,
        authorEmail: String,
        authorName: String,
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", jwksURI), true)
                noteModule(NoteService(noteRepository), timeOutInMillis)()
            }
            response = client.put(putUrl) {
                setBody(objectMapper.writeValueAsString(updateNoteRequest))
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        response.block()
    }

    private suspend fun delete(
        deleteUrl: String,
        authorEmail: String,
        authorName: String,
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", jwksURI), true)
                noteModule(NoteService(noteRepository), timeOutInMillis)()
            }
            response = client.delete(deleteUrl) {
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        response.block()
    }

    private suspend fun get(
        getUrl: String,
        authorEmail: String,
        authorName: String,
        block: suspend HttpResponse.() -> Unit
    ) {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", jwksURI), true)
                noteModule(NoteService(noteRepository), timeOutInMillis)()
            }
            response = client.get(getUrl) {
                header(HttpHeaders.ContentType, Application.Json.toString())
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        response.block()
    }

    private fun prepareRequestBody(
        createNoteRequest: com.hellofresh.cif.api.calculation.generated.model.CreateNoteRequest
    ) =
        objectMapper.writeValueAsString(createNoteRequest)

    companion object {
        val timeOutInMillis: Duration = Duration.parse("PT1S")
        val objectMapper: ObjectMapper = jacksonObjectMapper().findAndRegisterModules()
    }
}
