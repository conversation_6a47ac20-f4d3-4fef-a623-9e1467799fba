package com.hellofresh.cif.api.reporting

import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.calculation.generated.model.StockLiveVarianceReportingResponse
import com.hellofresh.cif.api.calculation.generated.model.StockVarianceResponse
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.inventory.models.InventoryVarianceFixtures
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import java.nio.charset.StandardCharsets
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking

class StockReportingApiFunctionalTest : CommonStockReportingTest() {
    private val dc = "VE"
    private val timeOutInMillis = Duration.parse("PT1S")
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"

    @Test
    fun `should respond with an empty sku list if no variance is found for DC`() {
        // when
        val httpResponse = getStockVariance(dc, LocalDate.now())

        // then
        assertEquals(HttpStatusCode.OK, httpResponse.status)
        val response: StockVarianceResponse
        runBlocking {
            response = objectMapper.readValue(httpResponse.bodyAsText(), StockVarianceResponse::class.java)
        }
        assertTrue(response.skus.isEmpty())
    }

    @Test
    fun `should return stock variances for multiple skus`() {
        val skus =
            listOf(
                "776d63ba-269e-4caa-b6ac-47ff6ece32fe",
                "98247c95-b49c-4884-89e7-c7ef3f9ea0af",
                "7c2efd4d-a763-4b56-b50d-b03079a4fb40",
            )
        skus.forEachIndexed { index, sku ->
            persistCalculationRecordsAndSku(
                UUID.fromString(sku),
                index.toBigDecimal(),
                (index + 1L).toBigDecimal(),
                market = "DE" + index
            )
        }
        val stockVarianceStr = this.javaClass.classLoader
            .getResourceAsStream("stock-variance-response.json")!!
        val expectedVarianceJson = String(stockVarianceStr.readAllBytes(), StandardCharsets.UTF_8)
            .replace("@fromYearWeek@", previousWeek)
            .replace("@toYearWeek@", currentWeekStr)
        val expectedStockVarianceResponse = objectMapper.readValue(
            expectedVarianceJson,
            StockVarianceResponse::class.java,
        )

        // when
        val httpResponse = getStockVariance(dc, currentCleardownDate)

        // then
        assertEquals(HttpStatusCode.OK, httpResponse.status)
        val response: StockVarianceResponse
        runBlocking {
            response = objectMapper.readValue(httpResponse.bodyAsText(), StockVarianceResponse::class.java)
        }
        assertEquals(expectedStockVarianceResponse.skus.toSet(), response.skus.toSet())
        assertEquals(expectedStockVarianceResponse.dc, response.dc)
        assertEquals(expectedStockVarianceResponse.period, response.period)
    }

    @Test
    fun `should return live stock variances`() {
        // given
        val inventoryVariance = InventoryVarianceFixtures.default
        persistInventoryVarianceFixtures(inventoryVariance)

        persistSkuSpecification(inventoryVariance.skuId)

        val dateString = "2023-06-02"
        val formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd")
        val cleardownDate = LocalDate.parse(dateString, formatter)
        val givenDcWeek = DcWeek(cleardownDate, cleardownDate.dayOfWeek)

        // when
        val httpResponse = getLiveStockVariance(inventoryVariance.dcCode, givenDcWeek)

        // then
        assertEquals(HttpStatusCode.OK, httpResponse.status)
        val liveVarianceResponse: StockLiveVarianceReportingResponse
        runBlocking {
            liveVarianceResponse = objectMapper.readValue(
                httpResponse.bodyAsText(),
                StockLiveVarianceReportingResponse::class.java,
            )
        }
        assertEquals(0, liveVarianceResponse.stockLiveVariances.size)
        // TODO assert here it should be 1 variance report
    }

    private fun getStockVariance(dcCode: String, cleardownDate: LocalDate): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", jwksURI), true)
                stockReportingRoutingModule(
                    StockReportingService(stockReportingRepository, purchaseOrderService),
                    timeOutInMillis,
                )()
            }
            response = client.get("/dc/$dcCode/stockVariance?cleardownDate=$cleardownDate") {
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }

    private fun getLiveStockVariance(dcCode: String, dcWeek: DcWeek): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", jwksURI), true)
                stockReportingRoutingModule(
                    StockReportingService(stockReportingRepository, purchaseOrderService),
                    timeOutInMillis,
                )()
            }
            response = client.get("/dc/$dcCode/liveVariance?week=${dcWeek.value}") {
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }
}
