package com.hellofresh.cif.api

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategies.SnakeCaseStrategy
import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.calculation.fixtures.Default.dcCode
import com.hellofresh.cif.api.calculation.generated.model.SkuDetailResponse
import com.hellofresh.cif.api.inventory.inventoryRoutingModule
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.inventory.lib.schema.tables.records.InventorySnapshotRecord
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.inventory.models.Inventory
import com.hellofresh.inventory.models.InventoryValue
import com.hellofresh.inventory.models.Location
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_WASTE
import com.hellofresh.sku.models.DEFAULT_MAX_DAYS_BEFORE_EXPIRY
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.UUID
import kotlin.test.BeforeTest
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertFalse

private val today = LocalDate.now()

@Suppress("MagicNumber")
class InventoryApiFunctionalTest : FunctionalTest() {
    private val jwksURI = "https://test.com"
    private val authorName = "testAuthor"
    private val authorEmail = "<EMAIL>"

    private val objectMapperSnakeCaseStrategy = ObjectMapper().findAndRegisterModules().setPropertyNamingStrategy(
        SnakeCaseStrategy(),
    )
    private val objectMapperCamelCaseStrategy = ObjectMapper().findAndRegisterModules()
    private val timeOutInMillis = Duration.parse("PT1S")

    private val inventoryInfo =
        InventoryValue(
            listOf(
                Inventory(
                    SkuQuantity.fromBigDecimal(BigDecimal.ONE),
                    today.plusDays(10),
                    location = Location("", LOCATION_TYPE_STAGING, null)
                )
            )
        )
    private val inventoryInfo2 =
        InventoryValue(
            listOf(
                Inventory(
                    SkuQuantity.fromBigDecimal(BigDecimal.TWO),
                    today.plusDays(11),
                    location = Location("", LOCATION_TYPE_STAGING, null)
                )
            )
        )

    private val inventoryRecordVE1: InventorySnapshotRecord = InventorySnapshotRecord(
        "VE",
        today,
        UUID.randomUUID(),
        UUID.randomUUID(),
        JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventoryInfo)),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val inventoryWithExpiringAndExpired = InventoryValue(
        listOf(
            Inventory(
                SkuQuantity.fromBigDecimal(BigDecimal.ONE),
                today.plusDays(4),
                location = Location("", LOCATION_TYPE_STAGING, null)
            ),
            Inventory(
                SkuQuantity.fromBigDecimal(BigDecimal.ONE),
                today,
                location = Location("", LOCATION_TYPE_STAGING, null)
            ),
        ),
    )
    private val inventoryRecordVE2: InventorySnapshotRecord = InventorySnapshotRecord(
        "VE",
        today,
        UUID.randomUUID(),
        UUID.randomUUID(),
        JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventoryWithExpiringAndExpired)),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val inventoryMultipleEntries = InventoryValue(
        listOf(
            Inventory(
                SkuQuantity.fromBigDecimal(BigDecimal.ONE),
                today.plusDays(5),
                location = Location("", LOCATION_TYPE_STAGING, null)
            ),
            Inventory(
                SkuQuantity.fromBigDecimal(BigDecimal.ONE),
                today,
                location = Location("", LOCATION_TYPE_STAGING, null)
            ),
            Inventory(
                SkuQuantity.fromBigDecimal(BigDecimal.ONE),
                today.plusDays(10),
                location = Location("", LOCATION_TYPE_STAGING, null)
            ),
        ),
    )

    private val inventoryRecordVE3: InventorySnapshotRecord = InventorySnapshotRecord(
        "VE",
        today,
        UUID.randomUUID(),
        UUID.randomUUID(),
        JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventoryMultipleEntries)),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val inventoryRecordIT = InventorySnapshotRecord(
        "IT",
        today,
        UUID.randomUUID(),
        UUID.randomUUID(),
        JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventoryInfo)),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val inventoryRecordITTomorrow = InventorySnapshotRecord(
        inventoryRecordIT.dcCode,
        today.plusDays(1),
        UUID.randomUUID(),
        UUID.randomUUID(),
        JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventoryInfo2)),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val inventoryRecordUS = InventorySnapshotRecord(
        "US",
        today,
        inventoryRecordIT.skuId,
        UUID.randomUUID(),
        JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventoryInfo)),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val inventoryInfoNoExpiry =
        InventoryValue(
            listOf(
                Inventory(
                    SkuQuantity.fromBigDecimal(BigDecimal.ONE),
                    null,
                    location = Location("", LOCATION_TYPE_STAGING, null)
                )
            )
        )

    private val inventoryRecordNoExpiry = InventorySnapshotRecord(
        "VE",
        today,
        UUID.randomUUID(),
        UUID.randomUUID(),
        JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventoryInfoNoExpiry)),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )
    private val inventoryRecordInvalidJsonValue = InventorySnapshotRecord(
        "VE",
        today,
        UUID.randomUUID(),
        UUID.randomUUID(),
        JSONB.valueOf("{}"),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val nullJsonValue1 = InventorySnapshotRecord(
        "VE",
        today,
        UUID.randomUUID(),
        UUID.randomUUID(),
        JSONB.valueOf(null),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val nullJsonValue2 = InventorySnapshotRecord(
        "VE",
        today.plusDays(1),
        nullJsonValue1.skuId,
        UUID.randomUUID(),
        JSONB.valueOf(null),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val unusableInventoryQty = BigDecimal(1000L)
    private val unusableInventoryExpiryDate = today.plusDays(10)

    private val unusableInventory = InventorySnapshotRecord(
        "VE",
        today,
        UUID.randomUUID(),
        UUID.randomUUID(),
        JSONB.valueOf(
            objectMapperSnakeCaseStrategy.writeValueAsString(
                InventoryValue(
                    listOf(
                        Inventory(
                            SkuQuantity.fromBigDecimal(unusableInventoryQty),
                            unusableInventoryExpiryDate,
                            Location("", LOCATION_TYPE_WASTE, null),
                        ),
                    ),
                ),
            ),
        ),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val commonSkuId = UUID.randomUUID()
    private val inventoryFromDC1: InventorySnapshotRecord = InventorySnapshotRecord(
        "D1",
        today,
        commonSkuId,
        UUID.randomUUID(),
        JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventoryInfo)),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    private val inventoryFromDC2 = InventorySnapshotRecord(
        "D2",
        inventoryFromDC1.date,
        commonSkuId,
        UUID.randomUUID(),
        JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventoryInfo)),
        LocalDateTime.now(),
        LocalDateTime.now(),
        OffsetDateTime.now(ZoneOffset.UTC),
    )

    @BeforeTest fun setUp() {
        val inventoryRecords = listOf(
            inventoryRecordVE1,
            inventoryRecordVE2,
            inventoryRecordVE3,
            inventoryRecordIT,
            inventoryRecordITTomorrow,
            inventoryRecordUS,
            inventoryRecordNoExpiry,
            inventoryRecordInvalidJsonValue,
            nullJsonValue1,
            nullJsonValue2,
            unusableInventory,
            inventoryFromDC1,
            inventoryFromDC2,
        )
        dsl.batchInsert(
            inventoryRecords,
        ).execute()

        insertSkus(inventoryRecords.map { it.skuId }.toSet())

        dsl.batchInsert(
            inventoryRecords.map {
                it.dcCode
            }.distinct()
                .map {
                    DcConfigRecord().apply {
                        dcCode = it
                        market = "DACH"
                        productionStart = "MONDAY"
                        cleardown = "FRIDAY"
                        zoneId = ZoneOffset.UTC.id
                        enabled = true
                        hasCleardown = true
                        recordTimestamp_ = LocalDateTime.now()
                    }
                },
        ).execute()
    }

    @Test fun `should map the unusable reason properly`() {
        val skuId = unusableInventory.skuId
        val dcCode = unusableInventory.dcCode
        runBlocking {
            skuDetailRequest(skuId, dcCode).apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val result = toSkuDetailResponses(body)
                val expected = listOf(
                    SkuDetailResponse(
                        dcCode = dcCode,
                        isUsable = false,
                        quantity = unusableInventoryQty.toLong(),
                        expiryDate = unusableInventoryExpiryDate,
                        unusableStockReason = "Waste",
                    ),
                )
                Assertions.assertIterableEquals(expected, result)
            }
        }
    }

    @Test fun `should return all inventories by date for a given sku code and DC`() {
        val skuId = inventoryRecordVE1.skuId
        val dcCode = inventoryRecordVE1.dcCode
        runBlocking {
            skuDetailRequest(skuId, dcCode).apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val inventoryList = toSkuDetailResponses(body)
                assertEquals(1, inventoryList.size)
                assertEquals(true, inventoryList.first().isUsable)
                assertEquals(inventoryInfo.inventory.first().qty.getValue().toLong(), inventoryList.first().quantity)
                assertEquals(dcCode, inventoryList.first().dcCode)
                assertEquals(inventoryInfo.inventory.first().expiryDate, inventoryList.first().expiryDate)
                assertNull(inventoryList.first().unusableStockReason)
            }
        }
    }

    @Test fun `should mark expiring and expired inventory as unusable`() {
        val skuId = inventoryRecordVE2.skuId
        val dcCode = inventoryRecordVE2.dcCode

        runBlocking {
            skuDetailRequest(skuId, dcCode).apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val inventoryList = toSkuDetailResponses(body)
                assertEquals(2, inventoryList.size)
                inventoryList.forEach {
                    assertFalse(it.isUsable!!)
                    assertNotNull(it.unusableStockReason)
                }
            }
        }
    }

    @Test fun `same skuId in 2 DCs should return 2 skuDetails if both DCs are requested for same skuId`() {
        assertEquals(inventoryFromDC1.skuId, inventoryFromDC2.skuId)
        assertEquals(inventoryFromDC1.date, inventoryFromDC2.date)
        runBlocking {
            skuDetailRequest(commonSkuId, inventoryFromDC1.dcCode, inventoryFromDC2.dcCode)
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val body = bodyAsText()
                    assertNotNull(body)
                    val result = toSkuDetailResponses(body)
                    val expected = listOf(inventoryFromDC1.dcCode, inventoryFromDC2.dcCode)
                    assertEquals(expected.size, result.size)
                    assertTrue(result.map { it.dcCode }.toSet().containsAll(expected))
                }
        }
    }

    @Test fun `should return the stock for the current day when multiple records are present`() {
        val skuId = inventoryRecordIT.skuId
        val dcCode = listOf(inventoryRecordIT.dcCode)

        runBlocking {
            skuDetailRequest(skuId, dcCode.joinToString(",")).apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val inventoryList = toSkuDetailResponses(body)
                assertEquals(1, inventoryList.size)
                assertTrue(inventoryList.map { it.dcCode }.toList().containsAll(dcCode))
                val skuDetail = inventoryList[0]
                assertEquals(inventoryInfo.inventory.first().qty.getValue().toLong(), skuDetail.quantity)
                assertEquals(inventoryInfo.inventory.first().expiryDate, skuDetail.expiryDate)
            }
        }
    }

    @Test fun `should return stock is usable for an inventory with no expiry date`() {
        val skuId = inventoryRecordNoExpiry.skuId
        val dcCode = inventoryRecordNoExpiry.dcCode

        runBlocking {
            skuDetailRequest(skuId, dcCode).apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val inventoryList = toSkuDetailResponses(body)
                assertEquals(1, inventoryList.size)
                assertTrue(inventoryList.first().isUsable ?: false)
                assertNull(inventoryList.first().expiryDate)
            }
        }
    }

    @Test
    fun `should return usable stock if unexpired and it expires exactly acceptableCodeLife days after today`() {
        // given inventory expiring after its shelf life
        val acceptableCodeLifeDays = 7
        val skuId = UUID.randomUUID()
        val inventory =
            InventoryValue(
                listOf(
                    Inventory(
                        SkuQuantity.fromBigDecimal(BigDecimal.ONE),
                        today.plusDays(acceptableCodeLifeDays.toLong()),
                        location = Location("", LOCATION_TYPE_STAGING, null),
                    ),
                ),
            )
        val skuSpecificationRecord = SkuSpecificationRecord().apply {
            id = skuId
            name = ""
            code = ""
            category = ""
            acceptableCodeLife = 0
            coolingType = ""
            packaging = ""
            market = ""
            uom = com.hellofresh.cif.api.schema.enums.Uom.UOM_LBS
        }
        val inventoryRecord = InventorySnapshotRecord(
            dcCode,
            today,
            skuId,
            UUID.randomUUID(),
            JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventory)),
            LocalDateTime.now(),
            LocalDateTime.now(),
            OffsetDateTime.now(ZoneOffset.UTC),
        )
        dsl.batchInsert(inventoryRecord, skuSpecificationRecord).execute()
        refreshSkuView()

        runBlocking {
            skuDetailRequest(skuId, dcCode).apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val inventoryList = toSkuDetailResponses(body)
                assertEquals(1, inventoryList.size)
                assertTrue(inventoryList.first().isUsable!!)
            }
        }
    }

    @Test
    fun `should default to 5 days shelf life and return usable if inventory expires in 5 days and the skuSpecification not available for skuId`() {
        val inventory = InventoryValue(
            listOf(
                Inventory(
                    SkuQuantity.fromBigDecimal(BigDecimal.ONE),
                    today.plusDays(DEFAULT_MAX_DAYS_BEFORE_EXPIRY),
                    location = Location("", LOCATION_TYPE_STAGING, null),
                ),
            ),
        )
        val skuId = UUID.randomUUID()
        insertSkus(setOf(skuId))
        val inventoryRecord = InventorySnapshotRecord(
            dcCode,
            today,
            skuId,
            UUID.randomUUID(),
            JSONB.valueOf(objectMapperSnakeCaseStrategy.writeValueAsString(inventory)),
            LocalDateTime.now(),
            LocalDateTime.now(),
            OffsetDateTime.now(ZoneOffset.UTC),
        )

        dsl.batchInsert(inventoryRecord).execute()

        runBlocking {
            skuDetailRequest(skuId, dcCode).apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val inventoryList = toSkuDetailResponses(body)
                assertEquals(1, inventoryList.size)
                assertTrue(inventoryList.first().isUsable!!)
            }
        }
    }

    @Test fun `should return a list of inventories ordered by useBy date`() {
        val skuId = inventoryRecordVE3.skuId
        val dcCode = inventoryRecordVE3.dcCode

        runBlocking {
            skuDetailRequest(skuId, dcCode).apply {
                val body = bodyAsText()
                assertEquals(HttpStatusCode.OK, status)
                assertNotNull(body)
                val inventoryList = toSkuDetailResponses(body)
                val expDatesSorted = inventoryMultipleEntries.inventory.map { it.expiryDate }.sortedBy { it }
                assertEquals(expDatesSorted, inventoryList.map { it.expiryDate })
            }
        }
    }

    @Test fun `should return 400 Bad Request if dcCodes param is missing`() {
        skuDetailRequest(UUID(0, 0), "").apply {
            assertEquals(HttpStatusCode.BadRequest, status)
        }
    }

    @Test fun `should return 500 internal server error if inventory has invalid json`() {
        val dcCode = inventoryRecordInvalidJsonValue.dcCode
        val skuId = inventoryRecordInvalidJsonValue.skuId
        skuDetailRequest(skuId, dcCode).apply {
            assertEquals(HttpStatusCode.InternalServerError, status)
        }
    }

    @Test fun `should return 500 internal server error if one of the inventory value has null json`() {
        val dcCode = nullJsonValue1.dcCode
        val skuId = nullJsonValue1.skuId
        skuDetailRequest(skuId, dcCode).apply {
            assertEquals(HttpStatusCode.InternalServerError, status)
        }
    }

    @Test
    fun `return HTTP 200 with an empty result when there is no data for the given DC & SKU`() {
        val nonExistentDcCode = "XX"
        val nonExistentSkuId = UUID.randomUUID()
        runBlocking {
            skuDetailRequest(nonExistentSkuId, nonExistentDcCode).apply {
                assertEquals(HttpStatusCode.OK, status)
                assertEquals("[]", bodyAsText())
            }
        }
    }

    private fun skuDetailRequest(skuId: UUID, vararg dcCodes: String): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            val dcCodesReq = Parameters.build {
                dcCodes.forEach { this.append("dcCodes", it) }
            }.formUrlEncode()
            this.application {
                configureJwtAuth(JwtCredentials(jwtSecret, "", "", "", jwksURI), true)
                inventoryRoutingModule(skuInventoryDetailService, timeOutInMillis) ()
            }
            response = client.get("/sku/$skuId/detail?$dcCodesReq") {
                this.addAuthHeader(authorEmail, authorName, jwtSecret)
            }
        }
        return response
    }

    private fun toSkuDetailResponses(body: String) =
        objectMapperCamelCaseStrategy.readValue(
            body,
            object : TypeReference<List<SkuDetailResponse>>() {},
        )
}
