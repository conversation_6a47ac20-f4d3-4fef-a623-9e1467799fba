package com.hellofresh.cif.api.warmup

import com.hellofresh.cif.api.calculation.CalculationResponseMapper
import com.hellofresh.cif.api.calculation.CalculationsPage
import com.hellofresh.cif.api.calculation.CalculationsService
import com.hellofresh.cif.api.calculation.WeeklyView
import com.hellofresh.cif.api.calculation.calculationRoutingModule
import com.hellofresh.cif.api.calculation.generated.model.ActualInboundResponse
import com.hellofresh.cif.api.calculation.generated.model.CalculationResponse
import com.hellofresh.cif.api.calculation.generated.model.ExpectedInboundResponse
import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.api.configuration.DcConfiguration
import com.hellofresh.cif.api.configuration.configRoutingModule
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.note.NoteService
import com.hellofresh.cif.api.note.noteModule
import com.hellofresh.cif.api.po.PurchaseOrderService
import com.hellofresh.cif.api.po.purchaseOrderModule
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.default
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import io.ktor.server.application.Application
import io.ktor.server.testing.testApplication
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.coEvery
import io.mockk.coVerify
import io.mockk.every
import io.mockk.mockk
import java.math.BigDecimal.TEN
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek.MONDAY
import java.time.Duration
import java.time.LocalDate
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.time.toKotlinDuration
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class ForecastApiWarmupTest {

    private val jwtCredentials = JwtCredentials(UUID.randomUUID().toString(), "test", "", "", "https://test.com")

    @Test
    fun `forecast api warmup calls different apis`() {
        testApplication {
            application {
                initRoutes()
            }

            ForecastApiWarmup(jwtCredentials, SimpleMeterRegistry(), client)
                .run()

            coVerify(atLeast = 1) { poService.findPurchaseOrders(any()) }
            coVerify(atLeast = 1) { configRepo.fetchByMarket(any()) }
            coVerify(atLeast = 1) { calculationsService.getWeeklyCalculations(any()) }
            coVerify(atLeast = 1) { noteService.getNotes(any()) }
        }
    }

    private fun Application.initRoutes() {
        configureJwtAuth(jwtCredentials, true)
        configRoutingModule(configRepo, timeOutInMillis)()
        calculationRoutingModule(
            calculationsService,
            calculationResponseMapper,
            timeOutInMillis,
        )()
        noteModule(noteService, timeOutInMillis)()
        purchaseOrderModule(poService, dcConfigService, timeOutInMillis)()
    }

    companion object {
        private val timeOutInMillis = Duration.ofMillis(1000L).toKotlinDuration()
        private val configRepo = mockk<ConfigService>(relaxed = true)
        private val calculationsService = mockk<CalculationsService>()
        private val calculationResponseMapper = mockk<CalculationResponseMapper>()
        private val noteService = mockk<NoteService>(relaxed = true)
        private val poService = mockk<PurchaseOrderService>(relaxed = true)
        private val dcConfigService = mockk<DcConfigService>(relaxed = true)

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            val dcConfiguration = DistributionCenterConfiguration.default("DC").copy(hasCleardown = false)
            coEvery { configRepo.fetchByMarket(any()) } returns mapOf(
                "DC" to
                    DcConfiguration(
                        dcConfiguration.dcCode,
                        dcConfiguration.market,
                        dcConfiguration.productionStart,
                        dcConfiguration.cleardown,
                        dcConfiguration.zoneId,
                        null,
                        false,
                        dcConfiguration.poCutoffTime,
                    ),
            )
            coEvery { dcConfigService.dcConfigurations["DC"] } returns dcConfiguration

            val weeklyView = mockk<WeeklyView>()
            val calculationsPage = CalculationsPage(listOf(weeklyView), 1, 1, 1)
            coEvery { calculationsService.getWeeklyCalculations(any()) } returns calculationsPage

            coEvery {
                calculationResponseMapper.toWeeklyCalculationResponse(weeklyView)
            } returns mockk<CalculationResponse>(relaxed = true).apply {
                every { skuId } returns UUID.randomUUID()
                every { dcCode } returns "DC"
                every { week } returns DcWeek(LocalDate.now(UTC), MONDAY).toString()
                every { netNeeds } returns TEN
                every { stockUpdate } returns ZERO
                every { usableStock } returns ZERO
                every { unusableStock } returns ZERO
                every { incomingPos } returns mockk {
                    every { incomingPos } returns ExpectedInboundResponse(ZERO)
                }
                every { inbound } returns mockk {
                    every { inbound } returns ActualInboundResponse(ZERO)
                }
                every { consumption } returns ZERO
                every { actualConsumption } returns ZERO
                every { dailyNeed } returns ZERO
                every { closingStock } returns ZERO
                every { safetyStock } returns ZERO
                every { safetyStockNeeds } returns ZERO
                every { expectedInboundTransferOrders } returns emptyList()
                every { expectedInboundTransferOrdersQuantity } returns ZERO
                every { actualInboundTransferOrders } returns emptyList()
                every { actualInboundTransferOrdersQuantity } returns ZERO
                every { expectedOutboundTransferOrders } returns emptyList()
                every { expectedOutboundTransferOrdersQuantity } returns ZERO
            }
        }
    }
}
