package com.hellofresh.cif.api.calculation.projectedWaste

import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.cif.api.calculation.CalculationFunctionalTest
import com.hellofresh.cif.api.calculation.Page
import com.hellofresh.cif.api.calculation.ProjectedWasteCalculationFilterRequest
import com.hellofresh.cif.api.calculation.generated.model.ProjectedWasteResponse
import com.hellofresh.cif.demand.lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.inventory.models.LocationType.LOCATION_TYPE_STAGING
import java.math.BigDecimal
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertTrue

internal class ProjectedWasteCalculationsApiFunctionalTest : CalculationFunctionalTest() {
    private val dcCodeVE = "VE"
    private val dcCodesVE = setOf(dcCodeVE)
    private val unusableInventory = listOf(
        ForecastInventory(
            qty = BigDecimal(100),
            expiryDate = LocalDate.now(UTC).plusDays(10),
            locationType = LOCATION_TYPE_STAGING,
        ),
    )
    private val dailyCalculationsCount = 7

    @Test
    fun `should return the correct sku which has projected waste`() {
        val skuId1 = UUID.randomUUID()
        val sku1 = Sku("sku code 1", skuId1)
        val currentWeek = prepareSkuCalculationRecords(sku1)
        val skuId2 = UUID.randomUUID()
        val sku2 = Sku("sku code 2", skuId2)
        insertSkuCalculations(
            sku2,
            dailyCalculationsCount,
            dcCodeVE,
            currentWeek,
            unusableInventory = unusableInventory,
        )

        val response =
            getProjectedWasteCalculationsOkResponse(
                ProjectedWasteCalculationFilterRequest(
                    dcCodes = dcCodesVE.toList(),
                    weeks = emptyList(),
                    skuCodes = listOf(sku1.skuCode, sku2.skuCode),
                    pageRequest = Page(
                        page = 1,
                        skuCount = 50,
                    ),
                ),
            )

        assertEquals(7, response.projectedWasteResponses?.size)
        assertProjectedWasteResponse(response.projectedWasteResponses!!, skuId1)
    }

    @Test
    fun `should return the correct sku which has projected waste in csv format`() {
        val skuId1 = UUID.randomUUID()
        val sku1 = Sku("sku code 1", skuId1)
        val currentWeek = prepareSkuCalculationRecords(sku1)
        val skuId2 = UUID.randomUUID()
        val sku2 = Sku("sku code 2", skuId2)
        insertSkuCalculations(
            sku2,
            dailyCalculationsCount,
            dcCodeVE,
            currentWeek,
            unusableInventory = unusableInventory,
        )

        val response =
            getProjectedWasteCalculationsOkResponse(
                ProjectedWasteCalculationFilterRequest(
                    dcCodes = dcCodesVE.toList(),
                    weeks = emptyList(),
                    skuCodes = listOf(sku1.skuCode, sku2.skuCode),
                    pageRequest = Page(
                        page = 1,
                        skuCount = 50,
                    ),
                ),
            )

        assertEquals(7, response.projectedWasteResponses?.size)
        response.projectedWasteResponses?.get(0)?.let { assertProjectedWasteResponseCsvValue(it) }
    }

    @Test
    fun `should return only the skus which has projected waste of given filter sku-category`() {
        val skuId1 = UUID.randomUUID()
        val sku1 = Sku("sku code 1", skuId1)
        prepareSkuCalculationRecords(sku1)
        val filterProjectedWasteWithCategory =
            getProjectedWasteCalculationsOkResponse(
                ProjectedWasteCalculationFilterRequest(
                    dcCodes = dcCodesVE.toList(),
                    weeks = emptyList(),
                    skuCodes = listOf(sku1.skuCode),
                    pageRequest = Page(
                        page = 1,
                        skuCount = 50,
                    ),
                    skuCategories = listOf("PHF"),
                ),
            )

        assertEquals(7, filterProjectedWasteWithCategory.projectedWasteResponses?.size)
        assertProjectedWasteResponse(filterProjectedWasteWithCategory.projectedWasteResponses!!, skuId1)
    }

    @Test
    fun `should return only the skus which has projected waste of given filter expiringInMax, expiringInMin`() {
        val skuId1 = UUID.randomUUID()
        val sku1 = Sku("sku code 1", skuId1)
        prepareSkuCalculationRecords(sku1)

        val filterProjectedWasteWithCategory =
            getProjectedWasteCalculationsOkResponse(
                ProjectedWasteCalculationFilterRequest(
                    dcCodes = dcCodesVE.toList(),
                    weeks = emptyList(),
                    skuCodes = listOf(sku1.skuCode),
                    pageRequest = Page(
                        page = 1,
                        skuCount = 50,
                    ),
                    skuCategories = listOf("PHF"),
                    expiringInMin = 0,
                    expiringInMax = 2,
                ),
            )

        assertEquals(7, filterProjectedWasteWithCategory.projectedWasteResponses?.size)
        assertProjectedWasteResponse(filterProjectedWasteWithCategory.projectedWasteResponses!!, skuId1)
    }

    @Test
    fun `should return zero projected waste if there are no expiring skus`() {
        val newDcConfigRecord = DcConfigRecord(
            "BN", "DACH", "FRIDAY", "FRIDAY", "Europe/Berlin",
            true, LocalDateTime.now(UTC), LocalDateTime.now(UTC), LocalDateTime.now(UTC), true, null, null,
            LocalTime.now(), emptyArray()
        )
        dsl.batchInsert(newDcConfigRecord).execute()
        dcConfigService.fetchOnDemand()
        val currentWeek = DcWeek(
            LocalDate.now(UTC),
            DayOfWeek.valueOf(newDcConfigRecord.productionStart),
        ).value

        val skuId1 = UUID.randomUUID()
        val sku1 = Sku("sku code 1", skuId1)
        insertSkuCalculations(
            sku1,
            dailyCalculationsCount,
            "BN",
            currentWeek,
            unusableInventory = unusableInventory,
        )
        val response =
            getProjectedWasteCalculationsOkResponse(
                ProjectedWasteCalculationFilterRequest(
                    dcCodes = listOf("BN"),
                    weeks = emptyList(),
                    skuCodes = listOf(sku1.skuCode),
                    pageRequest = Page(
                        page = 1,
                        skuCount = 50,
                    ),
                ),
            )

        assertEquals(0, response.projectedWasteResponses?.size)
    }

    private fun assertProjectedWasteResponseCsvValue(
        projectedWasteResponse: ProjectedWasteResponse,
    ) {
        assertEquals(projectedWasteResponse.dcCode, "VE")
        assertEquals(projectedWasteResponse.sku.skuCode, "sku code 1")
        assertEquals(projectedWasteResponse.sku.skuName, "sku name ${projectedWasteResponse.sku.skuId}")
        assertEquals(projectedWasteResponse.quantity.toString(), "100")
        assertTrue(projectedWasteResponse.isUsable!!)
    }

    private fun assertProjectedWasteResponse(
        projectedWasteCalculationsResponses: List<ProjectedWasteResponse>,
        skuId: UUID
    ) {
        projectedWasteCalculationsResponses.map { projectedWasteCalculationsResponse ->
            projectedWasteCalculationsResponse.apply {
                assertEquals("VE", projectedWasteCalculationsResponse.dcCode)
                assertEquals("sku code 1", projectedWasteCalculationsResponse.sku.skuCode)
                assertEquals("sku name $skuId", projectedWasteCalculationsResponse.sku.skuName)
                assertEquals(100, projectedWasteCalculationsResponse.quantity)
                assertTrue(projectedWasteCalculationsResponse.isUsable!!)
            }
        }
    }

    private fun prepareSkuCalculationRecords(sku1: Sku): String {
        dcConfigService.fetchOnDemand()
        val currentWeek = DcWeek(
            LocalDate.now(UTC),
            productionStart,
        ).value

        insertSkuCalculations(
            sku1,
            dailyCalculationsCount,
            dcCodeVE,
            currentWeek,
            unusableInventory = unusableInventory,
            expiredParam = BigDecimal(100),
        )
        return currentWeek
    }
}
