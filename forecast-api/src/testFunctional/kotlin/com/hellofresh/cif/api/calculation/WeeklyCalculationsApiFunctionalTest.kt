package com.hellofresh.cif.api.calculation

import com.hellofresh.cif.api.calculation.fixtures.RandomFixture
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.api.schema.enums.SubbedType.SUB_IN
import com.hellofresh.cif.api.toSkuSpecification
import com.hellofresh.demand.models.ConsumptionDetails
import com.hellofresh.demand.models.CrossDockings
import com.hellofresh.demand.models.Prekitting
import com.hellofresh.demand.models.Prekittings
import com.hellofresh.demand.models.RecipeBreakdown
import com.hellofresh.demand.models.Substitution
import com.hellofresh.demand.models.Substitutions
import io.ktor.client.statement.bodyAsText
import io.ktor.http.HttpStatusCode
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.junit.jupiter.api.Test

private const val HELLO_FRESH = "HelloFresh"
private const val EVERY_PLATE = "EveryPlate"

class WeeklyCalculationsApiFunctionalTest : CalculationFunctionalTest() {
    val dcVE = "VE"
    private val dcVeList = listOf(dcVE)

    @Test
    fun `should return 400 Bad request when week is not given`() {
        val req = CalculationRequest(
            dcCodes = dcVeList,
            weeks = listOf(),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
        )
        runBlocking {
            getWeeklyCalculations(req).apply {
                assertEquals(HttpStatusCode.BadRequest, status)
                assertEquals("""{"reason":"Required Parameter: weeks is not specified"}""", bodyAsText())
            }
        }
    }

    @Test
    fun `should return one page given calculations for 2 skus when 2 skus per page is requested`() {
        val weeks = (0..1)
            .map { "2022-W0$it" }
            .onEach { insertProdCalculationRecords(dc = dcVE, week = it) }

        val req = CalculationRequest(
            dcCodes = dcVeList,
            weeks = weeks,
            pageRequest = Page(1, 2),
            consumptionDaysAhead = 0,
        )

        val response = getWeeklyCalculationsOkResponse(req)

        assertEquals(1, response.totalPages)
        assertEquals(2, response.totalSkusCount)
        assertEquals(2, response.calculations.size)
    }

    @Test
    fun `should return weekly calculations with sum of actual consumptions`() {
        val actualConsumptionQty = BigDecimal(100)
        insertProdCalculationRecords(
            id = UUID(0, 0),
            daysCount = 1,
            dc = dcVE,
            week = "2022-W01",
            accConsumption = actualConsumptionQty,
        )

        val req = CalculationRequest(
            dcCodes = dcVeList,
            weeks = listOf("2022-W01"),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
        )

        val response = getWeeklyCalculationsOkResponse(req)

        assertEquals(1, response.totalPages)
        assertEquals(actualConsumptionQty, response.calculations.first().actualConsumption)
    }

    @Test
    fun `should return weekly calculations with sum of net needs`() {
        val accConsumption = BigDecimal(400)
        val closeStock = BigDecimal(300)
        val netNeeds = BigDecimal(800)
        insertProdCalculationRecords(
            id = UUID(0, 0),
            daysCount = 7,
            dc = dcVE,
            week = "2022-W01",
            accConsumption = accConsumption,
            closeStock = closeStock,
            netNeedsParam = netNeeds,
        )

        val req = CalculationRequest(
            dcCodes = dcVeList,
            weeks = listOf("2022-W01"),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
        )

        val response = getWeeklyCalculationsOkResponse(req)

        assertEquals(1, response.totalPages)
        assertEquals(BigDecimal(5600), response.calculations.first().netNeeds)
    }

    @Test
    fun `should return weekly calculations with closing stock`() {
        val closingStock = BigDecimal(100)
        insertProdCalculationRecords(
            id = UUID(0, 0),
            daysCount = 1,
            dc = dcVE,
            week = "2022-W01",
            closeStock = closingStock,
        )

        val req = CalculationRequest(
            dcCodes = dcVeList,
            weeks = listOf("2022-W01"),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
        )

        val response = getWeeklyCalculationsOkResponse(req)

        assertEquals(1, response.totalPages)
        assertEquals(closingStock, response.calculations.first().closingStock)
    }

    @Test
    fun `should return 3 pages given calculations for 7 skus when 3 skus per page is requested`() {
        val weeks = (0..6)
            .map { "2022-W0$it" }
            .onEach { insertProdCalculationRecords(dc = dcVE, week = it) }

        val req = CalculationRequest(
            dcCodes = dcVeList,
            weeks = weeks,
            pageRequest = Page(1, 3),
            consumptionDaysAhead = 0,
        )

        val response = getWeeklyCalculationsOkResponse(req)

        assertEquals(3, response.totalPages)
        assertEquals(7, response.totalSkusCount)
        assertEquals(3, response.calculations.size)
    }

    @Test
    fun `should return 200 OK, when query params are well formatted and no calculations are found`() {
        val req = CalculationRequest(
            dcCodes = dcVeList,
            weeks = listOf("2022-W01"),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
        )

        val response = getWeeklyCalculationsOkResponse(req)

        assertEquals(0, response.totalPages)
        assertEquals(0, response.calculations.size)
    }

    @Test
    fun `should return total sku at risk count in weekly calculations response matching given request`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.parse("2022-01-01")
        val firstCulinarySku = firstCulinarySku()
        val calculation = emptyCalculationRecord(givenWeek, day1, firstCulinarySku)

        val secondCulinarySku = secondCulinarySku()
        val calculationWithShortage = emptyCalculationRecord(
            givenWeek,
            day1,
            secondCulinarySku,
        ).apply { this.dailyNeeds = ONE }

        dsl.batchInsert(listOf(calculation, calculationWithShortage)).execute()
        insertSkuRecords(
            firstCulinarySku.toSkuSpecificationRecord(),
            secondCulinarySku.toSkuSpecificationRecord()
        )

        val req = CalculationRequest(
            weeks = listOf(givenWeek),
            dcCodes = listOf(FIXTURE_DC),
            pageRequest = Page(1, 3),
            consumptionDaysAhead = 0,
        )

        // when & then
        val response = getWeeklyCalculationsOkResponse(req)

        assertEquals(2, response.calculations.size)
        assertEquals(1, response.totalSkusAtRiskCount)
        assertEquals(2, response.totalSkusCount)
        assertTrue { response.calculations.any { it.skuId == secondCulinarySku.id } }
    }

    @Test
    fun `should return safety stock quantities in weekly calculations`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.parse("2022-01-01")
        val firstCulinarySku = firstCulinarySku()
        val calculation = emptyCalculationRecord(givenWeek, day1, firstCulinarySku).apply {
            safetystock = BigDecimal(11)
        }
        val calculationMonday = emptyCalculationRecord(givenWeek, day1.with(DayOfWeek.MONDAY), firstCulinarySku).apply {
            safetystockNeeds = BigDecimal(12)
        }
        dsl.batchInsert(calculation, calculationMonday).execute()
        insertSkuRecords(firstCulinarySku.toSkuSpecificationRecord())

        // when
        val req = CalculationRequest(
            weeks = listOf(givenWeek),
            dcCodes = listOf(FIXTURE_DC),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
        )
        val response = getWeeklyCalculationsOkResponse(req)

        // then
        assertEquals(1, response.calculations.size)
        assertEquals(calculation.safetystock, response.calculations.first().safetyStock)
        assertEquals(calculationMonday.safetystockNeeds, response.calculations.first().safetyStockNeeds)
    }

    @Test
    fun `should return consumption details in weekly calculations`() {
        // given
        val givenWeek = "2022-W01"
        val day1 = LocalDate.now()
        val subInSku = firstCulinarySku()

        val calculationIn = emptyCalculationRecord(givenWeek, day1.with(DayOfWeek.MONDAY), subInSku)
        val calculationOut = emptyCalculationRecord(givenWeek, day1.with(DayOfWeek.TUESDAY), subInSku)
        val calculationInOut = emptyCalculationRecord(givenWeek, day1.with(DayOfWeek.WEDNESDAY), subInSku)

        val calculations = listOf(calculationIn, calculationOut, calculationInOut)
        dsl.batchInsert(calculations).execute()
        insertSkuRecords(subInSku.toSkuSpecificationRecord())
        val consumptionDetails = JSONB.valueOf(
            objectMapper.writeValueAsString(
                ConsumptionDetails(
                    recipeBreakdowns = listOf(
                        RecipeBreakdown(
                            brand = HELLO_FRESH,
                            recipeIndex = "1",
                            qty = 100L,
                        ),
                        RecipeBreakdown(
                            brand = HELLO_FRESH,
                            recipeIndex = "2",
                            qty = 100L,
                        ),
                        RecipeBreakdown(
                            brand = EVERY_PLATE,
                            recipeIndex = "3",
                            qty = 100L,
                        ),
                        RecipeBreakdown(
                            brand = EVERY_PLATE,
                            recipeIndex = "4",
                            qty = 100L,
                        ),
                    ),
                    prekitting = Prekittings(
                        `in` = listOf(
                            Prekitting(
                                qty = 100,
                            ),
                        ),
                        out = listOf(
                            Prekitting(
                                qty = 100,
                            ),
                        ),
                    ),
                    substitutions = Substitutions(
                        `in` = listOf(
                            Substitution(
                                brand = HELLO_FRESH,
                                recipeIndex = "1",
                                qty = 100L,
                            ),
                        ),
                        out = listOf(
                            Substitution(
                                brand = HELLO_FRESH,
                                recipeIndex = "1",
                                qty = 100L,
                            ),
                        ),
                    ),
                    crossDockings = CrossDockings(emptyList(), emptyList())
                ),
            ),
        )
        val demand = calculations.map {
            createDemand(
                it.date,
                skuIdParam = it.cskuId,
                dcCodeParam = it.dcCode,
                subbed = SUB_IN,
                consumptionDetails = consumptionDetails,
                subInQty = 100L,
            )
        }
        dsl.batchInsert(demand).execute()
        dsl.execute("REFRESH MATERIALIZED VIEW calculation_substitution_view")

        // when
        val req = CalculationRequest(
            weeks = listOf(givenWeek),
            dcCodes = listOf(FIXTURE_DC),
            pageRequest = Page(1, 1),
            consumptionDaysAhead = 0,
        )
        val response = getWeeklyCalculationsOkResponse(req)

        // then
        assertEquals(1, response.calculations.size)
        assertEquals(
            600L,
            response.calculations.first().consumptionDetails?.consumption?.find {
                it.brand == HELLO_FRESH
            }?.qty,
        )
        assertEquals(
            600L,
            response.calculations.first().consumptionDetails?.consumption?.find {
                it.brand == EVERY_PLATE
            }?.qty,
        )
        assertEquals(300L, response.calculations.first().consumptionDetails?.substituted)
        assertEquals(300L, response.calculations.first().consumptionDetails?.prekitting)
    }

    @Suppress("LongParameterList")
    private fun insertProdCalculationRecords(
        dc: String,
        week: String,
        id: UUID = UUID.randomUUID(),
        daysCount: Long = 7L,
        accConsumption: BigDecimal = ZERO,
        closeStock: BigDecimal = ZERO,
        netNeedsParam: BigDecimal = ZERO,
    ) {
        assertTrue(daysCount <= 7)
        (0 until daysCount).map {
            RandomFixture().calculationRecord {
                dcCode = dc
                productionWeek = week
                date = LocalDate.now().plusDays(it.toLong())
                cskuId = id
                actualConsumption = accConsumption
                closingStock = closeStock
                netNeeds = netNeedsParam
            }
        }.let {
            insertSkuRecords(toSkuSpecification(it.first()))
            dsl.batchInsert(it).execute()
        }
    }
}
