package com.hellofresh.cif.api.reporting

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.fixtures.Default
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.api.schema.tables.records.InventoryVarianceRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationViewRecord
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.inventory.models.variance.DailyInventoryVarianceData
import com.hellofresh.inventory.models.variance.InventoryVariance
import java.math.BigDecimal
import java.time.DayOfWeek.FRIDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import org.jooq.JSONB
import org.junit.jupiter.api.BeforeEach

open class CommonStockReportingTest : FunctionalTest() {

    @BeforeEach
    fun initConfig() {
        val dcConfigRecord = DcConfigRecord().apply {
            market = "DE"
            this.dcCode = dc
            productionStart = cleardownWeekday.name
            zoneId = "UTC"
            enabled = true
            recordTimestamp_ = LocalDateTime.now()
            hasCleardown = true
            cleardown = cleardownWeekday.name
        }
        dsl.batchInsert(dcConfigRecord).execute()
    }

    fun persistCalculationRecordsAndSku(
        skuId: UUID = UUID.randomUUID(),
        openingStockCleardown: BigDecimal,
        closingStockDayBeforeCleardown: BigDecimal,
        dcCode: String = dc,
        numDays: Int = 7,
        market: String,
    ): WeekCalculations {
        val sku = createSku(skuId, market)
        dsl.executeInsert(sku)
        refreshSkuView()
        return persistCalculationRecords(
            skuId,
            openingStockCleardown,
            closingStockDayBeforeCleardown,
            dcCode,
            numDays,
            market,
        )
    }

    @Suppress("SpreadOperator")
    fun persistCalculationRecords(
        skuId: UUID = UUID.randomUUID(),
        openingStockCleardown: BigDecimal,
        closingStockDayBeforeCleardown: BigDecimal,
        dcCode: String = dc,
        numDays: Int = 7,
        market: String,
    ): WeekCalculations {
        // calculation on cleardown with openingStock != closing stock above
        val calculationRecordCleardown = Default.calculationRecord {
            this.cskuId = skuId
            this.dcCode = dcCode
            this.productionWeek = currentWeekStr
            this.openingStock = openingStockCleardown
            this.date = currentCleardownDate
        }
        // calculations for the rest of the week
        val weekCalculations = (1L..numDays).reversed().map { index ->
            Default.calculationRecord {
                this.cskuId = skuId
                this.dcCode = dcCode
                this.productionWeek = previousWeek
                this.openingStock = openingStockCleardown
                this.closingStock = closingStockDayBeforeCleardown
                this.actualInbound = index.toBigDecimal()
                this.date = currentCleardownDate.minusDays(index)
            }
        }
        val calculationRecords = listOf(calculationRecordCleardown, *weekCalculations.toTypedArray())
        dsl.batchInsert(calculationRecords).execute()
        // after inserting CalculationRecords, the result cannot be compared with the given CalculationRecords above because the
        // issued_at of the persisted record differs. Therefore, fetch the calculations from the DB and return them to be able to
        // perform equality assertions in tests.
        val dbCalculationsForSku = dsl.selectFrom(Tables.CALCULATION)
            .where(Tables.CALCULATION.CSKU_ID.eq(skuId))
            .toList().sortedBy { it.date }
        // return WeekCalculations here in order to make it easy to compare results in tests
        val stockCalculation = dbCalculationsForSku.map {
            StockCalculation(
                createSkuView(skuId, market),
                it,
            )
        }
        return WeekCalculations(stockCalculation.last(), stockCalculation.dropLast(1))
    }

    fun persistInventoryVarianceFixtures(inventoryVariance: InventoryVariance) =
        dsl.executeInsert(
            InventoryVarianceRecord().apply {
                dcCode = inventoryVariance.dcCode
                productionWeek = inventoryVariance.dcWeek
                skuId = inventoryVariance.skuId
                cleardownVariance = inventoryVariance.cleardownVariance.getValue()
                liveVariance = inventoryVariance.liveVariance.getValue()
                value = toJsonB(inventoryVariance.dailyInventoryVarianceData)
                uom = Uom.valueOf(inventoryVariance.uom.name)
            },
        )

    fun persistSkuSpecification(skuId: UUID) {
        val skuSpecificationRecord = SkuSpecificationRecord().apply {
            id = skuId
            name = "Name"
            code = "SPI-0000"
            category = "SPI"
            acceptableCodeLife = 0
            coolingType = ""
            packaging = ""
            market = "dach"
            uom = Uom.UOM_LBS
        }
        dsl.executeInsert(skuSpecificationRecord)
    }

    private fun toJsonB(dailyInventoryVarianceData: List<DailyInventoryVarianceData>): JSONB =
        JSONB.valueOf(objectMapper.writeValueAsString(dailyInventoryVarianceData))

    fun createSku(skuId: UUID, market: String) =
        SkuSpecificationRecord().apply {
            id = skuId
            name = "SKU 1"
            code = "PRO-00-00000-1"
            category = "PRO"
            acceptableCodeLife = 0
            coolingType = ""
            packaging = ""
            this.market = market
            uom = Uom.UOM_LBS
            brands = emptyArray()
        }

    fun createSkuView(skuId: UUID, market: String) =
        SkuSpecificationViewRecord().apply {
            id = skuId
            name = "SKU 1"
            code = "PRO-00-00000-1"
            category = "PRO"
            acceptableCodeLife = 0
            coolingType = ""
            packaging = ""
            this.market = market
            uom = Uom.UOM_LBS
            brands = emptyArray()
        }

    companion object {
        private val cleardownWeekday = FRIDAY
        val currentCleardownDate = LocalDate.now().with(TemporalAdjusters.previousOrSame(cleardownWeekday))
        val currentDcWeek = DcWeek(currentCleardownDate, cleardownWeekday)
        val currentWeekStr = currentDcWeek.toString()
        val previousWeek = DcWeek(currentCleardownDate.minusWeeks(1), cleardownWeekday).toString()
        const val dc = "VE"
        private val objectMapper = ObjectMapper().findAndRegisterModules().registerModule(JavaTimeModule())
    }
}
