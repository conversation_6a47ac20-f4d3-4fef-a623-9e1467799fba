package com.hellofresh.cif.api.note

import InfraPreparation.getMigratedDataSource
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.AdditionalFilter
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_CONSUMPTION_ONLY
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_INBOUND_SHORTAGE
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_INVENTORY_SHORTAGE
import com.hellofresh.cif.api.calculation.AdditionalFilter.WITH_PROJECTED_WASTE
import com.hellofresh.cif.api.calculation.fixtures.Default
import com.hellofresh.cif.api.calculation.fixtures.RandomFixture
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.api.schema.Tables.NOTE
import com.hellofresh.cif.api.schema.Tables.SKU_SPECIFICATION
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.distributionCenter.models.DcWeek
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.math.BigDecimal.ONE
import java.math.BigDecimal.TEN
import java.math.BigDecimal.TWO
import java.math.BigDecimal.ZERO
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.ZoneOffset
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.test.Test
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.jooq.SQLDialect.POSTGRES
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class NoteRepositoryImplFunctionalTest : FunctionalTest() {
    private val datasource = getMigratedDataSource()
    private val dbConfig = DefaultConfiguration().apply {
        setSQLDialect(POSTGRES)
        setDataSource(datasource)
        setExecutor(Executors.newSingleThreadExecutor())
    }
    private val dsl = DSL.using(dbConfig).withMetrics(SimpleMeterRegistry())

    private val noteRepo = NoteRepositoryImpl(dsl, dsl)

    @BeforeEach
    fun cleanupDb() {
        dbConfig.dsl().deleteFrom(NOTE).execute()
    }

    @Test
    fun `should create a new note`() {
        // when
        val dbNote = persistNote("text note1")

        // then
        assertEquals(authorEmail, dbNote.authorEmail)
        assertEquals(authorName, dbNote.authorName)
        assertEquals(dcCodes, dbNote.dcCodes)
        assertEquals(dcWeeks, dbNote.weeks)
        assertEquals("text note1", dbNote.text)
        assertEquals(0, dbNote.version)
        assertFalse(dbNote.isEdited)
        assertNotNull(dbNote.createdAt)
    }

    @Test
    fun `should update an existing note`() {
        val dbNote = persistNote("text note1")

        // when
        val actualNote = runBlocking {
            noteRepo.updateNote(
                dbNote.id,
                dbNote.authorName,
                dbNote.authorEmail,
                setOf(DcWeek("2023-W12")),
                "update this test note",
            )
        }

        // then
        assertEquals(authorEmail, actualNote.authorEmail)
        assertEquals(authorName, actualNote.authorName)
        assertEquals(dcCodes, actualNote.dcCodes.toSet())
        assertEquals(setOf(DcWeek("2023-W12")), actualNote.weeks)
        assertEquals("update this test note", actualNote.text)
        assertEquals(1, actualNote.version)
        assertTrue(actualNote.isEdited)
        assertNotNull(actualNote.createdAt)
    }

    @Test
    fun `should throw an exception while deleting a deleted note`() {
        val note = persistNote("text note1")
        runBlocking {
            noteRepo.deleteNote(
                note.id,
                note.authorName,
                note.authorEmail,
            )
        }

        // when
        val exception = assertThrows<IllegalArgumentException> {
            runBlocking {
                noteRepo.deleteNote(
                    note.id,
                    note.authorName,
                    note.authorEmail,
                )
            }
        }
        assertTrue(exception.message!!.contains("Expected to update 1 record but found instead 0"))
    }

    @Test
    fun `should throw an exception while deleting a non existing note`() {
        val exception = assertThrows<IllegalArgumentException> {
            runBlocking {
                noteRepo.deleteNote(
                    UUID.randomUUID(),
                    "authorName",
                    "authorEmail",
                )
            }
        }
        assertTrue(exception.message!!.contains("Expected to update 1 record but found instead 0"))
    }

    @Test
    fun `should not allowed to update a deleted note`() {
        val note = persistNote("text note1")
        runBlocking {
            noteRepo.deleteNote(
                note.id,
                note.authorName,
                note.authorEmail,
            )
        }

        // when
        val exception = assertThrows<IllegalArgumentException> {
            runBlocking {
                noteRepo.updateNote(
                    note.id,
                    note.authorName,
                    note.authorEmail,
                    setOf(DcWeek("2023-W12")),
                    "update this test note",
                )
            }
        }

        assertTrue(exception.message!!.contains("Expected to update 1 record but found instead 0"))
    }

    @Test
    fun `should throw an exception while updating a non existing note`() {
        val exception = assertThrows<IllegalArgumentException> {
            runBlocking {
                noteRepo.updateNote(
                    UUID.randomUUID(),
                    "authorName",
                    "authorEmail",
                    setOf(DcWeek("2023-W12")),
                    "update this test note",
                )
            }
        }
        assertTrue(exception.message!!.contains("Expected to update 1 record but found instead"))
    }

    @Test
    fun `should update an existing note multiple times`() {
        val dbNote = persistNote("text note1")

        // when
        val actualNote = runBlocking {
            (0..3).map { index ->
                noteRepo.updateNote(
                    dbNote.id,
                    dbNote.authorName,
                    dbNote.authorEmail,
                    setOf(DcWeek("2023-W12")),
                    "update this test note = $index",
                )
            }
        }.last()

        // then
        assertEquals(authorEmail, actualNote.authorEmail)
        assertEquals(authorName, actualNote.authorName)
        assertEquals(dcCodes, actualNote.dcCodes.toSet())
        assertEquals(setOf(DcWeek("2023-W12")), actualNote.weeks)
        assertEquals("update this test note = 3", actualNote.text)
        assertEquals(4, actualNote.version)
        assertTrue(actualNote.isEdited)
        assertNotNull(actualNote.createdAt)
    }

    @Test
    fun `note can be updated when it has multiple sku at risk weeks associated`() {
        val dbNote = persistNote(text = "text note", dcWeeks = setOf(DcWeek("2023-W10"), DcWeek("2023-W11")))

        dbNote.weeks.forEachIndexed { index, dcWeek ->
            persistCalculationsForNote(dbNote) {
                demanded = ONE
                productionWeek = dcWeek.toString()
                date = dcWeek.getStartDateInDcWeek(MONDAY, ZoneOffset.UTC).plusDays(index.toLong())
            }
        }

        // when
        val newNote = runBlocking {
            noteRepo.updateNote(
                dbNote.id,
                dbNote.authorName,
                dbNote.authorEmail,
                dbNote.weeks,
                "update this test note",
            )
        }

        // then
        assertEquals("update this test note", newNote.text)
        assertEquals(1, newNote.version)
        assertTrue(newNote.isEdited)
    }

    @Test
    fun `note can be deleted when it has multiple sku at risk weeks associated`() {
        val dbNote = persistNote(text = "text note", dcWeeks = setOf(DcWeek("2023-W01"), DcWeek("2023-W11")))

        dbNote.weeks.forEachIndexed { index, dcWeek ->
            persistCalculationsForNote(dbNote) {
                demanded = ONE
                productionWeek = dcWeek.toString()
                date = dcWeek.getStartDateInDcWeek(MONDAY, ZoneOffset.UTC).plusDays(index.toLong())
            }
        }

        // when
        val notesDeleted = runBlocking {
            noteRepo.deleteNote(dbNote.id, dbNote.authorName, dbNote.authorEmail)
        }

        // then
        assertEquals(1, notesDeleted)
    }

    @Test
    fun `should be able to delete a note`() {
        val note = persistNote("text note1")
        runBlocking {
            noteRepo.deleteNote(
                note.id,
                note.authorName,
                note.authorEmail,
            )
        }
        val notes = runBlocking {
            noteRepo.getNotes(FindNotesRequest(note.dcCodes, note.weeks))
        }
        assertTrue(notes.isEmpty())

        val (deletedRecord, originalRecord) = dsl.selectFrom(
            NOTE,
        ).where(NOTE.ID.eq(note.id)).orderBy(NOTE.VERSION.desc()).fetch()
            .partition { it.isDeleted }.let { it.first.first() to it.second.first() }

        assertEquals(note.id, originalRecord.id)
        assertEquals(note.id, deletedRecord.id)
        assertTrue(deletedRecord.isDeleted)
        assertEquals("text note1", originalRecord.noteText)
    }

    @Test
    fun `should be able to get a note`() {
        val expectedNote = persistNote("text note1")

        val actualNote = runBlocking {
            noteRepo.getNotes(FindNotesRequest(expectedNote.dcCodes, expectedNote.weeks))
        }.first()

        assertNote(expectedNote, actualNote)
    }

    @Test
    fun `should be able to get a note with single dc code and dc week`() {
        val expectedNote = persistNote("text note1")

        val actualNote = runBlocking {
            noteRepo.getNotes(FindNotesRequest(setOf("VE"), setOf(DcWeek("2023-W01"))))
        }.first()
        assertNote(expectedNote, actualNote)
    }

    @Test
    fun `should get empty list of notes if there are none for the requested dc code`() {
        persistNote("text note1")

        val actualNote = runBlocking {
            noteRepo.getNotes(FindNotesRequest(setOf("IT"), setOf(DcWeek("2023-W01"))))
        }
        assertTrue(actualNote.isEmpty())
    }

    @Test
    fun `should get empty list of notes if there are none for the requested dc week`() {
        persistNote("text note1")
        val actualNote = runBlocking {
            noteRepo.getNotes(FindNotesRequest(setOf("VE"), setOf(DcWeek("2023-W27"))))
        }
        assertTrue(actualNote.isEmpty())
    }

    @Test
    fun `should get empty list of notes if there are none for the requested dc code and dc week`() {
        persistNote("text note1")
        val actualNote = runBlocking {
            noteRepo.getNotes(FindNotesRequest(setOf("IT"), setOf(DcWeek("2023-W27"))))
        }
        assertTrue(actualNote.isEmpty())
    }

    @Test
    fun `should be able to filter the note for the given dc code and dc week`() {
        val expectedNote = persistNote("test note 1", setOf("IT"), setOf(DcWeek("2023-W03"), DcWeek("2023-W04")))
        persistNote("test note 2")
        persistNote("test note 3", setOf("GR"), setOf(DcWeek("2023-W04"), DcWeek("2023-W05")))

        val filteredNote = runBlocking {
            noteRepo.getNotes(FindNotesRequest(setOf("IT"), setOf(DcWeek("2023-W04"))))
        }

        assertEquals(1, filteredNote.size)
        assertNote(expectedNote, filteredNote.first())
    }

    @Test
    fun `should fetch notes sorted by the most recent created`() {
        val oldestNote = persistNote("oldest note")
        val olderNote = persistNote("older note")
        val latestNote = persistNote("latest note")

        val latestNotes = runBlocking {
            noteRepo.getNotes(FindNotesRequest(setOf("VE", "TO"), setOf(DcWeek("2023-W01"), DcWeek("2023-W11"))))
        }
        assertEquals(3, latestNotes.size)
        assertNote(latestNote, latestNotes.elementAt(0))
        assertNote(olderNote, latestNotes.elementAt(1))
        assertNote(oldestNote, latestNotes.elementAt(2))
    }

    @Test
    fun `should be able to fetch notes with dc code in different order`() {
        val dcCodes = setOf("BV", "GR")
        val expectedNote = persistNote("note dc code in different order", dcCodes)
        val dcCodeInDifferentOrder = dcCodes.reversed().toSet()

        val actualNotes = runBlocking {
            noteRepo.getNotes(FindNotesRequest(dcCodeInDifferentOrder, setOf(DcWeek("2023-W01"), DcWeek("2023-W11"))))
        }
        assertEquals(1, actualNotes.size)
        assertNote(expectedNote, actualNotes.first())
    }

    @Test
    fun `should create multiple notes having the same author, dcs, weeks and text`() {
        // when
        val dbNote1 = persistNote("text 1")
        val dbNote2 = persistNote("text 1")

        // then
        assertEquals(2, dbConfig.dsl().select(NOTE.ID).from(NOTE).count())
        assertTrue(dbNote1.id != dbNote2.id)
    }

    @Test
    fun `should be able to get a note even with no sku at risk`() {
        val expectedNote = persistNote("text note1")
        persistCalculationsForNote(expectedNote)
        val actualNote = runBlocking {
            noteRepo.getNotes(FindNotesRequest(expectedNote.dcCodes, expectedNote.weeks))
        }.first()

        assertNote(expectedNote, actualNote)
    }

    @Test
    fun `should be able to get a note with the sku at risk`() {
        val expectedNote = persistNote("text note1")
        persistCalculationsForNote(expectedNote) {
            dailyNeeds = TEN
        }
        val actualNote = runBlocking {
            noteRepo.getNotes(FindNotesRequest(expectedNote.dcCodes, expectedNote.weeks))
        }.first()

        assertNote(
            expectedNote.copy(atRisk = listOf(AtRisk(expectedNote.dcCodes.first(), expectedNote.weeks.first()))),
            actualNote,
        )
    }

    @Test
    fun `should be able to filter the note for the given skuIds`() {
        val expectedNote1 = persistNote("test note 1")
        val expectedNote2 = persistNote("test note 2")
        persistNote("test note 3")

        val filteredNotes = runBlocking {
            noteRepo.getNotes(
                FindNotesRequest(
                    dcCodes = expectedNote1.dcCodes,
                    dcWeeks = expectedNote1.weeks,
                    skuIds = setOf(expectedNote1.skuId, expectedNote2.skuId),
                ),
            )
        }

        assertEquals(2, filteredNotes.size)
        assertNote(expectedNote1, filteredNotes.first { it.id == expectedNote1.id })
        assertNote(expectedNote2, filteredNotes.first { it.id == expectedNote2.id })
    }

    @Test
    fun `should be able to filter the note for the given sku category`() {
        val expectedNote1 = persistNote("test note 1")
        val expectedNote2 = persistNote("test note 2")
        persistNote("test note 3")

        val sku1 = persistSkuSpecificationForNote(note = expectedNote1, market = "DE") { category = "CA1" }
        val sku2 = persistSkuSpecificationForNote(note = expectedNote2, market = "VE") { category = "CA2" }

        val filteredNotes = runBlocking {
            noteRepo.getNotes(
                FindNotesRequest(
                    dcCodes = expectedNote1.dcCodes,
                    dcWeeks = expectedNote1.weeks,
                    skuCategories = setOf(sku1.category, sku2.category),
                ),
            )
        }

        assertEquals(2, filteredNotes.size)
        assertNote(expectedNote1, filteredNotes.first { it.id == expectedNote1.id })
        assertNote(expectedNote2, filteredNotes.first { it.id == expectedNote2.id })
    }

    @Test
    fun `should be able to filter the note for the given sku location box`() {
        val expectedNote1 = persistNote("test note 1")
        val expectedNote2 = persistNote("test note 2")
        persistNote("test note 3")

        val sku1 = persistSkuSpecificationForNote(note = expectedNote1, market = "DE") { packaging = "MealKit" }
        val sku2 = persistSkuSpecificationForNote(note = expectedNote2, market = "VE") { packaging = "Coolpouch" }

        val filteredNotes = runBlocking {
            noteRepo.getNotes(
                FindNotesRequest(
                    dcCodes = expectedNote1.dcCodes,
                    dcWeeks = expectedNote1.weeks,
                    locationInBox = setOf(sku1.packaging, sku2.packaging),
                ),
            )
        }

        assertEquals(2, filteredNotes.size)
        assertNote(expectedNote1, filteredNotes.first { it.id == expectedNote1.id })
        assertNote(expectedNote2, filteredNotes.first { it.id == expectedNote2.id })
    }

    @Test
    fun `should be able to filter the note for the given calculation consumption only filter`() {
        val expectedNote1 = persistNote("test note 1")
        persistNote("test note 2")

        persistCalculationsForNote(expectedNote1) {
            demanded = BigDecimal(100)
        }

        val filteredNotes = runBlocking {
            noteRepo.getNotes(
                FindNotesRequest(
                    dcCodes = expectedNote1.dcCodes,
                    dcWeeks = expectedNote1.weeks,
                    additionalFilters = setOf(WITH_CONSUMPTION_ONLY),
                ),
            )
        }

        assertEquals(1, filteredNotes.size)
        assertNote(expectedNote1, filteredNotes.first { it.id == expectedNote1.id })
    }

    @Test
    fun `should be able to filter the note for the given calculation inventory shortage filter`() {
        val expectedNote1 = persistNote("test note 1")
        persistNote("test note 2")

        val calculation = persistCalculationsForNote(expectedNote1) {
            dailyNeeds = TEN
        }

        val filteredNotes = runBlocking {
            noteRepo.getNotes(
                FindNotesRequest(
                    dcCodes = expectedNote1.dcCodes,
                    dcWeeks = expectedNote1.weeks,
                    additionalFilters = setOf(WITH_INVENTORY_SHORTAGE),
                ),
            )
        }

        assertEquals(1, filteredNotes.size)
        assertNote(
            expectedNote1
                .copy(
                    atRisk = listOf(AtRisk(calculation.dcCode, DcWeek(calculation.productionWeek))),
                ),
            filteredNotes.first { it.id == expectedNote1.id },
        )
    }

    @Test
    fun `should be able to filter the note for the given calculation inbound shortage filter`() {
        val expectedNote1 = persistNote("test note 1")
        persistNote("test note 2")

        val calculation = persistCalculationsForNote(expectedNote1) {
            actualInbound = TEN
            expectedInbound = BigDecimal(20)
            date = LocalDate.now().minusDays(1)
        }

        val filteredNotes = runBlocking {
            noteRepo.getNotes(
                FindNotesRequest(
                    dcCodes = expectedNote1.dcCodes,
                    dcWeeks = expectedNote1.weeks,
                    additionalFilters = setOf(WITH_INBOUND_SHORTAGE),
                ),
            )
        }

        assertEquals(1, filteredNotes.size)
        assertNote(
            expectedNote1
                .copy(
                    atRisk = listOf(AtRisk(calculation.dcCode, DcWeek(calculation.productionWeek))),
                ),
            filteredNotes.first { it.id == expectedNote1.id },
        )
    }

    @Test
    fun `should be able to filter the note for the given calculation projected waste filter`() {
        val expectedNote1 = persistNote("test note 1")
        persistNote("test note 2")

        persistCalculationsForNote(expectedNote1) {
            expired = TEN
        }

        val filteredNotes = runBlocking {
            noteRepo.getNotes(
                FindNotesRequest(
                    dcCodes = expectedNote1.dcCodes,
                    dcWeeks = expectedNote1.weeks,
                    additionalFilters = setOf(WITH_PROJECTED_WASTE),
                ),
            )
        }

        assertEquals(1, filteredNotes.size)
        assertNote(expectedNote1, filteredNotes.first { it.id == expectedNote1.id })
    }

    @ParameterizedTest(name = "testing the notes filtering with different filters")
    @MethodSource("filterOptions")
    fun `should return note matching all filters`(
        filters: Set<AdditionalFilter>,
        demandedParam: BigDecimal,
        openingStockParam: BigDecimal
    ) {
        val expectedNote = persistNote("test note 1")
        persistNote("test note 2")
        val category = "AAA"
        val packaging = "mealkit"
        persistSkuSpecificationForNote(expectedNote, "VE") {
            this.category = category
            this.packaging = packaging
        }

        val calculation = persistCalculationsForNote(expectedNote) {
            expired = TEN
            actualInbound = ONE
            expectedInbound = TWO
            date = LocalDate.now().minusDays(1)
            demanded = demandedParam
            dailyNeeds = TEN
            openingStock = openingStockParam
        }

        val filteredNotes = runBlocking {
            noteRepo.getNotes(
                FindNotesRequest(
                    dcCodes = expectedNote.dcCodes,
                    dcWeeks = expectedNote.weeks,
                    skuIds = setOf(expectedNote.skuId),
                    additionalFilters = filters,
                    skuCategories = setOf(category),
                    locationInBox = setOf(packaging),
                ),
            )
        }

        assertEquals(1, filteredNotes.size)
        assertNote(
            expectedNote.copy(
                atRisk = listOf(AtRisk(calculation.dcCode, DcWeek(calculation.productionWeek))),
            ),
            filteredNotes.first(),
        )
    }

    private fun persistNote(
        text: String,
        dcCodes: Set<String> = setOf("VE", "TO"),
        dcWeeks: Set<DcWeek> = setOf(DcWeek("2023-W01"), DcWeek("2023-W11"))
    ) =
        runBlocking { noteRepo.createNote(UUID.randomUUID(), authorEmail, authorName, dcCodes, dcWeeks, text) }

    private fun persistSkuSpecificationForNote(
        note: Note,
        market: String,
        block: SkuSpecificationRecord.() -> Unit
    ) = SkuSpecificationRecord().apply {
        id = note.skuId
        name = "Some Sku"
        code = Default.skuCode
        category = ""
        acceptableCodeLife = 0
        coolingType = ""
        packaging = ""
        this.market = market
        uom = Uom.UOM_LBS
    }.also {
        it.block()
        dsl.insertInto(SKU_SPECIFICATION).set(it)
            .onDuplicateKeyUpdate()
            .set(it)
            .execute()
        refreshSkuView()
    }

    private fun persistCalculationsForNote(
        note: Note,
        block: CalculationRecord.() -> Unit = {}
    ) = RandomFixture().calculationRecord {
        cskuId = note.skuId
        dcCode = note.dcCodes.first()
        productionWeek = note.weeks.first().toString()
        demanded = ZERO
        actualInbound = ZERO
        expectedInbound = ZERO
        dailyNeeds = ZERO
        expired = ZERO
    }.also {
        it.block()
        dsl.batchInsert(it).execute()
    }

    private fun assertNote(expectedNote: Note, actualNote: Note) {
        assertEquals(expectedNote.authorEmail, actualNote.authorEmail)
        assertEquals(expectedNote.authorName, actualNote.authorName)
        assertEquals(expectedNote.dcCodes, actualNote.dcCodes)
        assertEquals(expectedNote.weeks, actualNote.weeks)
        assertEquals(expectedNote.text, actualNote.text)
        assertEquals(expectedNote.createdAt, actualNote.createdAt)
        assertEquals(expectedNote.atRisk, actualNote.atRisk)
        assertEquals(expectedNote.version, actualNote.version)
        assertEquals(expectedNote.isEdited, actualNote.isEdited)
    }

    companion object {
        private const val authorEmail = "a@b.c"
        private const val authorName = "Test One"
        private val dcCodes = setOf("VE", "TO")
        private val dcWeeks = setOf(DcWeek("2023-W01"), DcWeek("2023-W11"))

        @JvmStatic
        @Suppress("unused")
        fun filterOptions() = listOf(
            Arguments.of(
                setOf(
                    WITH_INBOUND_SHORTAGE,
                    WITH_INVENTORY_SHORTAGE,
                    WITH_INBOUND_SHORTAGE,
                    WITH_CONSUMPTION_ONLY,
                    WITH_PROJECTED_WASTE,
                ),
                TEN,
                ZERO,
            ),
        )
    }
}
