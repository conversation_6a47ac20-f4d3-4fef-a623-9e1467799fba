package com.hellofresh.cif.api.stockupdate

import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.Reason
import com.hellofresh.cif.api.schema.tables.StockUpdate.STOCK_UPDATE
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateDto
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateView
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.inventory.lib.schema.tables.records.StockUpdateRecord
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM.UOM_UNIT
import java.math.BigDecimal
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.util.UUID
import kotlin.test.assertNotNull
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test

class StockUpdateApiRepositoryImplTest : FunctionalTest() {
    @Test
    fun `should store stock update with default version`() {
        val stockUpdateDto =
            getInsertStockDto(LocalDate.now(), null, UUID.randomUUID(), SkuQuantity.fromLong(100, UOM_UNIT))
        runBlocking {
            stockUpdateApiRepository.upsertStockUpdate(listOf(stockUpdateDto))
            val insertedStockUpdate = dsl.selectFrom(
                STOCK_UPDATE,
            ).toList().first()
            assertTrue(insertedStockUpdate.skuId != null)
            assertEquals(insertedStockUpdate.version, 1)
            assertEquals(insertedStockUpdate.dcCode, stockUpdateDto.dcCode)
            assertEquals(insertedStockUpdate.date, stockUpdateDto.date)
            assertEquals(insertedStockUpdate.quantity, stockUpdateDto.quantity.getValue())
            assertEquals(insertedStockUpdate.uom.name, stockUpdateDto.quantity.unitOfMeasure.name)
            assertEquals(insertedStockUpdate.reason, stockUpdateDto.reason.value)
            assertEquals(insertedStockUpdate.reasonDetail, stockUpdateDto.reasonDetail)
            assertEquals(insertedStockUpdate.week, stockUpdateDto.week)
            assertEquals(insertedStockUpdate.authorName, stockUpdateDto.authorName)
            assertEquals(insertedStockUpdate.authorEmail, stockUpdateDto.authorEmail)
        }
    }

    @Test
    fun `should update stock update with version incremented`() {
        val date = LocalDate.now()
        val skuId = UUID.randomUUID()
        val insertStockUpdateDto = getInsertStockDto(date, version = null, skuId, SkuQuantity.fromLong(100, UOM_UNIT))
        insertSkus(setOf(skuId))
        runBlocking {
            stockUpdateApiRepository.upsertStockUpdate(listOf(insertStockUpdateDto))
            val updateDto = getInsertStockDto(date, version = 1, skuId, SkuQuantity.fromLong(200, UOM_UNIT))
            val result = stockUpdateApiRepository.upsertStockUpdate(listOf(updateDto))
            assertEquals(result[0].version, updateDto.version!! + 1)

            val updatedRecord = dsl.selectFrom(
                STOCK_UPDATE,
            ).toList().last()

            assertEquals(updatedRecord.version, updateDto.version!! + 1)
            assertEquals(updatedRecord.dcCode, updateDto.dcCode)
            assertEquals(updatedRecord.date, updateDto.date)
            assertEquals(updatedRecord.quantity, updateDto.quantity.getValue())
            assertEquals(updatedRecord.uom.name, updateDto.quantity.unitOfMeasure.name)
            assertEquals(updatedRecord.reason, updateDto.reason.value)
            assertEquals(updatedRecord.week, updateDto.week)
        }
    }

    @Test
    fun `getAllStockUpdates should return all records that match week and dc`() {
        val dc = createDcConfig().dcCode
        val date = LocalDate.now().plusWeeks(1)
        val week = DcWeek(date, MONDAY).value
        val skuId1 = UUID.randomUUID()
        val skuId2 = UUID.randomUUID()
        val expectedSkus = insertSkus(setOf(skuId1, skuId2))
        val stockUpdateRecord1 = createStockUpdateRecord(dc, date, week, BigDecimal(100), skuId1, 1)
        val stockUpdateRecord2 = createStockUpdateRecord(dc, date, week, BigDecimal(150), skuId1, 2)
        val stockUpdateRecord3 = createStockUpdateRecord(dc, date, week, BigDecimal(200), skuId2, 1)

        createStockUpdateRecord(
            dcCodeParam = dc,
            dateParam = date.plusWeeks(2),
            DcWeek(date.plusWeeks(2), MONDAY).value,
            qtyParam = BigDecimal(100),
            skuIdParam = skuId1,
            versionParam = 1,
        )
        createStockUpdateRecord(
            dcCodeParam = "ZZ",
            dateParam = date,
            week,
            qtyParam = BigDecimal(100),
            skuIdParam = skuId1,
            versionParam = 1,
        )

        val stockUpdates = runBlocking {
            stockUpdateApiRepository.getAllStockUpdates(dc, week)
        }

        assertEquals(2, stockUpdates.size)

        assertStockUpdateVersion(
            listOf(stockUpdateRecord1, stockUpdateRecord2),
            expectedSkus.first { it.id == stockUpdateRecord1.skuId },
            stockUpdates.first { it.skuId == stockUpdateRecord1.skuId },
        )
        assertStockUpdateVersion(
            listOf(stockUpdateRecord3),
            expectedSkus.first { it.id == stockUpdateRecord3.skuId },
            stockUpdates.first { it.skuId == stockUpdateRecord3.skuId },
        )
    }

    private fun assertStockUpdateVersion(
        records: List<StockUpdateRecord>,
        sku: com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord,
        stockUpdateView: StockUpdateView
    ) {
        assertEquals(records.size, stockUpdateView.versions.size)

        records.forEach {
            assertEquals(it.dcCode, stockUpdateView.dcCode)
            assertEquals(it.date, stockUpdateView.date)
            assertEquals(it.skuId, stockUpdateView.skuId)
            assertEquals(sku.code, stockUpdateView.skuCode)
            assertEquals(sku.name, stockUpdateView.skuName)
            assertEquals(sku.category, stockUpdateView.skuCategory)

            val version = stockUpdateView.versions.first { v -> v.version == it.version }
            assertEquals(it.quantity, version.quantity.getValue())
            assertEquals(it.uom.name, version.quantity.unitOfMeasure.name)
            assertEquals(it.reason, version.reason)
            assertEquals(it.reasonDetail, version.reasonDetail)
            assertEquals(it.authorName, version.authorName)
            assertNotNull(version.createdAt)
        }
    }

    private fun getInsertStockDto(date: LocalDate, version: Int?, skuId: UUID, quantity: SkuQuantity) = StockUpdateDto(
        skuId = skuId,
        dcCode = "VE",
        date = date,
        quantity = quantity,
        reason = Reason.WMS_ISSUE,
        week = "2024-W10",
        reasonDetail = "test reason detail",
        authorName = "authorName",
        authorEmail = "authorEmail",
        version = version,
        deleted = false,
    )
}
