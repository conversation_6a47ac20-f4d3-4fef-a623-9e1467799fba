package com.hellofresh.cif.api.calculation

import com.hellofresh.calculator.models.ForecastInventory
import com.hellofresh.cif.api.AuthUtils.addAuthHeader
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.InventoryRefreshType.CLEARDOWN
import com.hellofresh.cif.api.calculation.InventoryRefreshType.LIVE
import com.hellofresh.cif.api.calculation.fixtures.RandomFixture
import com.hellofresh.cif.api.calculation.fixtures.calculationRecord
import com.hellofresh.cif.api.calculation.generated.model.CalculationFiltersResponse
import com.hellofresh.cif.api.calculation.generated.model.DailyCalculationsResponse
import com.hellofresh.cif.api.calculation.generated.model.ProjectedWastesResponse
import com.hellofresh.cif.api.calculation.generated.model.WeeklyCalculationsResponse
import com.hellofresh.cif.api.calculation.projectedWaste.projectedWasteModule
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.api.schema.enums.SubbedType
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.DemandRecord
import com.hellofresh.cif.api.schema.tables.records.PurchaseOrderRecord
import com.hellofresh.cif.api.schema.tables.records.PurchaseOrderSkuRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.api.schema.tables.records.SupplierCulinarySkuRecord
import com.hellofresh.cif.api.schema.tables.records.SupplierRecord
import com.hellofresh.cif.api.schema.tables.records.SupplierSkuRecord
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.purchaseorder.DeliveryInfo
import com.hellofresh.cif.models.purchaseorder.DeliveryInfoStatus.CLOSED
import com.hellofresh.cif.models.purchaseorder.PoStatus.SENT
import com.hellofresh.cif.models.purchaseorder.PurchaseOrder
import com.hellofresh.cif.models.purchaseorder.PurchaseOrderSku
import com.hellofresh.cif.models.purchaseorder.Supplier
import com.hellofresh.cif.models.purchaseorder.TimeRange
import io.ktor.client.request.accept
import io.ktor.client.request.get
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpStatusCode
import io.ktor.http.Parameters
import io.ktor.http.ParametersBuilder
import io.ktor.http.formUrlEncode
import io.ktor.server.testing.testApplication
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import java.math.BigDecimal
import java.security.SecureRandom
import java.time.DayOfWeek
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset.UTC
import java.util.UUID
import kotlin.test.assertEquals
import kotlin.time.Duration
import kotlinx.coroutines.runBlocking
import org.jooq.JSONB
import org.junit.jupiter.api.BeforeEach

const val DEFAULT_TEST_STRATEGY = "UNSPECIFIED_STRATEGY"

abstract class CalculationFunctionalTest : FunctionalTest() {

    lateinit var productionStart: DayOfWeek
    lateinit var zoneId: ZoneId

    @BeforeEach
    fun beforeEach() {
        createDcConfig(FIXTURE_DC).also {
            productionStart = DayOfWeek.valueOf(it.productionStart)
            zoneId = ZoneId.of(it.zoneId)
        }
    }

    fun getDailyCalculations(
        req: CalculationRequest,
        contentType: ContentType = ContentType.Application.Json,
        jwtEnabled: Boolean = true
    ) = callCalculations("/calculation/dailyView", contentType, jwtEnabled) { addCalculationParams(req) }

    fun getProjectedWasteCalculations(
        req: ProjectedWasteCalculationFilterRequest,
        contentType: ContentType = ContentType.Application.Json,
    ) = callProjectedWasteCalculations("/projectedWaste", contentType) { addProjectedWasteCalculationFilterParams(req) }

    fun getWeeklyCalculations(
        req: CalculationRequest,
        contentType: ContentType = ContentType.Application.Json,
        jwtEnabled: Boolean = true
    ) = callCalculations("/calculation/weeklyView", contentType, jwtEnabled) { addCalculationParams(req) }

    fun getCalculationFilters(
        req: CalculationFilterRequest,
        jwtEnabled: Boolean = true
    ): HttpResponse {
        val url = "/calculation/filters"
        return callCalculations(path = url, jwtEnabled = jwtEnabled) {
            addCalculationFiltersParams(req)
        }
    }

    fun ParametersBuilder.addCalculationParams(req: CalculationRequest) {
        set("skuCount", (req.pageRequest as Page).skuCount.toString())
        set("page", (req.pageRequest as Page).page.toString())
        set("sortBy", req.sortBy.paramValue)
        addCalculationFiltersParams(req)
    }

    fun ParametersBuilder.addProjectedWasteCalculationFilterParams(req: ProjectedWasteCalculationFilterRequest) {
        req.dcCodes.forEach { append("dcCode", it) }
        set("skuCount", "50")
        set("page", "1")
        req.skuCategories.forEach { append("skuCategory", it) }
        req.additionalFilters.forEach { append("additionalFilters", it.name) }
        req.expiringInMax?.let { set("expiringInLessThanOrEqual", req.expiringInMax.toString()) }
        req.expiringInMin?.let { set("expiringInGreaterThanOrEqual", req.expiringInMin.toString()) }
    }

    fun ParametersBuilder.addCalculationFiltersParams(req: CalculationFiltersParams) {
        req.weeks.forEach { append("weeks", it) }
        req.skuCodes.forEach { append("skuCode", it) }
        req.skuCategories.forEach { append("skuCategory", it) }
        req.locationInBox.forEach { append("locationInBox", it) }
        set("consumptionDaysAhead", req.consumptionDaysAhead.toString())
        set("inventoryRefreshType", mapInventoryRefreshTypeParam(req))
        req.poDueInMax?.let { set("poDueInLessThanOrEqual", req.poDueInMax.toString()) }
        req.closingStockLessThanOrEqual?.let { set("closingStockLessThan", req.closingStockLessThanOrEqual.toString()) }
        req.poDueInMin?.let { set("poDueInGreaterThanOrEqual", req.poDueInMin.toString()) }
        req.additionalFilters.forEach { append("additionalFilters", it.name) }
        req.dcCodes.forEach { append("dcCode", it) }
        req.supplierIds.forEach { append("supplierId", it.toString()) }
        req.activeSupplierNames.forEach { append("activeSupplierName", it) }
    }

    private fun callCalculations(
        path: String,
        contentType: ContentType = ContentType.Application.Json,
        jwtEnabled: Boolean = true,
        builder: ParametersBuilder.() -> Unit
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", "https://test.com"), true)
                calculationRoutingModule(
                    CalculationsService(
                        SimpleMeterRegistry(),
                        dcConfigService,
                        usableInventoryEvaluator,
                        calculationsPendingStockUpdateService,
                        prodCalculationsRepo,
                        preProdCalculationsRepo,
                        statsigFeatureFlagClient,
                    ),
                    calculationResponseMapper,
                    timeOutInMillis,
                )()
            }
            val params = Parameters.build(builder).formUrlEncode()

            response = client.get("$path?$params") {
                accept(contentType)
                if (jwtEnabled) this.addAuthHeader(UUID.randomUUID().toString(), UUID.randomUUID().toString(), jwtSecret)
            }
        }
        return response
    }

    private fun callProjectedWasteCalculations(
        path: String,
        contentType: ContentType = ContentType.Application.Json,
        jwtEnabled: Boolean = true,
        builder: ParametersBuilder.() -> Unit
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "", "", "https://test.com"), true)
                projectedWasteModule(
                    projectedWasteCalculationService,
                    projectedWasteCalculationCsvConverter,
                    timeOutInMillis,
                )()
            }
            val params = Parameters.build(builder).formUrlEncode()

            response = client.get("$path?$params") {
                accept(contentType)
                if (jwtEnabled) this.addAuthHeader(UUID.randomUUID().toString(), UUID.randomUUID().toString(), jwtSecret)
            }
        }
        return response
    }

    private fun mapInventoryRefreshTypeParam(req: CalculationFiltersParams) =
        when (val inventoryRefreshType = req.inventoryRefreshType) {
            CLEARDOWN -> "cleardown"
            LIVE -> "live"
            else -> error("unknown inventoryRefreshType param mapping $inventoryRefreshType")
        }

    fun getDailyCalculationsOkResponse(req: CalculationRequest): DailyCalculationsResponse {
        val dailyCalculationsResponse: DailyCalculationsResponse
        runBlocking {
            getDailyCalculations(req).apply {
                assertEquals(HttpStatusCode.OK, status)
                val body = bodyAsText()
                dailyCalculationsResponse = objectMapper.readValue(body, DailyCalculationsResponse::class.java)
            }
        }
        return dailyCalculationsResponse
    }

    fun getProjectedWasteCalculationsOkResponse(
        req: ProjectedWasteCalculationFilterRequest,
        contentType: ContentType = ContentType.Application.Json
    ): ProjectedWastesResponse {
        val projectedWastesResponse: ProjectedWastesResponse
        runBlocking {
            getProjectedWasteCalculations(req, contentType).apply {
                assertEquals(HttpStatusCode.OK, status)
                val body = bodyAsText()
                projectedWastesResponse = objectMapper.readValue(body, ProjectedWastesResponse::class.java)
            }
        }
        return projectedWastesResponse
    }

    fun getCalculationFiltersOkResponse(req: CalculationFilterRequest): CalculationFiltersResponse {
        val calculationFiltersResponse: CalculationFiltersResponse
        runBlocking {
            getCalculationFilters(req).apply {
                assertEquals(HttpStatusCode.OK, status)
                val body = bodyAsText()
                calculationFiltersResponse = objectMapper.readValue(body, CalculationFiltersResponse::class.java)
            }
        }
        return calculationFiltersResponse
    }

    fun getWeeklyCalculationsOkResponse(req: CalculationRequest): WeeklyCalculationsResponse {
        val weeklyCalculationResponse: WeeklyCalculationsResponse
        runBlocking {
            getWeeklyCalculations(req).apply {
                assertEquals(HttpStatusCode.OK, status)
                val body = bodyAsText()
                weeklyCalculationResponse = objectMapper.readValue(body, WeeklyCalculationsResponse::class.java)
            }
        }
        return weeklyCalculationResponse
    }

    @SuppressWarnings("LongParameterList")
    fun newPurchaseOrder(
        poId: UUID?,
        poNumber: String,
        supplierId: UUID?,
        supplierName: String?,
        skuId: UUID,
        dcCode: String,
        date: LocalDate
    ): PurchaseOrder =
        PurchaseOrder(
            poNumber,
            poNumber + "_XX",
            poId,
            dcCode,
            TimeRange(date.atStartOfDay(UTC), date.atStartOfDay(UTC).plusMinutes(1)),
            supplierId?.let { Supplier(it, supplierName!!) },
            listOf(
                PurchaseOrderSku(
                    skuId,
                    SkuQuantity.fromLong(1),
                    listOf(DeliveryInfo("DELI1", date.atStartOfDay(UTC), CLOSED, SkuQuantity.fromLong(99))),
                ),
            ),
            poStatus = SENT,
        )

    @SuppressWarnings("LongParameterList")
    fun insertSkuCalculations(
        sku: Sku,
        calcCount: Int,
        dcCode: String,
        week: String,
        locationInBox: String = "",
        unusableInventory: List<ForecastInventory> = emptyList(),
        expiredParam: BigDecimal = BigDecimal.ZERO,
    ) {
        (0 until calcCount).map {
            RandomFixture().calculationRecord {
                this.cskuId = sku.skuId
                this.dcCode = dcCode
                productionWeek = week
                date = LocalDate.now(UTC).plusDays(it.toLong())
                dailyNeeds = BigDecimal.ZERO
                actualInbound = BigDecimal.ZERO
                expectedInbound = BigDecimal.ZERO
                strategy = DEFAULT_TEST_STRATEGY
                expired = expiredParam
                this.unusableInventory = JSONB.valueOf(
                    com.hellofresh.cif.lib.kafka.serde.objectMapper.writeValueAsString(
                        unusableInventory,
                    ),
                )
            }
        }.apply {
            dsl.batchInsert(this).execute()
        }.map {
            SkuSpecificationRecord()
                .also { self ->
                    self.id = sku.skuId
                    self.parentId = null
                    self.code = sku.skuCode
                    self.name = "sku name ${sku.skuId}"
                    self.category = "PHF"
                    self.packaging = locationInBox
                    self.coolingType = ""
                    self.acceptableCodeLife = 10
                    self.market = ""
                }
        }
            .toSet()
            .apply {
                dsl.batchInsert(this).execute()
                refreshSkuView()
            }
    }

    @Suppress("SpreadOperator")
    fun insertPurchaseOrderAndSupplier(po: PurchaseOrder) {
        val poRecord = PurchaseOrderRecord(
            "po-ref", UUID.randomUUID(), "po-nr", po.dcCode,
            po.expectedDeliveryTimeslot?.startTime?.toOffsetDateTime(),
            po.expectedDeliveryTimeslot?.endTime?.toOffsetDateTime(), po.supplier?.id,
            OffsetDateTime.now(UTC), OffsetDateTime.now(UTC), null, null
        )
        val poSkuRecords = po.purchaseOrderSkus.map {
            PurchaseOrderSkuRecord(
                it.skuId,
                it.expectedQuantity?.getValue(),
                poRecord.poNumber,
                Uom.UOM_UNIT,
            )
        }
        val supplierRecord = SupplierRecord(po.supplier?.id, po.supplier?.id, po.supplier?.name)
        val supplierId2 = UUID.randomUUID()
        val supplierId3 = UUID.randomUUID()
        val supplierRecord2 = SupplierRecord(supplierId2, supplierId2, "Test Supplier 2")
        val supplierRecord3 = SupplierRecord(supplierId3, supplierId3, "Test Supplier 3")

        val supplierSkuId1 = UUID.randomUUID()
        val supplierSkuId2 = UUID.randomUUID()
        val supplierSkuId3 = UUID.randomUUID()

        val supplierSkuRecord = SupplierSkuRecord().apply {
            this.supplierSkuId = supplierSkuId1
            this.skuId = po.purchaseOrderSkus.first().skuId
            this.mlorDays = 2
            this.status = "active"
        }

        val anotherSku = UUID.randomUUID()

        val supplierSkuRecord2 = supplierSkuRecord.copy().apply {
            this.supplierSkuId = supplierSkuId2
            this.skuId = po.purchaseOrderSkus.first().skuId
        }

        val supplierSkuRecord3 = supplierSkuRecord2.copy().apply {
            this.supplierSkuId = supplierSkuId3
            this.skuId = anotherSku
        }

        val supplierCulinarySku = SupplierCulinarySkuRecord().apply {
            this.id = supplierSkuId1
            this.supplierId = po.supplier?.id
            this.culinarySkuId = po.purchaseOrderSkus.first().skuId
            this.market = ""
            this.status = "active"
        }

        val supplierCulinarySku2 = supplierCulinarySku.copy().apply {
            this.id = supplierSkuId2
            this.supplierId = supplierId2
        }

        dsl
            .batchInsert(
                poRecord, *poSkuRecords.toTypedArray(), supplierRecord, supplierSkuRecord, supplierCulinarySku,
                supplierRecord2, supplierSkuRecord2, supplierCulinarySku2,
                supplierRecord3, supplierSkuRecord3
            )
            .execute()

        dsl.execute("refresh materialized view concurrently purchase_orders_view")
        dsl.execute("refresh materialized view concurrently po_calculations_view;")
    }

    @Suppress("LongParameterList")
    fun createDemand(
        day: LocalDate,
        skuIdParam: UUID,
        dcCodeParam: String,
        subbed: SubbedType,
        consumptionDetails: JSONB? = null,
        subInQty: Long? = null
    ) = DemandRecord(
        skuIdParam,
        dcCodeParam,
        day,
        LocalDateTime.now(UTC),
        LocalDateTime.now(UTC),
        LocalDateTime.now(UTC),
        subbed,
        BigDecimal(7L),
        subInQty,
        0,
        consumptionDetails,
        com.hellofresh.cif.api.schema.enums.Uom.UOM_UNIT,
    )

    @Suppress("LongParameterList")
    fun insertTestData(
        n: Int,
        dc: String,
        week: String,
        category: String = UUID.randomUUID().toString().take(3),
        code: String = UUID.randomUUID().toString(),
        accConsumption: BigDecimal = BigDecimal.ZERO,
        closeStock: BigDecimal = BigDecimal.ZERO,
        poDueIn: Int? = null,
        netNeedsParam: BigDecimal? = BigDecimal.ZERO,
    ) {
        val records = (0 until n).map {
            RandomFixture().calculationRecord {
                dcCode = dc
                productionWeek = week
                actualConsumption = accConsumption
                closingStock = closeStock
                maxPurchaseOrderDueIn = poDueIn
                netNeeds = netNeedsParam
                strategy = DEFAULT_TEST_STRATEGY
            }
        }
        dsl.batchInsert(records).execute()
        val skus = records.map {
            SkuSpecificationRecord()
                .apply {
                    this.id = it.cskuId
                    this.code = it.cskuId.toString()
                    this.name = it.cskuId.toString()
                    this.category = it.cskuId.toString().take(3)
                    this.packaging = ""
                    this.coolingType = ""
                    this.acceptableCodeLife = 10
                    this.market = ""
                }
        }
        skus.last().apply {
            this.code = code
            this.category = category
        }
        dsl.batchInsert(skus).execute()
        refreshSkuView()
    }

    fun <T> List<T>.safeSubList(fromIndex: Int, toIndex: Int): List<T> = this.subList(
        fromIndex,
        toIndex.coerceAtMost(this.size),
    )

    companion object {
        private val timeOutInMillis = Duration.parse("PT5S")
        fun randomQty() = SecureRandom().nextDouble().toBigDecimal()
    }

    data class Sku(val skuCode: String, val skuId: UUID)
}
