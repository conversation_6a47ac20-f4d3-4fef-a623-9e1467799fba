package com.hellofresh.cif.api.to

import com.fasterxml.jackson.module.kotlin.readValue
import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.calculation.generated.model.TransferOrdersDetailResponse
import com.hellofresh.cif.api.fileupload.FileUploadApiTest.Companion.timeOutInMillis
import com.hellofresh.cif.api.ktor.JwtCredentials
import com.hellofresh.cif.api.ktor.configureJwtAuth
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.transferorder.model.DeliveryInfo
import com.hellofresh.cif.transferorder.model.DeliveryInfoStatus
import com.hellofresh.cif.transferorder.model.ToTimeRange
import com.hellofresh.cif.transferorder.model.TransferOrder
import com.hellofresh.cif.transferorder.model.TransferOrderSku
import com.hellofresh.cif.transferorder.model.TransferOrderStatus
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.statement.HttpResponse
import io.ktor.client.statement.bodyAsText
import io.ktor.http.ContentType
import io.ktor.http.HttpHeaders
import io.ktor.http.HttpStatusCode
import io.ktor.server.testing.testApplication
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZoneId
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import kotlinx.coroutines.runBlocking
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class TransferOrderApiTest : FunctionalTest() {
    private val supplierId = UUID.randomUUID()
    private val supplierName = "Test Supplier"
    private val skuId = UUID.randomUUID()

    @BeforeEach
    fun init() {
        createDcConfig("VE")
        createDcConfig("BY")
        insertSupplier(supplierId, supplierName)
        insertSkus(setOf(skuId))
        refreshSkuView()
    }

    @ParameterizedTest
    @MethodSource("urlVariants")
    fun `should return list of transfer orders`(variant: UrlVariant) {
        val timeSlot =
            ToTimeRange(
                LocalDateTime.parse("2025-07-15T15:00:00.000").atZone(ZoneId.of("Europe/Berlin")),
                LocalDateTime.parse("2025-07-15T17:00:00.000").atZone(ZoneId.of("Europe/Berlin"))
            )
        val transferOrderSku = getTransferOrderSku(skuId, listOf(getDeliveryInfo()))
        val transferOrder = getTransferOrder(listOf(transferOrderSku), timeSlot)

        insertTransferOrder(transferOrder)
        insertTransferOrderSkus(transferOrderSku)
        insertGRN(
            skuId,
            transferOrder.destinationDc,
            transferOrderSku.expectedQuantity.getValue(),
            transferOrder.transferOrderNumber
        )
        refreshTransferOrderGRNView()

        runBlocking {
            val url = variant.urlBuilder(
                skuId,
                transferOrder.sourceDc,
                transferOrder.week,
                timeSlot.startTime.toLocalDate().toString(),
                timeSlot.endTime.toLocalDate().toString()
            )

            get(url)
                .apply {
                    assertEquals(HttpStatusCode.OK, status)
                    val response = objectMapper.readValue<TransferOrdersDetailResponse>(bodyAsText())
                    val to = response.transferOrders.first()

                    assertEquals(transferOrder.transferOrderNumber, to.transferOrderNumber)
                    assertEquals(transferOrder.sourceDc, to.sourceDcCode)
                    assertEquals(transferOrder.destinationDc, to.destinationDcCode)
                    assertEquals(transferOrder.week, to.week)
                    assertEquals(
                        transferOrder.expectedDeliveryTimeslot?.startTime?.toInstant(),
                        to.inboundStartTime?.toInstant()
                    )
                    assertEquals(
                        transferOrder.expectedDeliveryTimeslot?.endTime?.toInstant(),
                        to.inboundEndTime?.toInstant()
                    )

                    to.skus?.forEach { toSku ->
                        assertEquals(transferOrderSku.skuId, toSku.skuId)
                        assertEquals(transferOrderSku.supplierId, toSku.supplierId)
                        assertEquals(transferOrderSku.supplierName, toSku.supplierName)
                        assertTrue { toSku.deliveries?.isNotEmpty() == true }
                    }
                }
        }
    }

    @Test
    fun `should return empty list if no matching transfer orders`() {
        runBlocking {
            get("/transfer-orders?skuId=${UUID.randomUUID()}&dcCode=VE&weeks=2025-W10").apply {
                assertEquals(HttpStatusCode.OK, status)
                val response = objectMapper.readValue<TransferOrdersDetailResponse>(bodyAsText())
                assertTrue(response.transferOrders.isEmpty())
            }
        }
    }

    @Test
    fun `should return bad request if skuId is missing`() {
        runBlocking {
            get("/transfer-orders?dcCode=VE&weeks=2025-W01").apply {
                assertEquals(HttpStatusCode.BadRequest, status)
            }
        }
    }

    @Test
    fun `should return bad request if dcCode is missing`() {
        runBlocking {
            get("/transfer-orders?skuId=${UUID.randomUUID()}&weeks=2025-W01").apply {
                assertEquals(HttpStatusCode.BadRequest, status)
            }
        }
    }

    private fun get(
        getUrl: String
    ): HttpResponse {
        lateinit var response: HttpResponse
        testApplication {
            application {
                configureJwtAuth(JwtCredentials(jwtSecret, "test", "test", "test", "https://test.com"), false)
                transferOrderModule(
                    TransferOrderService(transferOrderRepository, configService),
                    dcConfigService,
                    timeOutInMillis
                )()
            }
            response = client.get(getUrl) {
                header(HttpHeaders.ContentType, ContentType.Application.Json.toString())
            }
        }
        return response
    }

    private fun getTransferOrder(skus: List<TransferOrderSku> = emptyList(), timeSlot: ToTimeRange) = TransferOrder(
        transferOrderNumber = "TO-001",
        sourceDc = "BY",
        destinationDc = "VE",
        week = "2025-W30",
        marketCode = "DACH",
        status = TransferOrderStatus.STATE_RESERVED,
        expectedDeliveryTimeslot = timeSlot,
        transferOrderSkus = skus
    )

    private fun getTransferOrderSku(skuId: UUID, deliveries: List<DeliveryInfo> = emptyList()) = TransferOrderSku(
        toNumber = "TO-001",
        skuId = skuId,
        supplierId = supplierId,
        supplierName = supplierName,
        expectedQuantity = SkuQuantity.fromLong(10),
        deliveries = deliveries
    )

    private fun getDeliveryInfo() = DeliveryInfo(
        id = "DEL-001",
        deliveryTime = LocalDateTime.of(2025, 7, 5, 10, 30),
        state = DeliveryInfoStatus.OPEN,
        quantity = SkuQuantity.fromLong(5),
        expiryDate = LocalDate.of(2025, 8, 1)
    )

    companion object {
        @JvmStatic
        fun urlVariants(): Stream<UrlVariant> = Stream.of(
            UrlVariant(
                "url with week param",
            ) { skuId, sourceDc, week, _, _ -> "/transfer-orders?skuId=$skuId&dcCode=$sourceDc&weeks=$week" },
            UrlVariant(
                "url with fromDate/toDate params",
            ) { skuId, sourceDc, _, fromDate, toDate -> "/transfer-orders?skuId=$skuId&dcCode=$sourceDc&fromDate=$fromDate&toDate=$toDate" }
        )
    }
}

data class UrlVariant(
    val description: String,
    val urlBuilder: (skuId: UUID, sourceDc: String, week: String, fromDate: String, toDate: String) -> String,
)
