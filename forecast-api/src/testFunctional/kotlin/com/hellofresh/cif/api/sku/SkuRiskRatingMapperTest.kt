package com.hellofresh.cif.api.sku

import com.hellofresh.cif.api.calculation.generated.model.SkuRiskRatingEnum
import com.hellofresh.cif.api.schema.enums.SkuRiskRating
import java.util.stream.Stream
import kotlin.test.assertEquals
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

open class SkuRiskRatingMapperTest {

    @ParameterizedTest
    @MethodSource("provideRiskRatings")
    fun `map sku risk rating to sku`(inputRiskRatingEnum: SkuRiskRatingEnum?, expectedValue: String) {
        val skuRiskRating = SkuRiskRatingMapper.mapToSkuRiskRating(inputRiskRatingEnum)
        assertEquals(expectedValue, skuRiskRating.name)
    }

    companion object {
        @JvmStatic
        fun provideRiskRatings(): Stream<Arguments> =
            Stream.of(
                Arguments.of(SkuRiskRatingEnum.LOW, SkuRiskRating.LOW.name),
                Arguments.of(SkuRiskRatingEnum.MEDIUM, SkuRiskRating.MEDIUM.name),
                Arguments.of(SkuRiskRatingEnum.MEDIUM_LOW, SkuRiskRating.MEDIUM_LOW.name),
                Arguments.of(SkuRiskRatingEnum.HIGH, SkuRiskRating.HIGH.name),
                Arguments.of(SkuRiskRatingEnum.CRITICAL, SkuRiskRating.CRITICAL.name),
                Arguments.of(null, SkuRiskRating.MEDIUM.name) // Assuming null maps to MEDIUM by default
            )
    }
}
