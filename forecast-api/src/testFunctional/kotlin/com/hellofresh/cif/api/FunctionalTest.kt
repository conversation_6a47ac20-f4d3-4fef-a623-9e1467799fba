package com.hellofresh.cif.api

import InfraPreparation.getMigratedDataSource
import com.fasterxml.jackson.databind.ObjectMapper
import com.hellofresh.cif.api.calculation.CalculationResponseMapper
import com.hellofresh.cif.api.calculation.csvexport.CalculationsCsvConverter
import com.hellofresh.cif.api.calculation.db.CalculationRepository
import com.hellofresh.cif.api.calculation.db.CalculationsRepositoryImpl
import com.hellofresh.cif.api.calculation.projectedWaste.ProjectedWasteCalculationCsvConverter
import com.hellofresh.cif.api.calculation.projectedWaste.ProjectedWasteCalculationService
import com.hellofresh.cif.api.calculation.stockupdate.CalculationsPendingStockUpdateService
import com.hellofresh.cif.api.cleardown.CleardownTriggerRepository
import com.hellofresh.cif.api.cleardown.CleardownTriggerRepositoryImpl
import com.hellofresh.cif.api.configuration.ConfigService
import com.hellofresh.cif.api.demand.DemandRepository
import com.hellofresh.cif.api.demand.DemandRepositoryImpl
import com.hellofresh.cif.api.fileexport.repository.FileExportRequestRepository
import com.hellofresh.cif.api.fileexport.repository.FileExportRequestRepositoryImpl
import com.hellofresh.cif.api.fileupload.FileUploadService
import com.hellofresh.cif.api.fileupload.repository.FileUploadRepository
import com.hellofresh.cif.api.fileupload.repository.FileUploadRepositoryImpl
import com.hellofresh.cif.api.inventory.SkuInventoryDetailService
import com.hellofresh.cif.api.note.NoteRepository
import com.hellofresh.cif.api.note.NoteRepositoryImpl
import com.hellofresh.cif.api.po.PurchaseOrderService
import com.hellofresh.cif.api.reporting.StockReportingRepository
import com.hellofresh.cif.api.reporting.StockReportingRepositoryImpl
import com.hellofresh.cif.api.schema.Tables
import com.hellofresh.cif.api.schema.enums.Uom
import com.hellofresh.cif.api.schema.tables.records.SafetyStockConfRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.api.schema.tables.records.SupplierRecord
import com.hellofresh.cif.api.schema.tables.records.SupplyQuantityRecommendationConfRecord
import com.hellofresh.cif.api.schema.tables.records.SupplyQuantityRecommendationRecord
import com.hellofresh.cif.api.shortShelfLife.repository.ShortShelfLifeRepositoryImpl
import com.hellofresh.cif.api.stockupdate.StockUpdateApiService
import com.hellofresh.cif.api.stockupdate.StockUpdateCalculationService
import com.hellofresh.cif.api.stockupdate.StockUpdateInputDataService
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateApiRepository
import com.hellofresh.cif.api.stockupdate.repository.StockUpdateApiRepositoryImpl
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.SQRConfigRepositoryImpl
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.StockUpdatesReadRepository
import com.hellofresh.cif.api.supplyQuantityRecommendation.repository.StockUpdatesReadRepositoryImpl
import com.hellofresh.cif.calculator.CalculatorClient
import com.hellofresh.cif.calculator.models.DayCalculationResult
import com.hellofresh.cif.config.ConfigurationLoader
import com.hellofresh.cif.db.metrics.MetricsDSLContext
import com.hellofresh.cif.db.metrics.withMetrics
import com.hellofresh.cif.demand.lib.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.distributionCenter.models.DcWeek
import com.hellofresh.cif.distributionCenterLib.DcConfigService
import com.hellofresh.cif.distributionCenterLib.repo.DcRepository
import com.hellofresh.cif.distributionCenterLib.repo.DcRepositoryImpl
import com.hellofresh.cif.featureflags.StatsigTestFeatureFlagClient
import com.hellofresh.cif.inventory.InventoryActivityRepository
import com.hellofresh.cif.inventory.InventoryActivityRepositoryImpl
import com.hellofresh.cif.inventory.InventoryRepository
import com.hellofresh.cif.inventory.InventoryRepositoryImpl
import com.hellofresh.cif.inventory.InventoryService
import com.hellofresh.cif.inventory.LiveInventoryRepository
import com.hellofresh.cif.inventory.LiveInventoryRepositoryImpl
import com.hellofresh.cif.inventory.StockUpdateRepository
import com.hellofresh.cif.inventory.StockUpdateRepositoryImpl
import com.hellofresh.cif.inventory.StockUpdateService
import com.hellofresh.cif.inventory.lib.schema.enums.Uom.UOM_UNIT
import com.hellofresh.cif.inventory.lib.schema.tables.records.StockUpdateRecord
import com.hellofresh.cif.lib.metrics.createMeterRegistry
import com.hellofresh.cif.models.SkuQuantity
import com.hellofresh.cif.models.SkuUOM
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepository
import com.hellofresh.cif.purchaseorder.PurchaseOrderRepositoryImpl
import com.hellofresh.cif.safetystock.repository.SafetyStockRepository
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepository
import com.hellofresh.cif.skuinput.repo.SkuInputDataRepositoryImpl
import com.hellofresh.cif.sqrlib.schema.tables.records.SqrShortShelfLifeConfRecord
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDER
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDERS_GRN_VIEW
import com.hellofresh.cif.transfer_order_lib.schema.Tables.TRANSFER_ORDER_SKUS
import com.hellofresh.cif.transfer_order_lib.schema.tables.records.GoodsReceivedNoteRecord
import com.hellofresh.cif.transfer_order_lib.schema.tables.records.TransferOrderRecord
import com.hellofresh.cif.transfer_order_lib.schema.tables.records.TransferOrderSkusRecord
import com.hellofresh.cif.transferorder.db.TransferOrderRepository
import com.hellofresh.cif.transferorder.db.TransferOrderRepositoryImpl
import com.hellofresh.cif.transferorder.model.TransferOrder
import com.hellofresh.cif.transferorder.model.TransferOrderSku
import com.hellofresh.inventory.models.UsableInventoryEvaluator
import io.micrometer.core.instrument.simple.SimpleMeterRegistry
import io.mockk.unmockkAll
import java.math.BigDecimal
import java.time.DayOfWeek.MONDAY
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneOffset.UTC
import java.util.UUID
import java.util.concurrent.Executors
import kotlin.random.Random
import org.jooq.SQLDialect
import org.jooq.impl.DSL
import org.jooq.impl.DefaultConfiguration
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeAll

open class FunctionalTest {
    val jwtSecret = "testSecret"
    val defaultTestStrategy = "ALGORITHM_FORECASTVARIANCE"
    val testSuperManagerRole = arrayListOf("siv.de.all.recommendation_manager")
    val testOnlyManager = arrayListOf("")
    val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())
    private val testAuthorEmail = "<EMAIL>"
    private val testAuthorName = "test-author"
    private val today = LocalDate.now()

    @AfterEach
    fun clear() {
        dsl.deleteFrom(Tables.CALCULATION).execute()
        dsl.deleteFrom(Tables.PRE_PRODUCTION_CALCULATION).execute()
        dsl.deleteFrom(Tables.LIVE_INVENTORY_CALCULATION).execute()
        dsl.deleteFrom(Tables.LIVE_INVENTORY_PRE_PRODUCTION_CALCULATION).execute()
        dsl.deleteFrom(Tables.DC_CONFIG).execute()
        dsl.deleteFrom(Tables.SKU_SPECIFICATION).execute()
        dsl.deleteFrom(Tables.DEMAND).execute()
        dsl.deleteFrom(Tables.NOTE).execute()
        dsl.deleteFrom(Tables.PURCHASE_ORDER_SKU).execute()
        dsl.deleteFrom(Tables.PURCHASE_ORDER).execute()
        dsl.deleteFrom(Tables.SUPPLIER).execute()
        dsl.deleteFrom(Tables.INVENTORY_VARIANCE).execute()
        dsl.deleteFrom(Tables.INVENTORY_CLEARDOWN_TRIGGER).execute()
        dsl.deleteFrom(Tables.STOCK_UPDATE).execute()
        dsl.deleteFrom(Tables.SUPPLY_QUANTITY_RECOMMENDATION_CONF).execute()
        dsl.deleteFrom(Tables.SUPPLY_QUANTITY_RECOMMENDATION).execute()
        dsl.deleteFrom(Tables.SQR_SHORT_SHELF_LIFE_CONF).execute()
        dsl.deleteFrom(Tables.SQR_SHORT_SHELF_LIFE).execute()
        dsl.deleteFrom(Tables.SAFETY_STOCK_CONF).execute()
        dsl.deleteFrom(Tables.SAFETY_STOCKS).execute()
        dsl.deleteFrom(Tables.FILE_EXPORT_REQUEST).execute()
        unmockkAll()
        dcConfigService.fetchOnDemand()
        refreshSkuView()
        dsl.deleteFrom(TRANSFER_ORDER_SKUS).execute()
        dsl.deleteFrom(TRANSFER_ORDER).execute()
        refreshTransferOrderGRNView()
    }

    fun insertSkus(skus: Set<UUID>): List<SkuSpecificationRecord> =
        skus.map {
            SkuSpecificationRecord().apply {
                this.id = it
                this.code = UUID.randomUUID().toString()
                this.name = UUID.randomUUID().toString()
                this.acceptableCodeLife = 0
                this.category = UUID.randomUUID().toString().take(3)
                this.packaging = ""
                this.coolingType = ""
                this.market = "market"
                uom = Uom.UOM_LBS
            }
        }.also {
            dsl.batchInsert(it).execute()
            refreshSkuView()
        }

    fun insertSkuRecords(vararg skus: SkuSpecificationRecord) {
        dsl.batchInsert(*skus).execute()
        refreshSkuView()
    }

    fun insertSkuRecords(skus: Collection<SkuSpecificationRecord>) {
        dsl.batchInsert(skus).execute()
        refreshSkuView()
    }

    fun refreshSkuView() =
        dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

    fun createSQRConfigRecord(
        dcCodeParam: String = "VE",
        weekParam: String = "2024-W20",
        skuIdParam: UUID = UUID.randomUUID(),
        recommendationEnabledParam: Boolean = Random.nextBoolean(),
        multiWeekEnabledParam: Boolean = Random.nextBoolean()
    ) =
        SupplyQuantityRecommendationConfRecord().apply {
            dcCode = dcCodeParam
            week = weekParam
            skuId = skuIdParam
            recommendationEnabled = recommendationEnabledParam
            multiWeekEnabled = multiWeekEnabledParam
        }

    fun createSafetyStockConfigRecord(
        dcCodeParam: String = "VE",
        weekParam: String = "2024-W20",
        skuIdParam: UUID = UUID.randomUUID(),
        safetyMultiplierParam: BigDecimal = BigDecimal.ONE
    ) =
        SafetyStockConfRecord().apply {
            dcCode = dcCodeParam
            week = weekParam
            skuId = skuIdParam
            riskMultiplier = safetyMultiplierParam
        }

    fun createSQRRecord(
        dcCodeParam: String = "VE",
        weekParam: String = "2024-W20",
        skuIdParam: UUID = UUID.randomUUID(),
        recommendationEnabledParam: Boolean = false,
        multiWeekEnabledParam: Boolean = false
    ) =
        SupplyQuantityRecommendationRecord().apply {
            dcCode = dcCodeParam
            week = weekParam
            skuId = skuIdParam
            uom = Uom.UOM_OZ
            sqr = BigDecimal(10)
            demand = BigDecimal(10L)
            inventoryRollover = BigDecimal(10.5)
            safetyStock = BigDecimal(10)
            recommendationEnabled = recommendationEnabledParam
            multiWeekEnabled = multiWeekEnabledParam
        }

    @Suppress("LongParameterList")
    fun createStockUpdateRecord(
        dcCodeParam: String,
        dateParam: LocalDate,
        weekParam: String = DcWeek(dateParam, MONDAY).toString(),
        qtyParam: BigDecimal,
        skuIdParam: UUID,
        versionParam: Int,
        deletedParam: Boolean? = false,
        uom: com.hellofresh.cif.inventory.lib.schema.enums.Uom = UOM_UNIT,
    ): StockUpdateRecord = StockUpdateRecord().apply {
        this.dcCode = dcCodeParam
        this.skuId = skuIdParam
        quantity = qtyParam
        this.uom = uom
        this.date = dateParam
        week = weekParam
        reason = "test-reason"
        reasonDetail = "test-reason-detail"
        authorName = testAuthorName
        authorEmail = testAuthorEmail
        this.deleted = deletedParam
        this.version = versionParam
    }.also {
        dsl.batchInsert(it).execute()
    }

    fun createDayCalculationResult(
        skuId: UUID,
        dcCode: String = "VE",
        todayParam: LocalDate = today,
        productionWeekParam: String = "2022-W41"
    ) = DayCalculationResult(
        SkuUOM.UOM_UNIT,
        skuId, dcCode, todayParam,
        SkuQuantity.fromLong(
            0,
        ),
        SkuQuantity.fromLong(1), SkuQuantity.fromLong(2), SkuQuantity.fromLong(3),
        setOf(
            "A",
        ),
        SkuQuantity.fromLong(
            4,
        ),
        setOf(
            "B",
        ),
        SkuQuantity.fromLong(
            5,
        ),
        SkuQuantity.fromLong(6), SkuQuantity.fromLong(7), SkuQuantity.fromLong(8), productionWeekParam,
        SkuQuantity.fromLong(9), netNeeds = SkuQuantity.fromLong(10),
        strategy = defaultTestStrategy,
    )

    fun createDcConfig(
        dcCode: String = "DC",
        market: String = "DACH",
        zoneId: String = "Europe/Berlin",
        block: DcConfigRecord.() -> Unit = {
        }
    ) =
        DcConfigRecord(
            dcCode,
            market,
            "MONDAY",
            "FRIDAY",
            zoneId,
            true,
            LocalDateTime.now(),
            LocalDateTime.now(),
            LocalDateTime.now(),
            true,
            LocalTime.MIDNIGHT,
            null,
            LocalTime.now(),
            emptyArray()
        ).apply { block() }
            .also {
                dsl.batchInsert(it).execute()
            }

    @SuppressWarnings("LongParameterList")
    fun insertSQRShortShelfLifeConfiguration(
        dcCode: String,
        date: LocalDate,
        skuId: UUID,
        bufferPercentage: BigDecimal,
        bufferAdditional: BigDecimal,
        touchlessOrderingEnabled: Boolean = true
    ) =
        SqrShortShelfLifeConfRecord().apply {
            this.dcCode = dcCode
            this.skuId = skuId
            this.date = date
            this.bufferPercentage = bufferPercentage
            this.bufferAdditional = bufferAdditional
            this.touchlessOrderingEnabled = touchlessOrderingEnabled
        }.also {
            dsl.batchInsert(it).execute()
        }

    fun insertTransferOrder(to: TransferOrder) = TransferOrderRecord().apply {
        transferOrderNumber = to.transferOrderNumber
        sourceDc = to.sourceDc
        destinationDc = to.destinationDc
        status = com.hellofresh.cif.transfer_order_lib.schema.enums.TransferOrderStatus.STATE_RESERVED
        week = to.week
        marketCode = to.marketCode
        deliveryStartTime = to.expectedDeliveryTimeslot?.startTime?.toOffsetDateTime()
        deliveryEndTime = to.expectedDeliveryTimeslot?.endTime?.toOffsetDateTime()
    }.also {
        dsl.batchInsert(it).execute()
    }

    fun insertTransferOrderSkus(toSku: TransferOrderSku) = TransferOrderSkusRecord().apply {
        transferOrderNumber = toSku.toNumber
        skuId = toSku.skuId
        supplierId = toSku.supplierId
        quantity = toSku.expectedQuantity.getValue()
        uom = com.hellofresh.cif.transfer_order_lib.schema.enums.Uom.UOM_UNIT
    }.also {
        dsl.batchInsert(it).execute()
    }

    fun insertSupplier(id: UUID, name: String) = SupplierRecord().apply {
        this.id = id
        this.name = name
    }.also {
        dsl.batchInsert(it).execute()
    }

    fun insertGRN(skuId: UUID, dcCode: String, quantity: BigDecimal, poNumber: String) =
        GoodsReceivedNoteRecord().apply {
            this.skuId = skuId
            this.dcCode = dcCode
            this.deliveryTime = LocalDateTime.now().plusMinutes(30).atZone(UTC).toOffsetDateTime()
            this.quantity = quantity
            this.deliveryStatus = "CLOSED"
            this.poRef = "$poNumber-ref"
            this.poNumber = poNumber
            deliveryId = UUID.randomUUID().toString()
        }.also {
            dsl.batchInsert(it).execute()
        }

    fun refreshTransferOrderGRNView() =
        dsl.query("refresh materialized view ${TRANSFER_ORDERS_GRN_VIEW.name}").execute()

    companion object {
        val objectMapper: ObjectMapper = ObjectMapper().findAndRegisterModules()
        private val statsigFeatureFlagClient = StatsigTestFeatureFlagClient(emptySet())

        val calculationResponseMapper = CalculationResponseMapper(statsigFeatureFlagClient)

        lateinit var dsl: MetricsDSLContext
        lateinit var configService: ConfigService
        private lateinit var inventoryService: InventoryService
        lateinit var prodCalculationsRepo: CalculationRepository
        lateinit var preProdCalculationsRepo: CalculationRepository
        private lateinit var dcConfigRepository: DcRepository
        lateinit var dcConfigService: DcConfigService
        lateinit var usableInventoryEvaluator: UsableInventoryEvaluator
        lateinit var calculationsCsvConverter: CalculationsCsvConverter
        lateinit var inventoryRepository: InventoryRepository
        private lateinit var liveInventoryRepository: LiveInventoryRepository
        private lateinit var inventoryActivityRepository: InventoryActivityRepository
        lateinit var skuInputDataRepository: SkuInputDataRepository
        lateinit var skuInventoryDetailService: SkuInventoryDetailService
        lateinit var stockReportingRepository: StockReportingRepository
        lateinit var purchaseOrderRepository: PurchaseOrderRepository
        lateinit var transferOrderRepository: TransferOrderRepository
        lateinit var purchaseOrderService: PurchaseOrderService
        lateinit var projectedWasteCalculationService: ProjectedWasteCalculationService
        lateinit var projectedWasteCalculationCsvConverter: ProjectedWasteCalculationCsvConverter
        lateinit var noteRepository: NoteRepository
        lateinit var demandRepository: DemandRepository
        private lateinit var safetyStockRepository: SafetyStockRepository
        lateinit var cleardownTriggerRepository: CleardownTriggerRepository
        lateinit var stockUpdateApiRepository: StockUpdateApiRepository
        private lateinit var stockUpdateRepository: StockUpdateRepository
        private lateinit var stockUpdateService: StockUpdateService
        private lateinit var stockUpdateInputDataService: StockUpdateInputDataService
        private lateinit var calculatorClient: CalculatorClient
        private lateinit var stockUpdateCalculationService: StockUpdateCalculationService
        private lateinit var stockUpdateApiService: StockUpdateApiService
        lateinit var calculationsPendingStockUpdateService: CalculationsPendingStockUpdateService
        lateinit var sqrConfigRepositoryImpl: SQRConfigRepositoryImpl
        lateinit var shortShelfLifeRepositoryImpl: ShortShelfLifeRepositoryImpl
        lateinit var stockUpdatesReadRepository: StockUpdatesReadRepository
        lateinit var fileExportRequestRepository: FileExportRequestRepository
        lateinit var fileUploadRepository: FileUploadRepository
        lateinit var fileUploadService: FileUploadService

        private val dataSource = getMigratedDataSource()
        private val brandConsumptionMarkets = ConfigurationLoader.getSet("csv.brands.consumption.markets")

        @BeforeAll
        @JvmStatic
        fun init() {
            val dbConfiguration = DefaultConfiguration()
                .apply {
                    setSQLDialect(SQLDialect.POSTGRES)
                    setDataSource(dataSource)
                    setExecutor(Executors.newFixedThreadPool(2))
                }
            dsl = DSL.using(dbConfiguration).withMetrics(SimpleMeterRegistry())
            dcConfigRepository = DcRepositoryImpl(dsl)
            dcConfigService = DcConfigService(SimpleMeterRegistry(), dcConfigRepository)
            usableInventoryEvaluator = UsableInventoryEvaluator(statsigFeatureFlagClient)
            stockUpdateRepository = StockUpdateRepositoryImpl(dsl)
            fileExportRequestRepository = FileExportRequestRepositoryImpl(dsl)
            prodCalculationsRepo =
                CalculationsRepositoryImpl(dsl)
            preProdCalculationsRepo =
                CalculationsRepositoryImpl(
                    dsl,
                    isProdCalculation = false,
                )
            inventoryRepository = InventoryRepositoryImpl(dsl)
            liveInventoryRepository = LiveInventoryRepositoryImpl(dsl)
            inventoryActivityRepository = InventoryActivityRepositoryImpl(dsl)
            inventoryService = InventoryService(
                inventoryRepository,
                liveInventoryRepository,
                inventoryActivityRepository,
                statsigFeatureFlagClient
            )
            configService = ConfigService(dcConfigService, inventoryService)
            inventoryRepository = InventoryRepositoryImpl(dsl)
            skuInputDataRepository = SkuInputDataRepositoryImpl(dsl, dcConfigService)
            cleardownTriggerRepository = CleardownTriggerRepositoryImpl(dsl, dcConfigService)
            skuInventoryDetailService = SkuInventoryDetailService(
                dcConfigService = dcConfigService,
                inventoryRepository = inventoryRepository,
                skuInputDataRepository = skuInputDataRepository,
                statsigFeatureFlagClient = statsigFeatureFlagClient,
            )
            stockReportingRepository = StockReportingRepositoryImpl(dsl)
            noteRepository = NoteRepositoryImpl(dsl, dsl)
            demandRepository = DemandRepositoryImpl(dsl, dcConfigService)
            safetyStockRepository = SafetyStockRepository(dsl)
            purchaseOrderRepository = PurchaseOrderRepositoryImpl(dsl, dcConfigService, statsigFeatureFlagClient)
            transferOrderRepository = TransferOrderRepositoryImpl(dsl, dcConfigService)
            stockUpdateApiRepository = StockUpdateApiRepositoryImpl(dcConfigService, dsl, dsl)
            purchaseOrderService = PurchaseOrderService(purchaseOrderRepository, configService)
            calculationsCsvConverter =
                CalculationsCsvConverter(dcConfigService, statsigFeatureFlagClient, brandConsumptionMarkets)
            projectedWasteCalculationService = ProjectedWasteCalculationService(
                meterRegistry = createMeterRegistry(),
                dsl,
                dcConfigService = dcConfigService,
                statsigFeatureFlagClient = statsigFeatureFlagClient,
            )
            projectedWasteCalculationCsvConverter = ProjectedWasteCalculationCsvConverter()
            calculatorClient = CalculatorClient(statsigFeatureFlagClient)
            stockUpdateService = StockUpdateService(inventoryService, stockUpdateRepository)
            stockUpdateInputDataService = StockUpdateInputDataService(
                skuInputDataRepository,
                inventoryService,
                purchaseOrderRepository,
                transferOrderRepository,
                com.hellofresh.cif.demand.DemandRepositoryImpl(dsl, statsigFeatureFlagClient),
                safetyStockRepository, emptySet()
            )
            stockUpdateCalculationService = StockUpdateCalculationService(
                calculatorClient,
                stockUpdateInputDataService
            )
            stockUpdateApiService =
                StockUpdateApiService(
                    stockUpdateService,
                    stockUpdateCalculationService,
                    stockUpdateApiRepository,
                    dcConfigService,
                    skuInputDataRepository
                )
            calculationsPendingStockUpdateService =
                CalculationsPendingStockUpdateService(dcConfigService, stockUpdateApiService, statsigFeatureFlagClient)
            sqrConfigRepositoryImpl = SQRConfigRepositoryImpl(dsl)
            shortShelfLifeRepositoryImpl = ShortShelfLifeRepositoryImpl(dsl, dsl)
            stockUpdatesReadRepository = StockUpdatesReadRepositoryImpl(dsl)
            fileUploadRepository = FileUploadRepositoryImpl(dsl)
            fileUploadService = FileUploadService(fileUploadRepository)
        }
    }
}
