package com.hellofresh.cif.api.config

import com.hellofresh.cif.api.FunctionalTest
import com.hellofresh.cif.api.configuration.DcConfiguration
import com.hellofresh.cif.api.configuration.DcConfiguration.Companion.getCurrentDcWeek
import com.hellofresh.cif.api.schema.tables.DcConfig
import com.hellofresh.cif.api.schema.tables.records.CalculationRecord
import com.hellofresh.cif.api.schema.tables.records.DcConfigRecord
import com.hellofresh.cif.api.schema.tables.records.SkuSpecificationRecord
import com.hellofresh.cif.distributionCenter.models.DistributionCenterConfiguration
import com.hellofresh.cif.distributionCenter.models.WmsSystem
import com.hellofresh.cif.sku_inputs_lib.schema.Tables.SKU_SPECIFICATION_VIEW
import java.time.DayOfWeek
import java.time.DayOfWeek.FRIDAY
import java.time.DayOfWeek.valueOf
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZoneId
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import kotlin.test.assertEquals
import kotlinx.coroutines.runBlocking
import org.junit.Assert.assertNull
import org.junit.jupiter.api.Test

class ConfigServiceTest : FunctionalTest() {
    private val zoneIdParam = "Europe/Berlin"
    private val clearDownDay = "FRIDAY"
    private val dcConfigRecordVe: DcConfigRecord = DcConfigRecord().apply {
        dcCode = "VE"
        market = "DACH"
        productionStart = "MONDAY"
        cleardown = clearDownDay
        zoneId = zoneIdParam
        enabled = true
        hasCleardown = true
        recordTimestamp_ = LocalDateTime.now()
        createdAt = LocalDateTime.now()
        updatedAt = LocalDateTime.now()
        scheduledCleardownTime = LocalTime.now()
        poCutoffTime = LocalTime.now()
    }
    private val skuSpecification =
        SkuSpecificationRecord()
            .apply {
                id = UUID.randomUUID()
                name = "Some Sku"
                code = "SPI-00-000271-1"
                category = "SPI"
                coolingType = ""
                packaging = "Assembly-Cool Pouch"
                acceptableCodeLife = 0
                market = "dach"
            }

    private val calculationVE = CalculationRecord().apply {
        dcCode = "VE"
        productionWeek = getCurrentDcWeek(FRIDAY).toString()
        cskuId = skuSpecification.id
        date = LocalDate.now()
    }
    private val defaultdistributionCenterConfiguration = DistributionCenterConfiguration(
        dcConfigRecordVe.dcCode,
        DayOfWeek.valueOf(dcConfigRecordVe.productionStart),
        DayOfWeek.valueOf(dcConfigRecordVe.cleardown),
        dcConfigRecordVe.market,
        ZoneId.of(dcConfigRecordVe.zoneId),
        wmsType = WmsSystem.WMS_SYSTEM_FCMS,
        poCutoffTime = dcConfigRecordVe.poCutoffTime,
    )
    private val expectedLatestCleardown = defaultdistributionCenterConfiguration.getLatestCleardown()
    private val expectedStartDate = defaultdistributionCenterConfiguration.getProductionStartDate(LocalDate.now())

    @Test
    fun `should return a previously stored DC configuration record`() {
        // given
        dsl.batchInsert(dcConfigRecordVe, skuSpecification, calculationVE).execute()

        // when
        val result = runBlocking { configService.fetchByMarket(dcConfigRecordVe.market) }

        // then
        assertEquals(1, result.size)
        val resultDcConfiguration = result[dcConfigRecordVe.dcCode]!!
        val expectedLatestCleardownTime = LocalDateTime.now(ZoneId.of(zoneIdParam)).with(
            TemporalAdjusters.previousOrSame(valueOf(clearDownDay)),
        )
        assertDcConfiguration(
            resultDcConfiguration,
            result,
            expectedStartDate,
            expectedLatestCleardown,
            expectedLatestCleardownTime,
        )
    }

    @Test
    fun `disabled DC doesn't have to be returned`() {
        // given
        dsl.batchInsert(
            dcConfigRecordVe.setEnabled(false),
            skuSpecification,
            calculationVE,
        ).execute()
        fun refreshSkuView() =
            dsl.query("refresh materialized view concurrently ${SKU_SPECIFICATION_VIEW.name}").execute()

        // when
        val result = runBlocking { configService.fetchByMarket(dcConfigRecordVe.market) }

        // then
        assertEquals(0, result.size)
    }

    @Test
    fun `should return a DC by code`() {
        // given
        val dcConfigDbRecord = dsl.insertInto(DcConfig.DC_CONFIG).set(dcConfigRecordVe).returning().fetch()[0]

        // when
        val result = runBlocking { configService.getByCode(dcConfigRecordVe.dcCode) }

        // then
        assertEquals(dcConfigDbRecord.dcCode, result?.dcCode)
        assertEquals(dcConfigDbRecord.productionStart, result?.productionStart?.name)
        assertEquals(dcConfigDbRecord.cleardown, result?.cleardown?.name)
        assertEquals(dcConfigDbRecord.market, result?.market)
        assertEquals(dcConfigDbRecord.zoneId, result?.zoneId?.id)
        assertEquals(dcConfigDbRecord.enabled, result?.enabled)
        assertEquals(dcConfigDbRecord.hasCleardown, result?.hasCleardown)
    }

    @Test
    fun `should return null if no DC is found`() {
        assertNull(runBlocking { configService.getByCode("XX") })
    }

    private fun assertDcConfiguration(
        resultDcConfiguration: DcConfiguration,
        result: Map<String, DcConfiguration>,
        expectedStartDate: LocalDate?,
        expectedLatestCleardown: LocalDate?,
        expectedLatestCleardownTime: LocalDateTime?,
    ) {
        assertEquals(dcConfigRecordVe.market, resultDcConfiguration.market)
        assertEquals(dcConfigRecordVe.dcCode, result.keys.first())
        assertEquals(dcConfigRecordVe.cleardown, resultDcConfiguration.clearDownDay.name)
        assertEquals(dcConfigRecordVe.productionStart, resultDcConfiguration.productionStartDay.name)
        assertEquals(expectedStartDate, resultDcConfiguration.startDate)
        assertEquals(expectedLatestCleardown, resultDcConfiguration.lastCleardown)
        assertEquals(
            expectedLatestCleardownTime?.toLocalDate(),
            resultDcConfiguration.lastCleardownTime?.toLocalDate(),
        )
    }
}
